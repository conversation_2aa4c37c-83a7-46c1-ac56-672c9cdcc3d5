# bundle exec rubocop --regenerate-todo --exclude-limit=10000 --auto-gen-only-exclude
inherit_from: .rubocop_todo.yml

plugins:
  - rubocop-capybara
  - rubocop-factory_bot
  - rubocop-performance
  - rubocop-rails
  - rubocop-rspec
  - rubocop-rspec_rails

require:
  - ./lib/rubocop/cops/customer_specific/explicit_subdomain_check.rb

AllCops:
  TargetRubyVersion: 3.1
  NewCops: enable
  Exclude:
    - 'Guardfile'
    - 'Rakefile'
    - 'bin/**/*'
    - 'config/**/*'
    - 'db/**/*'
    - 'db/schema.rb'
    - 'node_modules/**/*'

Rails:
  Enabled: true

Capybara/ClickLinkOrButtonStyle:
  Enabled: false

FactoryBot/FactoryAssociationWithStrategy:
  Enabled: false

Metrics/BlockLength:
  Exclude:
    - 'app/admin/**/*'

# Increase maximum method length to 25
Metrics/MethodLength:
  Max: 25

Metrics/AbcSize:
  Max: 25

# Use compact class and module definitions ('::') instead of nested ones
Style/ClassAndModuleChildren:
  EnforcedStyle: compact

# The below cops are disabled for initial rubocop install, but should be 
# enabled later.

# Enable when app is more complete
Lint/UnusedMethodArgument:
  Enabled: false

# Disable documentation check at the start of classes
Style/Documentation:
  Enabled: false

Style/EachWithObject:
  Enabled: false

# Disable check for the comment `# frozen_string_literal: true` at the
# beginning of files. Should be enabled later before transition to
# Ruby 3.0
# See rubocop documentation for more details
Style/FrozenStringLiteralComment:
  Enabled: false

Style/StringConcatenation:
  Mode: conservative # Prevent interpolating `content_tag`, etc.

Style/StringLiterals:
  EnforcedStyle: single_quotes
  SupportedStyles:
    - single_quotes
    - double_quotes

Style/HashLikeCase:
  Enabled: false

Style/LambdaCall:
  Enabled: false # I like .()

Style/MultilineBlockChain:
  Enabled: false

Style/OpenStructUse:
  Enabled: false

Style/PercentLiteralDelimiters:
  PreferredDelimiters:
    "%i": "[]"

Style/SignalException:
  EnforcedStyle: semantic

Style/SymbolArray:
  MinSize: 3

Style/WordArray:
  MinSize: 3

# Too much for now
Lint/AmbiguousBlockAssociation:
  Enabled: false

Layout/LineLength:
  Max: 100

Layout/SpaceAroundOperators:
  EnforcedStyleForExponentOperator: space

Rails/ActionOrder:
  Enabled: false

Rails/I18nLocaleTexts:
  Enabled: false

Rails/RakeEnvironment:
  Enabled: false

RSpec/DescribeClass:
  Enabled: false

RSpec/ExampleLength:
  Enabled: false

RSpec/ExpectChange:
  EnforcedStyle: block

RSpec/ImplicitSubject:
  Enabled: false

RSpec/MultipleExpectations:
  Enabled: false

RSpec/MultipleMemoizedHelpers:
  Max: 10

RSpec/NamedSubject:
  Enabled: false

RSpec/NestedGroups:
  Max: 5

RSpec/ScatteredLet:
  Enabled: false

RSpecRails/AvoidSetupHook:
  Enabled: false

Style/HashAsLastArrayItem:
  Enabled: false

Style/HashSyntax:
  EnforcedShorthandSyntax: either

Naming/InclusiveLanguage:
  Enabled: false

Naming/VariableNumber:
  AllowedPatterns:
    - '1099'

Performance/MethodObjectAsBlock:
  Enabled: false

Lint/UselessAssignment:
  AutoCorrect: false

Lint/ItWithoutArgumentsInBlock:
  Enabled: false
