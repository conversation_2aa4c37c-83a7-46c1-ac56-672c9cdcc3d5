# Add your own tasks in files placed in lib/tasks ending in .rake, for example
# lib/tasks/capistrano.rake, and they will automatically be available to Rake.

require File.expand_path('../config/application', __FILE__)

Rails.application.load_tasks

require 'elasticsearch/rails/tasks/import'

if Rails.env.development?
  require 'yard'
  YARD::Rake::YardocTask.new do |yard|
    yard.name = 'doc'
    yard.files = ['app/**/*.rb', 'lib/**/*.rb']
  end
end

unless Rails.env.production?
  require 'rubocop/rake_task'
  RuboCop::RakeTask.new do |task|
    task.requires << 'rubocop-rails'
    task.requires << 'rubocop-performance'
    task.requires << 'rubocop-rspec'
  end

  require 'haml_lint/rake_task'
  HamlLint::RakeTask.new

  require 'scss_lint/rake_task'
  SCSSLint::RakeTask.new do |t|
    t.files = ['app/assets/stylesheets']
  end

  task default: [:rubocop, :haml_lint, :spec, :eslint, :scss_lint]

  task :eslint do
    system 'node_modules/eslint/bin/eslint.js app/assets/javascripts/**/* frontend/**/*'
  end

  def changed_files(base)
    base ||= 'master...'
    `git diff --name-only --diff-filter=ACMRTUXB #{base}`.split("\n")
  end

  def check_files(files)
    ruby_files = files.grep(/\.rb$/)
    if ruby_files.any?

      ignore = YAML.load(File.open('.rubocop.yml'))['AllCops']['Exclude']
      ruby_files.select! do |file|
        ignore.none? do |glob|
          File.fnmatch(glob, file)
        end
      end

      rubocop = "rubocop #{ruby_files.join(' ')}"
      puts rubocop
      system rubocop
    else
      puts 'no staged ruby files'
    end

    haml_files = files.grep(/\.haml$/)
    if haml_files.any?
      haml_lint = "haml-lint #{haml_files.join(' ')}"
      puts haml_lint
      system haml_lint
    else
      puts 'no staged haml files'
    end

    js_files = files.grep(/\.jsx?(\.es6)?$/)
    if js_files.any?
      eslint = "node_modules/eslint/bin/eslint.js #{js_files.join(' ')}"
      puts eslint
      system eslint
    else
      puts 'no staged javascript files'
    end

    scss_files = files.grep(/.scss/)
    if scss_files.any?
      scss_lint = "scss-lint #{scss_files.join(' ')}"
      puts scss_lint
      system scss_lint
    else
      puts 'no staged scss files'
    end
  end

  task :check do
    files = `git diff --cached --name-only --diff-filter=ACMRTUXB`
    check_files(files.split("\n"))
  end

  task :check_branch, [:base] do |task, args|
    files = changed_files(args[:base])
    check_files(files)
  end

  task :updated_specs, [:base] do |task, args|
    files = changed_files(args[:base])
    specs = files.grep(/\Aspec.*_spec\.rb\Z/)
    if specs.any?
      system("bundle exec rspec #{specs.join(' ')}")
    else
      puts 'No changed specs.'
    end
  end
end
