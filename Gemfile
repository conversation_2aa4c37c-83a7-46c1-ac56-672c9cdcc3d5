source 'https://rubygems.org'

ruby File.read('.ruby-version').strip

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 7.0'

# Use postgresql as the database for Active Record
gem 'pg', '>= 0.18', '< 2.0'
# Use LibSass for stylesheets
gem 'sassc-rails'
# Use Terser as compressor for JavaScript assets
gem 'terser'
# Use CoffeeScript for .coffee assets and views
gem 'coffee-rails'

# Use jquery as the JavaScript library
gem 'jquery-rails'

# Turbolinks makes following links in your web application faster. Read more: https://github.com/rails/turbolinks
gem 'turbolinks'

# Use Puma as the app server
gem 'puma'

gem 'elasticsearch'
gem 'elasticsearch-model', '7.2.0'
gem 'elasticsearch-rails', '< 8'

gem 'money-rails'

# Authentication
gem 'devise'
gem 'doorkeeper', '~> 5.8'

gem 'kaminari', '>= 0.17.0'

gem 'uri-js-rails'

gem 'd3-rails', '~> 3.5'

gem 'color'

gem 'factory_bot_rails'

gem 'faker'

gem 'react-rails', '< 2.7'

gem 'lodash-rails'

# File Uploads
gem 'aws-sdk-s3'
gem 'image_processing', '~> 1'
gem 'kt-paperclip'
gem 'shrine'

gem 'babel-transpiler' # Sprockets ES6
gem 'sprockets', '~> 4'

gem 'active_model_serializers', '~> 0.10.6'

gem 'redis'

gem 'flipper', '< 1'
gem 'flipper-redis'
gem 'flipper-ui'

gem 'hamlit-rails'

gem 'phony_rails'

# Date recurrence
gem 'ice_cube', github: 'ice-cube-ruby/ice_cube', branch: 'master'

# Efficient redis-based background job processing and web interface
gem 'sidekiq'
gem 'sidekiq-limit_fetch'
gem 'sidekiq-scheduler'

gem 'plutus', github: 'aesthetikx/plutus', branch: 'rails7'

# Custom error page handling
gem 'gaffe'

# Geocode and reverse geocode addresses
gem 'geocoder'

# Lookup time zones by latitude and longitude
gem 'geo_names'

# ActiveAdmin
gem 'activeadmin'

# CORS for api routes
gem 'rack-cors'

# Excel / spreadsheet support
gem 'rubyXL', '~> 3.3'

# Generate and parse zip files
gem 'rubyzip'
gem 'zipline'

# Postgresql Views
gem 'scenic'

# PDF Form Filling / Parsing
gem 'pdf-forms'

gem 'amazing_print'

gem 'attr_encrypted'

gem 'http'

gem 'appsignal'
gem 'honeybadger'

gem 'boring_science', github: 'aesthetikx/boring_science'

gem 'american_date'

gem 'grover' # Generate pdfs from html

# Handling sendgrid inbound parse emails
gem 'griddler'
gem 'griddler-mailgun'

# Postgresql schema based multi tennancy
gem 'ros-apartment', require: 'apartment', github: 'aesthetikx/apartment', branch: 'connection_handling_patch'
gem 'ros-apartment-sidekiq', require: 'apartment-sidekiq'

# Form validation
gem 'reform'
gem 'reform-rails'

# Dynamic form collections
gem 'cocoon'

# Help find n+1 queries, unused eager loads, missing counter caches
gem 'bullet'

# Decorators / view models
gem 'draper'

# Throttling
gem 'rack-attack'

# Heavy metal SOAP client
gem 'savon', '~> 2'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.1.0', require: false

# SMS and Voice
gem 'twilio-ruby'

# Open-uri wrapper for downloading to temp files
gem 'down'

# Server side component of robot prevention
gem 'recaptcha', require: 'recaptcha/rails'

# Required for Zeamster middleware
gem 'faraday_middleware'

# Extend ActiveRecord pluck optionally to return array of hashes
gem 'pluck_to_hash'

# JSON web tokens for api authentication
gem 'jwt'

# Assist with cloning Active::Record objects
gem 'amoeba'

# Clean whitespace on Active::Record attributes
gem 'strip_attributes'

gem 'sitemap_generator'

# Generate documents from docx templates
gem 'docx_replace', github: 'aesthetikx/docx_replace', branch: 'master'
gem 'libreconv'
gem 'liquidoc', github: 'aesthetikx/liquidoc', branch: 'master'
gem 'omnidocx', github: 'aesthetikx/omnidocx', branch: 'master'

# Print numbers as words
gem 'humanize'

# Markdown rendering
gem 'redcarpet'

# Automatically add links to formatted text
gem 'rails_autolink'

# Better Arel Unions
gem 'active_record_union'

gem 'multi_xml' # Faraday XML handling for PayLease

gem 'aasm' # State machine DSL

gem 'after_commit_everywhere'

# Track user activity on important resources
gem 'audited', '~> 5.6'

# Slack notifications
gem 'slack-ruby-client'

# Counter Caches
gem 'counter_culture', '~> 2'

# Using github to get one commit to fix media_type deprecation
gem 'jsonapi-resources', github: 'cerebris/jsonapi-resources',
                         branch: 'release-0-10'

# More robust email regex
gem 'email_validator'

gem 'rails-reverse-proxy'

# Salesforce
gem 'restforce'

gem 'view_component'

# API Documentation
gem 'rswag-api', '< 2.7.0'
gem 'rswag-ui', '< 2.7.0' # CORS issue

gem 'holidays'

gem 'strong_migrations'

# Loan Amortization Data
gem 'google_drive', github: 'honzasterba/google-drive-ruby'

# Authorization
gem 'action_policy'

# Login Fingerprints
gem 'user_agent_parser'

# SFTP client
gem 'net-sftp'
# SFTP ED25519 support
gem 'bcrypt_pbkdf', '~> 1.1'
gem 'ed25519', '~> 1.3'

gem 'shigeki'

gem 'dry-validation'

group :development, :test do
  # Style Checking
  gem 'haml_lint', require: false
  gem 'rubocop', '~> 1', require: false
  gem 'rubocop-capybara', require: false
  gem 'rubocop-factory_bot', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
  gem 'rubocop-rspec_rails', require: false
  gem 'scss_lint', require: false

  # Golden master snapshot tests
  gem 'goldeen', github: 'aesthetikx/goldeen'

  # Security
  gem 'brakeman', require: false

  # Call 'debugger' anywhere in the code to stop execution and get a debugger
  gem 'debug'

  # Loads environment variables in development from .env
  gem 'dotenv-rails'

  gem 'rack_session_access'

  # Spring speeds up development by keeping your application running in the
  # background. Read more: https://github.com/rails/spring
  gem 'spring'

  gem 'spring-commands-rspec'

  gem 'parallel_tests'

  gem 'capybara'
  gem 'capybara-email'
  gem 'capybara-screenshot'
  gem 'puffing-billy'
  gem 'selenium-webdriver'

  gem 'rspec-its'
  gem 'rspec-rails'
  gem 'rspec-retry'

  gem 'rswag-specs'
end

group :development do
  # Helper for updating gems
  gem 'bummr', require: false

  # A helper for launching applications, used for save_and_open_page
  gem 'launchy'

  # Open sent emails in the browser in development
  gem 'letter_opener'

  # ViewComponent Developer Helper
  gem 'lookbook'
  # listen required for lookbook live reload
  gem 'listen'

  # LSP Support
  gem 'ruby-lsp', require: false

  # Access an IRB console on exception pages or by using <%= console %> in views
  gem 'web-console'

  # Yay! A Ruby Documentation tool
  gem 'yard'

  gem 'guard'
  gem 'guard-rspec', require: false
end

group :test do
  gem 'rails-controller-testing'
  gem 'rspec_junit_formatter'
  # TODO: Back to released version after release with validates_comparison_of
  gem 'shoulda-matchers', github: 'thoughtbot/shoulda-matchers'
  gem 'simplecov', '< 0.18.0', require: false
  gem 'vcr'
  gem 'webmock'
end

group :production do
  gem 'cloudflare-rails'
end

group :danger do
  gem 'danger'
  gem 'danger-brakeman'
  gem 'danger-commit_lint'
  gem 'danger-gem_changes'
  gem 'danger-rubocop'
  gem 'danger-todoist'
end

gem 'logger', '1.6.2'
gem 'with_advisory_lock', '~> 5.1'

gem 'jwe', '~> 1.0'
