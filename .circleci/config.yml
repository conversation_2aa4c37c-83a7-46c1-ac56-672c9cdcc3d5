version: 2.1

executors:
  machine-executor:
    machine:
      enabled: true
      image: default
      docker_layer_caching: true

workflows:
  default:
    jobs:
      - build
      - danger:
          requires:
            - build
          filters:
            branches:
              ignore:
                - master
                - staging
      - test:
          requires:
            - build
          filters:
            branches:
              ignore:
                - staging
      - upload_coverage:
          requires:
            - test
      - prepare_release:
          requires:
            - build
          filters:
            branches:
              only:
                - master
                - staging
      - release_staging:
          requires:
            - prepare_release
          filters:
            branches:
              only:
                - staging

jobs:
  build:
    executor: machine-executor
    parallelism: 1
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      # Source Code
      - restore_cache:
          keys:
            - source-cache-v1-{{ .Branch }}-{{ .Revision }}
            - source-cache-v1-{{ .Branch }}
            - source-cache-v1
      - checkout
      - save_cache:
          key: source-cache-v1-{{ .Branch }}-{{ .Revision }}
          paths:
            - .git

      - run:
          name: Build Container
          command: scripts/ci/1_build_container.sh << pipeline.git.branch >> << pipeline.git.revision >>

  test:
    executor: machine-executor
    parallelism: 6
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      # Source Code
      - restore_cache:
          keys:
            - source-cache-v1-{{ .Branch }}-{{ .Revision }}
            - source-cache-v1-{{ .Branch }}
            - source-cache-v1
      - checkout
      - save_cache:
          key: source-cache-v1-{{ .Branch }}-{{ .Revision }}
          paths:
            - .git

      - run:
          name: Fetch Container
          command: scripts/ci/2_fetch_container.sh << pipeline.git.revision >>

      - run:
          name: Install Code Climate Test Reporter
          command: scripts/ci/3_install_code_climate.sh

      - run:
          name: Prepare Test Schema
          command: scripts/ci/4_prepare_tests.sh

      - run:
          name: Run Tests
          command: scripts/ci/5_run_tests.sh

      - store_test_results:
          path: test-results

      - run:
          name: Code Climate Test Coverage
          command: |
            mkdir codeclimate
            ./cc-test-reporter format-coverage -t simplecov --prefix /opt/revela -o "codeclimate/codeclimate.$CIRCLE_NODE_INDEX.json"

      - run: aws s3 sync codeclimate/ "s3://revela-test-screenshots/coverage/<< pipeline.number >>"

  danger:
    executor: machine-executor
    parallelism: 1
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      # Source Code
      - restore_cache:
          keys:
            - source-cache-v1-{{ .Branch }}-{{ .Revision }}
            - source-cache-v1-{{ .Branch }}
            - source-cache-v1
      - checkout

      - run:
          name: Danger
          command: scripts/ci/9_run_danger.sh

  upload_coverage:
    executor: machine-executor
    parallelism: 1
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      - run:
          name: Install Code Climate Test Reporter
          command: |
            curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ./cc-test-reporter
            chmod +x ./cc-test-reporter
      - run:
          command: |
            mkdir coverage
            aws s3 sync "s3://revela-test-screenshots/coverage/<< pipeline.number >>" coverage/
            ./cc-test-reporter sum-coverage -p 6 coverage/codeclimate.*.json -o coverage/codeclimate.total.json
            ./cc-test-reporter upload-coverage -i coverage/codeclimate.total.json

  prepare_release:
    executor: machine-executor
    parallelism: 1
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      - checkout
      - run:
          name: Prepare Release
          command: scripts/ci/7_prepare_release.sh << pipeline.git.branch >> << pipeline.git.revision >>

  release_staging:
    executor: machine-executor
    parallelism: 1
    working_directory: ~/Revela/Revela
    environment:
      TZ: "America/Detroit"
      NODE_VERSION: "v14.15.4"
    steps:
      - checkout
      - run:
          name: Install Heroku CLI
          command: curl https://cli-assets.heroku.com/install.sh | sh
      - run:
          name: Release Staging
          command: scripts/ci/8_release_staging.sh << pipeline.git.branch >>
