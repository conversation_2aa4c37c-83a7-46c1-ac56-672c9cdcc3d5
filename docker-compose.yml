services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_USER: revela
      POSTGRES_PASSWORD: password
    ports:
      - '5432:5432'
    volumes:
      - 'revela-postgres:/var/lib/postgrsql/data'
  redis:
    image: redis:7
    ports:
      - '6379:6379'
    volumes:
      - "revela-redis:/var/lib/redis/data"
  elasticsearch:
    image: elasticsearch:8.12.0
    ports:
      - '9200:9200'
    volumes:
      - 'revela-elastic:/usr/share/elasticsearch/data'
    environment:
      cluster.name: 'elasticsearch'
      cluster.routing.allocation.disk.threshold_enabled: 'false'
      xpack.security.enabled: 'false'
      network.host: '0.0.0.0'
      http.port: '9200'
      discovery.type: 'single-node'
      ES_JAVA_OPTS: '-Xms750m -Xmx750m'
  web:
    build: .
    image: revela_web:latest
    shm_size: '256mb'
    environment:
      PORT: 3000
      RAILS_ENV: development
      REDIS_URL: redis://redis:6379
      ELASTICSEARCH_URL: http://elasticsearch:9200
      POSTGRES_HOST: postgres
      POSTGRES_USER: revela
      POSTGRES_PASSWORD: password
    ports:
      - '3000:3000'
    volumes:
      - ./test-results:/opt/revela/test-results
      - ./coverage:/opt/revela/coverage
    depends_on:
      - postgres
      - redis
      - elasticsearch
volumes:
  revela-postgres: {}
  revela-elastic: {}
  revela-redis: {}
