GIT
  remote: https://github.com/aesthetikx/apartment.git
  revision: d17f33deb14713cd0c8dfd28b4e11a4d76dd796b
  branch: connection_handling_patch
  specs:
    ros-apartment (2.11.0)
      activerecord (>= 5.0.0, < 7.1)
      parallel (< 2.0)
      public_suffix (>= 2.0.5, < 5.0)
      rack (>= 1.3.6, < 3.0)

GIT
  remote: https://github.com/aesthetikx/boring_science.git
  revision: 4169fa59a4c556856063aef292f58f510e8272b6
  specs:
    boring_science (0.1.3)
      kramdown (~> 2)
      rails (>= 5)
      turbolinks (~> 5)

GIT
  remote: https://github.com/aesthetikx/docx_replace.git
  revision: 4ae14c1ee1e9aab8a6c138a690dae880fc909ab4
  branch: master
  specs:
    docx_replace (1.2.0)
      rubyzip (~> 1.2, >= 1.2.1)

GIT
  remote: https://github.com/aesthetikx/goldeen.git
  revision: 5e9829e58eb813225582dad7b24a553a3f669245
  specs:
    goldeen (0.0.25)
      nokogiri (~> 1.8)
      thor (~> 1.0)

GIT
  remote: https://github.com/aesthetikx/liquidoc.git
  revision: 7cb4a74182b3098df7a3a12e27aec5bbcfc7138d
  branch: master
  specs:
    liquidoc (0.3.0)
      liquid (>= 4.0.0)
      marcel (~> 1)
      nokogiri (~> 1)
      rubyzip (>= 0.9.5)

GIT
  remote: https://github.com/aesthetikx/omnidocx.git
  revision: e5b95a5061599037e6f0818fe7605949c32bc786
  branch: master
  specs:
    omnidocx (0.1.2)
      nokogiri (~> 1.6)
      rubyzip (~> 1.1, >= 1.1.6)

GIT
  remote: https://github.com/aesthetikx/plutus.git
  revision: 40fc73446dfcb9b2d314bec12cc8c0606d135b2c
  branch: rails7
  specs:
    plutus (0.12.2)
      rails (>= 4.0)

GIT
  remote: https://github.com/cerebris/jsonapi-resources.git
  revision: 7297511ed58f50d00a1a7bccfb56733072b69687
  branch: release-0-10
  specs:
    jsonapi-resources (0.10.7)
      activerecord (>= 4.1)
      concurrent-ruby
      railties (>= 4.1)

GIT
  remote: https://github.com/honzasterba/google-drive-ruby.git
  revision: 475f03ac7ca909697742dc794f1dad8fb52ed1b7
  specs:
    google_drive (3.0.7)
      google-apis-drive_v3 (>= 0.5.0)
      google-apis-sheets_v4 (>= 0.4.0)
      googleauth (>= 0.5.0)
      nokogiri (>= 1.5.3)

GIT
  remote: https://github.com/ice-cube-ruby/ice_cube.git
  revision: 10ae8dc1c64ea23c9461f2b046cf7ee4513050b9
  branch: master
  specs:
    ice_cube (0.16.4)

GIT
  remote: https://github.com/thoughtbot/shoulda-matchers.git
  revision: 3fee9abab05d20aa489e6cd5cdab7e6a6468683b
  specs:
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    action_policy (0.7.4)
      ruby-next-core (>= 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    active_record_union (1.3.0)
      activerecord (>= 4.0)
    activeadmin (3.3.0)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    after_commit_everywhere (1.6.0)
      activerecord (>= 4.2)
      activesupport
    akami (1.3.3)
      base64
      gyoku (>= 0.4.0)
      nokogiri
    amazing_print (1.7.2)
    american_date (1.3.0)
    amoeba (3.3.0)
      activerecord (>= 5.2.0)
    appsignal (4.5.9)
      logger
      rack (>= 2.0.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.3)
    attr_encrypted (4.2.0)
      encryptor (~> 3.0.0)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1083.0)
    aws-sdk-core (3.222.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.183.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.3.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    bullet (8.0.3)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bummr (1.1.0)
      rainbow
      thor
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-email (3.0.2)
      capybara (>= 2.4, < 4.0)
      mail
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    case_transform (0.2)
      activesupport
    childprocess (5.1.0)
      logger (~> 1.5)
    claide (1.1.0)
    claide-plugins (0.9.2)
      cork
      nap
      open4 (~> 1.3)
    climate_control (1.2.0)
    cliver (0.3.2)
    cloudflare-rails (5.0.1)
      actionpack (>= 6.1, < 7.2.0)
      activesupport (>= 6.1, < 7.2.0)
      railties (>= 6.1, < 7.2.0)
      zeitwerk (>= 2.5.0)
    cocoon (1.2.15)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    color (1.8)
    colored2 (3.1.2)
    concurrent-ruby (1.3.4)
    connection_pool (2.5.0)
    content_disposition (1.0.0)
    cookiejar (0.3.4)
    cork (0.3.0)
      colored2 (~> 3.1)
    counter_culture (2.9.0)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.21.1)
      addressable
    csv (3.3.2)
    d3-rails (3.5.17)
      railties (>= 3.1)
    danger (9.5.1)
      base64 (~> 0.2)
      claide (~> 1.0)
      claide-plugins (>= 0.9.2)
      colored2 (~> 3.1)
      cork (~> 0.1)
      faraday (>= 0.9.0, < 3.0)
      faraday-http-cache (~> 2.0)
      git (~> 1.13)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      octokit (>= 4.0)
      pstore (~> 0.1)
      terminal-table (>= 1, < 4)
    danger-brakeman (0.0.3)
      brakeman
      danger-plugin-api (~> 1.0)
    danger-commit_lint (0.0.7)
      danger-plugin-api (~> 1.0)
    danger-gem_changes (0.0.7)
      danger-plugin-api (~> 1.0)
      nokogiri (~> 1.0)
    danger-plugin-api (1.0.0)
      danger (> 2.0)
    danger-rubocop (0.13.0)
      danger
      rubocop (~> 1.0)
    danger-todoist (2.0.1)
      danger-plugin-api (~> 1.0)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    disposable (0.6.3)
      declarative (>= 0.0.9, < 1.0.0)
      representable (>= 3.1.1, < 4)
    docile (1.4.1)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    doorkeeper (5.8.2)
      railties (>= 5)
    dotenv (3.1.7)
    dotenv-rails (3.1.7)
      dotenv (= 3.1.7)
      railties (>= 6.1)
    down (5.4.2)
      addressable (~> 2.8)
    draper (4.0.4)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-schema (1.14.1)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-logic (~> 1.5)
      dry-types (~> 1.8)
      zeitwerk (~> 2.6)
    dry-types (1.8.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.11.1)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-schema (~> 1.14)
      zeitwerk (~> 2.6)
    ed25519 (1.3.0)
    elasticsearch (7.17.11)
      elasticsearch-api (= 7.17.11)
      elasticsearch-transport (= 7.17.11)
    elasticsearch-api (7.17.11)
      multi_json
    elasticsearch-model (7.2.0)
      activesupport (> 3)
      elasticsearch (~> 7)
      hashie
    elasticsearch-rails (7.2.1)
    elasticsearch-transport (7.17.11)
      base64
      faraday (>= 1, < 3)
      multi_json
    em-http-request (1.1.7)
      addressable (>= 2.3.4)
      cookiejar (!= 0.3.1)
      em-socksify (>= 0.3)
      eventmachine (>= 1.0.3)
      http_parser.rb (>= 0.6.0)
    em-socksify (0.3.3)
      base64
      eventmachine (>= 1.0.0.beta.4)
    em-synchrony (1.0.6)
      eventmachine (>= 1.0.0.beta.1)
    email_validator (2.2.4)
      activemodel
    encryptor (3.0.0)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    eventmachine (1.2.7)
    eventmachine_httpserver (0.2.1)
    execjs (2.10.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-http-cache (2.5.1)
      faraday (>= 0.8)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    ffi (1.17.1)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    flipper (0.28.3)
      concurrent-ruby (< 2)
    flipper-redis (0.28.3)
      flipper (~> 0.28.3)
      redis (>= 3.0, < 6)
    flipper-ui (0.28.3)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 0.28.3)
      rack (>= 1.4, < 3)
      rack-protection (>= 1.5.3, <= 4.0.0)
      sanitize (< 7)
    formatador (1.1.0)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gaffe (1.2.0)
      rails (>= 4.0.0)
    geo_names (1.0.2)
      rest-client (~> 2)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    gli (2.22.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-core (0.11.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
      webrick
    google-apis-drive_v3 (0.43.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-sheets_v4 (0.24.0)
      google-apis-core (>= 0.11.0, < 2.a)
    googleauth (1.7.0)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    griddler (1.6.0)
      htmlentities
      rails (>= 3.2.0)
    griddler-mailgun (1.1.1)
      griddler
    grover (1.2.3)
      nokogiri (~> 1)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    gyoku (1.4.0)
      builder (>= 2.1.2)
      rexml (~> 3.0)
    haml (6.3.0)
      temple (>= 0.8.2)
      thor
      tilt
    haml_lint (0.62.0)
      haml (>= 5.0)
      parallel (~> 1.10)
      rainbow
      rubocop (>= 1.0)
      sysexits (~> 1.1)
    hamlit (3.0.3)
      temple (>= 0.8.2)
      thor
      tilt
    hamlit-rails (0.2.3)
      actionpack (>= 4.0.1)
      activesupport (>= 4.0.1)
      hamlit (>= 1.2.0)
      railties (>= 4.0.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.1)
    hashie (5.0.0)
    holidays (8.8.0)
    honeybadger (5.27.1)
      logger
      ostruct
    htmlbeautifier (1.4.3)
    htmlentities (4.3.4)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    http_parser.rb (0.8.0)
    httpclient (2.8.3)
    httpi (4.0.4)
      base64
      mutex_m
      nkf
      rack (>= 2.0, < 4)
    humanize (3.1.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.8.0)
    irb (1.14.3)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.12.2)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jsonapi-renderer (0.2.2)
    jwe (1.0.0)
      base64
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    kt-paperclip (7.2.2)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      marcel (~> 1.0.1)
      mime-types
      terrapin (>= 0.6.0, < 2.0)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    libreconv (0.9.5)
    lint_roller (1.1.0)
    liquid (5.0.1)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    lodash-rails (4.17.21)
      railties (>= 3.1)
    logger (1.6.2)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookbook (2.3.8)
      activemodel
      css_parser
      htmlbeautifier (~> 1.3)
      htmlentities (~> 4.3.4)
      marcel (~> 1.0)
      railties (>= 5.0)
      redcarpet (~> 3.5)
      rouge (>= 3.26, < 5.0)
      view_component (>= 2.0)
      yard (~> 0.9)
      zeitwerk (~> 2.5)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memoist (0.16.2)
    method_source (1.1.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0206)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    monetize (1.11.0)
      money (~> 6.12)
    money (6.16.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.2.0)
    nap (1.1.0)
    nenv (0.3.0)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.2.3)
    netrc (0.11.0)
    nio4r (2.7.4)
    nkf (0.2.0)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nori (2.7.1)
      bigdecimal
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    octokit (9.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    open4 (1.3.4)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    parallel (1.27.0)
    parallel_tests (5.1.0)
      parallel
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-forms (1.5.2)
      cliver (~> 0.3.2)
      rexml (~> 3.2, >= 3.2.6)
      safe_shell (>= 1.0.3, < 2.0)
    pg (1.5.9)
    phony (2.18.22)
    phony_rails (0.15.0)
      activesupport (>= 3.0)
      phony (>= 2.18.12)
    pluck_to_hash (1.0.2)
      activerecord (>= 4.0.2)
      activesupport (>= 4.0.2)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pstore (0.1.3)
    psych (5.2.2)
      date
      stringio
    public_suffix (4.0.7)
    puffing-billy (4.0.1)
      addressable (~> 2.5)
      em-http-request (~> 1.1, >= 1.1.0)
      em-synchrony
      eventmachine (~> 1.2)
      eventmachine_httpserver
      http_parser.rb (~> 0.8.0)
      multi_json
    puma (6.5.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack_session_access (0.2.0)
      builder (>= 2.0.0)
      rack (>= 1.0.0)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-reverse-proxy (0.13.0)
      actionpack
      addressable
    rails_autolink (1.1.8)
      actionview (> 3.1)
      activesupport (> 3.1)
      railties (> 3.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.9.4)
      logger
    rchardet (1.8.0)
    rdoc (6.10.0)
      psych (>= 4.0.0)
    react-rails (2.6.2)
      babel-transpiler (>= 0.7.0)
      connection_pool
      execjs
      railties (>= 3.2)
      tilt
    recaptcha (5.18.0)
    redcarpet (3.6.1)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    reform (2.6.2)
      disposable (>= 0.5.0, < 1.0.0)
      representable (>= 3.1.1, < 4)
      uber (< 0.2.0)
    reform-rails (0.2.6)
      activemodel (>= 5.0)
      reform (>= 2.3.1, < 3.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    restforce (8.0.0)
      faraday (>= 1.1.0, < 3.0.0)
      faraday-follow_redirects (<= 0.3.0, < 1.0.0)
      faraday-multipart (>= 1.0.0, < 2.0.0)
      faraday-net_http (< 4.0.0)
      hashie (>= 1.2.0, < 6.0)
      jwt (>= 1.5.6)
    retriable (3.1.2)
    rexml (3.4.1)
    ros-apartment-sidekiq (1.2.0)
      ros-apartment (>= 1.0)
      sidekiq (>= 2.11)
    rouge (4.5.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-its (2.0.0)
      rspec-core (>= 3.13.0)
      rspec-expectations (>= 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.0)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.13.2)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rswag-api (2.6.0)
      railties (>= 3.1, < 7.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.6.0)
      actionpack (>= 3.1, < 7.1)
      railties (>= 3.1, < 7.1)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-lsp (0.23.23)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 5)
      sorbet-runtime (>= 0.5.10782)
    ruby-next-core (1.1.1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyXL (3.4.33)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    rubyzip (1.3.0)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    safe_shell (1.1.0)
    sanitize (6.1.3)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    savon (2.15.1)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (>= 4, < 5)
      mail (~> 2.5)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (>= 3.7, < 6)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scss_lint (0.60.0)
      sass (~> 3.5, >= 3.5.5)
    selenium-webdriver (4.27.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shellany (0.0.1)
    shigeki (0.2.1)
    shrine (3.6.0)
      content_disposition (~> 1.0)
      down (~> 5.1)
    sidekiq (7.3.7)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-limit_fetch (4.4.1)
      sidekiq (>= 6)
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    signet (0.17.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.17.1)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    slack-ruby-client (1.0.0)
      faraday (>= 1.0)
      faraday_middleware
      gli
      hashie
      websocket-driver
    sorbet-runtime (0.5.12135)
    spring (4.2.1)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stringio (3.1.2)
    strip_attributes (1.14.1)
      activemodel (>= 3.0, < 9.0)
    strong_migrations (2.1.0)
      activerecord (>= 6.1)
    sysexits (1.2.0)
    temple (0.10.3)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    terrapin (1.0.1)
      climate_control
    terser (1.2.4)
      execjs (>= 0.3.0, < 3)
    thor (1.3.2)
    tilt (2.6.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    twilio-ruby (7.4.0)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.6.0)
    uniform_notifier (1.16.0)
    uri-js-rails (1.15.2)
    user_agent_parser (2.18.0)
    vcr (6.3.1)
      base64
    view_component (3.22.0)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (= 1.3.4)
      method_source (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    wasabi (5.0.3)
      addressable
      faraday (>= 1.9, < 3)
      nokogiri (>= 1.13.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.24.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.37)
    zeitwerk (2.7.3)
    zip_kit (6.2.1)
    zipline (2.0.0)
      actionpack (>= 6.0, < 8.0)
      content_disposition (~> 1.0)
      zip_kit (~> 6, >= 6.2.0, < 7)

PLATFORMS
  ruby

DEPENDENCIES
  aasm
  action_policy
  active_model_serializers (~> 0.10.6)
  active_record_union
  activeadmin
  after_commit_everywhere
  amazing_print
  american_date
  amoeba
  appsignal
  attr_encrypted
  audited (~> 5.6)
  aws-sdk-s3
  babel-transpiler
  bcrypt_pbkdf (~> 1.1)
  bootsnap (>= 1.1.0)
  boring_science!
  brakeman
  bullet
  bummr
  capybara
  capybara-email
  capybara-screenshot
  cloudflare-rails
  cocoon
  coffee-rails
  color
  counter_culture (~> 2)
  d3-rails (~> 3.5)
  danger
  danger-brakeman
  danger-commit_lint
  danger-gem_changes
  danger-rubocop
  danger-todoist
  debug
  devise
  docx_replace!
  doorkeeper (~> 5.8)
  dotenv-rails
  down
  draper
  dry-validation
  ed25519 (~> 1.3)
  elasticsearch
  elasticsearch-model (= 7.2.0)
  elasticsearch-rails (< 8)
  email_validator
  factory_bot_rails
  faker
  faraday_middleware
  flipper (< 1)
  flipper-redis
  flipper-ui
  gaffe
  geo_names
  geocoder
  goldeen!
  google_drive!
  griddler
  griddler-mailgun
  grover
  guard
  guard-rspec
  haml_lint
  hamlit-rails
  holidays
  honeybadger
  http
  humanize
  ice_cube!
  image_processing (~> 1)
  jquery-rails
  jsonapi-resources!
  jwe (~> 1.0)
  jwt
  kaminari (>= 0.17.0)
  kt-paperclip
  launchy
  letter_opener
  libreconv
  liquidoc!
  listen
  lodash-rails
  logger (= 1.6.2)
  lookbook
  money-rails
  multi_xml
  net-sftp
  omnidocx!
  parallel_tests
  pdf-forms
  pg (>= 0.18, < 2.0)
  phony_rails
  pluck_to_hash
  plutus!
  puffing-billy
  puma
  rack-attack
  rack-cors
  rack_session_access
  rails (~> 7.0)
  rails-controller-testing
  rails-reverse-proxy
  rails_autolink
  react-rails (< 2.7)
  recaptcha
  redcarpet
  redis
  reform
  reform-rails
  restforce
  ros-apartment!
  ros-apartment-sidekiq
  rspec-its
  rspec-rails
  rspec-retry
  rspec_junit_formatter
  rswag-api (< 2.7.0)
  rswag-specs
  rswag-ui (< 2.7.0)
  rubocop (~> 1)
  rubocop-capybara
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  rubocop-rspec_rails
  ruby-lsp
  rubyXL (~> 3.3)
  rubyzip
  sassc-rails
  savon (~> 2)
  scenic
  scss_lint
  selenium-webdriver
  shigeki
  shoulda-matchers!
  shrine
  sidekiq
  sidekiq-limit_fetch
  sidekiq-scheduler
  simplecov (< 0.18.0)
  sitemap_generator
  slack-ruby-client
  spring
  spring-commands-rspec
  sprockets (~> 4)
  strip_attributes
  strong_migrations
  terser
  turbolinks
  twilio-ruby
  uri-js-rails
  user_agent_parser
  vcr
  view_component
  web-console
  webmock
  with_advisory_lock (~> 5.1)
  yard
  zipline

RUBY VERSION
   ruby 3.2.7p253

BUNDLED WITH
   2.3.12
