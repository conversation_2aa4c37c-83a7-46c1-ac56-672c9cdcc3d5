Closes [REV-XXX](https://revelaco.atlassian.net/browse/REV-XXX)

## Pre-Merge Checklist

Ensure all items in the checklist below are complete before merging:

- [ ] Danger is not displaying any warnings
- [ ] RBAC has been considered or does not apply
- [ ] Any new user-facing functionality is behind a feature flag or does not apply
- [ ] Any migrations have been checked for reversibility and handle existing data or there are no migrations
- [ ] Any dependency changes have been deployed to and exercised on staging or there are no dependency changes
- [ ] Any required ENV changes have been made on relevant instances or there are no ENV changes
- [ ] This change has gone through UAT

## Staging Deploy Process

If a `staging` deployment is warranted, you can trigger it by merging your branch into the `staging` branch and pushing using git.

## Production Deploy Process

Deploy at least 10 minutes after the top of the hour following this process:

- [ ] Check that there are no [long-running jobs in progress in Sidekiq](https://www.revela.co/jobs/busy)
- [ ] Run `heroku login` (if not already logged in)
- [ ] Run `./scripts/release` to deploy
- [ ] Watch [Heroku Activity](https://dashboard.heroku.com/apps/still-plateau-99181/activity) for progress
- [ ] Optionally watch [Heroku logs](https://dashboard.heroku.com/apps/still-plateau-99181/logs)
- [ ] Monitor [Honeybadger for _new_ errors](https://app.honeybadger.io/projects/51991/faults?q=-is%3Aresolved+-is%3Aignored&sort=last_seen_desc)
- [ ] Monitor [Heroku metrics](https://dashboard.heroku.com/apps/still-plateau-99181/metrics/web?ending=0-hours-ago&starting=2-hours-ago) for any `Critical` or `Warning` events
- [ ] Change the status of your JIRA ticket to `Deployed`
