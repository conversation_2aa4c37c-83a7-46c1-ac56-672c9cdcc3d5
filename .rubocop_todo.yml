# This configuration was generated by
# `rubocop --auto-gen-config --auto-gen-only-exclude --exclude-limit 10000`
# on 2025-05-28 23:28:18 UTC using RuboCop version 1.75.8.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
Capybara/FindAllFirst:
  Exclude:
    - 'spec/system/management/onboardings/module_selection_spec.rb'
    - 'spec/system/management/onboardings/sidebars_spec.rb'

# Offense count: 26
Capybara/NegationMatcherAfterVisit:
  Exclude:
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/collections/demand_letter_expiration_spec.rb'
    - 'spec/system/collections/demand_letter_spec.rb'
    - 'spec/system/collections/late_balance_spec.rb'
    - 'spec/system/collections/move_to_evicting_spec.rb'
    - 'spec/system/collections/notice_sent_spec.rb'
    - 'spec/system/custom_forms/event_registration_with_payment_v1_submission_spec.rb'
    - 'spec/system/custom_forms/submission_spec.rb'
    - 'spec/system/inspections/archive_template_spec.rb'
    - 'spec/system/management/onboardings/module_settings_spec.rb'
    - 'spec/system/onboarding/backoffice_setup_spec.rb'
    - 'spec/system/organization/forms/show_spec.rb'
    - 'spec/system/portfolio/tenants/active_eviction_banner_spec.rb'
    - 'spec/system/rbac/rbac_helper_spec.rb'
    - 'spec/system/tenants/payment_plans/presets_spec.rb'
    - 'spec/system/tunisia/open_bank_account_spec.rb'

# Offense count: 354
CustomerSpecific/ExplicitSubdomainCheck:
  Exclude:
    - 'app/components/maintenance/work_orders/form_component.rb'
    - 'app/components/maintenance/work_orders/sidebar/custom_data_component.rb'
    - 'app/components/owners/contributions/request_checkout_component.rb'
    - 'app/controllers/accounting/invoices_controller.rb'
    - 'app/controllers/accounting/payables/invoices_controller.rb'
    - 'app/controllers/accounting/payables/payments_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/api/v1/tenant/sessions_controller.rb'
    - 'app/controllers/api/v2/base_controller.rb'
    - 'app/controllers/apply/plugin/units_controller.rb'
    - 'app/controllers/concerns/accounting/payables/batches_controller/customer_specific.rb'
    - 'app/controllers/concerns/management/after_sign_in_path.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/electronic_signatures_controller.rb'
    - 'app/controllers/guest_cards_controller.rb'
    - 'app/controllers/leasing/agreements_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/management/parking_lots_controller.rb'
    - 'app/controllers/management/portfolios_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/operations/collections/late_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/owner_controller.rb'
    - 'app/controllers/owners/documents_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/telephony/collections/communications/phone_calls_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/dashboard_controller.rb'
    - 'app/controllers/tenants/maintenance_tickets_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/decorators/agreement_decorator.rb'
    - 'app/decorators/lease_decorator.rb'
    - 'app/forms/electronic_signatures/inline_form.rb'
    - 'app/forms/member_onboarding/form.rb'
    - 'app/forms/member_onboarding/form/member_form.rb'
    - 'app/jobs/api/v2/refresh_owner_statement_entries_job.rb'
    - 'app/jobs/late_fees/assess_late_fee_job.rb'
    - 'app/jobs/maintenance/create_scheduled_tickets_job.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/notifications/closed_work_order_job.rb'
    - 'app/jobs/notifications/portal_message_received_job.rb'
    - 'app/jobs/payment_processing/process_electronic_payment_job.rb'
    - 'app/mailers/accounts_mailer.rb'
    - 'app/mailers/electronic_payments_mailer.rb'
    - 'app/mailers/electronic_signatures_mailer.rb'
    - 'app/mailers/leasing_mailer.rb'
    - 'app/mailers/maintenance/work_orders/appointments_mailer.rb'
    - 'app/mailers/maintenance/work_orders/owner_approvals_mailer.rb'
    - 'app/mailers/messaging_mailer.rb'
    - 'app/mailers/portal_email_links.rb'
    - 'app/models/brand.rb'
    - 'app/models/charge_schedule/entry.rb'
    - 'app/models/company.rb'
    - 'app/models/concerns/commercial_document.rb'
    - 'app/models/concerns/has_availability.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/concerns/maintenance_ticket/service_area.rb'
    - 'app/models/concerns/property/custom_data.rb'
    - 'app/models/concerns/tenant/custom_data.rb'
    - 'app/models/configuration/maintenance.rb'
    - 'app/models/customer.rb'
    - 'app/models/electronic_signature.rb'
    - 'app/models/guest_card.rb'
    - 'app/models/inspection/report.rb'
    - 'app/models/invoice/batchable.rb'
    - 'app/models/lease.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/listing.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/maintenance_ticket/appointment.rb'
    - 'app/models/role.rb'
    - 'app/models/scheduled_payment.rb'
    - 'app/models/taxes/nelco.rb'
    - 'app/models/tour.rb'
    - 'app/queries/electronic_signatures_query.rb'
    - 'app/serializers/owner_units_table_serializer.rb'
    - 'app/serializers/units_table_serializer.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/collections/communications/send_sms.rb'
    - 'app/services/lease/add_month_to_month_charge.rb'
    - 'app/services/lease/create_one_time_charges.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'app/services/lease/execute.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/lease_application/submit.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/maintenance_ticket/notify.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'app/services/payment/batch/process.rb'
    - 'app/services/smart_rent/webhooks/process_webhook.rb'
    - 'lib/accounting/cash_activity.rb'
    - 'lib/accounting/check.rb'
    - 'lib/accounting/inverse_accounts.rb'
    - 'lib/accounting/journal/health_snapshot.rb'
    - 'lib/action_index/accounting/target.rb'
    - 'lib/action_index/properties.rb'
    - 'lib/action_table/collections/late.rb'
    - 'lib/action_table/collections/notice_sent.rb'
    - 'lib/action_table/properties.rb'
    - 'lib/actions_menu/invoice/payable.rb'
    - 'lib/actions_menu/lead.rb'
    - 'lib/actions_menu/managed_entity.rb'
    - 'lib/actions_menu/tenant.rb'
    - 'lib/activity_digests/digest.rb'
    - 'lib/activity_log/description.rb'
    - 'lib/anomalies/anomaly/leaseless_invoices.rb'
    - 'lib/anomalies/anomaly/leaseless_payments.rb'
    - 'lib/cards/applications.rb'
    - 'lib/cards/expenses_overview.rb'
    - 'lib/cards/physical_occupancy.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/chart_of_accounts/tree_builder/income_statement.rb'
    - 'lib/collections/communications/cadence.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/extensions/plutus_entries_ledger.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/member_onboarding/tenants/configured_cohort.rb'
    - 'lib/navigation/employees.rb'
    - 'lib/navigation/employees/accounting.rb'
    - 'lib/navigation/employees/operations.rb'
    - 'lib/navigation/employees/portfolios.rb'
    - 'lib/payment_plan/calendar.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/cash_flow_statement.rb'
    - 'lib/reports/owner_date_clamping.rb'
    - 'lib/reports/v3/aging_receivables.rb'
    - 'lib/reports/v3/agreement_execution.rb'
    - 'lib/reports/v3/basis/resident.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'lib/reports/v3/comparative_trial_balance.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/consolidated_report/parent_child_columns.rb'
    - 'lib/reports/v3/expense_register/customer_specific.rb'
    - 'lib/reports/v3/lease_applications.rb'
    - 'lib/reports/v3/lease_directory.rb'
    - 'lib/reports/v3/lease_expiration.rb'
    - 'lib/reports/v3/ledger.rb'
    - 'lib/reports/v3/listings.rb'
    - 'lib/reports/v3/loan_payments.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_statement/property_section.rb'
    - 'lib/reports/v3/property_directory.rb'
    - 'lib/reports/v3/rent_roll.rb'
    - 'lib/reports/v3/rent_roll_detail.rb'
    - 'lib/reports/v3/residency.rb'
    - 'lib/reports/v3/security_deposits.rb'
    - 'lib/reports/v3/tenancy_schedule.rb'
    - 'lib/reports/v3/work_orders.rb'

# Offense count: 158
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, NonImplicitAssociationMethodNames.
# SupportedStyles: explicit, implicit
FactoryBot/AssociationStyle:
  Exclude:
    - 'spec/factories/access_tokens.rb'
    - 'spec/factories/accounting_entries.rb'
    - 'spec/factories/agreements_simple_agreements.rb'
    - 'spec/factories/approvals/change_requests.rb'
    - 'spec/factories/approvals_approvals.rb'
    - 'spec/factories/approvals_rejection.rb'
    - 'spec/factories/approvals_requests.rb'
    - 'spec/factories/assignments.rb'
    - 'spec/factories/attachments.rb'
    - 'spec/factories/audits.rb'
    - 'spec/factories/bank_account/funds_transfers.rb'
    - 'spec/factories/bank_accounts.rb'
    - 'spec/factories/brands.rb'
    - 'spec/factories/broadcasts.rb'
    - 'spec/factories/broadcasts_recipients.rb'
    - 'spec/factories/calendar_events.rb'
    - 'spec/factories/charge_schedule_entries.rb'
    - 'spec/factories/charge_schedule_entry_allocations.rb'
    - 'spec/factories/charge_schedules.rb'
    - 'spec/factories/chat_memberships.rb'
    - 'spec/factories/collections/evictions.rb'
    - 'spec/factories/collections_demand_letter_batches.rb'
    - 'spec/factories/collections_demand_letters.rb'
    - 'spec/factories/contact_timeline_entries.rb'
    - 'spec/factories/credit_cards.rb'
    - 'spec/factories/data_imports.rb'
    - 'spec/factories/electronic_signatures.rb'
    - 'spec/factories/financing_refinance_invites.rb'
    - 'spec/factories/financing_refinance_requests.rb'
    - 'spec/factories/guest_cards.rb'
    - 'spec/factories/income_certifications.rb'
    - 'spec/factories/inspection_activity.rb'
    - 'spec/factories/inspection_punch_list_entries.rb'
    - 'spec/factories/inspection_questions.rb'
    - 'spec/factories/inspection_records.rb'
    - 'spec/factories/inspection_reports.rb'
    - 'spec/factories/inspection_responses.rb'
    - 'spec/factories/insurance_policies.rb'
    - 'spec/factories/insurance_requirements.rb'
    - 'spec/factories/invoice_processing_emails.rb'
    - 'spec/factories/invoices.rb'
    - 'spec/factories/itemized_damages.rb'
    - 'spec/factories/lease_applications.rb'
    - 'spec/factories/lease_move_out_custom_damages.rb'
    - 'spec/factories/lease_notice_of_non_renewals.rb'
    - 'spec/factories/leases.rb'
    - 'spec/factories/lending/loan/installment_adjustments.rb'
    - 'spec/factories/lending/loans.rb'
    - 'spec/factories/line_item_markups.rb'
    - 'spec/factories/line_items.rb'
    - 'spec/factories/listings.rb'
    - 'spec/factories/maintenance/bid/items.rb'
    - 'spec/factories/maintenance/bid_request/invites.rb'
    - 'spec/factories/maintenance/bid_requests.rb'
    - 'spec/factories/maintenance/bids.rb'
    - 'spec/factories/maintenance_estimate_areas.rb'
    - 'spec/factories/maintenance_estimate_sections.rb'
    - 'spec/factories/maintenance_estimate_tasks.rb'
    - 'spec/factories/maintenance_estimates.rb'
    - 'spec/factories/maintenance_ticket/appointments.rb'
    - 'spec/factories/maintenance_ticket/defers.rb'
    - 'spec/factories/maintenance_ticket_events.rb'
    - 'spec/factories/maintenance_tickets.rb'
    - 'spec/factories/maintenance_work_performed_statement_items.rb'
    - 'spec/factories/management_contracts.rb'
    - 'spec/factories/member_onboarding/assignments.rb'
    - 'spec/factories/merchant_account_contacts.rb'
    - 'spec/factories/messages.rb'
    - 'spec/factories/messaging_emails.rb'
    - 'spec/factories/messaging_message_deliveries.rb'
    - 'spec/factories/metadata.rb'
    - 'spec/factories/mobile_devices.rb'
    - 'spec/factories/morocco/profiles.rb'
    - 'spec/factories/notification_preferences.rb'
    - 'spec/factories/notifications.rb'
    - 'spec/factories/occupancy_counts.rb'
    - 'spec/factories/owner_invites.rb'
    - 'spec/factories/ownerships.rb'
    - 'spec/factories/payment_plan/preset/installments.rb'
    - 'spec/factories/payment_plans.rb'
    - 'spec/factories/payments.rb'
    - 'spec/factories/plutus_entry_automatic_reversals.rb'
    - 'spec/factories/profit_stars_transactions.rb'
    - 'spec/factories/project_board_columns.rb'
    - 'spec/factories/project_dependencies.rb'
    - 'spec/factories/property/transfers.rb'
    - 'spec/factories/property_memberships.rb'
    - 'spec/factories/reports_email_schedules.rb'
    - 'spec/factories/reports_packet_template_entries.rb'
    - 'spec/factories/taggings.rb'
    - 'spec/factories/taxpayer_identifications.rb'
    - 'spec/factories/telephony_twilio_proxy_sessions.rb'
    - 'spec/factories/the_closing_docs_screening_groups.rb'
    - 'spec/factories/tours.rb'
    - 'spec/factories/tunisia/authorized_users.rb'
    - 'spec/factories/user/invites.rb'
    - 'spec/factories/user/profiles.rb'
    - 'spec/factories/utility_transfers.rb'
    - 'spec/factories/vendor/contract/property_memberships.rb'
    - 'spec/factories/vendor/contracts.rb'
    - 'spec/factories/vendor_assignments.rb'
    - 'spec/factories/zeamster_transactions.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: with_first_argument, with_fixed_indentation
Layout/ArgumentAlignment:
  Exclude:
    - 'app/services/contact/timeline_entry/create.rb'
    - 'spec/jobs/tenant/notify_due_invoices_job_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: with_first_element, with_fixed_indentation
Layout/ArrayAlignment:
  Exclude:
    - 'app/controllers/management/onboardings/modules_controller.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: leading, trailing
Layout/DotPosition:
  Exclude:
    - 'spec/models/listing_spec.rb'
    - 'spec/requests/zillow/webhooks/leads_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLineAfterGuardClause:
  Exclude:
    - 'app/models/maintenance/bid_request/invite.rb'
    - 'app/queries/member_onboarding/assignments_query.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EmptyLineBetweenMethodDefs, EmptyLineBetweenClassDefs, EmptyLineBetweenModuleDefs, DefLikeMacros, AllowAdjacentOneLineDefs, NumberOfEmptyLines.
Layout/EmptyLineBetweenDefs:
  Exclude:
    - 'app/controllers/management/onboardings/assignments_controller.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLines:
  Exclude:
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/models/management_contract/membership.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowAliasSyntax, AllowedMethods.
# AllowedMethods: alias_method, public, protected, private
Layout/EmptyLinesAroundAttributeAccessor:
  Exclude:
    - 'app/components/modal_component.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, no_empty_lines
Layout/EmptyLinesAroundBlockBody:
  Exclude:
    - 'spec/requests/zillow/webhooks/leads_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines, beginning_only, ending_only
Layout/EmptyLinesAroundClassBody:
  Exclude:
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLinesAroundMethodBody:
  Exclude:
    - 'app/helpers/custom_forms/submissions_form_field_helper.rb'
    - 'lib/docx_filling.rb'
    - 'lib/pdf_filling.rb'
    - 'spec/support/custom_form_field_builder.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowMultipleStyles, EnforcedHashRocketStyle, EnforcedColonStyle, EnforcedLastArgumentHashStyle.
# SupportedHashRocketStyles: key, separator, table
# SupportedColonStyles: key, separator, table
# SupportedLastArgumentHashStyles: always_inspect, always_ignore, ignore_implicit, ignore_explicit
Layout/HashAlignment:
  Exclude:
    - 'app/models/syndication/listing_supplement.rb'
    - 'spec/factories/zillow/claims.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Width, AllowedPatterns.
Layout/IndentationWidth:
  Exclude:
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowDoxygenCommentStyle, AllowGemfileRubyComment, AllowRBSInlineAnnotation, AllowSteepAnnotation.
Layout/LeadingCommentSpace:
  Exclude:
    - 'spec/jobs/search/reindex_all_job_spec.rb'

# Offense count: 524
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Max, AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns, SplitStrings.
# URISchemes: http, https
Layout/LineLength:
  Exclude:
    - 'Gemfile'
    - 'app/components/accounting/bank_accounts/post_transactions_modal_component.rb'
    - 'app/components/action_sidebar/electronic_signable/countersigner_modal_component.rb'
    - 'app/components/leasing/application/adjudicated_component.rb'
    - 'app/controllers/api/zapier/contact_timeline_entries_controller.rb'
    - 'app/controllers/api/zapier/contacts_controller.rb'
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/controllers/concerns/calendar_events_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/management/after_sign_in_path.rb'
    - 'app/controllers/leasing/agreements_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets/appointments_controller.rb'
    - 'app/controllers/maintenance/tickets/bid_requests_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/management/onboardings/modules_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/messaging/sms_controller.rb'
    - 'app/controllers/organization/forms_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/member_onboardings_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/vendor/vendor_assignments_controller.rb'
    - 'app/decorators/lease_decorator.rb'
    - 'app/helpers/custom_forms/submissions_automation_helper.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/customer_specific/marketplace/arrived/export_invoices_job.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/lending/loan/update_statement_data_job.rb'
    - 'app/jobs/management/bulk_property_balance_transfers_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/mailers/tours_mailer.rb'
    - 'app/models/concerns/custom_forms/payment_validations.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/custom_forms/form.rb'
    - 'app/models/custom_forms/form/exportable.rb'
    - 'app/models/invoice_payment.rb'
    - 'app/models/lease_membership.rb'
    - 'app/models/lending/loan/serialized_entries.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/policies/insurance/assurant/eligible_unit_policy.rb'
    - 'app/queries/contacts_query.rb'
    - 'app/queries/employees_query.rb'
    - 'app/queries/member_onboarding/assignments_query.rb'
    - 'app/queries/messaging/contact_group_memberships_query.rb'
    - 'app/queries/payment/batches_query.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/queries/unit_status_query.rb'
    - 'app/queries/vendors_query.rb'
    - 'app/resources/api/v2/shared.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/electronic_signature/create_signed_copy.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'app/services/inspectify/populate_responses/deficiencies.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/maintenance/bid_request/create.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/maintenance_ticket/persist.rb'
    - 'app/services/management_contract/generate_document.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/payment/record/cash/invoice_void_entries.rb'
    - 'app/services/payment/record/cash/post_apply_entries.rb'
    - 'app/services/payment/record/cash/prepayment_entry.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/populate_responses.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/populate_responses/interior_general.rb'
    - 'app/services/tenant/merge.rb'
    - 'lib/accounting/account_balance_matrix/customer_portfolio_matrix.rb'
    - 'lib/accounting/cash_basis_migration/compare_payment_journal_entries.rb'
    - 'lib/accounting/fee_management/month_end_review/summary_row.rb'
    - 'lib/accounting/fee_management/property_balances_query.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_index/accounting/invoices.rb'
    - 'lib/action_index/accounting/payables.rb'
    - 'lib/action_table/base.rb'
    - 'lib/action_table/leasing/pipeline/unsubmitted_applications.rb'
    - 'lib/action_table/member_onboarding/assignments.rb'
    - 'lib/action_table/messaging/contact_groups.rb'
    - 'lib/actions_menu/bank_account.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/chart_of_accounts/tree_builder/rendering.rb'
    - 'lib/custom_forms/automations/payment/v1.rb'
    - 'lib/custom_forms/template_name_to_class.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/maintenance_tickets.rb'
    - 'lib/importers/v3/messaging/contact_group.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/unpaid_invoices.rb'
    - 'lib/importers/v3/xlsx_parser.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/guarantor_profile.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'lib/navigation/employees.rb'
    - 'lib/payment_processing/credit_card_convenience_fee.rb'
    - 'lib/reams/requests/survey/answers.rb'
    - 'lib/reams/survey/answers.rb'
    - 'lib/reports/general_ledger.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/collections.rb'
    - 'lib/reports/v3/consolidated_property_budget_variance.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/receivables_summary.rb'
    - 'lib/reports/v3/unbalanced_forwards.rb'
    - 'lib/reports/v3/unit_status/summary.rb'
    - 'lib/routes/forms.rb'
    - 'lib/sidebar/base.rb'
    - 'lib/zeamster/transaction/reason_codes.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'scripts/swagger_dbml.rb'
    - 'spec/controllers/api/zapier/contact_timeline_entries_controller_spec.rb'
    - 'spec/controllers/api/zapier/contacts_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/controllers/custom_forms/submissions_controller_spec.rb'
    - 'spec/controllers/organization/forms_controller_spec.rb'
    - 'spec/factories/lease_application_custom_question_responses.rb'
    - 'spec/factories/the_closing_docs_screening_groups.rb'
    - 'spec/jobs/accounting/post_transactions_job_spec.rb'
    - 'spec/jobs/saferent/fetch_job_report_spec.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/accounting/fee_management/transfer_property_balances_spec.rb'
    - 'spec/lib/saferent/package_spec.rb'
    - 'spec/lib/zeamster/account/create_spec.rb'
    - 'spec/mailers/previews/vendor_assignments_mailer_preview.rb'
    - 'spec/models/custom_forms/automation_settings/payment/v1_spec.rb'
    - 'spec/models/custom_forms/form_spec.rb'
    - 'spec/models/custom_forms/submission_spec.rb'
    - 'spec/models/listing_spec.rb'
    - 'spec/models/member_onboarding/configuration_spec.rb'
    - 'spec/models/saferent/screening_spec.rb'
    - 'spec/models/taxes/batch_group_spec.rb'
    - 'spec/queries/invoices_query/by_user_spec.rb'
    - 'spec/queries/plutus/accounts_query_spec.rb'
    - 'spec/rails_helper.rb'
    - 'spec/requests/api/v2/accounting/journals/entries_spec.rb'
    - 'spec/services/inspectify/populate_responses_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/services/payment_processing/add_nsf_fee_spec.rb'
    - 'spec/services/payment_processing/create_electronic_payment_spec.rb'
    - 'spec/services/saferent/screening/create_report_spec.rb'
    - 'spec/services/sftp_to_go/helpers_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/additional_photos_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/health_and_safety_site_hazards_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/inspector_information_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/interior_general_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses_spec.rb'
    - 'spec/services/tunisia/webhook/check_payment/additional_verification_required_spec.rb'
    - 'spec/support/property_context_builder.rb'
    - 'spec/support/trust_accounting_context_builder.rb'
    - 'spec/system/accounting/payables/payments_spec.rb'
    - 'spec/system/accounting/receivables/create_tenant_payment_spec.rb'
    - 'spec/system/action_sidebar/electronic_signable/countersigner_modal_component_spec.rb'
    - 'spec/system/custom_forms/event_registration_v1_submission_spec.rb'
    - 'spec/system/custom_forms/event_registration_with_payment_v1_submission_spec.rb'
    - 'spec/system/custom_forms/submission_spec.rb'
    - 'spec/system/inspections/index_spec.rb'
    - 'spec/system/landlord_verifications/landlord_verification_spec.rb'
    - 'spec/system/leasing/lease_applications/income_verification_spec.rb'
    - 'spec/system/leasing/leases/itemized_damages_spec.rb'
    - 'spec/system/management/onboardings/assignments/index_spec.rb'
    - 'spec/system/management/onboardings/copy_spec.rb'
    - 'spec/system/operations/inspections/sidebar_spec.rb'
    - 'spec/system/organization/bank_accounts/transfer_spec.rb'
    - 'spec/system/organization/configurations_spec.rb'
    - 'spec/system/organization/forms/download_responses_spec.rb'
    - 'spec/system/organization/forms/index_spec.rb'
    - 'spec/system/organization/forms/preview_spec.rb'
    - 'spec/system/organization/forms/show_spec.rb'
    - 'spec/system/tunisia/create_card_spec.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: symmetrical, new_line, same_line
Layout/MultilineMethodCallBraceLayout:
  Exclude:
    - 'app/controllers/marketing/listings_controller.rb'
    - 'app/controllers/zillow/webhooks/leads_controller.rb'
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 12
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented, indented_relative_to_receiver
Layout/MultilineMethodCallIndentation:
  Exclude:
    - 'app/controllers/messaging/contact_groups_controller.rb'
    - 'app/services/data_exporting/export_maintenance/bid_request.rb'
    - 'lib/action_index/member_onboarding/assignments.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented
Layout/MultilineOperationIndentation:
  Exclude:
    - 'app/models/maintenance_ticket/attachment_proxy.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowForAlignment, EnforcedStyleForExponentOperator, EnforcedStyleForRationalLiterals.
# SupportedStylesForExponentOperator: space, no_space
# SupportedStylesForRationalLiterals: space, no_space
Layout/SpaceAroundOperators:
  Exclude:
    - 'app/models/syndication/listing_supplement.rb'
    - 'spec/models/listing_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceBeforeBlockBraces:
  Exclude:
    - 'spec/models/listing_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBrackets.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBrackets: space, no_space
Layout/SpaceInsideArrayLiteralBrackets:
  Exclude:
    - 'app/controllers/management/onboardings/modules_controller.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideHashLiteralBraces:
  Exclude:
    - 'app/models/syndication/listing_supplement.rb'
    - 'spec/requests/api/v2/invoices_spec.rb'
    - 'spec/requests/zillow/webhooks/leads_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: final_newline, final_blank_line
Layout/TrailingEmptyLines:
  Exclude:
    - 'spec/controllers/api/zapier/base_controller_spec.rb'

# Offense count: 56
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowInHeredoc.
Layout/TrailingWhitespace:
  Exclude:
    - 'app/controllers/api/zapier/contacts_controller.rb'
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/controllers/api/zapier/oauth_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/decorators/maintenance/bid_request/invite_decorator.rb'
    - 'app/queries/contacts_query.rb'
    - 'app/queries/member_onboarding/assignments_query.rb'
    - 'app/serializers/api/zapier/contact_serializer.rb'
    - 'lib/action_index/member_onboarding/assignments.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'spec/controllers/api/zapier/contacts_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/additional_photos_spec.rb'
    - 'spec/system/management/onboardings/assignments/index_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Lint/AmbiguousRegexpLiteral:
  Exclude:
    - 'spec/models/listing_spec.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowSafeAssignment.
Lint/AssignmentInCondition:
  Exclude:
    - 'app/controllers/api/zapier/contact_timeline_entries_controller.rb'
    - 'app/middleware/custom_subdomain_elevator.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'

# Offense count: 3
Lint/CopDirectiveSyntax:
  Exclude:
    - 'app/models/lease_membership.rb'
    - 'lib/member_onboarding/tenants/wizard.rb'

# Offense count: 19
# Configuration parameters: IgnoreLiteralBranches, IgnoreConstantBranches, IgnoreDuplicateElseBranch.
Lint/DuplicateBranch:
  Exclude:
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/decorators/has_availability_decorator.rb'
    - 'app/models/address.rb'
    - 'app/models/payment.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/ledger/clear_credit.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_table/base.rb'
    - 'lib/activity_digests/digest.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/reports/owner_date_clamping.rb'
    - 'scripts/autoscale.rb'

# Offense count: 10
Lint/DuplicateMethods:
  Exclude:
    - 'app/controllers/maintenance/tickets/batch_actions_controller.rb'
    - 'app/forms/contact_form.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/lease_application/employer.rb'
    - 'app/models/lease_application/reference.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/membership_agreement.rb'
    - 'lib/portal_messaging/message_proxy.rb'

# Offense count: 7
# Configuration parameters: AllowComments, AllowEmptyLambdas.
Lint/EmptyBlock:
  Exclude:
    - 'app/jobs/address/geocode_job.rb'
    - 'spec/factories/property/analyses.rb'
    - 'spec/services/assign_property_membership_spec.rb'
    - 'spec/system/invoice_processing/show_spec.rb'
    - 'spec/system/management/onboardings/assigning_tenants_specs.rb'

# Offense count: 2
# Configuration parameters: AllowComments.
Lint/EmptyClass:
  Exclude:
    - 'lib/custom_forms/automations/event_date_time.rb'
    - 'lib/custom_forms/automations/payment.rb'

# Offense count: 1
Lint/FloatComparison:
  Exclude:
    - 'app/models/company.rb'

# Offense count: 5
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'app/forms/agreements/bulk_charge_form.rb'
    - 'lib/reports/v3/basis/base.rb'

# Offense count: 56
# Configuration parameters: AllowedParentClasses.
Lint/MissingSuper:
  Exclude:
    - 'app/components/action_sidebar/item_component.rb'
    - 'app/components/help_tooltip_component.rb'
    - 'app/components/tunisia/two_factor/modal_component.rb'
    - 'app/importers/security_deposits_importer.rb'
    - 'app/services/lease/generate_document_packet.rb'
    - 'app/services/lease/generate_document_sample.rb'
    - 'lib/accounting/context/company.rb'
    - 'lib/accounting/context/guest_card.rb'
    - 'lib/accounting/context/lease_application.rb'
    - 'lib/accounting/context/lease_application_membership.rb'
    - 'lib/accounting/context/lease_membership.rb'
    - 'lib/accounting/context/property.rb'
    - 'lib/accounting/context/unit.rb'
    - 'lib/accounting/context/vendor.rb'
    - 'lib/accounting/context/vendor_receivable.rb'
    - 'lib/accounting/fee_management/customer_region_payments.rb'
    - 'lib/actions_menu/bid.rb'
    - 'lib/actions_menu/collections/eviction.rb'
    - 'lib/actions_menu/custom_form.rb'
    - 'lib/actions_menu/employee.rb'
    - 'lib/actions_menu/journal.rb'
    - 'lib/actions_menu/maintenance/estimate.rb'
    - 'lib/actions_menu/owner.rb'
    - 'lib/actions_menu/parking_lot.rb'
    - 'lib/actions_menu/payment_batch.rb'
    - 'lib/actions_menu/project.rb'
    - 'lib/actions_menu/task.rb'
    - 'lib/pay_lease/account/create.rb'
    - 'lib/pay_lease/transaction/create.rb'
    - 'lib/pay_lease/transaction/get.rb'
    - 'lib/pay_lease/transaction/refresh.rb'
    - 'lib/pay_lease/transaction/refund.rb'
    - 'lib/pay_lease/transaction/void.rb'
    - 'lib/sidebar/apply.rb'
    - 'lib/sidebar/apply/applicant.rb'
    - 'lib/sidebar/apply/applicants.rb'
    - 'lib/sidebar/customer_setup.rb'
    - 'lib/sidebar/customer_setup/client_entity.rb'
    - 'lib/sidebar/customer_setup/data_importing.rb'
    - 'lib/sidebar/move_out.rb'
    - 'lib/sidebar/portfolio_setup.rb'
    - 'lib/sidebar/portfolio_setup/config.rb'
    - 'lib/sidebar/portfolio_setup/entities.rb'
    - 'lib/sidebar/portfolio_setup/entity.rb'
    - 'lib/sidebar/portfolio_setup/persisted.rb'
    - 'lib/sidebar/property_setup.rb'
    - 'lib/zeamster/account/create.rb'
    - 'lib/zeamster/account/get.rb'
    - 'lib/zeamster/contact/create.rb'
    - 'lib/zeamster/contact/get.rb'
    - 'lib/zeamster/locations/get.rb'
    - 'lib/zeamster/transaction/create.rb'
    - 'lib/zeamster/transaction/get.rb'
    - 'lib/zeamster/transaction/refresh.rb'
    - 'lib/zeamster/transaction/refund.rb'
    - 'lib/zeamster/transaction/void.rb'

# Offense count: 4
Lint/NoReturnInBeginEndBlocks:
  Exclude:
    - 'lib/university/term_prorater.rb'

# Offense count: 20
# This cop supports safe autocorrection (--autocorrect).
Lint/RedundantCopDisableDirective:
  Exclude:
    - 'app/controllers/accounting/payables/invoices_controller.rb'
    - 'app/controllers/concerns/controller_common.rb'
    - 'app/controllers/concerns/payment_processing/prohibit_disabled_electronic_payments.rb'
    - 'app/models/concerns/document/verification_document_kinds.rb'
    - 'app/models/inspection/report/activity_logging.rb'
    - 'app/services/payment/record/cash/base.rb'
    - 'app/services/taxes/nelco/transmit/preparers/batch.rb'
    - 'lib/action_index/estimates.rb'
    - 'lib/action_index/leasing/pipeline/applications.rb'
    - 'lib/action_index/messaging/emails.rb'
    - 'lib/action_index/payables_batches.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/completion_summary.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/guarantor_profile.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/lease_agreement.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/member_profile.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/membership_agreement.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/risk_management_program.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/welcome.rb'
    - 'scripts/docker/install_chromedriver.rb'
    - 'scripts/heroku/fetch_database_interactive.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods.
# AllowedMethods: instance_of?, kind_of?, is_a?, eql?, respond_to?, equal?, presence, present?
Lint/RedundantSafeNavigation:
  Exclude:
    - 'app/components/contact/base_component.rb'
    - 'app/jobs/scheduled_payment/reminder_emails_job.rb'
    - 'lib/reference_services/credit_report_details.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods.
# AllowedMethods: present?, blank?, presence, try, try!
Lint/SafeNavigationConsistency:
  Exclude:
    - 'app/models/line_item/defaults.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Lint/ScriptPermission:
  Exclude:
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 13
Lint/ShadowingOuterLocalVariable:
  Exclude:
    - 'app/services/invoice/pay_with_balance.rb'
    - 'app/services/maintenance/estimate/create_project.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/importers/v3/directory_helpers.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/reports/catalog.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/lib/search/result_spec.rb'

# Offense count: 3
# Configuration parameters: AllowKeywordBlockArguments.
Lint/UnderscorePrefixedVariableName:
  Exclude:
    - 'app/services/payment/record/base.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/services/taxes/review_spec.rb'

# Offense count: 4
Lint/UnreachableCode:
  Exclude:
    - 'app/controllers/telephony/inbound_text_messages_controller.rb'
    - 'app/mailers/evictions_mailer.rb'
    - 'app/models/company.rb'
    - 'lib/reports/v3/consolidated_report.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, IgnoreEmptyBlocks, AllowUnusedKeywordArguments.
Lint/UnusedBlockArgument:
  Exclude:
    - 'app/resources/api/v2/accounting/amount_resource.rb'
    - 'app/resources/api/v2/accounting/entry_resource.rb'
    - 'lib/cron/every_hour.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, ContextCreatingMethods, MethodCreatingMethods, inherit_mode.
# ContextCreatingMethods: class_methods, included, prepended, concern, concerning
Lint/UselessAccessModifier:
  Exclude:
    - 'app/services/sierra_leone/inspection/orders/completed/populate_responses/inspector_information.rb'

# Offense count: 60
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
Lint/UselessAssignment:
  Exclude:
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/importers/prepayment_importer.rb'
    - 'app/models/inspection/report/activity_logging.rb'
    - 'app/services/invoice_processing/email/parse.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/payment_processing/batch_transactions.rb'
    - 'lib/importers/v3/base.rb'
    - 'lib/importers/v3/beginning_balances.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/importers/v3/member_directory.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/importers/rent_roll_importer_spec.rb'
    - 'spec/jobs/invoice_processing/notify_processors_job_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/v3/electronic_payments_spec.rb'
    - 'spec/lib/reports/v3/second_nature_monthly_verification_spec.rb'
    - 'spec/lib/reports/v3/tours_spec.rb'
    - 'spec/models/inspection/response_spec.rb'
    - 'spec/models/reams/order_spec.rb'
    - 'spec/requests/api/v2/work_orders_spec.rb'
    - 'spec/services/payment_processing/batch_transactions_spec.rb'
    - 'spec/services/reams/order/submit_spec.rb'
    - 'spec/shared/trust_accounting_context.rb'
    - 'spec/support/property_context_builder.rb'
    - 'spec/support/trust_accounting_context_builder.rb'
    - 'spec/system/accounting/accounts_spec.rb'
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/leasing/leases/edit_lease_spec.rb'
    - 'spec/system/leasing/move_outs/termination_spec.rb'
    - 'spec/system/lending/loans_spec.rb'
    - 'spec/system/management/month_end_review_spec.rb'
    - 'spec/system/management/onboardings/copy_spec.rb'
    - 'spec/system/marketing/listings_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/submit_spec.rb'
    - 'spec/system/telephony/text_messages_spec.rb'
    - 'spec/system/tenants/payments_spec.rb'
    - 'spec/system/tunisia/card_activities/review_spec.rb'

# Offense count: 29
Lint/UselessConstantScoping:
  Exclude:
    - 'app/components/accounting/ledger/statement/envelope_area_component.rb'
    - 'app/components/collections/eviction/timeline_component.rb'
    - 'app/components/recurring_schedule/preview_component.rb'
    - 'app/decorators/maintenance/bid_request/invite_decorator.rb'
    - 'app/jobs/reset_demo_instance_job.rb'
    - 'app/jobs/run_background_check_job.rb'
    - 'app/models/pay_lease_transaction.rb'
    - 'app/policies/insurance/assurant/eligible_unit_policy.rb'
    - 'app/services/inspectify/populate_responses/informations.rb'
    - 'app/services/inspectify/populate_responses/limitations.rb'
    - 'app/services/tunisia/customer_token/create.rb'
    - 'lib/accounting/ledger/statement.rb'
    - 'lib/bank_account/funds_transfer/suggested_transfers/exportable.rb'
    - 'lib/cards/available_units.rb'
    - 'lib/cards/leasing_conversion.rb'
    - 'lib/reports/bulk_ledgers.rb'
    - 'lib/zeamster/transaction/reason_codes.rb'
    - 'lib/zeamster/transaction/refresh.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, CheckForMethodsWithNoSideEffects.
Lint/Void:
  Exclude:
    - 'app/queries/taxes/candidate_1099_query.rb'
    - 'lib/member_onboarding/tenants/wizard.rb'
    - 'scripts/autoscale.rb'

# Offense count: 370
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes, Max.
Metrics/AbcSize:
  Exclude:
    - 'app/components/action_sidebar/electronic_signable/countersigner_modal_component.rb'
    - 'app/controllers/accounting/entries_controller.rb'
    - 'app/controllers/accounting/general_ledger_imports_controller.rb'
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/accounting/invoices_controller.rb'
    - 'app/controllers/accounting/payables/batches_controller.rb'
    - 'app/controllers/accounting/payables/invoices_controller.rb'
    - 'app/controllers/accounting/payables/payments_controller.rb'
    - 'app/controllers/accounting/payments/electronic_payments_controller.rb'
    - 'app/controllers/accounting/payments_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/accounting/receivables/invoices_controller.rb'
    - 'app/controllers/accounting/trial_balance_imports_controller.rb'
    - 'app/controllers/accounting_controller.rb'
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/controllers/apply/applicants_controller.rb'
    - 'app/controllers/concerns/active_admin/archiving.rb'
    - 'app/controllers/concerns/configurations_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/inspections/transform_multiple_photos_params.rb'
    - 'app/controllers/concerns/portal_maintenance_tickets_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/electronic_signatures_controller.rb'
    - 'app/controllers/guest_cards_controller.rb'
    - 'app/controllers/leasing/agreements_controller.rb'
    - 'app/controllers/leasing/applications/transfers_controller.rb'
    - 'app/controllers/leasing/commercial_leases_controller.rb'
    - 'app/controllers/leasing/lease_application_invites_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/leasing/pipelines_controller.rb'
    - 'app/controllers/leasing/tours_controller.rb'
    - 'app/controllers/leasing/unit_transfers_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets/appointments_controller.rb'
    - 'app/controllers/maintenance/tickets/bids_controller.rb'
    - 'app/controllers/maintenance/tickets/billings_controller.rb'
    - 'app/controllers/maintenance/tickets/defers_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/maintenance/vendor_assignments_controller.rb'
    - 'app/controllers/management/default_onboardings_controller.rb'
    - 'app/controllers/management/document_templates_controller.rb'
    - 'app/controllers/management/help_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/management/owners_controller.rb'
    - 'app/controllers/management/parking_reservations_controller.rb'
    - 'app/controllers/management/property_setups/occupancies_controller.rb'
    - 'app/controllers/management/tenants_controller.rb'
    - 'app/controllers/management/vendors_controller.rb'
    - 'app/controllers/marketing/listings_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/messaging/contact_groups_controller.rb'
    - 'app/controllers/messaging/emails_controller.rb'
    - 'app/controllers/operations/collections/evicting_controller.rb'
    - 'app/controllers/operations/inspection_responses_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/operations/projects/assignments_controller.rb'
    - 'app/controllers/operations/projects_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/organization/accounts_controller.rb'
    - 'app/controllers/organization/bank_accounts_controller.rb'
    - 'app/controllers/owners/dashboard_controller.rb'
    - 'app/controllers/owners/invites_controller.rb'
    - 'app/controllers/owners/payments_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/reports/email_schedules_controller.rb'
    - 'app/controllers/reports/rent_schedule_controller.rb'
    - 'app/controllers/tenants/maintenance_tickets_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/decorators/lease_decorator.rb'
    - 'app/exporters/payments_index_exporter.rb'
    - 'app/exporters/rent_roll_exporter.rb'
    - 'app/exporters/xlsx_exporter.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/parking_reservations_importer.rb'
    - 'app/importers/rent_roll_importer.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/agreements/generate_documents_job.rb'
    - 'app/jobs/data_import_job.rb'
    - 'app/jobs/invoice_processing/notify_processors_job.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/lending/loan/update_statement_data_job.rb'
    - 'app/jobs/maintenance/create_scheduled_tickets_job.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/notifications/approval_notifications_job.rb'
    - 'app/jobs/owner_contribution/process_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/jobs/payment_plan/generate_agreement_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/jobs/telephony/send_message_notifications_job.rb'
    - 'app/mailboxes/messaging_mailbox.rb'
    - 'app/mailers/accounts_mailer.rb'
    - 'app/models/approvals/rule.rb'
    - 'app/models/concerns/accounting/journal.rb'
    - 'app/models/concerns/attachments/token_redeemable.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/payment.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/models/saferent/screening.rb'
    - 'app/models/scheduled_payment.rb'
    - 'app/models/security_deposit.rb'
    - 'app/queries/leases_query.rb'
    - 'app/resources/api/v2/lease_application_resource.rb'
    - 'app/serializers/api/v1/work_order_serializer.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/accounting/create_payable.rb'
    - 'app/services/accounting/create_payable_payment.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/accounting/record_receivable_income.rb'
    - 'app/services/agreements/bulk_presets/add_charge.rb'
    - 'app/services/agreements/simple_agreement/generate_document_packet.rb'
    - 'app/services/bank_account/create.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/collections/communications/process.rb'
    - 'app/services/custom_forms/process_payment.rb'
    - 'app/services/customer/promote_sandbox.rb'
    - 'app/services/electronic_signature/create_signed_copy.rb'
    - 'app/services/electronic_signature/execute.rb'
    - 'app/services/electronic_signature/sign.rb'
    - 'app/services/evictions/demand_batch.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'app/services/guest_card/create.rb'
    - 'app/services/inspectify/populate_responses/deficiencies.rb'
    - 'app/services/inspectify/populate_responses/informations.rb'
    - 'app/services/inspection/prepare.rb'
    - 'app/services/invoice/forward.rb'
    - 'app/services/invoice/mark_paid.rb'
    - 'app/services/invoice/pay_with_balance.rb'
    - 'app/services/invoice/void.rb'
    - 'app/services/invoice_processing/email/parse.rb'
    - 'app/services/lease/addendum/generate_document_packet.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/move_out/process.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/generate_invoice.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/lease_application/submit.rb'
    - 'app/services/lending/loan/generate_statement_from_entry.rb'
    - 'app/services/maintenance/bid_request/create.rb'
    - 'app/services/maintenance/billing/bill.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/maintenance/billing/persist.rb'
    - 'app/services/maintenance/estimate/create_project.rb'
    - 'app/services/maintenance_ticket/persist.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/management_contract/generate_document.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/member_onboarding/create_member.rb'
    - 'app/services/member_onboarding/submit.rb'
    - 'app/services/owner/persist.rb'
    - 'app/services/pay_lease_transaction/notification/processed.rb'
    - 'app/services/pay_lease_transaction/status_changed.rb'
    - 'app/services/payment/record/accrual/receivable.rb'
    - 'app/services/payment/record/cash/invoice_void_entries.rb'
    - 'app/services/payment/void.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/create_electronic_payment.rb'
    - 'app/services/payment_processing/create_terminal_payment.rb'
    - 'app/services/profit_stars_transaction/status_changed.rb'
    - 'app/services/property/persist.rb'
    - 'app/services/reports/email_schedule/send.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/import.rb'
    - 'app/services/taxes/nelco/transmit/preparers/payer_company.rb'
    - 'app/services/tenant/merge.rb'
    - 'app/services/unit/merge.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'app/services/vendor_assignment/accept.rb'
    - 'app/services/vendor_assignment/decline.rb'
    - 'app/services/vendor_assignment/submit_invoice.rb'
    - 'app/services/work_order/resolve.rb'
    - 'app/services/zeamster_transaction/status_changed.rb'
    - 'lib/accounting/account_balance_matrix/customer_portfolio_matrix.rb'
    - 'lib/accounting/cash_activity.rb'
    - 'lib/accounting/fee_management/month_end_review.rb'
    - 'lib/accounting/fee_management/month_end_review/property_transfer.rb'
    - 'lib/accounting/fee_management/month_end_review/summary_table.rb'
    - 'lib/accounting/fee_management/property_balances_query.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/journal/health_workbook.rb'
    - 'lib/accounting/prorater.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_table/base.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/bank_account/funds_transfer/sources/manual.rb'
    - 'lib/cards/applications.rb'
    - 'lib/cards/price_per_square_foot.rb'
    - 'lib/chart_of_accounts/tree_builder/balance_sheet.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/chart_of_accounts/tree_builder/rendering.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/email_processor.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/extensions/plutus_entries_contact_name.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/base.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/entity_directory.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/journal_entries.rb'
    - 'lib/importers/v3/jurv_importer.rb'
    - 'lib/importers/v3/lease_charges.rb'
    - 'lib/importers/v3/maintenance_tickets.rb'
    - 'lib/importers/v3/merchant_accounts.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/property_directory.rb'
    - 'lib/importers/v3/rent_roll.rb'
    - 'lib/importers/v3/tenant_directory.rb'
    - 'lib/importers/v3/tenant_invoices.rb'
    - 'lib/importers/v3/tenant_payments.rb'
    - 'lib/importers/v3/unit_directory.rb'
    - 'lib/importers/v3/unpaid_invoices.rb'
    - 'lib/importers/v3/vendor_directory.rb'
    - 'lib/inspections/wizard.rb'
    - 'lib/leasing/application_form_filler.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/markupify.rb'
    - 'lib/member_onboarding/tenants/validator/required_information.rb'
    - 'lib/pay_lease/transaction/create.rb'
    - 'lib/profit_stars/customer/create.rb'
    - 'lib/profit_stars/customer/update.rb'
    - 'lib/profit_stars/money_center/authorize_transaction.rb'
    - 'lib/rbac/permissions.rb'
    - 'lib/reams/requests/survey/answers.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/cash_flow_statement/exportable.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/owner_date_clamping.rb'
    - 'lib/reports/v3/basis/agreement.rb'
    - 'lib/reports/v3/basis/inspection.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/parking_reservation.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/rent_roll.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'lib/reports/v3/basis/work_order.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_income_statement.rb'
    - 'lib/reports/v3/consolidated_property_budget_variance.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/general_ledger.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/journal_allocation.rb'
    - 'lib/reports/v3/lease_aging_receivables.rb'
    - 'lib/reports/v3/loan_payments.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/member_deposits.rb'
    - 'lib/reports/v3/member_portal_adoption.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_1099s.rb'
    - 'lib/reports/v3/owner_balances.rb'
    - 'lib/reports/v3/owner_property_balances.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/refinance_opportunities.rb'
    - 'lib/reports/v3/scope.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/reports/v3/unbalanced_forwards.rb'
    - 'lib/reports/v3/unit_status/rented_on_notice.rb'
    - 'lib/reports/v3/unpaid_lease_charges.rb'
    - 'lib/reports/v3/utility_directory.rb'
    - 'lib/reports/v3/vendor_1099s.rb'
    - 'lib/reports/v3/work_orders.rb'
    - 'lib/saferent/screening/xml_builder.rb'
    - 'lib/sidebar/base.rb'
    - 'lib/zillow/zif.rb'
    - 'spec/lib/accounting/fee_management/transfer_property_balances_spec.rb'
    - 'spec/services/custom_forms/process_payment_spec.rb'
    - 'spec/system/accounting/mark_paid_spec.rb'
    - 'spec/system/accounting/receivables/billing_spec.rb'
    - 'spec/system/custom_forms/event_registration_with_payment_v1_submission_spec.rb'
    - 'spec/system/management/onboardings/copy_spec.rb'
    - 'spec/system/organization/bank_accounts/transfer_spec.rb'
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 100
# Configuration parameters: CountComments, Max, CountAsOne, AllowedMethods, AllowedPatterns, inherit_mode.
# AllowedMethods: refine
Metrics/BlockLength:
  Exclude:
    - 'Gemfile'
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/maintenance/tickets/bids_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/models/concerns/attachments/token_redeemable.rb'
    - 'app/models/concerns/custom_forms/payment_validations.rb'
    - 'app/models/concerns/maintenance_ticket/status.rb'
    - 'app/models/concerns/property/custom_data.rb'
    - 'app/models/concerns/property/custom_status.rb'
    - 'app/models/sierra_leone/inspection/order/status.rb'
    - 'app/queries/approvals/query.rb'
    - 'app/resources/api/v2/shared.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/agreements/bulk_presets/add_charge.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'app/services/invoice/forward.rb'
    - 'app/services/invoice/void.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/member_onboarding/submit.rb'
    - 'app/services/member_onboarding/tenants/submit.rb'
    - 'app/services/pay_lease_transaction/notification/processed.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/create_terminal_payment.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'app/services/vendor_assignment/decline.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/journal/health_workbook.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/contacts/timeline/tenant.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/journal_entries.rb'
    - 'lib/lease_application/scorecard/metric.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/v3/basis/aging_receivables.rb'
    - 'lib/reports/v3/basis/resident.rb'
    - 'lib/reports/v3/journal_allocation.rb'
    - 'lib/reports/v3/ledger.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/member_deposits.rb'
    - 'lib/reports/v3/owner_statement/cash_summary.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/period_change.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/routes/api/v1.rb'
    - 'lib/routes/api/v2.rb'
    - 'lib/routes/apply.rb'
    - 'lib/routes/operations.rb'
    - 'lib/routes/owners.rb'
    - 'lib/routes/portfolio_setup.rb'
    - 'lib/routes/tenants.rb'
    - 'lib/routes/vendors.rb'
    - 'lib/tasks/tunisia.rake'
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 200
# Configuration parameters: CountComments, Max, CountAsOne.
Metrics/ClassLength:
  Exclude:
    - 'app/controllers/accounting/bank_reconciliations_controller.rb'
    - 'app/controllers/accounting/entries_controller.rb'
    - 'app/controllers/accounting/invoices_controller.rb'
    - 'app/controllers/accounting/payables/batches_controller.rb'
    - 'app/controllers/accounting/payables/invoices_controller.rb'
    - 'app/controllers/accounting/payments_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/accounting/receivables/invoices_controller.rb'
    - 'app/controllers/api/v1/work_orders_controller.rb'
    - 'app/controllers/leasing/agreements_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/leasing/pipelines_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/management/companies_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/management/owners_controller.rb'
    - 'app/controllers/management/properties_controller.rb'
    - 'app/controllers/management/tenants_controller.rb'
    - 'app/controllers/management/vendors_controller.rb'
    - 'app/controllers/marketing/listings_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/contact_groups_controller.rb'
    - 'app/controllers/messaging/emails_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/operations/tasks_controller.rb'
    - 'app/controllers/organization/accounts_controller.rb'
    - 'app/controllers/organization/bank_accounts_controller.rb'
    - 'app/controllers/organization/charts_of_accounts_controller.rb'
    - 'app/controllers/organization/configurations_controller.rb'
    - 'app/controllers/organization/employees_controller.rb'
    - 'app/controllers/organization/forms_controller.rb'
    - 'app/controllers/organization/tunisia/card_activities_controller.rb'
    - 'app/controllers/owners/contributions_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/portfolio_setup/entities_controller.rb'
    - 'app/controllers/reports/rent_schedule_controller.rb'
    - 'app/controllers/taxes/ten_ninety_nines_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/documents_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/controllers/vendor/vendor_assignments_controller.rb'
    - 'app/forms/member_onboarding/form.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/rent_roll_importer.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/owner_contribution/process_job.rb'
    - 'app/mailers/electronic_payments_mailer.rb'
    - 'app/models/address.rb'
    - 'app/models/approvals/rule.rb'
    - 'app/models/bank_account.rb'
    - 'app/models/bank_account/reconciliation.rb'
    - 'app/models/chart_of_accounts.rb'
    - 'app/models/company.rb'
    - 'app/models/customer.rb'
    - 'app/models/electronic_signature/request.rb'
    - 'app/models/inspection/report.rb'
    - 'app/models/invoice.rb'
    - 'app/models/lease.rb'
    - 'app/models/lease_application.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/maintenance/estimate.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/payment.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/models/pet.rb'
    - 'app/models/profit_stars_transaction.rb'
    - 'app/models/project.rb'
    - 'app/models/property_manager.rb'
    - 'app/models/recurring_schedule.rb'
    - 'app/models/taxes/candidate_1099.rb'
    - 'app/models/tenant.rb'
    - 'app/models/unit.rb'
    - 'app/models/zillow/claim.rb'
    - 'app/serializers/api/v1/work_order_serializer.rb'
    - 'app/services/accounting/gross_potential_rent/create_unit_entries.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/custom_forms/process_payment.rb'
    - 'app/services/customer/promote_sandbox.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'app/services/invoice_processing/email/parse.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/generate_document_sample.rb'
    - 'app/services/lease/move_out/process.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/maintenance/estimate/create_project.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'app/services/member_onboarding/tenants/submit.rb'
    - 'app/services/payment/record/accrual/receivable.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/batch_transactions.rb'
    - 'app/services/reports/email_schedule/send.rb'
    - 'app/services/saferent/screening/handle_response.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/tunisia/debit_card/create.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'lib/accounting/account_balance_matrix.rb'
    - 'lib/accounting/cash_activity.rb'
    - 'lib/accounting/check.rb'
    - 'lib/accounting/fee_management/bulk_property_balance_transfer.rb'
    - 'lib/accounting/fee_management/month_end_review.rb'
    - 'lib/accounting/fee_management/month_end_review/summary_row.rb'
    - 'lib/accounting/inverse_accounts.rb'
    - 'lib/accounting/yearly_progress_data.rb'
    - 'lib/action_index/agreements.rb'
    - 'lib/action_index/maintenance_tickets.rb'
    - 'lib/action_index/properties.rb'
    - 'lib/action_index/taxes/ten_ninety_nines/overview.rb'
    - 'lib/action_table/base.rb'
    - 'lib/action_table/listings.rb'
    - 'lib/action_table/properties.rb'
    - 'lib/actions_menu/bank_account.rb'
    - 'lib/actions_menu/invoice/payable.rb'
    - 'lib/actions_menu/lease.rb'
    - 'lib/actions_menu/managed_entity.rb'
    - 'lib/actions_menu/property.rb'
    - 'lib/activity_log/description.rb'
    - 'lib/activity_log/entry.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/chart_of_accounts/tree_builder/income_statement.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/base.rb'
    - 'lib/importers/v3/beginning_balances.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/entity_directory.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/guest_cards.rb'
    - 'lib/importers/v3/inspections.rb'
    - 'lib/importers/v3/journal_entries.rb'
    - 'lib/importers/v3/jurv_importer.rb'
    - 'lib/importers/v3/lease_charges.rb'
    - 'lib/importers/v3/maintenance_tickets.rb'
    - 'lib/importers/v3/managed_entity_balances.rb'
    - 'lib/importers/v3/member_directory.rb'
    - 'lib/importers/v3/merchant_accounts.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/property_directory.rb'
    - 'lib/importers/v3/rent_roll.rb'
    - 'lib/importers/v3/tenant_directory.rb'
    - 'lib/importers/v3/tenant_invoices.rb'
    - 'lib/importers/v3/tenant_payments.rb'
    - 'lib/importers/v3/unpaid_invoices.rb'
    - 'lib/member_onboarding/tenants/configured_cohort.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/guarantor_profile.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/cash_flow_statement.rb'
    - 'lib/reports/catalog.rb'
    - 'lib/reports/general_ledger.rb'
    - 'lib/reports/request.rb'
    - 'lib/reports/v3/basis/aging_receivables.rb'
    - 'lib/reports/v3/basis/agreement.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/management_contract.rb'
    - 'lib/reports/v3/basis/parking_reservation.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/rent_roll.rb'
    - 'lib/reports/v3/basis/resident.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'lib/reports/v3/basis/unit.rb'
    - 'lib/reports/v3/basis/vendor.rb'
    - 'lib/reports/v3/basis/work_order.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/collections.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_income_statement.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/general_ledger.rb'
    - 'lib/reports/v3/income_statement.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/journal_allocation.rb'
    - 'lib/reports/v3/journal_integrity.rb'
    - 'lib/reports/v3/ledger.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/period_change.rb'
    - 'lib/reports/v3/rent_roll_with_applicants.rb'
    - 'lib/reports/v3/report.rb'
    - 'lib/reports/v3/scope.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/reports/v3/unpaid_lease_charges.rb'
    - 'lib/saferent/screening/xml_builder.rb'
    - 'lib/university/term_prorater.rb'
    - 'lib/zillow/zif.rb'

# Offense count: 1
# Configuration parameters: LengthThreshold.
Metrics/CollectionLiteralLength:
  Exclude:
    - 'app/resources/api/v2/resi_share_resource/attributes.rb'

# Offense count: 245
# Configuration parameters: AllowedMethods, AllowedPatterns, Max.
Metrics/CyclomaticComplexity:
  Exclude:
    - 'app/components/action_sidebar/electronic_signable/countersigner_modal_component.rb'
    - 'app/components/leasing/lease/sidebar/next_rent_date_component.rb'
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/apply/applicants_controller.rb'
    - 'app/controllers/concerns/configurations_controller.rb'
    - 'app/controllers/concerns/controller_common.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/hosted_application_controller.rb'
    - 'app/controllers/concerns/portal_maintenance_tickets_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/electronic_signatures_controller.rb'
    - 'app/controllers/leasing/applications/transfers_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets/billings_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/management/property_setups/occupancies_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/messaging/contacts_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/confirmations_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/exporters/maintenance_tickets_exporter.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/parking_reservations_importer.rb'
    - 'app/importers/portfolio_list_importer.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/importers/trial_balance_importer/quickbooks_parser.rb'
    - 'app/jobs/accounting/debit_card_purchases/notify_reviewable.rb'
    - 'app/jobs/data_import_job.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/notifications/approval_notifications_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/jobs/scheduled_payment/reminder_emails_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/mailboxes/messaging_mailbox.rb'
    - 'app/mailers/accounts_mailer.rb'
    - 'app/mailers/copy_guardians.rb'
    - 'app/mailers/messaging_mailer.rb'
    - 'app/middleware/custom_subdomain_elevator.rb'
    - 'app/models/address.rb'
    - 'app/models/concerns/accounting/journal.rb'
    - 'app/models/concerns/has_age.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/concerns/property/property_types.rb'
    - 'app/models/concerns/taggable.rb'
    - 'app/models/invoice.rb'
    - 'app/models/invoice_payment.rb'
    - 'app/models/lease/chain.rb'
    - 'app/models/lease/termination_reasons.rb'
    - 'app/models/line_item/defaults.rb'
    - 'app/models/maintenance/bid_request/invite.rb'
    - 'app/models/pay_lease_transaction.rb'
    - 'app/models/payment.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/models/tenant.rb'
    - 'app/queries/agreements_query.rb'
    - 'app/queries/collections_query.rb'
    - 'app/queries/companies_query.rb'
    - 'app/queries/floorplans_query.rb'
    - 'app/queries/invoices_query.rb'
    - 'app/queries/lease_applications_query.rb'
    - 'app/queries/leases_query.rb'
    - 'app/queries/maintenance_tickets_query.rb'
    - 'app/queries/member_onboarding/assignments_query.rb'
    - 'app/queries/payment/batches_query.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/queries/reconciliation/items_query.rb'
    - 'app/queries/vendors_query.rb'
    - 'app/queries/waitlist_entries_query.rb'
    - 'app/serializers/api/v1/work_order_serializer.rb'
    - 'app/serializers/units_table_serializer.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/agreements/bulk_presets/add_charge.rb'
    - 'app/services/agreements/simple_agreement/generate_document_packet.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/electronic_signature/sign.rb'
    - 'app/services/inspectify/populate_responses/informations.rb'
    - 'app/services/invoice/forward.rb'
    - 'app/services/invoice/mark_paid.rb'
    - 'app/services/invoice/record/passthrough_entry.rb'
    - 'app/services/invoice/record/passthrough_reversal_entry.rb'
    - 'app/services/invoice/record/reversal_entry.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/generate_document_packet.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/lease_application/submit.rb'
    - 'app/services/maintenance_ticket/persist.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'app/services/member_onboarding/submit.rb'
    - 'app/services/owner/persist.rb'
    - 'app/services/payment/record/accrual/receivable.rb'
    - 'app/services/payment/record/cash/invoice_void_entries.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/batch_transactions.rb'
    - 'app/services/payment_processing/create_electronic_payment.rb'
    - 'app/services/profit_stars_transaction/status_changed.rb'
    - 'app/services/property/persist.rb'
    - 'app/services/reports/email_schedule/send.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/import.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'app/services/vendor_assignment/decline.rb'
    - 'app/services/zeamster_transaction/status_changed.rb'
    - 'lib/accounting/fee_management/month_end_review/property_transfer.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/prorater.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_table/base.rb'
    - 'lib/action_table/settings/tags.rb'
    - 'lib/activity_digests/digest.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/cards/applications.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/chart_of_accounts/tree_builder/rendering.rb'
    - 'lib/collections/communications/cadence.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/extensions/plutus_entries_contact_name.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/base.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/importers/v3/jurv_importer.rb'
    - 'lib/importers/v3/maintenance_tickets.rb'
    - 'lib/importers/v3/merchant_accounts.rb'
    - 'lib/importers/v3/metadata.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/property_directory.rb'
    - 'lib/importers/v3/unpaid_invoices.rb'
    - 'lib/importers/v3/vendor_directory.rb'
    - 'lib/inspections/wizard.rb'
    - 'lib/leasing/application_form_filler.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/member_onboarding/tenants/validator/required_information.rb'
    - 'lib/member_onboarding/tenants/wizard.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/completion_summary.rb'
    - 'lib/rbac/permissions.rb'
    - 'lib/reams/requests/survey/answers.rb'
    - 'lib/reference_services/credit_report_details.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/owner_date_clamping.rb'
    - 'lib/reports/v3/basis/agreement.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'lib/reports/v3/basis/work_order.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_income_statement.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/member_deposits.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_1099s.rb'
    - 'lib/reports/v3/owner_balances.rb'
    - 'lib/reports/v3/owner_property_balances.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/refinance_opportunities.rb'
    - 'lib/reports/v3/scope.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/reports/v3/unbalanced_forwards.rb'
    - 'lib/reports/v3/utility_directory.rb'
    - 'lib/reports/v3/vendor_1099s.rb'
    - 'lib/reports/v3/work_orders.rb'
    - 'lib/saferent/screening/xml_builder.rb'
    - 'lib/shrine_uploaders/image_uploader.rb'
    - 'lib/sidebar/base.rb'
    - 'lib/university/term_prorater.rb'
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 244
# Configuration parameters: CountComments, Max, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Exclude:
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/accounting/payables/batches_controller.rb'
    - 'app/controllers/accounting/payments_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/accounting/receivables/invoices_controller.rb'
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/portal_maintenance_tickets_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/electronic_signatures_controller.rb'
    - 'app/controllers/leasing/commercial_leases_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/leasing/unit_transfers_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets/bids_controller.rb'
    - 'app/controllers/maintenance/tickets/billings_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/management/document_templates_controller.rb'
    - 'app/controllers/management/help_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/management/onboardings/module_details.rb'
    - 'app/controllers/management/property_setups/occupancies_controller.rb'
    - 'app/controllers/management/vendors_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/messaging/contacts_controller.rb'
    - 'app/controllers/messaging/emails_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/operations/projects/assignments_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/organization/configurations_controller.rb'
    - 'app/controllers/owners/payments_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/decorators/job_posting_decorator.rb'
    - 'app/importers/chart_of_accounts_importer.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/rent_roll_importer.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/agreements/generate_documents_job.rb'
    - 'app/jobs/data_export_job.rb'
    - 'app/jobs/data_import_job.rb'
    - 'app/jobs/invoice_processing/notify_processors_job.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/management_fees/generate_management_fees_job.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/notifications/approval_notifications_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/jobs/telephony/send_message_notifications_job.rb'
    - 'app/mailboxes/messaging_mailbox.rb'
    - 'app/mailers/accounts_mailer.rb'
    - 'app/models/concerns/accounting/journal.rb'
    - 'app/models/concerns/attachments/token_redeemable.rb'
    - 'app/models/concerns/property/custom_data.rb'
    - 'app/models/concerns/property/custom_status.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/models/plutus/entry/exportable.rb'
    - 'app/models/security_deposit.rb'
    - 'app/queries/invoices_query.rb'
    - 'app/queries/payments_query.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/queries/ticket_regardable_query.rb'
    - 'app/queries/unit_status_query.rb'
    - 'app/queries/units_query.rb'
    - 'app/resources/api/v2/lease_application_resource.rb'
    - 'app/serializers/api/v1/work_order_serializer.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/accounting/post_transactions.rb'
    - 'app/services/agreements/bulk_presets/add_charge.rb'
    - 'app/services/agreements/simple_agreement/generate_document_packet.rb'
    - 'app/services/approvals/bulk_approve.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/custom_forms/process_payment.rb'
    - 'app/services/electronic_signature/create_signed_copy.rb'
    - 'app/services/electronic_signature/execute.rb'
    - 'app/services/electronic_signature/sign.rb'
    - 'app/services/evictions/demand_batch.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'app/services/invoice/forward.rb'
    - 'app/services/invoice/void.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/generate_document_sample.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/lease_application/submit.rb'
    - 'app/services/lending/loan/generate_statement_from_entry.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/maintenance/estimate/create_project.rb'
    - 'app/services/maintenance_ticket/persist.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/management_contract/generate_document.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/member_onboarding/create_member.rb'
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'app/services/member_onboarding/submit.rb'
    - 'app/services/pay_lease_transaction/notification/processed.rb'
    - 'app/services/payment/record/cash/invoice_void_entries.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/create_electronic_payment.rb'
    - 'app/services/payment_processing/create_terminal_payment.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/import.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'app/services/vendor_assignment/accept.rb'
    - 'app/services/vendor_assignment/decline.rb'
    - 'lib/accounting/account_balance_matrix/customer_portfolio_matrix.rb'
    - 'lib/accounting/cash_activity.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/inverse_accounts.rb'
    - 'lib/accounting/journal/health_workbook.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_table/base.rb'
    - 'lib/actions_menu/invoice/payable.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/cards/control_accounts.rb'
    - 'lib/cards/new_leads.rb'
    - 'lib/cards/physical_occupancy.rb'
    - 'lib/cards/price_per_square_foot.rb'
    - 'lib/chart_of_accounts/tree_builder/balance_sheet.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/contacts/timeline/tenant.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/entity_directory.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/guest_cards.rb'
    - 'lib/importers/v3/journal_entries.rb'
    - 'lib/importers/v3/lease_charges.rb'
    - 'lib/importers/v3/maintenance_tickets.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/tenant_directory.rb'
    - 'lib/importers/v3/tenant_invoices.rb'
    - 'lib/importers/v3/tenant_payments.rb'
    - 'lib/importers/v3/vendor_directory.rb'
    - 'lib/inspections/wizard.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'lib/reams/requests/get_work_orders.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/rent_roll.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/collections.rb'
    - 'lib/reports/v3/commercial_rent_roll.rb'
    - 'lib/reports/v3/consolidated_balance_sheet.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_income_statement.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/general_ledger.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/journal_allocation.rb'
    - 'lib/reports/v3/lease_aging_receivables.rb'
    - 'lib/reports/v3/ledger.rb'
    - 'lib/reports/v3/loan_payments.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/member_deposits.rb'
    - 'lib/reports/v3/member_portal_adoption.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_1099s.rb'
    - 'lib/reports/v3/owner_balances.rb'
    - 'lib/reports/v3/owner_property_balances.rb'
    - 'lib/reports/v3/owner_statement/cash_summary.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/period_change.rb'
    - 'lib/reports/v3/property_directory.rb'
    - 'lib/reports/v3/refinance_opportunities.rb'
    - 'lib/reports/v3/rent_roll_with_applicants.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/reports/v3/unbalanced_forwards.rb'
    - 'lib/reports/v3/unpaid_lease_charges.rb'
    - 'lib/reports/v3/utility_directory.rb'
    - 'lib/reports/v3/vendor_1099s.rb'
    - 'lib/reports/v3/work_orders.rb'
    - 'lib/sidebar/customer_setup/data_importing.rb'
    - 'spec/lib/reports/v3/shared/aging_receivables_reports_context.rb'
    - 'spec/models/custom_forms/form_spec.rb'
    - 'spec/support/trust_accounting_context_builder.rb'
    - 'spec/system/lease_applications/application_filling_context.rb'
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 33
# Configuration parameters: CountComments, Max, CountAsOne.
Metrics/ModuleLength:
  Exclude:
    - 'app/controllers/concerns/calendar_events_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/helpers/application_helper.rb'
    - 'app/models/concerns/archivable.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/concerns/maintenance_ticket/owner_approvable.rb'
    - 'app/models/taxes/nelco.rb'
    - 'app/queries/collections_query.rb'
    - 'app/queries/invoices_query.rb'
    - 'app/queries/lease_applications_query.rb'
    - 'app/queries/leases_query.rb'
    - 'app/queries/maintenance_tickets_query.rb'
    - 'app/queries/payments_query.rb'
    - 'app/queries/plutus/accounts_query.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/queries/tenants_query.rb'
    - 'app/queries/unit_status_query.rb'
    - 'app/queries/units_query.rb'
    - 'app/resources/api/v2/resi_share_resource/attributes.rb'
    - 'app/resources/api/v2/shared.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_basis.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/navigation/employees/accounting.rb'
    - 'lib/navigation/employees/operations.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/zeamster/transaction/reason_codes.rb'
    - 'spec/support/capybara_helper.rb'
    - 'spec/support/custom_form_field_builder.rb'
    - 'spec/support/trust_accounting_context_builder.rb'

# Offense count: 21
# Configuration parameters: Max, CountKeywordArgs, MaxOptionalParameters.
Metrics/ParameterLists:
  Exclude:
    - 'app/controllers/concerns/simple_layout.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/payment_processing/notify_system_failure_job.rb'
    - 'app/mailers/contact_mailer.rb'
    - 'app/services/contact/timeline_entry/create.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/maintenance/bid_request/create.rb'
    - 'app/services/payment_processing/create_electronic_payment.rb'
    - 'app/services/tunisia/book_payment/create.rb'
    - 'app/services/work_order/check_out.rb'
    - 'lib/reports/request.rb'
    - 'lib/reports/v3/basis/base.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/tunisia/authorized_user_presenter.rb'
    - 'lib/zeamster/transaction/create_from_terminal.rb'
    - 'spec/support/trust_accounting_context_builder.rb'

# Offense count: 190
# Configuration parameters: AllowedMethods, AllowedPatterns, Max.
Metrics/PerceivedComplexity:
  Exclude:
    - 'app/components/action_sidebar/electronic_signable/countersigner_modal_component.rb'
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/accounting/payments_controller.rb'
    - 'app/controllers/accounting/receivables/income_controller.rb'
    - 'app/controllers/apply/applicants_controller.rb'
    - 'app/controllers/concerns/configurations_controller.rb'
    - 'app/controllers/concerns/controller_common.rb'
    - 'app/controllers/concerns/hosted_application_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/electronic_signatures_controller.rb'
    - 'app/controllers/leasing/applications/transfers_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/maintenance/estimates_controller.rb'
    - 'app/controllers/maintenance/tickets/billings_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/management/parking_reservations_controller.rb'
    - 'app/controllers/member_onboardings_controller.rb'
    - 'app/controllers/messaging/contacts_controller.rb'
    - 'app/controllers/operations/inspections_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/confirmations_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payment_plans_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/decorators/lease_decorator.rb'
    - 'app/exporters/maintenance_tickets_exporter.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/data_import_job.rb'
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'app/jobs/notification_job.rb'
    - 'app/jobs/notifications/approval_notifications_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/mailboxes/messaging_mailbox.rb'
    - 'app/mailers/accounts_mailer.rb'
    - 'app/mailers/copy_guardians.rb'
    - 'app/mailers/messaging_mailer.rb'
    - 'app/models/concerns/accounting/journal.rb'
    - 'app/models/concerns/has_age.rb'
    - 'app/models/concerns/lease/computed_values.rb'
    - 'app/models/concerns/property/property_types.rb'
    - 'app/models/concerns/taggable.rb'
    - 'app/models/invoice.rb'
    - 'app/models/invoice_payment.rb'
    - 'app/models/line_item/defaults.rb'
    - 'app/models/maintenance/bid_request/invite.rb'
    - 'app/models/payment.rb'
    - 'app/models/payment/batch/pair.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/vendors_query.rb'
    - 'app/serializers/api/v1/work_order_serializer.rb'
    - 'app/serializers/units_table_serializer.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/accounting/record_receivable_income.rb'
    - 'app/services/agreements/bulk_presets/add_charge.rb'
    - 'app/services/agreements/simple_agreement/generate_document_packet.rb'
    - 'app/services/broadcast/create.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/electronic_signature/sign.rb'
    - 'app/services/inspectify/populate_responses/informations.rb'
    - 'app/services/invoice/forward.rb'
    - 'app/services/invoice/mark_paid.rb'
    - 'app/services/invoice/record/passthrough_reversal_entry.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/demand_for_possession/values.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/generate_document_packet.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/make_payment.rb'
    - 'app/services/lease_application/submit.rb'
    - 'app/services/maintenance_ticket/persist.rb'
    - 'app/services/management/prepare_disbursements.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/member_onboarding/submit.rb'
    - 'app/services/payment/record/accrual/receivable.rb'
    - 'app/services/payment/record/cash/invoice_void_entries.rb'
    - 'app/services/payment/void.rb'
    - 'app/services/payment_method/create.rb'
    - 'app/services/payment_processing/create_electronic_payment.rb'
    - 'app/services/profit_stars_transaction/status_changed.rb'
    - 'app/services/scheduled_payment/process.rb'
    - 'app/services/sierra_leone/inspection/orders/completed/import.rb'
    - 'app/services/unit/reservation/submit.rb'
    - 'app/services/zeamster_transaction/status_changed.rb'
    - 'lib/accounting/fee_management/month_end_review/property_transfer.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'lib/accounting/prorater.rb'
    - 'lib/accounting/sae_due_to_from_matrix.rb'
    - 'lib/action_table/base.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/cards/applications.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'
    - 'lib/chart_of_accounts/tree_builder/rendering.rb'
    - 'lib/cron/every_day.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_entries_contact_name.rb'
    - 'lib/greek_housing/cohort.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/importers/v3/base.rb'
    - 'lib/importers/v3/bill_detail.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/importers/v3/jurv_importer.rb'
    - 'lib/importers/v3/merchant_accounts.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/importers/v3/tenant_directory.rb'
    - 'lib/importers/v3/unpaid_invoices.rb'
    - 'lib/importers/v3/vendor_directory.rb'
    - 'lib/inspections/wizard.rb'
    - 'lib/leasing/application_form_filler.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/member_onboarding/tenants/validator/required_information.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/completion_summary.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/guarantor_profile.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'lib/rbac/permissions.rb'
    - 'lib/reams/requests/survey/answers.rb'
    - 'lib/reference_services/credit_report_details.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/owner_date_clamping.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/work_order.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/comparative_cash_flow_statement.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_income_statement.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/general_ledger.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'lib/reports/v3/management_revenue.rb'
    - 'lib/reports/v3/member_deposits.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/refinance_opportunities.rb'
    - 'lib/reports/v3/scope.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'lib/reports/v3/ten_ninety_nines.rb'
    - 'lib/reports/v3/trial_balance.rb'
    - 'lib/reports/v3/unbalanced_forwards.rb'
    - 'lib/reports/v3/utility_directory.rb'
    - 'lib/reports/v3/vendor_1099s.rb'
    - 'lib/reports/v3/work_orders.rb'
    - 'lib/saferent/screening/xml_builder.rb'
    - 'lib/sidebar/base.rb'
    - 'lib/university/term_prorater.rb'

# Offense count: 1
Naming/AccessorMethodName:
  Exclude:
    - 'app/models/payment/batch/batching.rb'

# Offense count: 19
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyleForLeadingUnderscores.
# SupportedStylesForLeadingUnderscores: disallowed, required, optional
Naming/MemoizedInstanceVariableName:
  Exclude:
    - 'app/controllers/attachments_controller.rb'
    - 'app/controllers/concerns/hosted_application_controller.rb'
    - 'app/controllers/reports/rent_schedule_controller.rb'
    - 'app/controllers/taxes/batch_groups_controller.rb'
    - 'app/decorators/tenant_decorator.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/lease_application/employer.rb'
    - 'app/serializers/chat_serializer.rb'
    - 'app/services/zillow/process_lead.rb'
    - 'lib/accounting/yearly_progress_data.rb'
    - 'lib/reports/v3/income_statement.rb'
    - 'lib/reports/v3/unit_status/unrentable_down.rb'
    - 'lib/university/term_prorater.rb'

# Offense count: 16
# Configuration parameters: MinNameLength, AllowNamesEndingInNumbers, AllowedNames, ForbiddenNames.
# AllowedNames: as, at, by, cc, db, id, if, in, io, ip, of, on, os, pp, to
Naming/MethodParameterName:
  Exclude:
    - 'app/components/electronic_documents/metadata_fields_component.rb'
    - 'app/controllers/accounting_controller.rb'
    - 'app/exporters/underwriting_exporter.rb'
    - 'app/helpers/active_admin/views_helper.rb'
    - 'app/services/lease/generate_document_packet.rb'
    - 'app/services/zillow/claims/dropdown_options.rb'
    - 'lib/anomalies/anomaly/overlapping_leases.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/cash_flow_statement/exportable.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'

# Offense count: 8
# Configuration parameters: NamePrefix, ForbiddenPrefixes, AllowedMethods, MethodDefinitionMacros, UseSorbetSigs.
# NamePrefix: is_, has_, have_, does_
# ForbiddenPrefixes: is_, has_, have_, does_
# AllowedMethods: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicateName:
  Exclude:
    - 'app/components/custom_forms/element/base_component.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/models/custom_forms/form.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/line_item.rb'
    - 'app/models/saferent/screening.rb'
    - 'app/queries/properties_query.rb'
    - 'lib/saferent/screening/xml_builder.rb'

# Offense count: 56
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers, AllowedPatterns.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: TLS1_1, TLS1_2, capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339, x86_64
Naming/VariableNumber:
  Exclude:
    - 'app/controllers/reports/rent_schedule_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/forms/member_onboarding/form.rb'
    - 'app/forms/member_onboarding/params.rb'
    - 'app/models/concerns/proratable.rb'
    - 'app/models/hap_contract.rb'
    - 'app/models/income_certification.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/services/inspectify/order/create.rb'
    - 'app/services/member_onboarding/create_member.rb'
    - 'lib/accounting/month_prorater.rb'
    - 'lib/accounting/prorater.rb'
    - 'lib/greek_housing/cohorts/sae_metadata.rb'
    - 'lib/tasks/statistics.rake'
    - 'lib/university/semester.rb'
    - 'lib/university/term_prorater.rb'
    - 'spec/jobs/scheduled_payment/reminder_emails_job_spec.rb'
    - 'spec/lib/accounting/prorater_spec.rb'
    - 'spec/models/scheduled_payment_spec.rb'
    - 'spec/services/taxes/nelco/transmit/preparers/batch_spec.rb'
    - 'spec/system/listings/zillow/listings.rb'
    - 'spec/system/reporting/account_ledger_spec.rb'
    - 'spec/system/tunisia/create_card_spec.rb'
    - 'spec/system/tunisia/open_bank_account_spec.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Performance/BigDecimalWithNumericArgument:
  Exclude:
    - 'app/controllers/owners/dashboard_controller.rb'
    - 'spec/controllers/api/v1/time_tracking_controller_spec.rb'

# Offense count: 4
# Configuration parameters: MinSize.
Performance/CollectionLiteralInLoop:
  Exclude:
    - 'app/jobs/late_fees/slack_late_fees_job.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/reports/v3/basis/tenancy_schedule.rb'
    - 'spec/rails_helper.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/Count:
  Exclude:
    - 'app/services/lease/generate_document.rb'
    - 'lib/accounting/ledger/payment_statistics.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/MapCompact:
  Exclude:
    - 'app/controllers/zillow/claims_controller.rb'
    - 'lib/pay_lease/transaction/create.rb'
    - 'lib/reports/v3/investor_home_data.rb'

# Offense count: 32
Performance/MapMethodChain:
  Exclude:
    - 'app/controllers/accounting_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/decorators/reports/packet_template_decorator.rb'
    - 'app/exporters/underwriting_exporter.rb'
    - 'app/mailers/collections/eviction_vendors_mailer.rb'
    - 'app/models/approvals/rule.rb'
    - 'app/models/itemized_damages.rb'
    - 'app/services/payment_processing/batch_transactions.rb'
    - 'lib/accounting/fee_management/month_end_review.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/reference_services/report.rb'
    - 'lib/reports/packet/renderer.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'
    - 'lib/reports/v3/semester_gross_potential_rent.rb'
    - 'spec/jobs/notifications/daily_notifications_mail_job_spec.rb'
    - 'spec/queries/collections_query_spec.rb'
    - 'spec/system/leasing/leases/index_spec.rb'
    - 'spec/system/maintenance/tickets/index_spec.rb'
    - 'spec/system/portfolio/tenants/index_spec.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
Performance/RedundantBlockCall:
  Exclude:
    - 'app/models/accounting/invoice_balance.rb'
    - 'app/models/accounting/property_cash_balance.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: MaxKeyValuePairs.
Performance/RedundantMerge:
  Exclude:
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'lib/reports/v3/rent_roll_detail.rb'
    - 'lib/reports/v3/tenancy_schedule.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/StringInclude:
  Exclude:
    - 'spec/system/tunisia/create_card_spec.rb'
    - 'spec/system/tunisia/open_bank_account_spec.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: OnlySumOrWithInitialValue.
Performance/Sum:
  Exclude:
    - 'lib/reports/v3/investor_home_data.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/TimesMap:
  Exclude:
    - 'lib/inspections/wizard.rb'

# Offense count: 123
RSpec/AnyInstance:
  Exclude:
    - 'spec/controllers/accounting/budgets_controller_spec.rb'
    - 'spec/controllers/accounting/payables/payments_controller_spec.rb'
    - 'spec/controllers/accounting/receivables/income_controller_spec.rb'
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/controllers/api/zapier/contact_timeline_entries_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/controllers/leasing/applications/screenings_controller_spec.rb'
    - 'spec/controllers/messaging/broadcasts_controller_spec.rb'
    - 'spec/controllers/owners/payments_controller_spec.rb'
    - 'spec/importers/v3/base_spec.rb'
    - 'spec/importers/v3/unpaid_invoices_spec.rb'
    - 'spec/jobs/data_import_job_spec.rb'
    - 'spec/jobs/inspectify/order/create_job_spec.rb'
    - 'spec/jobs/lease_application/update_reported_household_income_job_spec.rb'
    - 'spec/jobs/lease_application/update_verified_household_income_job_spec.rb'
    - 'spec/jobs/run_background_check_job_spec.rb'
    - 'spec/jobs/search/index_record_job_spec.rb'
    - 'spec/jobs/search/reindex_model_job_spec.rb'
    - 'spec/jobs/sftp_to_go_import_job_spec.rb'
    - 'spec/lib/accounting/cash_basis/unapplied_payments_spec.rb'
    - 'spec/lib/accounting/ledger/payment_statistics_spec.rb'
    - 'spec/lib/reports/v3/commercial_rent_roll_spec.rb'
    - 'spec/lib/reports/v3/consolidated_balance_sheet_spec.rb'
    - 'spec/lib/reports/v3/consolidated_budget_variance_spec.rb'
    - 'spec/lib/reports/v3/consolidated_income_statement_spec.rb'
    - 'spec/lib/reports/v3/evictions_spec.rb'
    - 'spec/lib/reports/v3/lease_aging_receivables_spec.rb'
    - 'spec/lib/reports/v3/lease_directory_spec.rb'
    - 'spec/lib/reports/v3/leasing_commission_spec.rb'
    - 'spec/lib/reports/v3/residency_spec.rb'
    - 'spec/lib/reports/v3/second_nature_monthly_verification_spec.rb'
    - 'spec/lib/reports/v3/shared.rb'
    - 'spec/lib/reports/v3/tenant_directory_spec.rb'
    - 'spec/lib/reports/v3/unpaid_lease_charges_spec.rb'
    - 'spec/lib/reports/v3/work_orders_spec.rb'
    - 'spec/lib/saferent/screening/xml_builder_spec.rb'
    - 'spec/lib/the_closing_docs/screening_group/create_spec.rb'
    - 'spec/mailers/copy_guardians_spec.rb'
    - 'spec/models/concerns/rbac/has_role_spec.rb'
    - 'spec/models/invoice/has_revela_invoice_number_spec.rb'
    - 'spec/models/vendor_assignment_spec.rb'
    - 'spec/requests/reports/requests_controller_spec.rb'
    - 'spec/services/collections/communications/provision_phone_number_spec.rb'
    - 'spec/services/collections/communications/send_sms_spec.rb'
    - 'spec/services/inspectify/order/completed_spec.rb'
    - 'spec/services/inspectify/populate_responses_spec.rb'
    - 'spec/services/management_fees/bill_entity_spec.rb'
    - 'spec/services/member_onboarding/tenants/submit_spec.rb'
    - 'spec/services/mercury/order/submit_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/services/payment_processing/add_nsf_fee_spec.rb'
    - 'spec/services/payment_processing/create_electronic_payment_spec.rb'
    - 'spec/services/reams/order/fetch_canceled_spec.rb'
    - 'spec/services/reams/order/submit_spec.rb'
    - 'spec/services/scheduled_payment/process_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses_spec.rb'
    - 'spec/services/taxes/nelco/transmit/preparers/batch_spec.rb'
    - 'spec/services/taxes/nelco/transmit/service_spec.rb'
    - 'spec/shared/electronic_signatures_context.rb'
    - 'spec/shared/owner_work_order_context.rb'
    - 'spec/support/request_helpers.rb'
    - 'spec/system/accounting/statements/work_performed_spec.rb'
    - 'spec/system/action_sidebar/electronic_signable/countersigner_modal_component_spec.rb'
    - 'spec/system/inspections/reams_spec.rb'
    - 'spec/system/lease_applications/credit_references_spec.rb'
    - 'spec/system/leasing/leads_spec.rb'
    - 'spec/system/leasing/lease_applications/adjudication_spec.rb'
    - 'spec/system/leasing/move_outs/reopen_move_out_spec.rb'
    - 'spec/system/leasing/waitlists/pipeline_spec.rb'
    - 'spec/system/management/bulk_property_balance_transfers_spec.rb'
    - 'spec/system/management/help_form_spec.rb'
    - 'spec/system/management/prepare_disbursements_spec.rb'
    - 'spec/system/management/user_fingerprint_spec.rb'
    - 'spec/system/memberships/member_onboarding_form_spec.rb'
    - 'spec/system/owners/documents_spec.rb'
    - 'spec/system/portfolio/tenants/prepare_refund_spec.rb'
    - 'spec/system/portfolio/tenants/seven_day_demand_spec.rb'
    - 'spec/system/portfolio/tenants_spec.rb'
    - 'spec/system/portfolio/vendor_invites_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/resend_email_spec.rb'
    - 'spec/system/telephony/phone_numbers_spec.rb'
    - 'spec/system/tenants/member_profile_spec.rb'
    - 'spec/system/users/resend_fingerprint_spec.rb'
    - 'spec/system/vendor/invoice_request_spec.rb'

# Offense count: 12
# This cop supports unsafe autocorrection (--autocorrect-all).
RSpec/BeEq:
  Exclude:
    - 'spec/models/bank/branch_spec.rb'
    - 'spec/services/onboarding/validate_subdomain_spec.rb'
    - 'spec/system/landlord_verifications/landlord_verification_spec.rb'
    - 'spec/system/memberships/member_onboarding_form_spec.rb'
    - 'spec/system/tenants/member_profile_spec.rb'

# Offense count: 1
RSpec/BeforeAfterAll:
  Exclude:
    - 'spec/importers/tenant_ledger_importer_spec.rb'

# Offense count: 464
# Configuration parameters: Prefixes, AllowedPatterns.
# Prefixes: when, with, without
RSpec/ContextWording:
  Exclude:
    - 'spec/controllers/accounting/bank_reconciliations_controller_spec.rb'
    - 'spec/controllers/accounting/budgets_controller_spec.rb'
    - 'spec/controllers/accounting/entries_controller_spec.rb'
    - 'spec/controllers/accounting/invoice_controller.rb'
    - 'spec/controllers/accounting/journals_controller_spec.rb'
    - 'spec/controllers/accounting/payables/batches_controller_spec.rb'
    - 'spec/controllers/accounting/payables/invoices_controller_spec.rb'
    - 'spec/controllers/accounting/payables/payments_controller_spec.rb'
    - 'spec/controllers/accounting/payments_controller_spec.rb'
    - 'spec/controllers/accounting/receivables/income_controller_spec.rb'
    - 'spec/controllers/api/v1/inspection_reports_controller_spec.rb'
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/controllers/leasing/applications/screenings_controller_spec.rb'
    - 'spec/controllers/leasing/guest_cards_controller_spec.rb'
    - 'spec/controllers/leasing/leases_controller_spec.rb'
    - 'spec/controllers/leasing/move_outs_controller_spec.rb'
    - 'spec/controllers/leasing/unit_transfers_controller_spec.rb'
    - 'spec/controllers/leasing/waitlist_entries_controller_spec.rb'
    - 'spec/controllers/listings_controller_spec.rb'
    - 'spec/controllers/maintenance/tags_controller_spec.rb'
    - 'spec/controllers/maintenance/tickets_controller_spec.rb'
    - 'spec/controllers/management/calendar_events_controller_spec.rb'
    - 'spec/controllers/management/floorplans_controller_spec.rb'
    - 'spec/controllers/management/owners_controller_spec.rb'
    - 'spec/controllers/management/parking_lots_controller_spec.rb'
    - 'spec/controllers/management/parking_reservations_controller_spec.rb'
    - 'spec/controllers/management/portfolios_controller_spec.rb'
    - 'spec/controllers/management/properties_controller_spec.rb'
    - 'spec/controllers/management/property_setups_controller_spec.rb'
    - 'spec/controllers/management/tenants_controller_spec.rb'
    - 'spec/controllers/management/units/downtimes_controller_spec.rb'
    - 'spec/controllers/management/units_controller_spec.rb'
    - 'spec/controllers/management/vendors/sku_lists_controller_spec.rb'
    - 'spec/controllers/management/vendors_controller_spec.rb'
    - 'spec/controllers/marketing/listings_controller_spec.rb'
    - 'spec/controllers/messaging/broadcasts_controller_spec.rb'
    - 'spec/controllers/operations/inspection_templates_controller_spec.rb'
    - 'spec/controllers/operations/projects_controller_spec.rb'
    - 'spec/controllers/operations/tasks_controller_spec.rb'
    - 'spec/controllers/organization/accounts_controller_spec.rb'
    - 'spec/controllers/organization/charts_of_accounts_controller_spec.rb'
    - 'spec/controllers/organization/companies_controller_spec.rb'
    - 'spec/controllers/organization/configurations/credit_presets_controller_spec.rb'
    - 'spec/controllers/organization/employees_controller_spec.rb'
    - 'spec/controllers/owners/payments_controller_spec.rb'
    - 'spec/controllers/tenants/payment_methods_controller_spec.rb'
    - 'spec/importers/chart_of_accounts_importer_spec.rb'
    - 'spec/importers/v3/shared_invoice_importing.rb'
    - 'spec/jobs/activity_digests/owner_emails_job_spec.rb'
    - 'spec/jobs/maintenance/create_scheduled_tickets_job_spec.rb'
    - 'spec/jobs/notification_job_spec.rb'
    - 'spec/jobs/notifications/daily_notifications_mail_job_spec.rb'
    - 'spec/jobs/reports/run_email_schedules_job_spec.rb'
    - 'spec/jobs/run_background_check_job_spec.rb'
    - 'spec/jobs/scheduled_payment/process_payments_job_spec.rb'
    - 'spec/jobs/tenant/notify_new_invoices_job_spec.rb'
    - 'spec/lib/accounting/cash_basis/unapplied_payments_spec.rb'
    - 'spec/lib/accounting/cash_basis_spec.rb'
    - 'spec/lib/accounting/company_context_spec.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/accounting/property_context_spec.rb'
    - 'spec/lib/accounting/prorater_spec.rb'
    - 'spec/lib/griddler/email_processor_spec.rb'
    - 'spec/lib/pay_lease/account/create_spec.rb'
    - 'spec/lib/profit_stars/account/create_spec.rb'
    - 'spec/lib/profit_stars/account/get_for_customer_spec.rb'
    - 'spec/lib/profit_stars/account/update_spec.rb'
    - 'spec/lib/profit_stars/customer/create_spec.rb'
    - 'spec/lib/profit_stars/customer/update_spec.rb'
    - 'spec/lib/profit_stars/money_center/authorize_transaction_spec.rb'
    - 'spec/lib/profit_stars/payment/create_spec.rb'
    - 'spec/lib/profit_stars/payment/refund_spec.rb'
    - 'spec/lib/profit_stars/payment/void_spec.rb'
    - 'spec/lib/profit_stars/test_connection_spec.rb'
    - 'spec/lib/profit_stars/test_credentials_spec.rb'
    - 'spec/lib/profit_stars/transactions/credit_and_debit_reports_spec.rb'
    - 'spec/lib/profit_stars/transactions/credits_and_debits_transaction_detail_report_spec.rb'
    - 'spec/lib/profit_stars/transactions/historical_events_spec.rb'
    - 'spec/lib/profit_stars/transactions/refresh_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/v3/consolidated_property_income_statement_spec.rb'
    - 'spec/lib/reports/v3/income_statement_spec.rb'
    - 'spec/lib/reports/v3/shared.rb'
    - 'spec/lib/reports/v3/shared/owner_balance_reports_context.rb'
    - 'spec/lib/reports/v3/shared/subdivision_accounting_reports_context.rb'
    - 'spec/lib/saferent/screening/xml_builder_spec.rb'
    - 'spec/lib/search/result_spec.rb'
    - 'spec/lib/zeamster/account/create_spec.rb'
    - 'spec/lib/zeamster/account/get_spec.rb'
    - 'spec/lib/zeamster/contact/create_spec.rb'
    - 'spec/lib/zeamster/contact/get_spec.rb'
    - 'spec/lib/zeamster/transaction/create_spec.rb'
    - 'spec/models/approvals/request_spec.rb'
    - 'spec/models/background_check_spec.rb'
    - 'spec/models/charge_preset_spec.rb'
    - 'spec/models/concerns/approvals/approvable_spec.rb'
    - 'spec/models/customer/registration_spec.rb'
    - 'spec/models/customer_spec.rb'
    - 'spec/models/invoice_spec.rb'
    - 'spec/models/lease/chain_spec.rb'
    - 'spec/models/lease_application/applicant_spec.rb'
    - 'spec/models/lease_application_spec.rb'
    - 'spec/models/lease_membership/aging_delinquency_spec.rb'
    - 'spec/models/lease_spec.rb'
    - 'spec/models/listing_spec.rb'
    - 'spec/models/merchant_account_spec.rb'
    - 'spec/models/pay_lease_transaction_spec.rb'
    - 'spec/models/plutus/accounts_spec.rb'
    - 'spec/models/plutus/entries_spec.rb'
    - 'spec/models/portfolio_spec.rb'
    - 'spec/models/scheduled_payment_spec.rb'
    - 'spec/policies/payment/refundable_spec.rb'
    - 'spec/policies/payment/voidable_spec.rb'
    - 'spec/policies/portfolio_mode_policy_spec.rb'
    - 'spec/queries/lease_applications_query_spec.rb'
    - 'spec/queries/payments_query_spec.rb'
    - 'spec/queries/tenants_query_spec.rb'
    - 'spec/queries/units_query_spec.rb'
    - 'spec/requests/api/v2/rswag_context.rb'
    - 'spec/services/accounting/create_retained_earnings_spec.rb'
    - 'spec/services/charge_schedule/bill_spec.rb'
    - 'spec/services/collections/communications/collections_phone_provisioning_context.rb'
    - 'spec/services/customer/billing_spec.rb'
    - 'spec/services/guest_card/create_spec.rb'
    - 'spec/services/inspection/prepare_spec.rb'
    - 'spec/services/invoice/waive_spec.rb'
    - 'spec/services/ledger/apply_security_deposit_spec.rb'
    - 'spec/services/management_fees/bill_entity_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/services/payment/void_spec.rb'
    - 'spec/services/profit_stars_transaction/status_changed_spec.rb'
    - 'spec/services/scheduled_payment/process_spec.rb'
    - 'spec/services/work_order/close_spec.rb'
    - 'spec/services/zeamster_transaction/status_changed_spec.rb'
    - 'spec/shared/electronic_signatures_context.rb'
    - 'spec/shared/maintenance_ticket_attachment_context.rb'
    - 'spec/shared/owner_payments_context.rb'
    - 'spec/shared/owner_work_order_context.rb'
    - 'spec/shared/payment_methods_context.rb'
    - 'spec/shared/tenant_ledger_importing_context.rb'
    - 'spec/system/accounting/bank_reconciliations_spec.rb'
    - 'spec/system/accounting/budget_spec.rb'
    - 'spec/system/accounting/invoices_spec.rb'
    - 'spec/system/accounting/journals/entries_spec.rb'
    - 'spec/system/accounting/mark_paid_spec.rb'
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/accounting/payables/invoice_editing_spec.rb'
    - 'spec/system/accounting/payables/payments_spec.rb'
    - 'spec/system/accounting/payments/refund_payment_spec.rb'
    - 'spec/system/accounting/payments/void_payment_spec.rb'
    - 'spec/system/accounting/receivables/income_spec.rb'
    - 'spec/system/accounting/statements/work_performed_spec.rb'
    - 'spec/system/apply_plugin/unit_selection_spec.rb'
    - 'spec/system/electronic_signatures/signature_history_spec.rb'
    - 'spec/system/inspections/new_inspection_template_spec.rb'
    - 'spec/system/lease_applications/application_filling_context.rb'
    - 'spec/system/lease_applications/hosted_application_spec.rb'
    - 'spec/system/lease_applications/lease_application_invites_spec.rb'
    - 'spec/system/leasing/application_spec.rb'
    - 'spec/system/leasing/electronic_signatures_spec.rb'
    - 'spec/system/leasing/external_guest_cards_spec.rb'
    - 'spec/system/leasing/guest_cards_spec.rb'
    - 'spec/system/leasing/tours_spec.rb'
    - 'spec/system/leasing/waitlists/pipeline_spec.rb'
    - 'spec/system/listings/listing_spec.rb'
    - 'spec/system/maintenance/estimates/create_project_spec.rb'
    - 'spec/system/maintenance/estimates/inspection_estimate_spec.rb'
    - 'spec/system/maintenance/tickets/attach_receipt_spec.rb'
    - 'spec/system/maintenance/tickets/close_spec.rb'
    - 'spec/system/maintenance/tickets/index_spec.rb'
    - 'spec/system/maintenance/tickets/vendor_assignment_spec.rb'
    - 'spec/system/management/companies_spec.rb'
    - 'spec/system/management/onboardings/assigning_tenants_specs.rb'
    - 'spec/system/marketing/listings_spec.rb'
    - 'spec/system/notifications/unread_message_notifications.rb'
    - 'spec/system/onboarding/backoffice_setup_spec.rb'
    - 'spec/system/onboarding/customer_setup_spec.rb'
    - 'spec/system/organization/bank_accounts/ledger_spec.rb'
    - 'spec/system/organization/bank_accounts_spec.rb'
    - 'spec/system/organization/charts_of_accounts_spec.rb'
    - 'spec/system/organization/companies_spec.rb'
    - 'spec/system/organization/configurations_spec.rb'
    - 'spec/system/organization/forms/publish_form_spec.rb'
    - 'spec/system/organization/roles_spec.rb'
    - 'spec/system/owners/owner_accounting_activity_context.rb'
    - 'spec/system/owners/payment_methods_spec.rb'
    - 'spec/system/portfolio/owners_spec.rb'
    - 'spec/system/portfolio/parking/commercial_allocations_spec.rb'
    - 'spec/system/portfolio/parking/lots_spec.rb'
    - 'spec/system/portfolio/parking/reservation_form_context.rb'
    - 'spec/system/portfolio/portfolios_spec.rb'
    - 'spec/system/portfolio/properties_spec.rb'
    - 'spec/system/portfolio/property_setup/buildings_spec.rb'
    - 'spec/system/portfolio/setup/cards_spec.rb'
    - 'spec/system/portfolio/setup/configuration_spec.rb'
    - 'spec/system/portfolio/setup/entities_spec.rb'
    - 'spec/system/portfolio/tenants/scheduled_payments_spec.rb'
    - 'spec/system/portfolio/tenants/timeline/events_spec.rb'
    - 'spec/system/portfolio/tenants_spec.rb'
    - 'spec/system/portfolio/vendor_invites_spec.rb'
    - 'spec/system/portfolio/vendors_spec.rb'
    - 'spec/system/projects/bid_collection_task_spec.rb'
    - 'spec/system/projects/boards_spec.rb'
    - 'spec/system/projects/phases_spec.rb'
    - 'spec/system/projects/projects_spec.rb'
    - 'spec/system/projects/tasks_spec.rb'
    - 'spec/system/reporting/commercial_parking_spec.rb'
    - 'spec/system/reporting/security_deposit_spec.rb'
    - 'spec/system/shared/bid_requests.rb'
    - 'spec/system/shared/commercial_documents.rb'
    - 'spec/system/shared/invoice_filling_context.rb'
    - 'spec/system/taxes/ten_ninety_nines/nelco_registration_spec.rb'
    - 'spec/system/telephony/phone_numbers_spec.rb'
    - 'spec/system/tenants/dashboard_spec.rb'
    - 'spec/system/tenants/inspections_spec.rb'
    - 'spec/system/tenants/invoices_spec.rb'
    - 'spec/system/tenants/payment_methods_spec.rb'
    - 'spec/system/tenants/payments_spec.rb'
    - 'spec/system/tenants/scheduled_payments_spec.rb'
    - 'spec/system/tenants/utility_transfer_spec.rb'
    - 'spec/system/vendor/assignment_pages_spec.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: SkipBlocks, EnforcedStyle, OnlyStaticConstants.
# SupportedStyles: described_class, explicit
RSpec/DescribedClass:
  Exclude:
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/models/accounting/ledger/persisted_spec.rb'
    - 'spec/queries/tenants_query_spec.rb'
    - 'spec/services/custom_forms/update_form_metadata_spec.rb'
    - 'spec/services/payment_processing/add_nsf_fee_spec.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AutoCorrect.
RSpec/EmptyExampleGroup:
  Exclude:
    - 'spec/importers/v3/bill_detail_spec.rb'
    - 'spec/services/assign_property_membership_spec.rb'
    - 'spec/system/tenants/dashboard_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowConsecutiveOneLiners.
RSpec/EmptyLineAfterExample:
  Exclude:
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowConsecutiveOneLiners.
RSpec/EmptyLineAfterHook:
  Exclude:
    - 'spec/factories/member_onboarding/charge.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
RSpec/EmptyLineAfterSubject:
  Exclude:
    - 'spec/models/listing_spec.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
RSpec/ExpectActual:
  Exclude:
    - 'spec/system/organization/forms/index_spec.rb'

# Offense count: 6
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: method_call, block
RSpec/ExpectChange:
  Exclude:
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/services/contact/timeline_entry/create_spec.rb'
    - 'spec/system/organization/configurations/payment_plans_spec.rb'

# Offense count: 25
RSpec/ExpectInHook:
  Exclude:
    - 'spec/lib/accounting/trust_accounting/security_deposits_spec.rb'
    - 'spec/lib/reports/v3/shared/management_portfolio_activity_context.rb'
    - 'spec/mailers/copy_guardians_spec.rb'
    - 'spec/system/accounting/payments/unapply_payment_spec.rb'
    - 'spec/system/leasing/waitlists/pipeline_spec.rb'
    - 'spec/system/listings/contact_spec.rb'
    - 'spec/system/maintenance/tickets/close_spec.rb'
    - 'spec/system/management/month_end_review_spec.rb'
    - 'spec/system/portfolio/properties_spec.rb'

# Offense count: 1
RSpec/ExpectInLet:
  Exclude:
    - 'spec/lib/accounting/cash_basis_migration/invoice_voids_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/HooksBeforeExamples:
  Exclude:
    - 'spec/importers/v3/inspections_spec.rb'

# Offense count: 28
# This cop supports safe autocorrection (--autocorrect).
RSpec/IncludeExamples:
  Exclude:
    - 'spec/lib/reports/v3/income_statement_spec.rb'
    - 'spec/lib/reports/v3/period_change_balance_sheet_spec.rb'
    - 'spec/lib/reports/v3/period_change_spec.rb'
    - 'spec/mailers/users/login_fingerprints_mailer_spec.rb'
    - 'spec/models/agreements/simple_agreement_spec.rb'
    - 'spec/models/bank_account/funds_transfer_spec.rb'
    - 'spec/models/charge_schedule/entry_spec.rb'
    - 'spec/models/lease_spec.rb'
    - 'spec/models/lending/loan_spec.rb'
    - 'spec/models/management_contract/membership_spec.rb'
    - 'spec/models/management_contract_spec.rb'
    - 'spec/models/owner_spec.rb'
    - 'spec/models/property_manager_spec.rb'
    - 'spec/models/tenant_spec.rb'
    - 'spec/services/collections/evictions/prohibit_payments_spec.rb'
    - 'spec/services/evictions/generate_charges_spec.rb'
    - 'spec/services/taxes/file_spec.rb'
    - 'spec/system/accounting/invoices/recurring_schedules_spec.rb'
    - 'spec/system/accounting/journals/entries/recurring_journal_entries_spec.rb'
    - 'spec/system/listings/listings_spec.rb'

# Offense count: 10
# Configuration parameters: Max, AllowedIdentifiers, AllowedPatterns.
RSpec/IndexedLet:
  Exclude:
    - 'spec/lib/extensions/plutus_amounts_extension_balance_spec.rb'
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/management/onboardings/assignments/index_spec.rb'
    - 'spec/system/owners/owner_accounting_activity_context.rb'

# Offense count: 27
# Configuration parameters: AssignmentOnly.
RSpec/InstanceVariable:
  Exclude:
    - 'spec/importers/tenant_ledger_importer_spec.rb'
    - 'spec/jobs/maintenance/create_scheduled_tickets_job_spec.rb'
    - 'spec/queries/saferent/screenings_query_spec.rb'
    - 'spec/services/lease/create_spec.rb'
    - 'spec/shared/tenant_ledger_importing_context.rb'
    - 'spec/system/owners/owner_accounting_activity_context.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
RSpec/LeadingSubject:
  Exclude:
    - 'spec/models/listing_spec.rb'
    - 'spec/services/contact/timeline_entry/create_spec.rb'

# Offense count: 233
RSpec/LetSetup:
  Exclude:
    - 'spec/controllers/accounting/budgets_controller_spec.rb'
    - 'spec/controllers/accounting/entries_controller_spec.rb'
    - 'spec/controllers/accounting/payables/batches_controller_spec.rb'
    - 'spec/controllers/accounting/payables/payments_controller_spec.rb'
    - 'spec/controllers/api/v1/employees_controller_spec.rb'
    - 'spec/controllers/api/v1/inspection_records_controller_spec.rb'
    - 'spec/controllers/api/v1/inspection_service_areas_controller_spec.rb'
    - 'spec/controllers/api/v1/notifications_controller_spec.rb'
    - 'spec/controllers/api/v1/properties_controller_spec.rb'
    - 'spec/controllers/api/v1/sku_lists_controller_spec.rb'
    - 'spec/controllers/api/v1/vendors_controller_spec.rb'
    - 'spec/controllers/api/zapier/contacts_controller_spec.rb'
    - 'spec/controllers/customer_setup/bank_accounts_controller_spec.rb'
    - 'spec/controllers/customer_setup/charts_of_accounts_controller_spec.rb'
    - 'spec/controllers/customer_setup/employees_controller_spec.rb'
    - 'spec/controllers/customer_setup/properties_controller_spec.rb'
    - 'spec/controllers/management/property_setups/buildings_controller_spec.rb'
    - 'spec/controllers/management/property_setups/floorplans_controller_spec.rb'
    - 'spec/controllers/management/property_setups/leases_controller_spec.rb'
    - 'spec/controllers/management/property_setups/units_controller_spec.rb'
    - 'spec/controllers/management/vendors/sku_lists_controller_spec.rb'
    - 'spec/controllers/organization/bank_accounts_controller_spec.rb'
    - 'spec/controllers/paylease_webhooks_controller_spec.rb'
    - 'spec/controllers/telephony/inbound_text_messages_controller_spec.rb'
    - 'spec/controllers/telephony/proxy_callbacks_controller_spec.rb'
    - 'spec/exporters/underwriting_exporter_spec.rb'
    - 'spec/importers/portfolio_list_importer_spec.rb'
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/importers/v3/maintenance_tickets_spec.rb'
    - 'spec/importers/v3/rent_roll_spec.rb'
    - 'spec/importers/v3/shared_invoice_importing.rb'
    - 'spec/jobs/accounting/debit_card_purchases/notify_reviewable_spec.rb'
    - 'spec/jobs/activity_digests/owner_emails_job_spec.rb'
    - 'spec/jobs/customer/billing/generate_invoices_job_spec.rb'
    - 'spec/jobs/notifications/daily_notifications_mail_job_spec.rb'
    - 'spec/jobs/notifications/review_owner_company_job_spec.rb'
    - 'spec/jobs/notifications/review_owner_property_job_spec.rb'
    - 'spec/jobs/saferent/fetch_job_report_spec.rb'
    - 'spec/jobs/scheduled_payment/process_payments_job_spec.rb'
    - 'spec/jobs/scheduled_payment/reminder_emails_job_spec.rb'
    - 'spec/jobs/tenant/notify_due_invoices_job_spec.rb'
    - 'spec/jobs/tenant/notify_new_invoices_job_spec.rb'
    - 'spec/lib/accounting/cash_basis/unapplied_payments_spec.rb'
    - 'spec/lib/accounting/cash_basis_spec.rb'
    - 'spec/lib/accounting/company_context_spec.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/accounting/funds_transfers/sources_spec.rb'
    - 'spec/lib/accounting/gl_code_spec.rb'
    - 'spec/lib/accounting/trust_accounting/security_deposits_spec.rb'
    - 'spec/lib/griddler/email_processor_spec.rb'
    - 'spec/lib/inspections/wizard_spec.rb'
    - 'spec/lib/rent_linx/data_linx_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/general_ledger_spec.rb'
    - 'spec/lib/reports/v3/entity_directory_spec.rb'
    - 'spec/lib/reports/v3/listings_spec.rb'
    - 'spec/lib/reports/v3/shared/management_portfolio_activity_context.rb'
    - 'spec/lib/reports/v3/unrecovered_expenses_spec.rb'
    - 'spec/lib/reports/v3/vendor_directory_spec.rb'
    - 'spec/lib/saferent/screening/xml_builder_spec.rb'
    - 'spec/lib/zeamster/transaction/refund_spec.rb'
    - 'spec/lib/zeamster/transaction/void_spec.rb'
    - 'spec/lib/zillow/zif_spec.rb'
    - 'spec/mailers/notifications_mailer_spec.rb'
    - 'spec/mailers/reports_mailer_spec.rb'
    - 'spec/mailers/vendors_mailer_spec.rb'
    - 'spec/models/accounting/ledger/persisted_spec.rb'
    - 'spec/models/bank_account_spec.rb'
    - 'spec/models/company_spec.rb'
    - 'spec/models/concerns/approvals/approvable_spec.rb'
    - 'spec/models/invoice_spec.rb'
    - 'spec/models/property_manager_spec.rb'
    - 'spec/models/user/login_fingerprint_spec.rb'
    - 'spec/queries/active_tenants_query_spec.rb'
    - 'spec/queries/approvals/query_spec.rb'
    - 'spec/queries/invoices_query_spec.rb'
    - 'spec/queries/line_items_query_spec.rb'
    - 'spec/queries/maintenance_tickets_query_spec.rb'
    - 'spec/queries/tenants_query_spec.rb'
    - 'spec/queries/units_query_spec.rb'
    - 'spec/requests/api/v2/rswag_context.rb'
    - 'spec/services/accounting/apply_credit/datewise_priority_strategy_spec.rb'
    - 'spec/services/approvals/approve_spec.rb'
    - 'spec/services/charge_schedule/bill_spec.rb'
    - 'spec/services/customer/billing_spec.rb'
    - 'spec/services/electronic_signature/sign_spec.rb'
    - 'spec/services/ledger/apply_security_deposit_spec.rb'
    - 'spec/services/ledger/create_refund_payable_spec.rb'
    - 'spec/services/maintenance_ticket/notify_spec.rb'
    - 'spec/services/management_fees/bill_entity_spec.rb'
    - 'spec/services/mercury/order/submit_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/services/payment/void_spec.rb'
    - 'spec/services/payment_processing/add_nsf_fee_spec.rb'
    - 'spec/services/payment_processing/batch_transactions_spec.rb'
    - 'spec/services/scheduled_payment/process_spec.rb'
    - 'spec/services/taxes/nelco/confirm/service_spec.rb'
    - 'spec/services/taxes/nelco/status_check/service_spec.rb'
    - 'spec/services/taxes/nelco/transmit/preparers/batch_spec.rb'
    - 'spec/services/work_order/close_spec.rb'
    - 'spec/shared/maintenance_ticket_attachment_context.rb'
    - 'spec/shared/owner_payments_context.rb'
    - 'spec/system/accounting/invoices_spec.rb'
    - 'spec/system/accounting/journals/entries_spec.rb'
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/accounting/payables/payments_spec.rb'
    - 'spec/system/accounting/payments/refund_payment_spec.rb'
    - 'spec/system/accounting/payments_spec.rb'
    - 'spec/system/accounting/receivables/income_spec.rb'
    - 'spec/system/apply_plugin/floorplan_selection_spec.rb'
    - 'spec/system/compliance/property_compliance_tab.rb'
    - 'spec/system/electronic_signatures/signature_history_spec.rb'
    - 'spec/system/employees/account_confirmations_spec.rb'
    - 'spec/system/invoice_processing/show_spec.rb'
    - 'spec/system/invoice_processing/statistics_spec.rb'
    - 'spec/system/lease_applications/checkout_spec.rb'
    - 'spec/system/lease_applications/transfer_application_spec.rb'
    - 'spec/system/leasing/electronic_signatures_spec.rb'
    - 'spec/system/leasing/leases/new_lease_spec.rb'
    - 'spec/system/leasing/move_out_spec.rb'
    - 'spec/system/leasing/notice_of_non_renewal_spec.rb'
    - 'spec/system/leasing/tours_spec.rb'
    - 'spec/system/listings/contact_spec.rb'
    - 'spec/system/management/management_fees_spec.rb'
    - 'spec/system/management/month_end_review_spec.rb'
    - 'spec/system/operations/activity_log_spec.rb'
    - 'spec/system/organization/bank_accounts_spec.rb'
    - 'spec/system/organization/employees/audits_spec.rb'
    - 'spec/system/organization/forms/index_spec.rb'
    - 'spec/system/owners/contribution_requests_spec.rb'
    - 'spec/system/owners/owner_accounting_activity_context.rb'
    - 'spec/system/owners/payment_methods_spec.rb'
    - 'spec/system/owners/payments_spec.rb'
    - 'spec/system/portfolio/portfolios_spec.rb'
    - 'spec/system/portfolio/property_setup/unit_directory_spec.rb'
    - 'spec/system/portfolio/setup/configuration_spec.rb'
    - 'spec/system/portfolio/tenants/payment_methods_spec.rb'
    - 'spec/system/portfolio/units_spec.rb'
    - 'spec/system/portfolio/vendors_spec.rb'
    - 'spec/system/reporting/account_ledger_spec.rb'
    - 'spec/system/reporting/management_fees_report_spec.rb'
    - 'spec/system/reporting/rent_roll_spec.rb'
    - 'spec/system/reporting/tenant_ledger_spec.rb'
    - 'spec/system/tenants/payment_methods_spec.rb'
    - 'spec/system/tenants/payments_spec.rb'
    - 'spec/system/tunisia/card_activities/edit_spec.rb'
    - 'spec/system/vendor/setup_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
RSpec/MatchArray:
  Exclude:
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'

# Offense count: 10
RSpec/MessageChain:
  Exclude:
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/controllers/api/zapier/contact_timeline_entries_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/services/custom_forms/process_payment_spec.rb'
    - 'spec/system/portfolio/vendor_invites_spec.rb'
    - 'spec/system/telephony/phone_numbers_spec.rb'

# Offense count: 112
# Configuration parameters: EnforcedStyle.
# SupportedStyles: have_received, receive
RSpec/MessageSpies:
  Exclude:
    - 'spec/controllers/leasing/background_checks_controller_spec.rb'
    - 'spec/controllers/sftp_to_go_controller_spec.rb'
    - 'spec/controllers/the_closing_docs/webhooks_controller_spec.rb'
    - 'spec/importers/v3/member_directory_spec.rb'
    - 'spec/jobs/accounting/post_transactions_job_spec.rb'
    - 'spec/jobs/alliance/send_fuzzy_rent_roll_job_spec.rb'
    - 'spec/jobs/collections/communications/process_job_spec.rb'
    - 'spec/jobs/collections/eviction/after_create_job_spec.rb'
    - 'spec/jobs/inspectify/order/create_job_spec.rb'
    - 'spec/jobs/lease/move_ins_job_spec.rb'
    - 'spec/jobs/lending/loan/generate_statement_job_spec.rb'
    - 'spec/jobs/notifications/review_owner_company_job_spec.rb'
    - 'spec/jobs/notifications/review_owner_property_job_spec.rb'
    - 'spec/jobs/pay_lease/refresh_transactions_job_spec.rb'
    - 'spec/jobs/payment_processing/transaction_batching_job_spec.rb'
    - 'spec/jobs/profit_stars/refresh_transactions_job_spec.rb'
    - 'spec/jobs/reams/order/fetch_canceled_job_spec.rb'
    - 'spec/jobs/reams/order/fetch_new_job_spec.rb'
    - 'spec/jobs/reports/generate_packet_job_spec.rb'
    - 'spec/jobs/reports/run_email_schedule_job_spec.rb'
    - 'spec/jobs/reports/send_email_schedule_sample_job_spec.rb'
    - 'spec/jobs/scheduled_payment/process_payments_job_spec.rb'
    - 'spec/jobs/sierra_leone/inspection/orders/completed/import_job_spec.rb'
    - 'spec/jobs/tunisia/application_form/refresh_all_job_spec.rb'
    - 'spec/lib/email/verifier_spec.rb'
    - 'spec/lib/sftp_to_go/support_spec.rb'
    - 'spec/models/custom_forms/element/email_spec.rb'
    - 'spec/models/customer_spec.rb'
    - 'spec/models/inspection/report_spec.rb'
    - 'spec/models/user/login_fingerprint_spec.rb'
    - 'spec/models/vendor/invite_spec.rb'
    - 'spec/rails_helper.rb'
    - 'spec/requests/zillow/webhooks/leads_spec.rb'
    - 'spec/services/accounting/post_transactions_spec.rb'
    - 'spec/services/alliance/send_fuzzy_rent_roll_spec.rb'
    - 'spec/services/broadcast/create_spec.rb'
    - 'spec/services/custom_forms/process_payment_spec.rb'
    - 'spec/services/electronic_signature/execute_spec.rb'
    - 'spec/services/electronic_signature/sign_spec.rb'
    - 'spec/services/lease_application/initiate_screening_spec.rb'
    - 'spec/services/payment/record/cash_spec.rb'
    - 'spec/services/reports/email_schedule/send_spec.rb'
    - 'spec/services/sftp_to_go/download_file_buffer_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/complete_spec.rb'
    - 'spec/services/tunisia/webhook/check_payment/additional_verification_required_spec.rb'
    - 'spec/services/tunisia/webhook/transaction_created_spec.rb'
    - 'spec/system/collections/assistance_rent_spec.rb'
    - 'spec/system/collections/demand_letter_spec.rb'
    - 'spec/system/maintenance/tickets/attachments_spec.rb'
    - 'spec/system/management/bulk_property_balance_transfers_spec.rb'
    - 'spec/system/organization/bank_accounts/transfer_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/resend_email_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/submit_spec.rb'
    - 'spec/system/tenants/scheduled_payments_spec.rb'
    - 'spec/system/tunisia/create_card_spec.rb'
    - 'spec/system/tunisia/open_bank_account_spec.rb'

# Offense count: 189
# Configuration parameters: AllowSubject, Max.
RSpec/MultipleMemoizedHelpers:
  Exclude:
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/lib/accounting/cash_basis/unapplied_payments_spec.rb'
    - 'spec/lib/accounting/cash_basis_spec.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/accounting/fee_management/transfer_property_balances_spec.rb'
    - 'spec/lib/accounting/funds_transfers/sources_spec.rb'
    - 'spec/lib/accounting/trust_accounting/security_deposits_spec.rb'
    - 'spec/lib/invoice_processing/assigner_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/general_ledger_spec.rb'
    - 'spec/lib/reports/v3/shared/management_portfolio_activity_context.rb'
    - 'spec/services/accounting/apply_credit_spec.rb'
    - 'spec/services/charge_schedule/bill_spec.rb'
    - 'spec/services/custom_forms/update_form_layout_spec.rb'
    - 'spec/services/lease/move_out/process_spec.rb'
    - 'spec/services/ledger/apply_security_deposit_spec.rb'
    - 'spec/services/ledger/clear_credit_spec.rb'
    - 'spec/services/ledger/create_refund_payable_spec.rb'
    - 'spec/services/management_fees/bill_entity_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/services/scheduled_payment/process_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses_spec.rb'
    - 'spec/services/taxes/nelco/create_account/service_spec.rb'
    - 'spec/system/accounting/bank_reconciliations_spec.rb'
    - 'spec/system/accounting/payables/payments_spec.rb'
    - 'spec/system/leasing/leases/itemized_damages_spec.rb'
    - 'spec/system/leasing/leases/new_lease_spec.rb'
    - 'spec/system/management/month_end_review_spec.rb'
    - 'spec/system/operations/activity_log_spec.rb'
    - 'spec/system/owners/owner_accounting_activity_context.rb'
    - 'spec/system/tenants/payments_spec.rb'

# Offense count: 31
# Configuration parameters: Max, AllowedGroups.
RSpec/NestedGroups:
  Exclude:
    - 'spec/importers/portfolio_list_importer_spec.rb'
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/importers/v3/property_directory_spec.rb'
    - 'spec/importers/v3/rent_roll_spec.rb'
    - 'spec/models/custom_forms/submission_spec.rb'
    - 'spec/models/scheduled_payment_spec.rb'
    - 'spec/services/invoice/waive_spec.rb'
    - 'spec/services/lease/move_out/process_spec.rb'
    - 'spec/services/ledger/apply_security_deposit_spec.rb'
    - 'spec/system/tenants/member_onboarding_spec.rb'

# Offense count: 31
# Configuration parameters: AllowedPatterns.
# AllowedPatterns: ^expect_, ^assert_
RSpec/NoExpectationExample:
  Exclude:
    - 'spec/importers/v3/shared.rb'
    - 'spec/jobs/accounting/cash_basis_migration/compare_payments_job_spec.rb'
    - 'spec/jobs/accounting/cash_basis_migration/summary_job_spec.rb'
    - 'spec/jobs/anomalies_job_spec.rb'
    - 'spec/jobs/api/v2/remove_old_keys_job_spec.rb'
    - 'spec/jobs/notifications/firebase_push_notification_job_spec.rb'
    - 'spec/jobs/run_background_check_job_spec.rb'
    - 'spec/lib/accounting/trust_accounting/security_deposits_spec.rb'
    - 'spec/lib/alever_seeds_spec.rb'
    - 'spec/lib/reports/general_ledger_spec.rb'
    - 'spec/lib/reports/v3/shared.rb'
    - 'spec/lib/sierra_leone/inspection/order/cancel/xlsx_spec.rb'
    - 'spec/lib/sierra_leone/inspection/order/xlsx_spec.rb'
    - 'spec/mailers/copy_guardians_spec.rb'
    - 'spec/models/custom_forms/form_spec.rb'
    - 'spec/models/plutus/entries_spec.rb'
    - 'spec/system/accounting/mark_paid_spec.rb'
    - 'spec/system/vendors/bid_requests_spec.rb'

# Offense count: 1
RSpec/OverwritingSetup:
  Exclude:
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'

# Offense count: 31
RSpec/PendingWithoutReason:
  Exclude:
    - 'spec/controllers/operations/inspections_controller_spec.rb'
    - 'spec/controllers/reports/report_controller.rb'
    - 'spec/importers/tenant_ledger_importer_spec.rb'
    - 'spec/importers/v3/jurv_importer_spec.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/profit_stars/payment/refund_spec.rb'
    - 'spec/lib/reports/v3/general_ledger_spec.rb'
    - 'spec/models/company_spec.rb'
    - 'spec/models/concerns/searchable.rb'
    - 'spec/models/customer_spec.rb'
    - 'spec/queries/units_query_spec.rb'
    - 'spec/services/customer/billing/saferent_screening_fee_spec.rb'
    - 'spec/services/lease/create_spec.rb'
    - 'spec/system/accounting/budget_spec.rb'
    - 'spec/system/accounting/journals/trial_balance_imports_spec.rb'
    - 'spec/system/accounting/payables/batches_spec.rb'
    - 'spec/system/leasing/waitlists/pipeline_spec.rb'
    - 'spec/system/maintenance/schedule_spec.rb'
    - 'spec/system/management/onboardings/assigning_tenants_specs.rb'
    - 'spec/system/management/onboardings/assignments/index_spec.rb'
    - 'spec/system/management/unit_filters_spec.rb'
    - 'spec/system/messaging/chats/direct_messages_spec.rb'
    - 'spec/system/portfolio/floorplans_spec.rb'
    - 'spec/system/portfolio/units/downtime_spec.rb'
    - 'spec/system/reporting/deposit_register_spec.rb'
    - 'spec/system/reporting/security_deposit_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/prepare_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/resend_email_spec.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Strict, EnforcedStyle, AllowedExplicitMatchers.
# SupportedStyles: inflected, explicit
RSpec/PredicateMatcher:
  Exclude:
    - 'spec/system/owners/analyses_spec.rb'

# Offense count: 13
RSpec/RepeatedDescription:
  Exclude:
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/lib/extensions/plutus_amounts_extension_balance_spec.rb'
    - 'spec/models/accounting/ledger/persisted_spec.rb'
    - 'spec/services/charge_schedule/bill/marketplace_spec.rb'
    - 'spec/system/inspections/index_spec.rb'
    - 'spec/system/users/sessions_spec.rb'

# Offense count: 7
RSpec/RepeatedExample:
  Exclude:
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/models/accounting/ledger/persisted_spec.rb'
    - 'spec/system/management/onboardings/assigning_tenants_specs.rb'

# Offense count: 4
RSpec/RepeatedExampleGroupBody:
  Exclude:
    - 'spec/controllers/apply/verification_documents_controller_spec.rb'
    - 'spec/controllers/leasing/pipelines_controller_spec.rb'

# Offense count: 2
RSpec/RepeatedExampleGroupDescription:
  Exclude:
    - 'spec/controllers/apply/verification_documents_controller_spec.rb'

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
RSpec/ScatteredSetup:
  Exclude:
    - 'spec/models/inspection/report_spec.rb'
    - 'spec/models/listing_spec.rb'

# Offense count: 23
# Configuration parameters: Include, CustomTransform, IgnoreMethods, IgnoreMetadata.
# Include: **/*_spec.rb
RSpec/SpecFilePathFormat:
  Exclude:
    - 'spec/jobs/saferent/fetch_job_report_spec.rb'
    - 'spec/jobs/taxes/refresh_1099_views_job_spec.rb'
    - 'spec/lib/accounting/basis_switch_spec.rb'
    - 'spec/lib/accounting/company_context_spec.rb'
    - 'spec/lib/accounting/property_context_spec.rb'
    - 'spec/lib/custom_forms/template/event_registration_v1_spec.rb'
    - 'spec/lib/custom_forms/template/event_registration_with_payment_v1_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/v3/owner_1099s_spec.rb'
    - 'spec/lib/reports/v3/vendor_1099s_spec.rb'
    - 'spec/models/budget_amount_spec.rb'
    - 'spec/models/invoice_processing_email_spec.rb'
    - 'spec/models/lease/computed_amount_spec.rb'
    - 'spec/models/plutus/entries_spec.rb'
    - 'spec/queries/invoices_query/by_user_spec.rb'
    - 'spec/queries/taxes/candidate_1099_query_spec.rb'
    - 'spec/services/charge_schedule/bill/marketplace_spec.rb'
    - 'spec/services/lease_application/run_safe_rent_screening_spec.rb'
    - 'spec/services/reams/order/fetch_canceled_spec.rb'
    - 'spec/services/reams/order/fetch_new_spec.rb'
    - 'spec/services/telephony/text_messages/send_spec.rb'
    - 'spec/services/tunisia/webhook/check_payment/additional_verification_required_spec.rb'
    - 'spec/services/tunisia/webhook/transaction_created_spec.rb'

# Offense count: 3
# Configuration parameters: Include.
# Include: **/*_spec*rb*, **/spec/**/*
RSpec/SpecFilePathSuffix:
  Exclude:
    - 'spec/system/compliance/property_compliance_tab.rb'
    - 'spec/system/management/onboardings/assigning_tenants_specs.rb'
    - 'spec/system/notifications/unread_message_notifications.rb'

# Offense count: 16
RSpec/StubbedMock:
  Exclude:
    - 'spec/jobs/profit_stars/refresh_transactions_job_spec.rb'
    - 'spec/jobs/reams/order/fetch_canceled_job_spec.rb'
    - 'spec/jobs/reams/order/fetch_new_job_spec.rb'
    - 'spec/jobs/tunisia/application_form/refresh_all_job_spec.rb'
    - 'spec/models/vendor/invite_spec.rb'
    - 'spec/services/tunisia/webhook/check_payment/additional_verification_required_spec.rb'
    - 'spec/services/tunisia/webhook/transaction_created_spec.rb'
    - 'spec/system/management/bulk_property_balance_transfers_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/resend_email_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/submit_spec.rb'
    - 'spec/system/users/resend_fingerprint_spec.rb'

# Offense count: 2
RSpec/SubjectDeclaration:
  Exclude:
    - 'spec/policies/portfolio_mode_policy_spec.rb'
    - 'spec/system/maintenance/tickets/new_ticket_spec.rb'

# Offense count: 2
RSpec/SubjectStub:
  Exclude:
    - 'spec/lib/reports/catalog_spec.rb'
    - 'spec/models/reams/order_spec.rb'

# Offense count: 1
RSpec/UnspecifiedException:
  Exclude:
    - 'spec/services/morocco/white_label_auth_spec.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
RSpec/VerifiedDoubleReference:
  Exclude:
    - 'spec/importers/v3/employee_directory_spec.rb'
    - 'spec/importers/v3/member_directory_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses_spec.rb'

# Offense count: 115
# Configuration parameters: IgnoreNameless, IgnoreSymbolicNames.
RSpec/VerifiedDoubles:
  Exclude:
    - 'spec/decorators/ledger_decorator_spec.rb'
    - 'spec/lib/accounting/ledger/payment_statistics_spec.rb'
    - 'spec/models/vendor/invite_spec.rb'
    - 'spec/policies/portfolio_mode_policy_spec.rb'
    - 'spec/services/sftp_to_go/helpers_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/answer_utils_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/import_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/additional_photos_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/exterior_general_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/health_and_safety_site_hazards_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/inspector_information_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/interior_general_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/property_profile_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/sump_pump_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses_spec.rb'
    - 'spec/system/taxes/ten_ninety_nines/nelco_registration_spec.rb'

# Offense count: 1
RSpec/VoidExpect:
  Exclude:
    - 'spec/jobs/invoice_processing/notify_processors_job_spec.rb'

# Offense count: 7
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Inferences.
RSpecRails/InferredSpecType:
  Exclude:
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/controllers/api/zapier/contact_timeline_entries_controller_spec.rb'
    - 'spec/controllers/api/zapier/contacts_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/models/admin_user/customer_access_spec.rb'
    - 'spec/system/action_sidebar/electronic_signable/countersigner_modal_component_spec.rb'
    - 'spec/system/operations/inspections/sidebar_spec.rb'

# Offense count: 12
# This cop supports unsafe autocorrection (--autocorrect-all).
RSpecRails/TravelAround:
  Exclude:
    - 'spec/jobs/activity_digests/owner_emails_job_spec.rb'
    - 'spec/jobs/late_fees/assess_late_fees_job_spec.rb'
    - 'spec/lib/reports/cash_basis_general_ledger_spec.rb'
    - 'spec/lib/reports/general_ledger_spec.rb'
    - 'spec/lib/reports/v3/shared.rb'
    - 'spec/requests/api/v2/rswag_context.rb'
    - 'spec/services/charge_schedule/bill/marketplace_spec.rb'
    - 'spec/services/customer/billing_spec.rb'
    - 'spec/services/lease/demand_for_possession_spec.rb'
    - 'spec/system/listings/request_tour_spec.rb'
    - 'spec/system/management/management_fees_spec.rb'
    - 'spec/system/management/month_end_review_spec.rb'

# Offense count: 11
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: NilOrEmpty, NotPresent, UnlessPresent.
Rails/Blank:
  Exclude:
    - 'app/controllers/organization/tunisia/internal_transfers_controller.rb'
    - 'app/controllers/zillow/claims_controller.rb'
    - 'app/services/member_onboarding/create_lease.rb'
    - 'app/services/taxes/nelco/transmit/preparers/phone.rb'
    - 'app/services/taxes/nelco/transmit/preparers/ten_ninety_nine.rb'
    - 'lib/breadcrumb/property.rb'
    - 'lib/importers/v3/guest_cards.rb'
    - 'lib/member_onboarding/tenants/configured_cohort.rb'
    - 'spec/factories/lease_application_memberships.rb'

# Offense count: 12
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowToTime.
# SupportedStyles: strict, flexible
Rails/Date:
  Exclude:
    - 'lib/zillow/hotpads/from_listing.rb'
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/importers/v3/beginning_balances_spec.rb'
    - 'spec/jobs/invoice_processing/notify_processors_job_spec.rb'
    - 'spec/models/invoice_spec.rb'
    - 'spec/services/sierra_leone/inspection/orders/completed/populate_responses/inspector_information_spec.rb'
    - 'spec/system/leasing/move_outs/assessment_spec.rb'
    - 'spec/system/owners/analyses_spec.rb'

# Offense count: 12
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforceForPrefixed.
Rails/Delegate:
  Exclude:
    - 'app/resources/api/v2/tour_resource.rb'
    - 'app/services/taxes/nelco/transmit/preparers/address.rb'
    - 'app/services/taxes/nelco/transmit/preparers/payee.rb'
    - 'app/services/taxes/nelco/transmit/preparers/payer_company.rb'
    - 'app/services/taxes/nelco/transmit/preparers/ten_ninety_nine_misc.rb'
    - 'app/services/taxes/nelco/transmit/preparers/ten_ninety_nine_nec.rb'
    - 'lib/active_model/cocoon_model/collection_proxy.rb'
    - 'lib/lease_application/scorecard/metric.rb'
    - 'lib/maintenance/billing/billing_panel.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/step.rb'
    - 'lib/payment_processing/convenience_fee.rb'
    - 'lib/reports/v3/report.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Severity.
Rails/DeprecatedActiveModelErrorsMethods:
  Exclude:
    - 'app/models/chart_of_accounts.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Whitelist, AllowedMethods, AllowedReceivers.
# Whitelist: find_by_sql, find_by_token_for
# AllowedMethods: find_by_sql, find_by_token_for
# AllowedReceivers: Gem::Specification, page
Rails/DynamicFindBy:
  Exclude:
    - 'app/controllers/telephony/inbound_text_messages_controller.rb'
    - 'app/jobs/telephony/handle_inbound_text_message_job.rb'
    - 'spec/queries/telephony/contacts_query_spec.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: slashes, arguments
Rails/FilePath:
  Exclude:
    - 'app/jobs/seven_day_notice_job.rb'
    - 'app/services/chart_of_accounts/generate_default.rb'
    - 'app/services/forms/schedule_e.rb'
    - 'lib/leasing/application_form_filler.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/tasks/custom_seeds.rake'
    - 'spec/rails_helper.rb'

# Offense count: 16
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods, AllowedPatterns.
# AllowedMethods: order, limit, select, lock
Rails/FindEach:
  Exclude:
    - 'app/controllers/accounting/entries_controller.rb'
    - 'app/controllers/api/v1/work_orders_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/jobs/collections/eviction/after_create_job.rb'
    - 'app/jobs/invoice_processing/notify_processors_job.rb'
    - 'app/services/account/merge.rb'
    - 'app/services/accounting/cash_basis_migration/clone_materialized_entries.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/management/month_end_review/pay_expenses.rb'
    - 'app/services/property/transfer_entity.rb'
    - 'spec/lib/reports/v3/move_in_move_out_spec.rb'

# Offense count: 34
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Exclude:
    - 'app/models/chat.rb'
    - 'app/models/company.rb'
    - 'app/models/concerns/brandable.rb'
    - 'app/models/customer.rb'
    - 'app/models/floorplan.rb'
    - 'app/models/invoice.rb'
    - 'app/models/line_item.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/owner.rb'
    - 'app/models/project.rb'
    - 'app/models/project/phase.rb'
    - 'app/models/property.rb'
    - 'app/models/property_manager.rb'
    - 'app/models/task.rb'
    - 'app/models/unit.rb'
    - 'app/models/vendor.rb'

# Offense count: 2
# Configuration parameters: Include.
# Include: **/app/helpers/**/*.rb
Rails/HelperInstanceVariable:
  Exclude:
    - 'app/helpers/tunisia_helper.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/IndexWith:
  Exclude:
    - 'lib/zillow/hotpads/identifiers.rb'

# Offense count: 11
# Configuration parameters: IgnoreScopes, Include.
# Include: **/app/models/**/*.rb
Rails/InverseOf:
  Exclude:
    - 'app/models/concerns/property/member_onboardable.rb'
    - 'app/models/floorplan.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/property_manager.rb'
    - 'app/models/unit.rb'

# Offense count: 18
# Configuration parameters: Include.
# Include: **/app/controllers/**/*.rb, **/app/mailers/**/*.rb
Rails/LexicallyScopedActionFilter:
  Exclude:
    - 'app/controllers/accounting/bank_accounts/transfers_controller.rb'
    - 'app/controllers/accounting/bank_reconciliations_controller.rb'
    - 'app/controllers/accounting/invoices_controller.rb'
    - 'app/controllers/accounting/receivables/invoices_controller.rb'
    - 'app/controllers/concerns/configurations_controller.rb'
    - 'app/controllers/concerns/payment_processing/prevent_overpayment.rb'
    - 'app/controllers/leasing/guest_cards_controller.rb'
    - 'app/controllers/leasing/leases_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/management/onboardings_controller.rb'
    - 'app/controllers/organization/employees_controller.rb'
    - 'app/controllers/owners/registrations_controller.rb'
    - 'app/controllers/property_managers/registrations_controller.rb'
    - 'app/controllers/reports_controller.rb'
    - 'app/controllers/tenants/scheduled_payments_controller.rb'
    - 'app/controllers/vendor/vendor_assignments_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/NegateInclude:
  Exclude:
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: **/app/**/*.rb, **/config/**/*.rb, db/**/*.rb, **/lib/**/*.rb
Rails/Output:
  Exclude:
    - 'spec/lib/rent_linx/data_linx_spec.rb'
    - 'spec/lib/zillow/zif_spec.rb'

# Offense count: 2
Rails/OutputSafety:
  Exclude:
    - 'app/controllers/concerns/reports/v3/requests_context.rb'
    - 'app/controllers/organization/tunisia/bank_accounts/statements_controller.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/Pluck:
  Exclude:
    - 'lib/fcm_error.rb'
    - 'lib/pay_lease/account/create.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: conservative, aggressive
Rails/PluckInWhere:
  Exclude:
    - 'app/queries/plutus/accounts_query.rb'
    - 'lib/reports/v3/receivables_summary.rb'

# Offense count: 34
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedReceivers.
# AllowedReceivers: ActionMailer::Preview, ActiveSupport::TimeZone
Rails/RedundantActiveRecordAllMethod:
  Exclude:
    - 'app/controllers/accounting/invoice_targets_controller.rb'
    - 'app/controllers/accounting/statements_controller.rb'
    - 'app/controllers/api/v1/inspection_service_areas_controller.rb'
    - 'app/controllers/api/v1/inspection_templates_controller.rb'
    - 'app/controllers/customer_setup/administrators_controller.rb'
    - 'app/controllers/customer_setup/employees_controller.rb'
    - 'app/controllers/invoice_processing/emails_controller.rb'
    - 'app/controllers/operations/collections/history_controller.rb'
    - 'app/controllers/reports/packet_templates_controller.rb'
    - 'app/jobs/notifications/portal_message_received_job.rb'
    - 'app/jobs/unit/update_statuses_job.rb'
    - 'app/queries/charts_of_accounts_query.rb'
    - 'app/queries/chats_query.rb'
    - 'app/queries/parking_lots_query.rb'
    - 'app/queries/plutus/accounts_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/services/customer/populate_instance.rb'
    - 'app/services/customer/register.rb'
    - 'app/services/member_onboarding/membership_agreement/fill_document.rb'
    - 'lib/action_index/invoice_processing_emails.rb'
    - 'lib/activity_digests/digest.rb'
    - 'lib/reports/v3/basis/agreement.rb'
    - 'lib/reports/v3/basis/audit_log.rb'
    - 'lib/reports/v3/basis/security_deposit.rb'
    - 'lib/reports/v3/collections.rb'
    - 'lib/reports/v3/management_fees.rb'
    - 'spec/jobs/maintenance/create_scheduled_tickets_job_spec.rb'
    - 'spec/system/invoice_processing/show_spec.rb'
    - 'spec/system/organization/configurations_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/RedundantForeignKey:
  Exclude:
    - 'app/models/syndication/property_supplement.rb'

# Offense count: 140
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/RedundantPresenceValidationOnBelongsTo:
  Exclude:
    - 'app/models/accounting/statement.rb'
    - 'app/models/approvals/request.rb'
    - 'app/models/approvals/response.rb'
    - 'app/models/assignment.rb'
    - 'app/models/background_check.rb'
    - 'app/models/bank_account.rb'
    - 'app/models/billing.rb'
    - 'app/models/brand.rb'
    - 'app/models/broadcast.rb'
    - 'app/models/broadcasts_recipient.rb'
    - 'app/models/budget_amount.rb'
    - 'app/models/budget_amount/monthly.rb'
    - 'app/models/budget_amount/project.rb'
    - 'app/models/building.rb'
    - 'app/models/calendar_event.rb'
    - 'app/models/charge_preset.rb'
    - 'app/models/charge_schedule.rb'
    - 'app/models/charge_schedule/entry.rb'
    - 'app/models/charge_schedule/entry/allocation.rb'
    - 'app/models/chat_membership.rb'
    - 'app/models/configuration.rb'
    - 'app/models/contact/timeline_entry.rb'
    - 'app/models/credit_card.rb'
    - 'app/models/credit_preset.rb'
    - 'app/models/data_import.rb'
    - 'app/models/deposit_batch.rb'
    - 'app/models/floorplan.rb'
    - 'app/models/guest_card.rb'
    - 'app/models/hap_contract.rb'
    - 'app/models/inspection/activity.rb'
    - 'app/models/inspection/punch_list_entry.rb'
    - 'app/models/inspection/question.rb'
    - 'app/models/inspection/record.rb'
    - 'app/models/inspection/report.rb'
    - 'app/models/inspection/response.rb'
    - 'app/models/invoice.rb'
    - 'app/models/invoice_payment.rb'
    - 'app/models/invoice_processing/email.rb'
    - 'app/models/itemized_damages.rb'
    - 'app/models/labor_item.rb'
    - 'app/models/lease.rb'
    - 'app/models/lease/move_out.rb'
    - 'app/models/lease/move_out/custom_damage.rb'
    - 'app/models/lease/move_out/membership.rb'
    - 'app/models/lease/notice_of_non_renewal.rb'
    - 'app/models/lease/term.rb'
    - 'app/models/lease_application.rb'
    - 'app/models/lease_application/contact.rb'
    - 'app/models/lease_application/credit_reference.rb'
    - 'app/models/lease_application/income_source.rb'
    - 'app/models/lease_application_membership.rb'
    - 'app/models/lease_membership.rb'
    - 'app/models/line_item.rb'
    - 'app/models/line_item/markup.rb'
    - 'app/models/listing.rb'
    - 'app/models/maintenance/estimate.rb'
    - 'app/models/maintenance/estimate/area.rb'
    - 'app/models/maintenance/estimate/section.rb'
    - 'app/models/maintenance/estimate/task.rb'
    - 'app/models/maintenance/work_performed_statement/item.rb'
    - 'app/models/maintenance_ticket.rb'
    - 'app/models/maintenance_ticket/event.rb'
    - 'app/models/merchant_account/contact.rb'
    - 'app/models/message.rb'
    - 'app/models/metadata.rb'
    - 'app/models/mobile_device.rb'
    - 'app/models/notification.rb'
    - 'app/models/notification/preferences.rb'
    - 'app/models/owner/invite.rb'
    - 'app/models/ownership.rb'
    - 'app/models/parking/allocation.rb'
    - 'app/models/parking_reservation.rb'
    - 'app/models/parking_space.rb'
    - 'app/models/pay_lease_transaction.rb'
    - 'app/models/payment.rb'
    - 'app/models/portfolios_bank_account.rb'
    - 'app/models/profit_stars_transaction.rb'
    - 'app/models/project.rb'
    - 'app/models/project/bid.rb'
    - 'app/models/project/board/column.rb'
    - 'app/models/project/dependency.rb'
    - 'app/models/project/phase.rb'
    - 'app/models/project_membership.rb'
    - 'app/models/property/parcel.rb'
    - 'app/models/property_membership.rb'
    - 'app/models/reports/email_schedule.rb'
    - 'app/models/sku_list/item.rb'
    - 'app/models/tagging.rb'
    - 'app/models/task.rb'
    - 'app/models/taxpayer_identification.rb'
    - 'app/models/telephony/twilio_proxy_session.rb'
    - 'app/models/todo.rb'
    - 'app/models/unit.rb'
    - 'app/models/unit/downtime.rb'
    - 'app/models/utilities/transfer.rb'
    - 'app/models/vendor/invite.rb'
    - 'app/models/waitlist_entry.rb'
    - 'app/models/zeamster_transaction.rb'

# Offense count: 34
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: spec/controllers/**/*.rb, spec/requests/**/*.rb, test/controllers/**/*.rb, test/integration/**/*.rb
Rails/ResponseParsedBody:
  Exclude:
    - 'spec/controllers/api/v1/sessions_controller_spec.rb'
    - 'spec/controllers/api/v1/time_tracking_controller_spec.rb'
    - 'spec/controllers/api/zapier/base_controller_spec.rb'
    - 'spec/controllers/api/zapier/contact_timeline_entries_controller_spec.rb'
    - 'spec/controllers/api/zapier/contacts_controller_spec.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/controllers/leasing/leases_controller_spec.rb'
    - 'spec/controllers/operations/pulse_controller_spec.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/RootPathnameMethods:
  Exclude:
    - 'app/jobs/data_import_job.rb'
    - 'lib/lending/loan/statement/entry.rb'
    - 'lib/tasks/custom_seeds.rake'

# Offense count: 61
# Configuration parameters: ForbiddenMethods, AllowedMethods.
# ForbiddenMethods: decrement!, decrement_counter, increment!, increment_counter, insert, insert!, insert_all, insert_all!, toggle!, touch, touch_all, update_all, update_attribute, update_column, update_columns, update_counters, upsert, upsert_all
Rails/SkipsModelValidations:
  Exclude:
    - 'app/components/tenants/dashboard/insurance_banner_component.rb'
    - 'app/controllers/accounting/payments/applies_controller.rb'
    - 'app/controllers/concerns/notifications/notifications_controller.rb'
    - 'app/controllers/concerns/operations/collections/notice_sent_controller.rb'
    - 'app/controllers/management/onboardings/assignments_controller.rb'
    - 'app/controllers/operations/collections/demand_letter_controller.rb'
    - 'app/controllers/organization/tunisia/card_activities_controller.rb'
    - 'app/controllers/organization/tunisia/user_tokens_controller.rb'
    - 'app/controllers/tenants/assurant_controller.rb'
    - 'app/jobs/clear_notifications_job.rb'
    - 'app/jobs/invoice_processing/notify_processors_job.rb'
    - 'app/jobs/payment/rejournal_with_reconciliations_job.rb'
    - 'app/models/api/v2/key.rb'
    - 'app/models/chart_of_accounts.rb'
    - 'app/models/concerns/payment/reversable.rb'
    - 'app/models/zillow/claims_property.rb'
    - 'app/services/payment/unapply.rb'
    - 'app/services/tenant/merge.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'spec/controllers/custom_forms/submissions_controller_spec.rb'
    - 'spec/jobs/counter_culture/update_counts_job_spec.rb'
    - 'spec/lib/accounting/trust_accounting/security_deposits_spec.rb'
    - 'spec/lib/reports/v3/consolidated_property_income_statement_spec.rb'
    - 'spec/lib/reports/v3/damage_waivers_spec.rb'
    - 'spec/lib/reports/v3/evictions_spec.rb'
    - 'spec/lib/reports/v3/guardian_directory_spec.rb'
    - 'spec/lib/reports/v3/investor_income_statement_spec.rb'
    - 'spec/lib/reports/v3/lease_directory_spec.rb'
    - 'spec/lib/reports/v3/move_in_move_out_spec.rb'
    - 'spec/lib/reports/v3/owner_1099s_spec.rb'
    - 'spec/lib/reports/v3/reservations_spec.rb'
    - 'spec/lib/reports/v3/tenant_balances_spec.rb'
    - 'spec/lib/reports/v3/tenant_directory_spec.rb'
    - 'spec/lib/reports/v3/tours_spec.rb'
    - 'spec/lib/reports/v3/vendor_1099s_spec.rb'
    - 'spec/mailers/unread_messages_mailer_spec.rb'
    - 'spec/models/lease_membership/aging_delinquency_spec.rb'
    - 'spec/system/accounting/journals/amounts/index_spec.rb'
    - 'spec/system/accounting/journals/entries/index_spec.rb'
    - 'spec/system/contacts/mockups_spec.rb'
    - 'spec/system/messaging/email/receipts_spec.rb'
    - 'spec/system/notifications/unread_message_notifications.rb'
    - 'spec/system/organization/forms/preview_spec.rb'
    - 'spec/system/tunisia/statements_spec.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/SquishedSQLHeredocs:
  Exclude:
    - 'app/queries/documents_query.rb'
    - 'lib/accounting/account_balance_matrix/customer_portfolio_matrix.rb'
    - 'lib/cards/invoice_card.rb'

# Offense count: 16
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: strict, flexible
Rails/TimeZone:
  Exclude:
    - 'scripts/autoscale.rb'
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 8
# Configuration parameters: TransactionMethods.
Rails/TransactionExitStatement:
  Exclude:
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/trial_balance_importer.rb'
    - 'app/jobs/late_fees/assess_late_fees_job.rb'
    - 'app/services/approvals/approve.rb'
    - 'app/services/invoice_processing/email/parse.rb'

# Offense count: 4
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/UniqueValidationWithoutIndex:
  Exclude:
    - 'app/models/customer.rb'
    - 'app/models/messaging/contact_group.rb'
    - 'app/models/owner/invite.rb'
    - 'app/models/project/phase.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/WhereEquals:
  Exclude:
    - 'app/models/management_contract/membership.rb'
    - 'app/queries/member_onboarding/assignments_query.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/WhereNot:
  Exclude:
    - 'app/queries/member_onboarding/assignments_query.rb'

# Offense count: 2
# Configuration parameters: Severity.
Rails/WhereNotWithMultipleConditions:
  Exclude:
    - 'app/controllers/messaging/sms_controller.rb'
    - 'app/models/landlord_verification.rb'

# Offense count: 57
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/WhereRange:
  Exclude:
    - 'app/controllers/accounting/journals/clearings_controller.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/jobs/notifications/daily_notifications_mail_job.rb'
    - 'app/models/bank_account/reconciliation.rb'
    - 'app/models/deposit_batch.rb'
    - 'app/models/electronic_signature.rb'
    - 'app/models/maintenance_ticket/event.rb'
    - 'app/models/user/login_fingerprint.rb'
    - 'app/models/waitlist_entry.rb'
    - 'app/queries/collections_query.rb'
    - 'app/queries/invoices_query.rb'
    - 'app/queries/lease_memberships_query.rb'
    - 'app/queries/leases_query.rb'
    - 'app/queries/listings_query.rb'
    - 'app/queries/maintenance_ticket/attachments_query.rb'
    - 'app/queries/payments_query.rb'
    - 'app/queries/plutus/accounts_query.rb'
    - 'app/queries/plutus/entries_query.rb'
    - 'app/serializers/bookings_table_serializer.rb'
    - 'app/services/accounting/create_retained_earnings.rb'
    - 'app/services/lease/generate_document.rb'
    - 'lib/accounting/journal/health_snapshot.rb'
    - 'lib/accounting/ledger.rb'
    - 'lib/cards/expenses_overview.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/physical_occupancy.rb'
    - 'lib/reports/v3/basis/aging_payables.rb'
    - 'lib/reports/v3/basis/aging_receivables.rb'
    - 'lib/reports/v3/basis/audit_log.rb'
    - 'lib/reports/v3/basis/inspection.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/security_deposit.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/consolidated_report/entities_column.rb'
    - 'lib/reports/v3/consolidated_report/property_column.rb'
    - 'lib/reports/v3/receivables_summary.rb'
    - 'lib/university/semester.rb'

# Offense count: 2
Security/MarshalLoad:
  Exclude:
    - 'app/models/lending/loan/serialized_entries.rb'
    - 'app/models/reports/email_schedule.rb'

# Offense count: 3
Security/Open:
  Exclude:
    - 'app/jobs/twilio/parse_voicebase_transcription_job.rb'
    - 'lib/tasks/statistics.rake'
    - 'scripts/docker/install_chromedriver.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowOnlyRestArgument, UseAnonymousForwarding, RedundantRestArgumentNames, RedundantKeywordRestArgumentNames, RedundantBlockArgumentNames.
# RedundantRestArgumentNames: args, arguments
# RedundantKeywordRestArgumentNames: kwargs, options, opts
# RedundantBlockArgumentNames: blk, block, proc
Style/ArgumentsForwarding:
  Exclude:
    - 'lib/accounting/fee_management/month_end_review/summary_table.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/ArrayIntersect:
  Exclude:
    - 'app/jobs/owner_contribution/process_job.rb'
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ProceduralMethods, FunctionalMethods, AllowedMethods, AllowedPatterns, AllowBracesOnProceduralOneLiners, BracesRequiredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining, always_braces
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# AllowedMethods: lambda, proc, it
Style/BlockDelimiters:
  Exclude:
    - 'app/decorators/maintenance/bid_request/invite_decorator.rb'
    - 'spec/controllers/api/zapier/guest_cards_controller_spec.rb'
    - 'spec/lib/reports/v3/budget_variance_spec.rb'
    - 'spec/lib/reports/v3/profit_and_loss_spec.rb'
    - 'spec/models/lease/move_out_spec.rb'
    - 'spec/services/ledger/clear_credit_spec.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: MinBranchesCount.
Style/CaseLikeIf:
  Exclude:
    - 'app/components/inspections/sidebar/assignee_component.rb'
    - 'app/services/ledger/clear_credit.rb'
    - 'lib/greek_housing/cohort.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, EnforcedStyleForClasses, EnforcedStyleForModules.
# SupportedStyles: nested, compact
# SupportedStylesForClasses: ~, nested, compact
# SupportedStylesForModules: ~, nested, compact
Style/ClassAndModuleChildren:
  Exclude:
    - 'lib/active_admin/add_audits_panel_to_default_main_content.rb'
    - 'lib/collections/communications.rb'

# Offense count: 1
Style/ClassVars:
  Exclude:
    - 'app/models/concerns/attachments/token_redeemable.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CombinableLoops:
  Exclude:
    - 'scripts/heroku/full_database_restore.rb'
    - 'spec/lib/invoice_processing/assigner_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, SingleLineConditionsOnly, IncludeTernaryExpressions.
# SupportedStyles: assign_to_condition, assign_inside_condition
Style/ConditionalAssignment:
  Exclude:
    - 'lib/reports/v3/loan_summary.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: allowed_in_returns, forbidden
Style/DoubleNegation:
  Exclude:
    - 'app/models/custom_forms/form_field.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, EnforcedStyle, AllowComments.
# SupportedStyles: empty, nil, both
Style/EmptyElse:
  Exclude:
    - 'lib/importers/v3/unpaid_invoices.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/FileEmpty:
  Exclude:
    - 'lib/document_filling/document_cache.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/GlobalStdStream:
  Exclude:
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 21
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinBodyLength, AllowConsecutiveConditionals.
Style/GuardClause:
  Exclude:
    - 'app/controllers/api/zapier/contact_timeline_entries_controller.rb'
    - 'app/controllers/api/zapier/contacts_controller.rb'
    - 'app/controllers/custom_forms/submissions_controller.rb'
    - 'app/controllers/management/bulk_property_balance_transfers_controller.rb'
    - 'app/controllers/management/portfolios_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/controllers/vendor/vendor_assignments_controller.rb'
    - 'app/mailers/electronic_payments_mailer.rb'
    - 'app/mailers/maintenance/work_orders/appointments_mailer.rb'
    - 'app/mailers/maintenance/work_orders/owner_approvals_mailer.rb'
    - 'app/models/line_item.rb'
    - 'app/services/lease/move_out/process.rb'
    - 'app/services/payment/record/cash/base.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/payment_processing/cart.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedReceivers.
# AllowedReceivers: Thread.current
Style/HashEachMethods:
  Exclude:
    - 'app/admin/cash_basis_migration.rb'
    - 'app/controllers/concerns/configurations_controller.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/importers/v3/general_ledger.rb'
    - 'scripts/swagger_dbml.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/IdenticalConditionalBranches:
  Exclude:
    - 'app/services/payment/record/cash/post_trust_entry.rb'
    - 'lib/greek_housing/cohort.rb'

# Offense count: 212
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Exclude:
    - 'Dangerfile'
    - 'app/admin/customer.rb'
    - 'app/admin/customer_pricing_plans.rb'
    - 'app/admin/payment.rb'
    - 'app/admin/property_manager.rb'
    - 'app/components/accounting/invoices/pending_approval_banner_component.rb'
    - 'app/controllers/accounting/bank_accounts/transfers_controller.rb'
    - 'app/controllers/accounting/entries_controller.rb'
    - 'app/controllers/accounting/payables/invoices_controller.rb'
    - 'app/controllers/api/v2/base_controller.rb'
    - 'app/controllers/concerns/contact_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/has_portal_masquerading.rb'
    - 'app/controllers/concerns/initial_confirmation_fingerprint_controller.rb'
    - 'app/controllers/concerns/portal_maintenance_tickets_controller.rb'
    - 'app/controllers/concerns/reports/v3/filter_context.rb'
    - 'app/controllers/concerns/scheduled_payments_controller.rb'
    - 'app/controllers/custom_forms/submissions_controller.rb'
    - 'app/controllers/customer_setup/client_entities_controller.rb'
    - 'app/controllers/guest_cards_controller.rb'
    - 'app/controllers/leasing/agreements/bulk_preset_controller.rb'
    - 'app/controllers/maintenance/tickets/messages_controller.rb'
    - 'app/controllers/management/companies_controller.rb'
    - 'app/controllers/messaging/broadcasts_controller.rb'
    - 'app/controllers/operations/project_boards_controller.rb'
    - 'app/controllers/operations/pulse_controller.rb'
    - 'app/controllers/organization/companies_controller.rb'
    - 'app/controllers/organization/employees_controller.rb'
    - 'app/controllers/owners/companies_controller.rb'
    - 'app/controllers/owners/confirmations_controller.rb'
    - 'app/controllers/owners/documents_controller.rb'
    - 'app/controllers/owners/reports_controller.rb'
    - 'app/controllers/property_managers/confirmations_controller.rb'
    - 'app/controllers/reports/rent_schedule_controller.rb'
    - 'app/controllers/taxes/batch_groups_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/confirmations_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/vendor/vendor_assignments_controller.rb'
    - 'app/decorators/lease_application/applicant_decorator.rb'
    - 'app/exporters/chart_of_accounts_exporter.rb'
    - 'app/forms/user/login_fingerprint/confirm_form.rb'
    - 'app/importers/chart_of_accounts_importer.rb'
    - 'app/importers/general_ledger_importer.rb'
    - 'app/importers/rent_roll_importer.rb'
    - 'app/jobs/invoice_processing/notify_processors_job.rb'
    - 'app/jobs/maintenance_ticket/deferred/expire_job.rb'
    - 'app/jobs/telephony/handle_proxy_callback_job.rb'
    - 'app/jobs/zeamster/refresh_merchant_account_job.rb'
    - 'app/jobs/zeamster/refresh_page_job.rb'
    - 'app/mailers/vendors_mailer.rb'
    - 'app/models/application_record.rb'
    - 'app/models/charge_schedule.rb'
    - 'app/models/concerns/invoice/has_attachments.rb'
    - 'app/models/concerns/invoice_payment/locked_periods.rb'
    - 'app/models/lease_application/applicant.rb'
    - 'app/models/line_item.rb'
    - 'app/models/occupancy_count.rb'
    - 'app/models/property/analysis.rb'
    - 'app/models/reports/packet_template/entry.rb'
    - 'app/models/taxes/batch_group.rb'
    - 'app/queries/plutus/amounts_query.rb'
    - 'app/queries/plutus/entries_query.rb'
    - 'app/queries/tenants_query.rb'
    - 'app/services/approvals/approve.rb'
    - 'app/services/approvals/bulk_approve.rb'
    - 'app/services/charge_schedule/bill.rb'
    - 'app/services/collections/communications/send_mail.rb'
    - 'app/services/electronic_signature/sign.rb'
    - 'app/services/inspection/prepare.rb'
    - 'app/services/invoice/record/receivable.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease/demand_for_possession.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/lease/notice_to_quit.rb'
    - 'app/services/lease_application/initiate_screening.rb'
    - 'app/services/maintenance/billing/create_invoice.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/member_onboarding/create_tenant.rb'
    - 'app/services/owner/persist.rb'
    - 'app/services/payment/record/accrual/receivable.rb'
    - 'app/services/payment/record/cash/base.rb'
    - 'app/services/payment/record/cash/prepayment_entry.rb'
    - 'app/services/taxes/file.rb'
    - 'app/services/taxes/nelco/create_account/response.rb'
    - 'app/services/work_order/resolve.rb'
    - 'lib/accounting/cash_activity.rb'
    - 'lib/action_index/accounting/target.rb'
    - 'lib/action_table/base.rb'
    - 'lib/actions_menu/lead.rb'
    - 'lib/actions_menu/maintenance/estimate.rb'
    - 'lib/actions_menu/managed_entity.rb'
    - 'lib/actions_menu/tenant.rb'
    - 'lib/actions_menu/tenant/ledger.rb'
    - 'lib/activity_log/description.rb'
    - 'lib/anomalies/create_xlsx_file.rb'
    - 'lib/audited/custom_audit.rb'
    - 'lib/cards/invoice_card.rb'
    - 'lib/cards/physical_occupancy.rb'
    - 'lib/cron/every_hour.rb'
    - 'lib/exportable/xlsx.rb'
    - 'lib/extensions/plutus_amounts_extension_balance.rb'
    - 'lib/extensions/plutus_basis.rb'
    - 'lib/importers/v3/general_ledger/cache.rb'
    - 'lib/importers/v3/jurv_importer.rb'
    - 'lib/lending/amortization_schedule.rb'
    - 'lib/member_onboarding/tenants/wizard/steps/guarantor_profile.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'lib/navigation/employees/accounting.rb'
    - 'lib/reports/general_ledger/exportable.rb'
    - 'lib/reports/request.rb'
    - 'lib/reports/v3/basis/activity_log_entry.rb'
    - 'lib/reports/v3/basis/agreement.rb'
    - 'lib/reports/v3/basis/collections_communication.rb'
    - 'lib/reports/v3/basis/collections_opt_out.rb'
    - 'lib/reports/v3/basis/electronic_signature.rb'
    - 'lib/reports/v3/basis/eviction.rb'
    - 'lib/reports/v3/basis/guest_card.rb'
    - 'lib/reports/v3/basis/lease.rb'
    - 'lib/reports/v3/basis/listing.rb'
    - 'lib/reports/v3/basis/parking_reservation.rb'
    - 'lib/reports/v3/basis/payable_line_item.rb'
    - 'lib/reports/v3/basis/payment.rb'
    - 'lib/reports/v3/basis/prospects.rb'
    - 'lib/reports/v3/basis/resident.rb'
    - 'lib/reports/v3/basis/scheduled_payment.rb'
    - 'lib/reports/v3/basis/security_deposit.rb'
    - 'lib/reports/v3/basis/shared/recurring_schedule_basis.rb'
    - 'lib/reports/v3/basis/unit.rb'
    - 'lib/reports/v3/basis/vendor_contract.rb'
    - 'lib/reports/v3/basis/work_order.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/control_adjustments.rb'
    - 'lib/reports/v3/maintenance_income.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/owner_balances.rb'
    - 'lib/reports/v3/owner_property_balances.rb'
    - 'lib/reports/v3/owner_statement/property_transactions_table.rb'
    - 'lib/reports/v3/property_directory.rb'
    - 'lib/reports/v3/utility_directory.rb'
    - 'lib/reports/v3/work_orders.rb'
    - 'lib/shrine_uploaders/image_uploader.rb'
    - 'lib/sidebar/move_out.rb'
    - 'lib/tasks/brakeman.rake'
    - 'lib/zeamster/transaction/refresh.rb'
    - 'spec/factories/custom_forms/form_fields.rb'
    - 'spec/factories/invoices.rb'
    - 'spec/factories/leases.rb'
    - 'spec/factories/payments.rb'
    - 'spec/importers/v3/general_ledger_importer_spec.rb'
    - 'spec/rails_helper.rb'
    - 'spec/services/ledger/apply_security_deposit_spec.rb'
    - 'spec/services/payment_processing/batch_transactions_spec.rb'
    - 'spec/support/trust_accounting_context_builder.rb'
    - 'spec/system/shared/commercial_documents.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods.
# AllowedMethods: nonzero?
Style/IfWithBooleanLiteralBranches:
  Exclude:
    - 'app/models/member_onboarding/lease_agreement.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: line_count_dependent, lambda, literal
Style/Lambda:
  Exclude:
    - 'spec/models/listing_spec.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/LineEndConcatenation:
  Exclude:
    - 'spec/system/tunisia/internal_transfers_spec.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/MapCompactWithConditionalBlock:
  Exclude:
    - 'scripts/swagger_dbml.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/MapIntoArray:
  Exclude:
    - 'app/services/work_order/check_out.rb'
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods, AllowedPatterns.
Style/MethodCallWithoutArgsParentheses:
  Exclude:
    - 'spec/factories/lease_applications.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowMethodComparison, ComparisonsThreshold.
Style/MultipleComparison:
  Exclude:
    - 'app/services/taxes/nelco/transmit/error.rb'
    - 'lib/zeamster/transaction/create.rb'

# Offense count: 7
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Exclude:
    - 'app/controllers/api/zapier/contact_timeline_entries_controller.rb'
    - 'app/decorators/maintenance/bid_request/invite_decorator.rb'
    - 'app/policies/insurance/assurant/eligible_unit_policy.rb'
    - 'app/queries/contacts_query.rb'
    - 'scripts/swagger_dbml.rb'
    - 'spec/factories/lease_applications.rb'

# Offense count: 21
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinDigits, Strict, AllowedNumbers, AllowedPatterns.
Style/NumericLiterals:
  Exclude:
    - 'spec/factories/lease_applications.rb'
    - 'spec/lib/accounting/fee_management/transfer_property_balances_spec.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowedMethods, AllowedPatterns.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'app/models/taxpayer_identification.rb'
    - 'lib/cron/every_hour.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowSafeAssignment, AllowInMultilineConditions.
Style/ParenthesesAroundCondition:
  Exclude:
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowedCompactTypes.
# SupportedStyles: compact, exploded
Style/RaiseArgs:
  Exclude:
    - 'app/controllers/marketing/listings_controller.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Methods.
Style/RedundantArgument:
  Exclude:
    - 'app/controllers/concerns/api/v1/authenticated_route.rb'
    - 'app/services/payment/record/cash/base.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantAssignment:
  Exclude:
    - 'app/queries/contacts_query.rb'
    - 'app/services/payment_processing/add_nsf_fee.rb'
    - 'lib/reports/v3/unpaid_lease_charges.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantBegin:
  Exclude:
    - 'app/controllers/api/zapier/contacts_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods.
# AllowedMethods: nonzero?
Style/RedundantCondition:
  Exclude:
    - 'lib/member_onboarding/tenants/wizard/steps/risk_management_program.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantInterpolation:
  Exclude:
    - 'lib/reports/v3/basis/payable_line_item.rb'

# Offense count: 197
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantLineContinuation:
  Exclude:
    - 'app/admin/zeamster_transaction.rb'
    - 'app/components/accounting/commercial_documents/journal_entries_list_component.rb'
    - 'app/components/accounting/receivables/terms_and_fees_component.rb'
    - 'app/controllers/accounting/bank_reconciliations_controller.rb'
    - 'app/controllers/accounting/entries/recurring_schedules_controller.rb'
    - 'app/controllers/accounting/entries_controller.rb'
    - 'app/controllers/accounting/general_ledger_imports_controller.rb'
    - 'app/controllers/accounting/invoices/recurring_schedules_controller.rb'
    - 'app/controllers/accounting/journals_controller.rb'
    - 'app/controllers/accounting/payments/electronic_payments_controller.rb'
    - 'app/controllers/accounting/payments_controller.rb'
    - 'app/controllers/concerns/electronic_documents_controller.rb'
    - 'app/controllers/concerns/has_portal_messages.rb'
    - 'app/controllers/concerns/has_portal_notifications.rb'
    - 'app/controllers/concerns/hosted_application_controller.rb'
    - 'app/controllers/concerns/payment_processing/prohibit_disabled_electronic_payments.rb'
    - 'app/controllers/concerns/scheduled_payments_controller.rb'
    - 'app/controllers/customer_setup/bank_accounts_controller.rb'
    - 'app/controllers/customers_controller.rb'
    - 'app/controllers/invoice_processing/emails_controller.rb'
    - 'app/controllers/leasing/lease_application_invites_controller.rb'
    - 'app/controllers/leasing/lease_documents_controller.rb'
    - 'app/controllers/leasing/move_outs_controller.rb'
    - 'app/controllers/maintenance/tickets/appointments_controller.rb'
    - 'app/controllers/maintenance/tickets/defers_controller.rb'
    - 'app/controllers/maintenance/tickets/sidebar_balances_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/controllers/management/companies_controller.rb'
    - 'app/controllers/management/masquerades_controller.rb'
    - 'app/controllers/management/owners_controller.rb'
    - 'app/controllers/management/property_setups/buildings_controller.rb'
    - 'app/controllers/management/property_setups/floorplans_controller.rb'
    - 'app/controllers/management/property_setups/leases_controller.rb'
    - 'app/controllers/management/property_setups/units_controller.rb'
    - 'app/controllers/management_controller.rb'
    - 'app/controllers/marketing/listings_controller.rb'
    - 'app/controllers/messaging/emails_controller.rb'
    - 'app/controllers/operations/collections/evicting_controller.rb'
    - 'app/controllers/organization/charts_of_accounts_controller.rb'
    - 'app/controllers/owners/invoice_approvals_controller.rb'
    - 'app/controllers/registrations_controller.rb'
    - 'app/controllers/reports/packet_templates_controller.rb'
    - 'app/controllers/taxes/batch_groups_controller.rb'
    - 'app/controllers/taxes/ten_ninety_nines_controller.rb'
    - 'app/controllers/tenants/maintenance_tickets_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/controllers/tenants/payments_controller.rb'
    - 'app/controllers/vendors/accounting_controller.rb'
    - 'app/controllers/vendors/maintenance_tickets/invoices_controller.rb'
    - 'app/forms/terminal_form.rb'
    - 'app/jobs/customer_specific/marketplace/setup_ach_account_job.rb'
    - 'app/jobs/maintenance_ticket/appointment/send_reminders_job.rb'
    - 'app/jobs/tenant/notify_new_invoices_job.rb'
    - 'app/mailers/adjudications_mailer.rb'
    - 'app/mailers/maintenance/work_orders/appointments_mailer.rb'
    - 'app/mailers/open_charge_mailer.rb'
    - 'app/models/address.rb'
    - 'app/models/concerns/accounting_context.rb'
    - 'app/models/concerns/approvals/approvable.rb'
    - 'app/models/concerns/has_ledger.rb'
    - 'app/models/document.rb'
    - 'app/models/floorplan.rb'
    - 'app/models/invoice_attachment.rb'
    - 'app/models/itemized_damages.rb'
    - 'app/models/line_item/defaults.rb'
    - 'app/models/notification/preferences.rb'
    - 'app/models/tenant.rb'
    - 'app/queries/invoices_query.rb'
    - 'app/queries/properties_query.rb'
    - 'app/queries/tenants_query.rb'
    - 'app/services/accounting/gross_potential_rent/create_unit_entries.rb'
    - 'app/services/invoice/record/amounts.rb'
    - 'app/services/invoice/record/passthrough.rb'
    - 'app/services/lease/generate_document.rb'
    - 'app/services/management/record_owner_credit.rb'
    - 'app/services/management_fees/bill_property.rb'
    - 'app/services/property/persist.rb'
    - 'lib/accounting/account_balance_matrix.rb'
    - 'lib/accounting/context.rb'
    - 'lib/accounting/context/lease_application.rb'
    - 'lib/accounting/fee_management/month_end_review.rb'
    - 'lib/accounting/gl_code.rb'
    - 'lib/accounting/yearly_progress_data.rb'
    - 'lib/action_index/listings.rb'
    - 'lib/action_index/properties.rb'
    - 'lib/action_table/accounting/accounts.rb'
    - 'lib/actions_menu/base.rb'
    - 'lib/bank_account/funds_transfer/sources/direct_payments.rb'
    - 'lib/bank_account/funds_transfer/sources/manual.rb'
    - 'lib/bank_account/funds_transfer/sources/merchant_processing_revenue.rb'
    - 'lib/cards/new_leads.rb'
    - 'lib/cards/price_per_square_foot.rb'
    - 'lib/collections/demand_letter/configured_template.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/lease_application/scorecard/deductions.rb'
    - 'lib/reports/general_ledger.rb'
    - 'lib/reports/v3/basis/aging_receivables.rb'
    - 'lib/reports/v3/basis/member_directory_basis.rb'
    - 'lib/reports/v3/basis/resident.rb'
    - 'lib/reports/v3/collections.rb'
    - 'lib/reports/v3/consolidated_property_income_statement.rb'
    - 'lib/reports/v3/consolidated_report/subdivision_income_statement_columns.rb'
    - 'lib/reports/v3/expense_register.rb'
    - 'lib/reports/v3/income_statement.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/investor_income_statement.rb'
    - 'lib/reports/v3/journal_allocation.rb'
    - 'lib/reports/v3/move_in_move_out.rb'
    - 'lib/reports/v3/profit_and_loss.rb'
    - 'lib/reports/v3/subdivision_balance_sheet.rb'
    - 'lib/reports/v3/subdivision_income_statement.rb'
    - 'lib/reports/v3/subdivision_trial_balance.rb'
    - 'lib/reports/v3/unpaid_lease_charges.rb'
    - 'spec/factories/messaging_emails.rb'
    - 'spec/factories/waitlist_entries.rb'
    - 'spec/lib/profit_stars/transactions/refresh_spec.rb'
    - 'spec/models/lease_application/applicant_spec.rb'
    - 'spec/services/payment/record/accrual_spec.rb'
    - 'spec/shared/trust_accounting_context.rb'
    - 'spec/support/matchers/clone.rb'
    - 'spec/system/accounting/journals/entries_spec.rb'
    - 'spec/system/accounting/owner_contributions/record_contribution_spec.rb'
    - 'spec/system/accounting/owner_credits_spec.rb'
    - 'spec/system/organization/charts_of_accounts_spec.rb'
    - 'spec/system/portfolio/property_setup/land_and_tax_spec.rb'
    - 'spec/system/shared/commercial_documents.rb'

# Offense count: 15
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantParentheses:
  Exclude:
    - 'app/controllers/apply/references_controller.rb'
    - 'app/controllers/organization/tunisia/bank_accounts_controller.rb'
    - 'app/importers/general_ledger_importer/amount_row.rb'
    - 'app/models/company.rb'
    - 'app/services/taxes/nelco/transmit/error_parser.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/member_onboarding/wizard.rb'
    - 'lib/reports/v3/period_change.rb'
    - 'lib/tasks/statistics.rake'
    - 'scripts/swagger_dbml.rb'

# Offense count: 9
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowMultipleReturnValues.
Style/RedundantReturn:
  Exclude:
    - 'app/controllers/api/zapier/contacts_controller.rb'
    - 'app/controllers/customer_setup/charts_of_accounts_controller.rb'
    - 'app/controllers/reports_controller.rb'
    - 'app/models/lease_application.rb'
    - 'app/policies/payment/voidable.rb'
    - 'app/services/data_exporting/export_maintenance/bid_request.rb'
    - 'app/services/zillow/process_lead.rb'
    - 'lib/accounting/context/company.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantSelfAssignmentBranch:
  Exclude:
    - 'app/models/concerns/commercial_document.rb'
    - 'lib/reports/balance_sheet.rb'
    - 'lib/reports/cash_flow_statement.rb'
    - 'lib/reports/v3/aging_receivables.rb'
    - 'lib/reports/v3/basis/agreement.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: implicit, explicit
Style/RescueStandardError:
  Exclude:
    - 'app/services/zillow/process_lead.rb'

# Offense count: 9
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods, AllowedPatterns.
Style/ReturnNilInPredicateMethodDefinition:
  Exclude:
    - 'app/models/configuration/screening.rb'
    - 'app/models/invoice.rb'
    - 'app/models/invoice_payment.rb'
    - 'app/services/charge_schedule/bill.rb'

# Offense count: 31
# Configuration parameters: Max.
Style/SafeNavigationChainLength:
  Exclude:
    - 'app/admin/reports_email_schedules.rb'
    - 'app/controllers/api/v1/sessions_controller.rb'
    - 'app/controllers/leasing/applications/saferent/screenings_controller.rb'
    - 'app/controllers/leasing/commercial_leases_controller.rb'
    - 'app/controllers/tenant_controller.rb'
    - 'app/controllers/tenants/dashboard_controller.rb'
    - 'app/controllers/tenants/member_onboardings_controller.rb'
    - 'app/controllers/tenants/member_profiles_controller.rb'
    - 'app/decorators/attachment_decorator.rb'
    - 'app/exporters/underwriting_exporter.rb'
    - 'app/importers/tenant_ledger_importer.rb'
    - 'app/models/bank_account.rb'
    - 'app/models/project/phase.rb'
    - 'app/models/tunisia/authorized_user.rb'
    - 'app/models/vendor_assignment.rb'
    - 'app/queries/employees_query.rb'
    - 'app/queries/tenants_query.rb'
    - 'app/services/lease/create.rb'
    - 'app/services/lease_application/prepare.rb'
    - 'lib/accounting/fee_management/month_end_review.rb'
    - 'lib/extensions/plutus_entries_contact_name.rb'
    - 'lib/importers/v3/rent_roll.rb'
    - 'lib/importers/v3/tenant_payments.rb'
    - 'lib/member_onboarding/tenants/configured_cohort.rb'
    - 'lib/member_onboarding/tenants/validator/required_guarantor_information.rb'
    - 'lib/reports/v3/basis/scheduled_payment.rb'
    - 'lib/reports/v3/rent_roll_with_applicants.rb'
    - 'lib/shrine_uploaders/image_uploader.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/SelectByRegexp:
  Exclude:
    - 'scripts/heroku/full_database_restore.rb'

# Offense count: 16
# This cop supports safe autocorrection (--autocorrect).
Style/SelfAssignment:
  Exclude:
    - 'spec/support/property_context_builder.rb'
    - 'spec/support/trust_accounting_context_builder.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowSend.
Style/SendWithLiteralMethodName:
  Exclude:
    - 'lib/active_model/cocoon_model/associations.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: only_raise, only_fail, semantic
Style/SignalException:
  Exclude:
    - 'app/services/reams/order/fetchable.rb'
    - 'spec/support/trust_accounting_context_builder.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowModifier.
Style/SoleNestedConditional:
  Exclude:
    - 'app/middleware/custom_subdomain_elevator.rb'
    - 'app/services/reams/order/fetch/canceled.rb'
    - 'lib/chart_of_accounts/tree_builder/cash_flow_statement.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: RequireEnglish, EnforcedStyle.
# SupportedStyles: use_perl_names, use_english_names, use_builtin_english_names
Style/SpecialGlobalVars:
  Exclude:
    - 'app/jobs/lending/loan/update_statement_data_job.rb'

# Offense count: 305
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Exclude:
    - 'app/controllers/api/zapier/guest_cards_controller.rb'
    - 'app/controllers/maintenance/tickets_controller.rb'
    - 'app/models/concerns/custom_forms/payment_validations.rb'
    - 'app/models/management_contract/membership.rb'
    - 'lib/accounting/fee_management/bulk_property_balance_transfer.rb'
    - 'lib/accounting/fee_management/transfer_property_balances.rb'
    - 'spec/lib/accounting/fee_management/property_balances_query_spec.rb'
    - 'spec/lib/accounting/fee_management/transfer_property_balances_spec.rb'
    - 'spec/models/custom_forms/element/email_spec.rb'
    - 'spec/models/custom_forms/element/phone_number_spec.rb'
    - 'spec/models/custom_forms/element/short_text_spec.rb'
    - 'spec/models/custom_forms/element/single_check_box_spec.rb'
    - 'spec/support/capybara_helper.rb'
    - 'spec/support/trust_accounting_context_builder.rb'
    - 'spec/system/management/bulk_property_balance_transfers_spec.rb'

# Offense count: 82
# This cop supports safe autocorrection (--autocorrect).
Style/SuperArguments:
  Exclude:
    - 'app/components/help_tooltip_component.rb'
    - 'app/components/modal/bulk_action_component.rb'
    - 'app/controllers/concerns/simple_layout.rb'
    - 'app/importers/prepayment_importer.rb'
    - 'app/middleware/custom_subdomain_elevator.rb'
    - 'app/models/concerns/contact.rb'
    - 'app/models/concerns/has_charge_schedule.rb'
    - 'app/models/concerns/has_metadata.rb'
    - 'app/models/concerns/payment/has_journal_entries.rb'
    - 'app/models/concerns/taggable.rb'
    - 'app/models/inspection/record.rb'
    - 'app/models/line_item.rb'
    - 'app/models/payment_plan.rb'
    - 'app/models/task.rb'
    - 'app/queries/taxes/batches_query.rb'
    - 'app/queries/taxes/submissions_query.rb'
    - 'app/resources/api/v2/accounting/account_resource.rb'
    - 'app/resources/api/v2/accounting/amount_resource.rb'
    - 'app/resources/api/v2/accounting/entry_resource.rb'
    - 'app/resources/api/v2/charge_resource.rb'
    - 'app/resources/api/v2/entity_resource.rb'
    - 'app/resources/api/v2/estimate_resource.rb'
    - 'app/resources/api/v2/guest_card_resource.rb'
    - 'app/resources/api/v2/invoice_resource.rb'
    - 'app/resources/api/v2/lease_application_membership_resource.rb'
    - 'app/resources/api/v2/lease_application_resource.rb'
    - 'app/resources/api/v2/lease_resource.rb'
    - 'app/resources/api/v2/listing_resource.rb'
    - 'app/resources/api/v2/move_out_resource.rb'
    - 'app/resources/api/v2/owner_resource.rb'
    - 'app/resources/api/v2/owner_statement/entry_resource.rb'
    - 'app/resources/api/v2/property_resource.rb'
    - 'app/resources/api/v2/rent_roll_resource.rb'
    - 'app/resources/api/v2/shared.rb'
    - 'app/resources/api/v2/tenant_resource.rb'
    - 'app/resources/api/v2/unit_resource.rb'
    - 'app/resources/api/v2/vendor_resource.rb'
    - 'app/resources/api/v2/work_order_resource.rb'
    - 'app/services/tunisia/service.rb'
    - 'lib/actions_menu/floorplan_card.rb'
    - 'lib/actions_menu/inspection_template.rb'
    - 'lib/actions_menu/invoice.rb'
    - 'lib/actions_menu/lead.rb'
    - 'lib/actions_menu/lease.rb'
    - 'lib/actions_menu/payment.rb'
    - 'lib/actions_menu/property.rb'
    - 'lib/actions_menu/tenant.rb'
    - 'lib/actions_menu/unit.rb'
    - 'lib/actions_menu/vendor.rb'
    - 'lib/cards/payables_summary.rb'
    - 'lib/importers/v3/paid_invoices.rb'
    - 'lib/inspections/wizard/review_page.rb'
    - 'lib/reports/account_ledger.rb'
    - 'lib/reports/v3/basis/customer_payment.rb'
    - 'lib/reports/v3/basis/member_directory_basis.rb'
    - 'lib/reports/v3/basis/receivable_payment.rb'
    - 'lib/reports/v3/basis/tenant.rb'
    - 'lib/reports/v3/budget_variance.rb'
    - 'lib/reports/v3/consolidated_balance_sheet.rb'
    - 'lib/reports/v3/consolidated_budget_variance.rb'
    - 'lib/reports/v3/consolidated_property_budget_variance.rb'
    - 'lib/reports/v3/consolidated_report.rb'
    - 'lib/reports/v3/delinquency.rb'
    - 'lib/reports/v3/investor_home_data.rb'
    - 'lib/reports/v3/lease_expiration.rb'
    - 'lib/reports/v3/ledger.rb'
    - 'lib/reports/v3/period_change.rb'
    - 'lib/reports/v3/prepayment.rb'
    - 'lib/reports/v3/rent_roll_with_applicants.rb'
    - 'lib/reports/v3/second_nature_monthly_verification.rb'
    - 'lib/reports/v3/tenant_balances.rb'
    - 'lib/zeamster/service.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: .
# SupportedStyles: percent, brackets
Style/SymbolArray:
  EnforcedStyle: percent
  MinSize: 6

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArguments:
  Exclude:
    - 'app/controllers/owners/documents_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, diff_comma, no_comma
Style/TrailingCommaInArrayLiteral:
  Exclude:
    - 'app/controllers/organization/configurations_controller.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/YAMLFileRead:
  Exclude:
    - 'scripts/regenerate_swagger.rb'
