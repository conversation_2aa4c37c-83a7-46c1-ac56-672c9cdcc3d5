# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
!/log/.keep
/tmp
/coverage
/personal

# Ignore dotenv
.env.local
.env.production
.env.test
.env.test.local

# Ignore npm modules
/node_modules

# Ignore built semantic assets
/public/assets/themes
/vendor/assets/javascripts/semantic.min.js
/vendor/assets/stylesheets/semantic.min.css

# Ignore built pdf worker
app/assets/javascripts/pdf.worker.js*

# Ignore webpack bundle
/app/assets/javascripts/*-bundle.js*

*.swp

# Ignore OS X custom attributes
.DS_Store

# Generated documentation
/doc
.yardoc

# Uploaded attachments
/public/system
/public/uploads
/storage

/.rspec-failures
/.approvals

pdf.pdf

.irb_history

.idea

# Ignore yarnage
.yarn/cache
.yarn/install-state.gz
.yarnrc.yml
yarn-error.log

.byebug_history

/.vscode/**

CLAUDE.md
