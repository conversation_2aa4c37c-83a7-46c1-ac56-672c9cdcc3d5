var gulp = require('gulp');
var parallel = gulp.parallel;
var series = gulp.series;
var rename = require('gulp-rename');
var replace = require('gulp-replace');

require('./node_modules/fomantic-ui/tasks/collections/build')(gulp);

gulp.task('remove-dom-warning', function () {
  var directory = './node_modules/fomantic-ui/src/definitions/modules/';
  var path = directory + 'transition.js';
  return gulp.src([path])
             .pipe(replace('module.error(error.noAnimation, settings.animation, element);', ''))
             .pipe(gulp.dest(directory));
});

gulp.task('fix-uninvert-modals', function () {
  var directory = './node_modules/fomantic-ui/src/definitions/modules/';
  var path = directory + 'modal.js';
  return gulp.src([path])
             .pipe(replace('module.remove.dimmerStyles();', ''))
             .pipe(gulp.dest(directory));
});

gulp.task('semantic-rename-theme', function () {
  return gulp.src('./node_modules/fomantic-ui/src/theme.config.example')
             .pipe(rename('theme.config'))
             .pipe(gulp.dest('./node_modules/fomantic-ui/src'));
});

gulp.task('semantic-override-site', function () {
  return gulp.src('./semantic/site/**/*')
             .pipe(gulp.dest('./node_modules/fomantic-ui/src/site'), { overwrite: true });
});

gulp.task('semantic-build', parallel('build'));

gulp.task('semantic-custom-theme', parallel('semantic-override-site', 'semantic-rename-theme'));

gulp.task('semantic-ui', series(parallel('remove-dom-warning', 'fix-uninvert-modals', 'semantic-custom-theme'), 'semantic-build'));

gulp.task('semantic-copy-themes', function () {
  return gulp.src(['./node_modules/fomantic-ui/dist/themes/**/*'])
             .pipe(gulp.dest('./public/assets/themes/'));
});

gulp.task('semantic-copy-css', function () {
  return gulp.src('./node_modules/fomantic-ui/dist/semantic.min.css')
             .pipe(gulp.dest('./vendor/assets/stylesheets/'));
});

gulp.task('semantic-copy-js', function () {
  return gulp.src('./node_modules/fomantic-ui/dist/semantic.min.js')
             .pipe(gulp.dest('./vendor/assets/javascripts/'));
});

gulp.task('semantic-copy', series('semantic-ui', parallel('semantic-copy-css', 'semantic-copy-js', 'semantic-copy-themes')));

gulp.task('installx', parallel('semantic-copy'));

gulp.task('default', parallel('installx'));
