# ruby:3.2.7
FROM ruby@sha256:5f5c0b1bfb183999837c63c93cc422e1da63ac72eeb758227f3a2fd2ae5f42f3

LABEL org.opencontainers.image.authors="<EMAIL>"

WORKDIR /opt/revela

# Update bundler
RUN gem install bundler
RUN chmod -R 777 /usr/local/bundle

# Initial setup scripts
COPY scripts/docker scripts/docker
RUN scripts/docker/update_apt.sh
RUN scripts/docker/install_dependencies.sh
RUN scripts/docker/install_chromedriver.rb

# Set Eastern Time
ENV TZ=America/Detroit
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Add non root user
# https://github.com/moby/moby/issues/6119...........
RUN useradd -m rails && chown rails:rails /opt/revela
USER rails

# Don't install gem docs
RUN echo 'gem: --no-ri --no-rdoc' > ~/.gemrc

# Add node_modules binaries to path
ENV PATH="/opt/revela/bin:node_modules/.bin:${PATH}"

# Pupeteer Chrome Path and Options
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome
ENV GROVER_NO_SANDBOX=true

# Install node packages
COPY package.json yarn.lock ./
RUN yarn install --ignore-scripts && yarn cache clean

# Compile semantic
COPY semantic semantic
COPY semantic.json Gulpfile.js ./
COPY scripts/build_semantic.sh scripts/build_semantic.sh
RUN scripts/build_semantic.sh

# Install gems
COPY Gemfile Gemfile.lock .ruby-version ./
RUN bundle install --jobs 4

# Run webpack
COPY webpack webpack
COPY frontend frontend
COPY webpack.config.js .babelrc ./
RUN webpack --config webpack/prod.config.js

# Everything else
COPY --chown=rails . /opt/revela

# Set git version from build-arg
ARG revision
ENV APP_REVISION=${revision}

USER root

# This is where I think we need to
# - install unopkg on the system
# - copy over the
#   - the python and other files that will make up the extension
#   - the scripts to build/register the extension with libreoffice (using unopkg)
#   - run the scripts and install unopkg

User rails

CMD ["bundle", "exec", "rails", "s", "-b", "0.0.0.0"]
