/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run "npm run dev" in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run "npm run deploy" to publish your worker
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

import * as jose from 'jose'

/**
 * @typedef {Object} Env
 */

export default {
  /**
   * @param {Request} request
   * @param {Env} env
   * @param {ExecutionContext} ctx
   * @returns {Promise<Response>}
   */
  async fetch(request, env, ctx) {
    const ecPrivateKey = await jose.importPKCS8(env['PRIVATE_KEY'], 'RSA-OAEP')

    const request_uri = new URL(request.url);
    const encrypted = request_uri.searchParams.get('token');
    let parsed;
    try {
      const {plaintext, _protectedHeader} = await jose.compactDecrypt(encrypted, ecPrivateKey);

      const decoder = new TextDecoder();
      const decrypted = decoder.decode(plaintext);
      parsed = JSON.parse(decrypted);

      const timestampInMilliseconds = Date.now();
      const timestampInSeconds = Math.floor(timestampInMilliseconds / 1000);
      if (parsed.exp < timestampInSeconds) {
        throw new Error('Expired');
      }
    } catch(error) {
      return new Response('Invalid or Expired Link', {
        status: 401, 'content-type': 'text/plain'
      })
    }

    const requestOptions = {
      method: 'GET',
      redirect: 'follow'
    };
    let file_response = await fetch(parsed.link, requestOptions);
    let data = await file_response.blob();

    let responseContentDisposition;
    if (request.headers['Content-Disposition']?.indexOf('attachment') > -1) {
      responseContentDisposition = `attachment; filename="${parsed.filename}"`;
    } else {
      responseContentDisposition = 'inline';
    }

    return new Response(data, {
      status: 200,
      headers: {
        'content-type': parsed.content_type,
        'Content-Disposition': responseContentDisposition
      }
    });
  },
};
