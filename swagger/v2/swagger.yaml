---
openapi: 3.0.1
info:
  title: API V2
  version: v2
  description: |
    <p>
      Welcome to the V2 Revela API!
    </p>
    <p>
      This API conforms to the
      <a href="https://jsonapi.org">JSON:API specification</a>,
      so this API should be familiar to users who have used other JSON:API endpoints.
    </p>
    <p>
      This means that requests can specify query parameters such as
      <code>?page[size]=250</code>,
      <code>?filter[updated_after]=2000-01-01</code>,
      or <code>?include=properties,properties.address</code>.
    </p>
    <p>
      Each request must be authenticated with token bearer authentication.
      This means that each request should include a header like <code>Authorization: Bearer {api_token}</code>.
    </p>
    <p>
      The below examples are interactive.
      After clicking the &ldquo;Authorize&rdquo; button and entering a valid token,
      you can click &ldquo;Try it out&rdquo; to perform an API request in the browser.
    </p>
    <p>
      For additional support or assitance, please reach out via <NAME_EMAIL>.
    </p>
    <p>
      <h3>Changelog</h3>
      <dl>
        <dt>2025-05-13</dt>
        <dd>Added <var>start_date</var> and <var>end_date</var> filters to <var>accounting/amounts</var> endpoint</dd>
        <dd>Added <var>start_date</var> and <var>end_date</var> filters to <var>accounting/entries</var> endpoint</dd>
        <dt>2025-01-10</dt>
        <dd>Published <var>inspections</var> endpoints</dd>
        <dt>2024-11-01</dt>
        <dd>Added <var>applied_payments</var> endpoint</dd>
        <dt>2024-10-30</dt>
        <dd>Added <var>payments</var> endpoint</dd>
        <dt>2024-10-15</dt>
        <dd>Added Buyer and Seller relationships to <var>invoices</var> endpoint</dd>
        <dt>2024-09-22</dt>
        <dd>Published <var>management_contracts</var> endpoint</dd>
        <dt>2024-09-21</dt>
        <dd>Published <var>work_order_appointments</var> endpoint</dd>
        <dt>2024-09-11</dt>
        <dd>Published <var>pets</var> endpoint</dd>
        <dt>2024-09-09</dt>
        <dd>Added approval timestamps to <var>work_orders</var> endpoint</dd>
        <dt>2024-09-09</dt>
        <dd>Added <var>date_of_birth</var> to <var>tenants</var> endpoint</dd>
        <dt>2024-07-31</dt>
        <dd>Added <var>estimates</var> endpoint</dd>
        <dt>2024-06-12</dt>
        <dd>Added <var>latitude</var> and <var>longitude</var> to <var>addresses</var> endpoint</dd>
        <dd>Added <var>archived_at</var> to <var>vendors</var> endpoint</dd>
        <dt>2024-02-11</dt>
        <dd>Added <var>total_monthly_reported_income</var> to <var>lease_applications</var> endpoint</dd>
        <dt>2024-01-17</dt>
        <dd>Added <var>published_at</var> to <var>listings</var> endpoint</dd>
        <dt>2023-12-15</dt>
        <dd>Added <var>original_end_date</var> to <var>leases</var> endpoint</dd>
        <dt>2023-11-13</dt>
        <dd>Published <var>line_items</var> endpoint</dd>
        <dt>2023-11-01</dt>
        <dd>Added <var>lease</var> relationship to <var>invoices</var> endpoint</dd>
        <dt>2023-10-31</dt>
        <dd>Added <var>balance</var> to <var>invoices</var> endpoint</dd>
        <dt>2023-10-19</dt>
        <dd>Published <var>invoices</var> endpoint</dd>
        <dt>2023-10-12</dt>
        <dd>Add <var>tags</var> to <var>tenants</var> endpoint</dd>
        <dt>2023-10-11</dt>
        <dd>Add <var>archived_at</var> to <var>entities</var> endpoint</dd>
        <dd>Add <var>archived_at</var> to <var>owners</var> endpoint</dd>
        <dd>Add <var>archived_at</var> to <var>properties</var> endpoint</dd>
        <dd>Add <var>tags</var> to <var>entities</var> endpoint</dd>
        <dd>Add <var>tags</var> to <var>owners</var> endpoint</dd>
        <dd>Add <var>tags</var> to <var>properties</var> endpoint</dd>
        <dt>2023-10-02</dt>
        <dd>Document <var>employees</var> endpoint</dd>
        <dt>2023-09-24</dt>
        <dd>Added <var>renewal_status</var> to <var>leases</var> endpoint</dd>
        <dt>2023-09-23</dt>
        <dd>Added <var>custom_data</var> to <var>work_orders</var> endpoint</dd>
        <dt>2023-08-16</dt>
        <dd>Added <var>rent_roll</var> endpoint</dd>
        <dt>2023-08-02</dt>
        <dd>Added <var>reason</var> to <var>notices_of_non_renewal</var> endpoint</dd>
        <dt>2023-07-26</dt>
        <dd>Added <var>owners</var> endpoint</dd>
        <dt>2023-07-25</dt>
        <dd>Added <var>entities</var> endpoint</dd>
        <dt>2023-07-20</dt>
        <dd>Added <var>portfolios</var> endpoint</dd>
        <dt>2023-07-10</dt>
        <dd>Added <var>status</var> to <var>units</var> endpoint</dd>
        <dt>2023-07-05</dt>
        <dd>Added <var>status</var> to <var>tenants</var> endpoint</dd>
        <dt>2023-06-26</dt>
        <dd>Added <var>base_rent_amount</var> to <var>leases</var> endpoint</dd>
        <dt>2023-06-21</dt>
        <dd>Added <var>cost</var> to <var>work_orders</var> endpoint</dd>
        <dt>2023-06-19</dt>
        <dd>Published <var>prospects</var> endpoint</dd>
        <dt>2023-06-17</dt>
        <dd>Added <var>requested_move_in_date</var> to <var>guest_cards</var> endpoint</dd>
      </dl>
    </p>
components:
  securitySchemes:
    bearer_auth:
      type: http
      scheme: bearer
paths:
  "/api/v2/addresses":
    get:
      summary: list addresses
      security:
      - bearer_auth: []
      tags:
      - Addresses
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '914'
                  type: addresses
                  links:
                    self: http://alever.lvh.me/api/v2/addresses/914
                  attributes:
                    line_one: 3291 Stracke Haven
                    line_two: Apt. 277
                    city: Gutkowskifurt
                    region: Michigan
                    postal_code: 92521-3607
                    country: United States
                    latitude: 0.0
                    longitude: 0.0
                    created_at: '2022-03-14T18:07:14.287-04:00'
                    updated_at: '2022-03-14T18:07:14.287-04:00'
                links:
                  first: http://alever.lvh.me/api/v2/addresses?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/addresses?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/addresses/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show address
      security:
      - bearer_auth: []
      tags:
      - Addresses
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '915'
                  type: addresses
                  links:
                    self: http://alever.lvh.me/api/v2/addresses/915
                  attributes:
                    line_one: 96579 Christiansen Extension
                    line_two: Suite 177
                    city: Lake Edmund
                    region: Arkansas
                    postal_code: '03866-9057'
                    country: United States
                    latitude: 0.0
                    longitude: 0.0
                    created_at: '2022-03-14T18:07:14.346-04:00'
                    updated_at: '2022-03-14T18:07:14.346-04:00'
  "/api/v2/applied_payments":
    get:
      summary: list applied payments
      security:
      - bearer_auth: []
      tags:
      - Applied Payments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: applied_payments
                  links:
                    self: http://alever.lvh.me/api/v2/applied_payments/1
                  attributes:
                    amount: '64.50'
                    date: '2000-01-01'
                    reversal_date:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    invoice:
                      links:
                        self: http://alever.lvh.me/api/v2/applied_payments/1/relationships/invoice
                        related: http://alever.lvh.me/api/v2/applied_payments/1/invoice
                    payment:
                      links:
                        self: http://alever.lvh.me/api/v2/applied_payments/1/relationships/payment
                        related: http://alever.lvh.me/api/v2/applied_payments/1/payment
                links:
                  first: http://alever.lvh.me/api/v2/applied_payments?page%5Bsize%5D=25
  "/api/v2/applied_payments/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show applied payment
      security:
      - bearer_auth: []
      tags:
      - Applied Payments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1'
                  type: applied_payments
                  links:
                    self: http://alever.lvh.me/api/v2/applied_payments/1
                  attributes:
                    amount: '11.24'
                    date: '2000-01-01'
                    reversal_date:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    invoice:
                      links:
                        self: http://alever.lvh.me/api/v2/applied_payments/1/relationships/invoice
                        related: http://alever.lvh.me/api/v2/applied_payments/1/invoice
                    payment:
                      links:
                        self: http://alever.lvh.me/api/v2/applied_payments/1/relationships/payment
                        related: http://alever.lvh.me/api/v2/applied_payments/1/payment

  "/api/v2/delinquencies":
    get:
      summary: list delinquencies
      security:
      - bearer_auth: []
      tags:
      - Delinquencies
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '14'
                  type: delinquencies
                  links:
                    self: http://alever.lvh.me/api/v2/delinquencies/14
                  attributes:
                    balance: '300.00'
                    overdue: '300.00'
                    thirty_days: '0.00'
                    sixty_days: '0.00'
                    ninety_days: '0.00'
                    ninety_one_plus_days: '300.00'
                links:
                  first: http://alever.lvh.me/api/v2/delinquencies?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/delinquencies?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/delinquencies/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show delinquency
      security:
      - bearer_auth: []
      tags:
      - Delinquencies
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '15'
                  type: delinquencies
                  links:
                    self: http://alever.lvh.me/api/v2/delinquencies/15
                  attributes:
                    balance: '300.00'
                    overdue: '300.00'
                    thirty_days: '0.00'
                    sixty_days: '0.00'
                    ninety_days: '0.00'
                    ninety_one_plus_days: '300.00'
  "/api/v2/demand_notices":
    get:
      summary: list demand notices
      security:
      - bearer_auth: []
      tags:
      - Demand Notices
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '5'
                  type: demand_notices
                  links:
                    self: http://alever.lvh.me/api/v2/demand_notices/5
                  attributes:
                    overdue_balance: '500.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/demand_notices/5/relationships/lease
                        related: http://alever.lvh.me/api/v2/demand_notices/5/lease
                links:
                  first: http://alever.lvh.me/api/v2/demand_notices?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/demand_notices?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/demand_notices/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show demand notice
      security:
      - bearer_auth: []
      tags:
      - Demand Notices
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '6'
                  type: demand_notices
                  links:
                    self: http://alever.lvh.me/api/v2/demand_notices/6
                  attributes:
                    overdue_balance: '500.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/demand_notices/6/relationships/lease
                        related: http://alever.lvh.me/api/v2/demand_notices/6/lease
  "/api/v2/employees":
    get:
      summary: list employees
      security:
      - bearer_auth: []
      tags:
      - Employees
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1306'
                  type: employees
                  links:
                    self: http://alever.lvh.me/api/v2/employees/1306
                  attributes:
                    first_name: Sample
                    last_name: Employee
                    email: <EMAIL>
                    phone: "(*************"
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    assigned_work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/employees/1306/relationships/assigned_work_orders
                        related: http://alever.lvh.me/api/v2/employees/1306/assigned_work_orders
                links:
                  first: http://alever.lvh.me/api/v2/employees?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/employees?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/employees/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show employee
      security:
      - bearer_auth: []
      tags:
      - Employees
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1308'
                  type: employees
                  links:
                    self: http://alever.lvh.me/api/v2/employees/1308
                  attributes:
                    first_name: Sample
                    last_name: Employee
                    email: <EMAIL>
                    phone: "(*************"
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    assigned_work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/employees/1308/relationships/assigned_work_orders
                        related: http://alever.lvh.me/api/v2/employees/1308/assigned_work_orders
  "/api/v2/entities":
    get:
      summary: list entities
      security:
      - bearer_auth: []
      tags:
      - Entities
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: entities
                  links:
                    self: http://alever.lvh.me/api/v2/entities/1
                  attributes:
                    name: Client Entity, LLC
                    business_type: llc
                    trade_name: Client Entity
                    state_of_incorporation: Delaware
                    tags: []
                    archived_at:
                    created_at: '2023-07-24T14:22:58.949-04:00'
                    updated_at: '2023-07-24T14:22:59.047-04:00'
                  relationships:
                    management_contract:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/1/relationships/management_contract
                        related: http://alever.lvh.me/api/v2/entities/1/management_contract
                    portfolio:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/1/relationships/portfolio
                        related: http://alever.lvh.me/api/v2/entities/1/portfolio
                    owners:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/1/relationships/owners
                        related: http://alever.lvh.me/api/v2/entities/1/owners
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/1/relationships/properties
                        related: http://alever.lvh.me/api/v2/entities/1/properties
  "/api/v2/entities/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show entity
      security:
      - bearer_auth: []
      tags:
      - Entities
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '126'
                  type: entities
                  links:
                    self: http://alever.lvh.me/api/v2/entities/126
                  attributes:
                    name: Company 16 Group
                    business_type: scorp
                    trade_name: Company 16
                    state_of_incorporation: Delaware
                    tags: []
                    archived_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    management_contract:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/126/relationships/management_contract
                        related: http://alever.lvh.me/api/v2/entities/126/management_contract
                    portfolio:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/126/relationships/portfolio
                        related: http://alever.lvh.me/api/v2/entities/126/portfolio
                    owners:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/126/relationships/owners
                        related: http://alever.lvh.me/api/v2/entities/126/owners
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/entities/126/relationships/properties
                        related: http://alever.lvh.me/api/v2/entities/126/properties
  "/api/v2/estimates":
    get:
      summary: list estimates
      security:
      - bearer_auth: []
      tags:
      - Estimates
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: estimates
                  links:
                    self: http://alever.lvh.me/api/v2/estimates/1
                  attributes:
                    summary: Molestiae sed nobis. Vero eveniet distinctio. Repellat
                      est est.
                    amount: '11174.00'
                    materials: '5437.00'
                    labor: '5737.00'
                    markup: '0.00'
                    materials_markup: '0.00'
                    labor_markup: '0.00'
                    approval_requested_at:
                    approved_at:
                    rejected_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/1/relationships/property
                        related: http://alever.lvh.me/api/v2/estimates/1/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/1/relationships/unit
                        related: http://alever.lvh.me/api/v2/estimates/1/unit
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/1/relationships/work_order
                        related: http://alever.lvh.me/api/v2/estimates/1/work_order
                    prepared_by:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/1/relationships/prepared_by
                        related: http://alever.lvh.me/api/v2/estimates/1/prepared_by
                links:
                  first: http://alever.lvh.me/api/v2/estimates?page%5Bsize%5D=25
  "/api/v2/estimates/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show estimate
      security:
      - bearer_auth: []
      tags:
      - Estimates
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '2'
                  type: estimates
                  links:
                    self: http://alever.lvh.me/api/v2/estimates/2
                  attributes:
                    summary: Blanditiis quam magni. Molestiae incidunt id. Maiores
                      quos rem.
                    amount: '13231.00'
                    materials: '3937.00'
                    labor: '9294.00'
                    markup: '0.00'
                    materials_markup: '0.00'
                    labor_markup: '0.00'
                    approval_requested_at:
                    approved_at:
                    rejected_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/2/relationships/property
                        related: http://alever.lvh.me/api/v2/estimates/2/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/2/relationships/unit
                        related: http://alever.lvh.me/api/v2/estimates/2/unit
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/2/relationships/work_order
                        related: http://alever.lvh.me/api/v2/estimates/2/work_order
                    prepared_by:
                      links:
                        self: http://alever.lvh.me/api/v2/estimates/2/relationships/prepared_by
                        related: http://alever.lvh.me/api/v2/estimates/2/prepared_by
  "/api/v2/evictions":
    get:
      summary: list evictions
      security:
      - bearer_auth: []
      tags:
      - Evictions
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '5'
                  type: evictions
                  links:
                    self: http://alever.lvh.me/api/v2/evictions/5
                  attributes:
                    overdue_balance: '1000.00'
                    opened_at: '2000-01-01T00:00:00.000-05:00'
                    court_date: '2000-01-01T00:00:00.000-05:00'
                    anticipated_eviction_date: '2000-01-01T00:00:00.000-05:00'
                    closed_at: '2000-01-01T00:00:00.000-05:00'
                    outcome: paid_in_full
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/evictions/5/relationships/lease
                        related: http://alever.lvh.me/api/v2/evictions/5/lease
                links:
                  first: http://alever.lvh.me/api/v2/evictions?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/evictions?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/evictions/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show eviction
      security:
      - bearer_auth: []
      tags:
      - Evictions
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '6'
                  type: evictions
                  links:
                    self: http://alever.lvh.me/api/v2/evictions/6
                  attributes:
                    overdue_balance: '1000.00'
                    opened_at: '2000-01-01T00:00:00.000-05:00'
                    court_date: '2000-01-01T00:00:00.000-05:00'
                    anticipated_eviction_date: '2000-01-01T00:00:00.000-05:00'
                    closed_at: '2000-01-01T00:00:00.000-05:00'
                    outcome: paid_in_full
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/evictions/6/relationships/lease
                        related: http://alever.lvh.me/api/v2/evictions/6/lease
  "/api/v2/floorplans":
    get:
      summary: list floorplans
      security:
      - bearer_auth: []
      tags:
      - Floorplans
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '454'
                  type: floorplans
                  links:
                    self: http://alever.lvh.me/api/v2/floorplans/454
                  attributes:
                    name: Two bedroom model E
                    bedrooms: 5
                    bathrooms: 2.5
                    square_feet: 782
                    market_rate: '622.48'
                    created_at: '2022-03-14T18:07:14.829-04:00'
                    updated_at: '2022-03-14T18:07:14.829-04:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/454/relationships/property
                        related: http://alever.lvh.me/api/v2/floorplans/454/property
                    listing:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/454/relationships/listing
                        related: http://alever.lvh.me/api/v2/floorplans/454/listing
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/454/relationships/units
                        related: http://alever.lvh.me/api/v2/floorplans/454/units
                links:
                  first: http://alever.lvh.me/api/v2/floorplans?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/floorplans?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/floorplans/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show floorplan
      security:
      - bearer_auth: []
      tags:
      - Floorplans
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '455'
                  type: floorplans
                  links:
                    self: http://alever.lvh.me/api/v2/floorplans/455
                  attributes:
                    name: Three bedroom model B
                    bedrooms: 4
                    bathrooms: 0.5
                    square_feet: 855
                    market_rate: '694.63'
                    created_at: '2022-03-14T18:07:14.979-04:00'
                    updated_at: '2022-03-14T18:07:14.979-04:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/455/relationships/property
                        related: http://alever.lvh.me/api/v2/floorplans/455/property
                    listing:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/455/relationships/listing
                        related: http://alever.lvh.me/api/v2/floorplans/455/listing
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/floorplans/455/relationships/units
                        related: http://alever.lvh.me/api/v2/floorplans/455/units
  "/api/v2/guest_cards":
    get:
      summary: list guest cards
      security:
      - bearer_auth: []
      tags:
      - Guest Cards
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '45'
                  type: guest_cards
                  links:
                    self: http://alever.lvh.me/api/v2/guest_cards/45
                  attributes:
                    first_name: Jeraldine
                    last_name: Mitchell3
                    email: <EMAIL>
                    phone: "(*************"
                    message:
                    source: Walk In
                    preferred_contact_method:
                    requested_style:
                    requested_move_in_date: '2000-04-01'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/45/relationships/property
                        related: http://alever.lvh.me/api/v2/guest_cards/45/property
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/45/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/guest_cards/45/floorplan
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/45/relationships/tenant
                        related: http://alever.lvh.me/api/v2/guest_cards/45/tenant
                    tours:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/45/relationships/tours
                        related: http://alever.lvh.me/api/v2/guest_cards/45/tours
                links:
                  first: http://alever.lvh.me/api/v2/guest_cards?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/guest_cards?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/guest_cards/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show guest card
      security:
      - bearer_auth: []
      tags:
      - Guest Cards
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '46'
                  type: guest_cards
                  links:
                    self: http://alever.lvh.me/api/v2/guest_cards/46
                  attributes:
                    first_name: Xenia
                    last_name: Senger4
                    email: <EMAIL>
                    phone: "(*************"
                    message:
                    source: Walk In
                    preferred_contact_method:
                    requested_style:
                    requested_move_in_date: '2000-04-01'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/46/relationships/property
                        related: http://alever.lvh.me/api/v2/guest_cards/46/property
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/46/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/guest_cards/46/floorplan
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/46/relationships/tenant
                        related: http://alever.lvh.me/api/v2/guest_cards/46/tenant
                    tours:
                      links:
                        self: http://alever.lvh.me/api/v2/guest_cards/46/relationships/tours
                        related: http://alever.lvh.me/api/v2/guest_cards/46/tours
  "/api/v2/inspection_questions":
    get:
      summary: list inspection template questions
      security:
      - bearer_auth: []
      tags:
      - Inspection Questions
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '19'
                  type: inspection_questions
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_questions/19
                  attributes:
                    category: property
                    room_type:
                    section: exterior
                    format: multi_select
                    prompt: Roof material
                    multi_select_options:
                    - Shingle
                    - Metal
                    - Tile
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    template:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_questions/19/relationships/template
                        related: http://alever.lvh.me/api/v2/inspection_questions/19/template
                links:
                  first: http://alever.lvh.me/api/v2/inspection_questions?page%5Bsize%5D=25
  "/api/v2/inspection_questions/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show inspection template question
      security:
      - bearer_auth: []
      tags:
      - Inspection Questions
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '20'
                  type: inspection_questions
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_questions/20
                  attributes:
                    category: property
                    room_type:
                    section: exterior
                    format: multi_select
                    prompt: Roof material
                    multi_select_options:
                    - Shingle
                    - Metal
                    - Tile
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    template:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_questions/20/relationships/template
                        related: http://alever.lvh.me/api/v2/inspection_questions/20/template
  "/api/v2/inspection_responses":
    get:
      summary: list inspection responses
      security:
      - bearer_auth: []
      tags:
      - Inspection Responses
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '15'
                  type: inspection_responses
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_responses/15
                  attributes:
                    body: Shingle
                    note:
                    photos:
                    - id: 6
                      content_type: 'image/jpeg'
                      download_url: https://example.com/presigned_url
                      file_size: 213066
                      filename: first_image.jpeg
                    - id: 7
                      content_type: 'image/jpeg'
                      download_url: https://example.com/presigned_url_2
                      file_size: 150000
                      filename: another_image.jpeg
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    inspection:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_responses/15/relationships/inspection
                        related: http://alever.lvh.me/api/v2/inspection_responses/15/inspection
                    question:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_responses/15/relationships/question
                        related: http://alever.lvh.me/api/v2/inspection_responses/15/question
                links:
                  first: http://alever.lvh.me/api/v2/inspection_responses?page%5Bsize%5D=25
  "/api/v2/inspection_responses/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show inspection response
      security:
      - bearer_auth: []
      tags:
      - Inspection Responses
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '16'
                  type: inspection_responses
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_responses/16
                  attributes:
                    body: Shingle
                    note:
                    photos:
                    - id: 6
                      content_type:
                      download_url: https://example.com/presigned_url
                      file_size: 213066
                      filename: first_image.jpeg
                    - id: 7
                      content_type:
                      download_url: https://example.com/presigned_url_2
                      file_size: 150000
                      filename: another_image.jpeg
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    inspection:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_responses/16/relationships/inspection
                        related: http://alever.lvh.me/api/v2/inspection_responses/16/inspection
                    question:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_responses/16/relationships/question
                        related: http://alever.lvh.me/api/v2/inspection_responses/16/question
  "/api/v2/inspection_templates":
    get:
      summary: list inspection templates
      security:
      - bearer_auth: []
      tags:
      - Inspection Templates
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '63'
                  type: inspection_templates
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_templates/63
                  attributes:
                    name: Exterior Inspection
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    archived_at:
                  relationships:
                    questions:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_templates/63/relationships/questions
                        related: http://alever.lvh.me/api/v2/inspection_templates/63/questions
                links:
                  first: http://alever.lvh.me/api/v2/inspection_templates?page%5Bsize%5D=25
  "/api/v2/inspection_templates/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show inspection template
      security:
      - bearer_auth: []
      tags:
      - Inspection Templates
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '64'
                  type: inspection_templates
                  links:
                    self: http://alever.lvh.me/api/v2/inspection_templates/64
                  attributes:
                    name: Exterior Inspection
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    archived_at:
                  relationships:
                    questions:
                      links:
                        self: http://alever.lvh.me/api/v2/inspection_templates/64/relationships/questions
                        related: http://alever.lvh.me/api/v2/inspection_templates/64/questions
  "/api/v2/inspections":
    get:
      summary: list inspections
      security:
      - bearer_auth: []
      tags:
      - Inspections
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '15'
                  type: inspections
                  links:
                    self: http://alever.lvh.me/api/v2/inspections/15
                  attributes:
                    kind: recurring
                    name: Inspect 123 Main St. Exterior
                    comments:
                    completed_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    archived_at:
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/15/relationships/property
                        related: http://alever.lvh.me/api/v2/inspections/15/property
                    template:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/15/relationships/template
                        related: http://alever.lvh.me/api/v2/inspections/15/template
                    responses:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/15/relationships/responses
                        related: http://alever.lvh.me/api/v2/inspections/15/responses
                links:
                  first: http://alever.lvh.me/api/v2/inspections?page%5Bsize%5D=25
  "/api/v2/inspections/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show inspection
      security:
      - bearer_auth: []
      tags:
      - Inspections
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '16'
                  type: inspections
                  links:
                    self: http://alever.lvh.me/api/v2/inspections/16
                  attributes:
                    kind: recurring
                    name: Inspect 123 Main St. Exterior
                    comments:
                    completed_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    archived_at:
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/16/relationships/property
                        related: http://alever.lvh.me/api/v2/inspections/16/property
                    template:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/16/relationships/template
                        related: http://alever.lvh.me/api/v2/inspections/16/template
                    responses:
                      links:
                        self: http://alever.lvh.me/api/v2/inspections/16/relationships/responses
                        related: http://alever.lvh.me/api/v2/inspections/16/responses
  "/api/v2/invoices":
    get:
      summary: list invoices
      security:
      - bearer_auth: []
      tags:
      - Invoices
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: invoices
                  links:
                    self: http://alever.lvh.me/api/v2/invoices/1
                  attributes:
                    description: Sample Invoice
                    invoice_date: '2000-01-01'
                    post_date: '2000-01-01'
                    due_date: '2000-01-31'
                    amount: '100.00'
                    balance: '0.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    line_items:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/line_items
                        related: http://alever.lvh.me/api/v2/invoices/1/line_items
                    buyer:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/buyer
                        related: http://alever.lvh.me/api/v2/invoices/1/buyer
                    seller:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/seller
                        related: http://alever.lvh.me/api/v2/invoices/1/seller
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/lease
                        related: http://alever.lvh.me/api/v2/invoices/1/lease
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/invoices/1/work_orders
                    applied_payments:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/applied_payments
                        related: http://alever.lvh.me/api/v2/invoices/1/applied_payments
                links:
                  first: http://alever.lvh.me/api/v2/invoices?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/invoices?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/invoices/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show invoice
      security:
      - bearer_auth: []
      tags:
      - Invoices
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1'
                  type: invoices
                  links:
                    self: http://alever.lvh.me/api/v2/invoices/1
                  attributes:
                    description: Sample Invoice
                    invoice_date: '2000-01-01'
                    post_date: '2000-01-01'
                    due_date: '2000-01-31'
                    amount: '100.00'
                    balance: '0.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    line_items:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/line_items
                        related: http://alever.lvh.me/api/v2/invoices/1/line_items
                    buyer:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/buyer
                        related: http://alever.lvh.me/api/v2/invoices/1/buyer
                    seller:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/seller
                        related: http://alever.lvh.me/api/v2/invoices/1/seller
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/lease
                        related: http://alever.lvh.me/api/v2/invoices/1/lease
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/invoices/1/work_orders
                    applied_payments:
                      links:
                        self: http://alever.lvh.me/api/v2/invoices/1/relationships/applied_payments
                        related: http://alever.lvh.me/api/v2/invoices/1/applied_payments
  "/api/v2/leases":
    get:
      summary: list leases
      security:
      - bearer_auth: []
      tags:
      - Leases
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: leases
                  links:
                    self: http://alever.lvh.me/api/v2/leases/1
                  attributes:
                    start_date: '2021-09-14'
                    end_date: '2022-09-14'
                    move_in_date: '2021-09-14'
                    move_out_date:
                    executed_at:
                    archived_at:
                    proration: per_diem
                    security_deposit: '0.00'
                    base_rent_amount: '1000.00'
                    total_monthly_amount: '1025.00'
                    monthly_rent_amount: '1000.00'
                    renewal_type: fixed
                    renewal_status: no_renewal
                    original_end_date:
                  relationships:
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/unit
                        related: http://alever.lvh.me/api/v2/leases/1/unit
                    renewed:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/renewed
                        related: http://alever.lvh.me/api/v2/leases/1/renewed
                    renewal:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/renewal
                        related: http://alever.lvh.me/api/v2/leases/1/renewal
                    delinquency:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/delinquency
                        related: http://alever.lvh.me/api/v2/leases/1/delinquency
                    tenants:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/tenants
                        related: http://alever.lvh.me/api/v2/leases/1/tenants
                    lease_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/lease_memberships
                        related: http://alever.lvh.me/api/v2/leases/1/lease_memberships
                    charges:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/charges
                        related: http://alever.lvh.me/api/v2/leases/1/charges
                    pets:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/pets
                        related: http://alever.lvh.me/api/v2/leases/1/pets
                links:
                  first: http://alever.lvh.me/api/v2/leases?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/leases?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/leases/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show lease
      security:
      - bearer_auth: []
      tags:
      - Leases
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1'
                  type: leases
                  links:
                    self: http://alever.lvh.me/api/v2/leases/1
                  attributes:
                    start_date: '2021-09-14'
                    end_date: '2022-09-14'
                    move_in_date: '2021-09-14'
                    move_out_date:
                    executed_at:
                    archived_at:
                    proration: per_diem
                    security_deposit: '0.00'
                    base_rent_amount: '1000.00'
                    total_monthly_amount: '1025.00'
                    monthly_rent_amount: '1000.00'
                    renewal_type: fixed
                    renewal_status: no_renewal
                    original_end_date:
                  relationships:
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/unit
                        related: http://alever.lvh.me/api/v2/leases/1/unit
                    renewed:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/renewed
                        related: http://alever.lvh.me/api/v2/leases/1/renewed
                    renewal:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/renewal
                        related: http://alever.lvh.me/api/v2/leases/1/renewal
                    delinquency:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/delinquency
                        related: http://alever.lvh.me/api/v2/leases/1/delinquency
                    tenants:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/tenants
                        related: http://alever.lvh.me/api/v2/leases/1/tenants
                    lease_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/lease_memberships
                        related: http://alever.lvh.me/api/v2/leases/1/lease_memberships
                    charges:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/charges
                        related: http://alever.lvh.me/api/v2/leases/1/charges
                    pets:
                      links:
                        self: http://alever.lvh.me/api/v2/leases/1/relationships/pets
                        related: http://alever.lvh.me/api/v2/leases/1/pets
  "/api/v2/lease_memberships":
    get:
      summary: list lease memberships
      security:
      - bearer_auth: []
      tags:
      - Lease Memberships
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '124'
                  type: lease_memberships
                  links:
                    self: http://alever.lvh.me/api/v2/lease_memberships/124
                  attributes:
                    role: primary_tenant
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_memberships/124/relationships/lease
                        related: http://alever.lvh.me/api/v2/lease_memberships/124/lease
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_memberships/124/relationships/tenant
                        related: http://alever.lvh.me/api/v2/lease_memberships/124/tenant
                links:
                  first: http://alever.lvh.me/api/v2/lease_memberships?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/lease_memberships?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/lease_memberships/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show lease membership
      security:
      - bearer_auth: []
      tags:
      - Lease Memberships
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '125'
                  type: lease_memberships
                  links:
                    self: http://alever.lvh.me/api/v2/lease_memberships/125
                  attributes:
                    role: primary_tenant
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_memberships/125/relationships/lease
                        related: http://alever.lvh.me/api/v2/lease_memberships/125/lease
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_memberships/125/relationships/tenant
                        related: http://alever.lvh.me/api/v2/lease_memberships/125/tenant
  "/api/v2/lease_applications":
    get:
      summary: list lease applications
      security:
      - bearer_auth: []
      tags:
      - Lease Applications
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '34'
                  type: lease_applications
                  links:
                    self: http://alever.lvh.me/api/v2/lease_applications/34
                  attributes:
                    first_name: Monroe
                    last_name: Senger5
                    email: <EMAIL>
                    phone: "(*************"
                    submitted_at: '2000-01-01T00:00:00.000-05:00'
                    approved_at:
                    rejected_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    applicants:
                    - first_name: Monroe
                      last_name: Senger5
                      email: <EMAIL>
                      phone: "(*************"
                      applicant_type: primary_applicant
                    total_monthly_income: '1000.00'
                    total_monthly_reported_income: '900.00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/property
                        related: http://alever.lvh.me/api/v2/lease_applications/34/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/unit
                        related: http://alever.lvh.me/api/v2/lease_applications/34/unit
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/lease_applications/34/floorplan
                    tenants:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/tenants
                        related: http://alever.lvh.me/api/v2/lease_applications/34/tenants
                    lease_application_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/lease_application_memberships
                        related: http://alever.lvh.me/api/v2/lease_applications/34/lease_application_memberships
                    pets:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/34/relationships/pets
                        related: http://alever.lvh.me/api/v2/lease_applications/34/pets
                links:
                  first: http://alever.lvh.me/api/v2/lease_applications?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/lease_applications?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/lease_applications/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show lease application
      security:
      - bearer_auth: []
      tags:
      - Lease Applications
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '35'
                  type: lease_applications
                  links:
                    self: http://alever.lvh.me/api/v2/lease_applications/35
                  attributes:
                    first_name: Carley
                    last_name: Auer6
                    email: <EMAIL>
                    phone: "(*************"
                    submitted_at: '2000-01-01T00:00:00.000-05:00'
                    approved_at:
                    rejected_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    applicants:
                    - first_name: Carley
                      last_name: Auer6
                      email: <EMAIL>
                      phone: "(*************"
                      applicant_type: primary_applicant
                    total_monthly_income: '1000.00'
                    total_monthly_reported_income: '900.00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/property
                        related: http://alever.lvh.me/api/v2/lease_applications/35/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/unit
                        related: http://alever.lvh.me/api/v2/lease_applications/35/unit
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/lease_applications/35/floorplan
                    tenants:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/tenants
                        related: http://alever.lvh.me/api/v2/lease_applications/35/tenants
                    lease_application_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/lease_application_memberships
                        related: http://alever.lvh.me/api/v2/lease_applications/35/lease_application_memberships
                    pets:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_applications/35/relationships/pets
                        related: http://alever.lvh.me/api/v2/lease_applications/35/pets
  "/api/v2/lease_application_memberships":
    get:
      summary: list lease application memberships
      security:
      - bearer_auth: []
      tags:
      - Lease Application Memberships
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '31'
                  type: lease_application_memberships
                  links:
                    self: http://alever.lvh.me/api/v2/lease_application_memberships/31
                  attributes:
                    applicant_type: primary_applicant
                    relation_to_primary_applicant:
                    financially_responsible: true
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease_application:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_application_memberships/31/relationships/lease_application
                        related: http://alever.lvh.me/api/v2/lease_application_memberships/31/lease_application
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_application_memberships/31/relationships/tenant
                        related: http://alever.lvh.me/api/v2/lease_application_memberships/31/tenant
                links:
                  first: http://alever.lvh.me/api/v2/lease_application_memberships?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/lease_application_memberships?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/lease_application_memberships/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show lease application membership
      security:
      - bearer_auth: []
      tags:
      - Lease Application Memberships
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '32'
                  type: lease_application_memberships
                  links:
                    self: http://alever.lvh.me/api/v2/lease_application_memberships/32
                  attributes:
                    applicant_type: primary_applicant
                    relation_to_primary_applicant:
                    financially_responsible: true
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease_application:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_application_memberships/32/relationships/lease_application
                        related: http://alever.lvh.me/api/v2/lease_application_memberships/32/lease_application
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/lease_application_memberships/32/relationships/tenant
                        related: http://alever.lvh.me/api/v2/lease_application_memberships/32/tenant
  "/api/v2/line_items":
    get:
      summary: list line items
      security:
      - bearer_auth: []
      tags:
      - Line Items
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: line_items
                  links:
                    self: http://alever.lvh.me/api/v2/line_items/1
                  attributes:
                    description: Sample Line Item
                    quantity: 2
                    unit_price: '100.00'
                    amount: '200.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    invoice:
                      links:
                        self: http://alever.lvh.me/api/v2/line_items/1/relationships/invoice
                        related: http://alever.lvh.me/api/v2/line_items/1/invoice
                links:
                  first: http://alever.lvh.me/api/v2/line_items?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/line_items?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/line_items/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show line item
      security:
      - bearer_auth: []
      tags:
      - Line Items
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1'
                  type: line_items
                  links:
                    self: http://alever.lvh.me/api/v2/line_items/1
                  attributes:
                    description: Sample Line Item
                    quantity: 2
                    unit_price: '100.00'
                    amount: '200.00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    invoice:
                      links:
                        self: http://alever.lvh.me/api/v2/line_items/1/relationships/invoice
                        related: http://alever.lvh.me/api/v2/line_items/1/invoice
  "/api/v2/listings":
    get:
      summary: list listings
      security:
      - bearer_auth: []
      tags:
      - Listings
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '7'
                  type: listings
                  links:
                    self: http://alever.lvh.me/api/v2/listings/7
                  attributes:
                    name: Roob Parks Three bedroom model E
                    description: Corrupti eveniet quia. Accusantium nesciunt est.
                      Quis quaerat eligendi.
                    amenities: []
                    market_rate: '508.46'
                    bedrooms: 1
                    bathrooms: 1.0
                    square_feet: 462
                    date_available:
                    published: true
                    published_at: '2024-01-17T11:44:03.255-05:00'
                    photo_urls: []
                    created_at: '2022-03-14T18:07:16.051-04:00'
                    updated_at: '2022-03-14T18:07:16.051-04:00'
                  relationships:
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/listings/7/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/listings/7/floorplan
                links:
                  first: http://alever.lvh.me/api/v2/listings?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/listings?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/listings/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show listing
      security:
      - bearer_auth: []
      tags:
      - Listings
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '8'
                  type: listings
                  links:
                    self: http://alever.lvh.me/api/v2/listings/8
                  attributes:
                    name: Dorsey Throughway Two bedroom model A
                    description: Repudiandae natus consequatur. Qui a vitae. Corporis
                      quo dicta.
                    amenities: []
                    market_rate: '495.97'
                    bedrooms: 1
                    bathrooms: 1.0
                    square_feet: 326
                    date_available:
                    published: true
                    published_at: '2024-01-17T11:44:03.425-05:00'
                    photo_urls: []
                    created_at: '2022-03-14T18:07:16.180-04:00'
                    updated_at: '2022-03-14T18:07:16.180-04:00'
                  relationships:
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/listings/8/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/listings/8/floorplan
  "/api/v2/management_contracts":
    get:
      summary: list management contracts
      security:
      - bearer_auth: []
      tags:
      - Management Contracts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '39'
                  type: management_contracts
                  links:
                    self: http://alever.lvh.me/api/v2/management_contracts/39
                  attributes:
                    start_date:
                    end_date:
                    executed_at:
                    minimum_amount: '0.00'
                    maintenance_limit:
                    payables_limit:
                    eligible_for_demand_letter_fees: true
                    eligible_for_eviction_fees: true
                    eligible_for_property_cash_rebalance: true
                    rent_markup:
                      percentage: '8.0'
                    parking_markup:
                    late_markup:
                    new_lease_markup:
                      fixed: '100.00'
                    renewal_markup:
                    labor_markup:
                    material_markup:
                    other_account_markups:
                    - account: 4450 - Maintenance Fees
                      percentage: '15.0'
                  relationships:
                    entity:
                      links:
                        self: http://alever.lvh.me/api/v2/management_contracts/39/relationships/entity
                        related: http://alever.lvh.me/api/v2/management_contracts/39/entity
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/management_contracts/39/relationships/properties
                        related: http://alever.lvh.me/api/v2/management_contracts/39/properties
                links:
                  first: http://alever.lvh.me/api/v2/management_contracts?page%5Bsize%5D=25
  "/api/v2/management_contracts/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show management contract
      security:
      - bearer_auth: []
      tags:
      - Management Contracts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '40'
                  type: management_contracts
                  links:
                    self: http://alever.lvh.me/api/v2/management_contracts/40
                  attributes:
                    start_date:
                    end_date:
                    executed_at:
                    minimum_amount: '0.00'
                    maintenance_limit:
                    payables_limit:
                    eligible_for_demand_letter_fees: true
                    eligible_for_eviction_fees: true
                    eligible_for_property_cash_rebalance: true
                    rent_markup:
                      percentage: '8.0'
                    parking_markup:
                    late_markup:
                    new_lease_markup:
                      fixed: '100.00'
                    renewal_markup:
                    labor_markup:
                    material_markup:
                    other_account_markups:
                    - account: 4450 - Maintenance Fees
                      percentage: '15.0'
                  relationships:
                    entity:
                      links:
                        self: http://alever.lvh.me/api/v2/management_contracts/40/relationships/entity
                        related: http://alever.lvh.me/api/v2/management_contracts/40/entity
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/management_contracts/40/relationships/properties
                        related: http://alever.lvh.me/api/v2/management_contracts/40/properties
  "/api/v2/move_outs":
    get:
      summary: list move_outs
      security:
      - bearer_auth: []
      tags:
      - Move Outs
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '18'
                  type: move_outs
                  links:
                    self: http://alever.lvh.me/api/v2/move_outs/18
                  attributes:
                    move_out_date: '1999-12-29'
                    return_of_possession_date: '1999-12-30'
                    termination_date: '1999-12-31'
                    termination_reason: other
                    termination_description: Sample description
                    walk_through_date: '1999-12-31'
                    walk_through_summary: Sample summary
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/move_outs/18/relationships/lease
                        related: http://alever.lvh.me/api/v2/move_outs/18/lease
                links:
                  first: http://alever.lvh.me/api/v2/move_outs?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/move_outs?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/move_outs/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show move_out
      security:
      - bearer_auth: []
      tags:
      - Move Outs
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '20'
                  type: move_outs
                  links:
                    self: http://alever.lvh.me/api/v2/move_outs/20
                  attributes:
                    move_out_date: '1999-12-29'
                    return_of_possession_date: '1999-12-30'
                    termination_date: '1999-12-31'
                    termination_reason: other
                    termination_description: Sample description
                    walk_through_date: '1999-12-31'
                    walk_through_summary: Sample summary
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/move_outs/20/relationships/lease
                        related: http://alever.lvh.me/api/v2/move_outs/20/lease
  "/api/v2/notices_of_non_renewal":
    get:
      summary: list notices of non renewal
      security:
      - bearer_auth: []
      tags:
      - Notices Of Non Renewal
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '5'
                  type: notices_of_non_renewal
                  links:
                    self: http://alever.lvh.me/api/v2/notices_of_non_renewal/5
                  attributes:
                    submitted_at: '2000-01-01'
                    reason: other
                    anticipated_move_out_date: '2000-07-01'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/notices_of_non_renewal/5/relationships/lease
                        related: http://alever.lvh.me/api/v2/notices_of_non_renewal/5/lease
                links:
                  first: http://alever.lvh.me/api/v2/notices_of_non_renewal?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/notices_of_non_renewal?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/notices_of_non_renewal/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show notice of non renewal
      security:
      - bearer_auth: []
      tags:
      - Notices Of Non Renewal
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '6'
                  type: notices_of_non_renewal
                  links:
                    self: http://alever.lvh.me/api/v2/notices_of_non_renewal/6
                  attributes:
                    submitted_at: '2000-01-01'
                    reason: other
                    anticipated_move_out_date: '2000-07-01'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/notices_of_non_renewal/6/relationships/lease
                        related: http://alever.lvh.me/api/v2/notices_of_non_renewal/6/lease
  "/api/v2/owners":
    get:
      summary: list owners
      security:
      - bearer_auth: []
      tags:
      - Owners
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '11'
                  type: owners
                  links:
                    self: http://alever.lvh.me/api/v2/owners/11
                  attributes:
                    first_name: Royal
                    last_name: Frami
                    email: <EMAIL>
                    phone: "(*************"
                    tags: []
                    archived_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    entities:
                      links:
                        self: http://alever.lvh.me/api/v2/owners/11/relationships/entities
                        related: http://alever.lvh.me/api/v2/owners/11/entities
                links:
                  first: http://alever.lvh.me/api/v2/owners?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/owners?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/owners/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show owner
      security:
      - bearer_auth: []
      tags:
      - Owners
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '12'
                  type: owners
                  links:
                    self: http://alever.lvh.me/api/v2/owners/12
                  attributes:
                    first_name: Tyrell
                    last_name: Bins
                    email: <EMAIL>
                    phone: "(*************"
                    tags: []
                    archived_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    entities:
                      links:
                        self: http://alever.lvh.me/api/v2/owners/12/relationships/entities
                        related: http://alever.lvh.me/api/v2/owners/12/entities
  "/api/v2/payments":
    get:
      summary: list payments
      security:
      - bearer_auth: []
      tags:
      - Payments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '11'
                  type: payments
                  links:
                    self: http://alever.lvh.me/api/v2/payments/11
                  attributes:
                    date: '2000-01-01'
                    reversal_date:
                    description: An Invoice Payment
                    amount: '85.97'
                    convenience_fee_amount: '0.00'
                    payment_method: check
                    reference_number:
                    status: completed
                    electronic_transaction_status:
                    note:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    payer:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/11/relationships/payer
                        related: http://alever.lvh.me/api/v2/payments/11/payer
                    payee:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/11/relationships/payee
                        related: http://alever.lvh.me/api/v2/payments/11/payee
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/11/relationships/lease
                        related: http://alever.lvh.me/api/v2/payments/11/lease
                    applied_payments:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/11/relationships/applied_payments
                        related: http://alever.lvh.me/api/v2/payments/11/applied_payments
                links:
                  first: http://alever.lvh.me/api/v2/payments?page%5Bsize%5D=25
  "/api/v2/payments/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show payment
      security:
      - bearer_auth: []
      tags:
      - Payments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '12'
                  type: payments
                  links:
                    self: http://alever.lvh.me/api/v2/payments/12
                  attributes:
                    date: '2000-01-01'
                    reversal_date:
                    description: An Invoice Payment
                    amount: '89.39'
                    convenience_fee_amount: '0.00'
                    payment_method: check
                    reference_number:
                    status: completed
                    electronic_transaction_status:
                    note:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    payer:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/12/relationships/payer
                        related: http://alever.lvh.me/api/v2/payments/12/payer
                    payee:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/12/relationships/payee
                        related: http://alever.lvh.me/api/v2/payments/12/payee
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/12/relationships/lease
                        related: http://alever.lvh.me/api/v2/payments/12/lease
                    applied_payments:
                      links:
                        self: http://alever.lvh.me/api/v2/payments/12/relationships/applied_payments
                        related: http://alever.lvh.me/api/v2/payments/12/applied_payments
  "/api/v2/pets":
    get:
      summary: list pets
      security:
      - bearer_auth: []
      tags:
      - Pets
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: pets
                  links:
                    self: http://alever.lvh.me/api/v2/pets/1
                  attributes:
                    name: Sparky
                    breed: Bloodhound
                    kind: dog
                    color: lemon
                    weight: 130
                    kind_detail:
                    age: 4
                    service_animal: false
                  relationships:
                    lease_application:
                      links:
                        self: http://alever.lvh.me/api/v2/pets/1/relationships/lease_application
                        related: http://alever.lvh.me/api/v2/pets/1/lease_application
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/pets/1/relationships/lease
                        related: http://alever.lvh.me/api/v2/pets/1/lease
                links:
                  first: http://alever.lvh.me/api/v2/pets?page%5Bsize%5D=25
  "/api/v2/pets/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show pet
      security:
      - bearer_auth: []
      tags:
      - Pets
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '2'
                  type: pets
                  links:
                    self: http://alever.lvh.me/api/v2/pets/2
                  attributes:
                    name: Scooter
                    breed: Toy Poodle
                    kind: dog
                    color: viridian
                    weight: 82
                    kind_detail:
                    age: 5
                    service_animal: false
                  relationships:
                    lease_application:
                      links:
                        self: http://alever.lvh.me/api/v2/pets/2/relationships/lease_application
                        related: http://alever.lvh.me/api/v2/pets/2/lease_application
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/pets/2/relationships/lease
                        related: http://alever.lvh.me/api/v2/pets/2/lease
  "/api/v2/portfolios":
    get:
      summary: list portfolios
      security:
      - bearer_auth: []
      tags:
      - Portfolios
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '4740'
                  type: portfolios
                  links:
                    self: http://alever.lvh.me/api/v2/portfolios/4740
                  attributes:
                    name: Parker, Kemmer and Prohaska
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    entities:
                      links:
                        self: http://alever.lvh.me/api/v2/portfolios/4740/relationships/entities
                        related: http://alever.lvh.me/api/v2/portfolios/4740/entities
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/portfolios/4740/relationships/properties
                        related: http://alever.lvh.me/api/v2/portfolios/4740/properties
                links:
                  first: http://alever.lvh.me/api/v2/portfolios?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/portfolios?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/portfolios/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show portfolio
      security:
      - bearer_auth: []
      tags:
      - Portfolios
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '4741'
                  type: portfolios
                  links:
                    self: http://alever.lvh.me/api/v2/portfolios/4741
                  attributes:
                    name: Bergstrom Inc
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    entities:
                      links:
                        self: http://alever.lvh.me/api/v2/portfolios/4740/relationships/entities
                        related: http://alever.lvh.me/api/v2/portfolios/4740/entities
                    properties:
                      links:
                        self: http://alever.lvh.me/api/v2/portfolios/4741/relationships/properties
                        related: http://alever.lvh.me/api/v2/portfolios/4741/properties
  "/api/v2/properties":
    get:
      summary: list properties
      security:
      - bearer_auth: []
      tags:
      - Properties
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '442'
                  type: properties
                  links:
                    self: http://alever.lvh.me/api/v2/properties/442
                  attributes:
                    name: Gerhold Knoll
                    category: apartment
                    year_built: 1994
                    custom_status:
                    custom_data: {}
                    tags: []
                    archived_at:
                    created_at: '2022-03-14T18:07:16.265-04:00'
                    updated_at: '2022-03-14T18:07:16.265-04:00'
                  relationships:
                    entity:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/442/relationships/entity
                        related: http://alever.lvh.me/api/v2/properties/442/entity
                    address:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/442/relationships/address
                        related: http://alever.lvh.me/api/v2/properties/442/address
                    floorplans:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/442/relationships/floorplans
                        related: http://alever.lvh.me/api/v2/properties/442/floorplans
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/442/relationships/units
                        related: http://alever.lvh.me/api/v2/properties/442/units
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/442/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/properties/442/work_orders
                links:
                  first: http://alever.lvh.me/api/v2/properties?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/properties?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/properties/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show property
      security:
      - bearer_auth: []
      tags:
      - Properties
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '443'
                  type: properties
                  links:
                    self: http://alever.lvh.me/api/v2/properties/443
                  attributes:
                    name: Marry Island
                    category: retail
                    year_built: 1995
                    custom_status:
                    custom_data: {}
                    tags: []
                    archived_at:
                    created_at: '2022-03-14T18:07:16.345-04:00'
                    updated_at: '2022-03-14T18:07:16.345-04:00'
                  relationships:
                    entity:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/443/relationships/entity
                        related: http://alever.lvh.me/api/v2/properties/443/entity
                    address:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/443/relationships/address
                        related: http://alever.lvh.me/api/v2/properties/443/address
                    floorplans:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/443/relationships/floorplans
                        related: http://alever.lvh.me/api/v2/properties/443/floorplans
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/443/relationships/units
                        related: http://alever.lvh.me/api/v2/properties/443/units
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/properties/443/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/properties/443/work_orders
  "/api/v2/prospects":
    get:
      summary: list prospects
      security:
      - bearer_auth: []
      tags:
      - Prospects
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '25'
                  type: prospects
                  links:
                    self: http://alever.lvh.me/api/v2/prospects/25
                  attributes:
                    first_name: Ulysses
                    last_name: Haley25
                    email: <EMAIL>
                    phone: "(*************"
                    status: Submitted Guest Card
                    source: Walk In
                    guest_card_count: 1
                    last_guest_card: '2000-01-01'
                    tour_count: 0
                    last_tour:
                    application_count: 0
                    last_application:
                    lease_count: 0
                    last_lease:
                    first_contacted_at:
                    last_contacted_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/property
                        related: http://alever.lvh.me/api/v2/prospects/25/property
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/tenant
                        related: http://alever.lvh.me/api/v2/prospects/25/tenant
                    guest_cards:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/guest_cards
                        related: http://alever.lvh.me/api/v2/prospects/25/guest_cards
                    tours:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/tours
                        related: http://alever.lvh.me/api/v2/prospects/25/tours
                    lease_applications:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/lease_applications
                        related: http://alever.lvh.me/api/v2/prospects/25/lease_applications
                    leases:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/25/relationships/leases
                        related: http://alever.lvh.me/api/v2/prospects/25/leases
                links:
                  first: http://alever.lvh.me/api/v2/prospects?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/prospects?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/prospects/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show prospect
      security:
      - bearer_auth: []
      tags:
      - Prospects
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '26'
                  type: prospects
                  links:
                    self: http://alever.lvh.me/api/v2/prospects/26
                  attributes:
                    first_name: Gerardo
                    last_name: Corkery26
                    email: <EMAIL>
                    phone: "(*************"
                    status: Submitted Guest Card
                    source: Walk In
                    guest_card_count: 1
                    last_guest_card: '2000-01-01'
                    tour_count: 0
                    last_tour:
                    application_count: 0
                    last_application:
                    lease_count: 0
                    last_lease:
                    first_contacted_at:
                    last_contacted_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/property
                        related: http://alever.lvh.me/api/v2/prospects/26/property
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/tenant
                        related: http://alever.lvh.me/api/v2/prospects/26/tenant
                    guest_cards:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/guest_cards
                        related: http://alever.lvh.me/api/v2/prospects/26/guest_cards
                    tours:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/tours
                        related: http://alever.lvh.me/api/v2/prospects/26/tours
                    lease_applications:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/lease_applications
                        related: http://alever.lvh.me/api/v2/prospects/26/lease_applications
                    leases:
                      links:
                        self: http://alever.lvh.me/api/v2/prospects/26/relationships/leases
                        related: http://alever.lvh.me/api/v2/prospects/26/leases
  "/api/v2/rent_roll":
    get:
      summary: list rent roll entries
      security:
      - bearer_auth: []
      tags:
      - Rent Roll
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1625'
                  type: rent_roll
                  links:
                    self: http://alever.lvh.me/api/v2/rent_roll/1625
                  attributes:
                    market_rate: '1000.00'
                    start_date: '1999-07-01'
                    end_date: '2000-07-01'
                    security_deposit: '0.00'
                    total_monthly_amount: '0.00'
                    monthly_rent: '0.00'
                    monthly_primary_rent: '0.00'
                    monthly_secondary_rent: '0.00'
                    monthly_other_charges: '0.00'
                    move_in_costs: '0.00'
                    base_rent: '0.00'
                    balance: '0.00'
                    first_name: Ezra
                    last_name: Grimes27
                    email: <EMAIL>
                    phone: "(*************"
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1625/relationships/property
                        related: http://alever.lvh.me/api/v2/rent_roll/1625/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1625/relationships/unit
                        related: http://alever.lvh.me/api/v2/rent_roll/1625/unit
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1625/relationships/lease
                        related: http://alever.lvh.me/api/v2/rent_roll/1625/lease
                    primary_tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1625/relationships/primary_tenant
                        related: http://alever.lvh.me/api/v2/rent_roll/1625/primary_tenant
                links:
                  first: http://alever.lvh.me/api/v2/rent_roll?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/rent_roll?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/rent_roll/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show rent roll entry
      security:
      - bearer_auth: []
      tags:
      - Rent Roll
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1626'
                  type: rent_roll
                  links:
                    self: http://alever.lvh.me/api/v2/rent_roll/1626
                  attributes:
                    market_rate: '1000.00'
                    start_date: '1999-07-01'
                    end_date: '2000-07-01'
                    security_deposit: '0.00'
                    total_monthly_amount: '0.00'
                    monthly_rent: '0.00'
                    monthly_primary_rent: '0.00'
                    monthly_secondary_rent: '0.00'
                    monthly_other_charges: '0.00'
                    move_in_costs: '0.00'
                    base_rent: '0.00'
                    balance: '0.00'
                    first_name: Mohammad
                    last_name: Durgan28
                    email: <EMAIL>
                    phone: "(*************"
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1626/relationships/property
                        related: http://alever.lvh.me/api/v2/rent_roll/1626/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1626/relationships/unit
                        related: http://alever.lvh.me/api/v2/rent_roll/1626/unit
                    lease:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1626/relationships/lease
                        related: http://alever.lvh.me/api/v2/rent_roll/1626/lease
                    primary_tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/rent_roll/1626/relationships/primary_tenant
                        related: http://alever.lvh.me/api/v2/rent_roll/1626/primary_tenant
  "/api/v2/tenants":
    get:
      summary: list tenants
      security:
      - bearer_auth: []
      tags:
      - Tenants
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '213'
                  type: tenants
                  links:
                    self: http://alever.lvh.me/api/v2/tenants/213
                  attributes:
                    first_name: Eveline
                    last_name: Reilly5
                    email: <EMAIL>
                    phone: "(*************"
                    date_of_birth: "1970-01-01"
                    status: current_resident
                    tags: []
                    created_at: '2022-07-22T08:54:10.552-04:00'
                    updated_at: '2022-07-22T08:54:10.555-04:00'
                  relationships:
                    lease_application_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/213/relationships/lease_application_memberships
                        related: http://alever.lvh.me/api/v2/tenants/213/lease_application_memberships
                    lease_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/213/relationships/lease_memberships
                        related: http://alever.lvh.me/api/v2/tenants/213/lease_memberships
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/213/relationships/units
                        related: http://alever.lvh.me/api/v2/tenants/213/units
                links:
                  first: http://alever.lvh.me/api/v2/tenants?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/tenants?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/tenants/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show tenant
      security:
      - bearer_auth: []
      tags:
      - Tenants
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '214'
                  type: tenants
                  links:
                    self: http://alever.lvh.me/api/v2/tenants/214
                  attributes:
                    first_name: Felica
                    last_name: Haag6
                    email: <EMAIL>
                    phone: "(*************"
                    date_of_birth: "1970-01-01"
                    status: current_resident
                    tags: []
                    created_at: '2022-07-22T08:54:10.645-04:00'
                    updated_at: '2022-07-22T08:54:10.648-04:00'
                  relationships:
                    lease_application_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/214/relationships/lease_application_memberships
                        related: http://alever.lvh.me/api/v2/tenants/214/lease_application_memberships
                    lease_memberships:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/214/relationships/lease_memberships
                        related: http://alever.lvh.me/api/v2/tenants/214/lease_memberships
                    units:
                      links:
                        self: http://alever.lvh.me/api/v2/tenants/214/relationships/units
                        related: http://alever.lvh.me/api/v2/tenants/214/units
  "/api/v2/tours":
    get:
      summary: list tours
      security:
      - bearer_auth: []
      tags:
      - Tours
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '15'
                  type: tours
                  links:
                    self: http://alever.lvh.me/api/v2/tours/15
                  attributes:
                    first_name: Amado
                    last_name: Denesik11
                    email: <EMAIL>
                    phone: "(*************"
                    scheduled_time: '2000-01-03T00:00:00.000-05:00'
                    message:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/15/relationships/property
                        related: http://alever.lvh.me/api/v2/tours/15/property
                    guest_card:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/15/relationships/guest_card
                        related: http://alever.lvh.me/api/v2/tours/15/guest_card
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/15/relationships/tenant
                        related: http://alever.lvh.me/api/v2/tours/15/tenant
                links:
                  first: http://alever.lvh.me/api/v2/tours?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/tours?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/tours/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show tour
      security:
      - bearer_auth: []
      tags:
      - Tours
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '16'
                  type: tours
                  links:
                    self: http://alever.lvh.me/api/v2/tours/16
                  attributes:
                    first_name: Filiberto
                    last_name: Dietrich12
                    email: <EMAIL>
                    phone: "(*************"
                    scheduled_time: '2000-01-03T00:00:00.000-05:00'
                    message:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/16/relationships/property
                        related: http://alever.lvh.me/api/v2/tours/16/property
                    guest_card:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/16/relationships/guest_card
                        related: http://alever.lvh.me/api/v2/tours/16/guest_card
                    tenant:
                      links:
                        self: http://alever.lvh.me/api/v2/tours/16/relationships/tenant
                        related: http://alever.lvh.me/api/v2/tours/16/tenant
  "/api/v2/units":
    get:
      summary: list units
      security:
      - bearer_auth: []
      tags:
      - Units
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '183'
                  type: units
                  links:
                    self: http://alever.lvh.me/api/v2/units/183
                  attributes:
                    name: 2656 Champlin Ville Suite 951
                    bedrooms: 5
                    bathrooms: 3.0
                    square_feet: 217
                    floor: 1
                    market_rate: '735.38'
                    status: rented
                    created_at: '2022-07-22T08:56:29.395-04:00'
                    updated_at: '2022-07-22T08:56:29.395-04:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/units/183/relationships/property
                        related: http://alever.lvh.me/api/v2/units/183/property
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/units/183/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/units/183/floorplan
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/units/183/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/units/183/work_orders
                    leases:
                      links:
                        self: http://alever.lvh.me/api/v2/units/183/relationships/leases
                        related: http://alever.lvh.me/api/v2/units/183/leases
                links:
                  first: http://alever.lvh.me/api/v2/units?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/units?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/units/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show unit
      security:
      - bearer_auth: []
      tags:
      - Units
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '184'
                  type: units
                  links:
                    self: http://alever.lvh.me/api/v2/units/184
                  attributes:
                    name: 69080 Maricela Overpass Apt. 944
                    bedrooms: 2
                    bathrooms: 1.0
                    square_feet: 950
                    floor: 1
                    market_rate: '649.85'
                    status: rented
                    created_at: '2022-07-22T08:56:29.531-04:00'
                    updated_at: '2022-07-22T08:56:29.531-04:00'
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/units/184/relationships/property
                        related: http://alever.lvh.me/api/v2/units/184/property
                    floorplan:
                      links:
                        self: http://alever.lvh.me/api/v2/units/184/relationships/floorplan
                        related: http://alever.lvh.me/api/v2/units/184/floorplan
                    work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/units/184/relationships/work_orders
                        related: http://alever.lvh.me/api/v2/units/184/work_orders
                    leases:
                      links:
                        self: http://alever.lvh.me/api/v2/units/184/relationships/leases
                        related: http://alever.lvh.me/api/v2/units/184/leases
  "/api/v2/vendor_assignments":
    get:
      summary: list vendor assignments
      security:
      - bearer_auth: []
      tags:
      - Vendor Assignments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '13'
                  type: vendor_assignments
                  links:
                    self: http://alever.lvh.me/api/v2/vendor_assignments/13
                  relationships:
                    vendor:
                      links:
                        self: http://alever.lvh.me/api/v2/vendor_assignments/13/relationships/vendor
                        related: http://alever.lvh.me/api/v2/vendor_assignments/13/vendor
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/vendor_assignments/13/relationships/work_order
                        related: http://alever.lvh.me/api/v2/vendor_assignments/13/work_order
                links:
                  first: http://alever.lvh.me/api/v2/vendor_assignments?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/vendor_assignments?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/vendor_assignments/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show vendor assignment
      security:
      - bearer_auth: []
      tags:
      - Vendor Assignments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '14'
                  type: vendor_assignments
                  links:
                    self: http://alever.lvh.me/api/v2/vendor_assignments/14
                  relationships:
                    vendor:
                      links:
                        self: http://alever.lvh.me/api/v2/vendor_assignments/14/relationships/vendor
                        related: http://alever.lvh.me/api/v2/vendor_assignments/14/vendor
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/vendor_assignments/14/relationships/work_order
                        related: http://alever.lvh.me/api/v2/vendor_assignments/14/work_order
  "/api/v2/vendors":
    get:
      summary: list vendors
      security:
      - bearer_auth: []
      tags:
      - Vendors
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '166'
                  type: vendors
                  links:
                    self: http://alever.lvh.me/api/v2/vendors/166
                  attributes:
                    name: Sample Vendor
                    category: contractor
                    email:
                    phone:
                    website: www.samplevendor.com
                    notes:
                    tags:
                    - Sample
                    archived_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    address:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/166/relationships/address
                        related: http://alever.lvh.me/api/v2/vendors/166/address
                    vendor_contacts:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/166/relationships/vendor_contacts
                        related: http://alever.lvh.me/api/v2/vendors/166/vendor_contacts
                    insurance_policies:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/166/relationships/insurance_policies
                        related: http://alever.lvh.me/api/v2/vendors/166/insurance_policies
                    assigned_work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/166/relationships/assigned_work_orders
                        related: http://alever.lvh.me/api/v2/vendors/166/assigned_work_orders
                links:
                  first: http://alever.lvh.me/api/v2/vendors?page%5Bsize%5D=25
  "/api/v2/vendors/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show vendor
      security:
      - bearer_auth: []
      tags:
      - Vendors
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '168'
                  type: vendors
                  links:
                    self: http://alever.lvh.me/api/v2/vendors/168
                  attributes:
                    name: Sample Vendor
                    category: contractor
                    email:
                    phone:
                    website: www.samplevendor.com
                    notes:
                    tags:
                    - Sample
                    archived_at:
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    address:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/168/relationships/address
                        related: http://alever.lvh.me/api/v2/vendors/168/address
                    vendor_contacts:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/168/relationships/vendor_contacts
                        related: http://alever.lvh.me/api/v2/vendors/168/vendor_contacts
                    insurance_policies:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/168/relationships/insurance_policies
                        related: http://alever.lvh.me/api/v2/vendors/168/insurance_policies
                    assigned_work_orders:
                      links:
                        self: http://alever.lvh.me/api/v2/vendors/168/relationships/assigned_work_orders
                        related: http://alever.lvh.me/api/v2/vendors/168/assigned_work_orders
  "/api/v2/work_orders":
    get:
      summary: list work orders
      security:
      - bearer_auth: []
      tags:
      - Work Orders
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '60'
                  type: work_orders
                  links:
                    self: http://alever.lvh.me/api/v2/work_orders/60
                  attributes:
                    number: '60'
                    subject: Broken Television
                    description: Est occaecati animi. Laudantium dolorem praesentium.
                    status: new
                    urgency: normal
                    cost: '0.00'
                    custom_data: {}
                    tags: []
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    closed_at:
                    approval_requested_at:
                    approved_at:
                    rejected_at:
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/property
                        related: http://alever.lvh.me/api/v2/work_orders/60/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/unit
                        related: http://alever.lvh.me/api/v2/work_orders/60/unit
                    assigned_employees:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/assigned_employees
                        related: http://alever.lvh.me/api/v2/work_orders/60/assigned_employees
                    vendor_assignments:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/vendor_assignments
                        related: http://alever.lvh.me/api/v2/work_orders/60/vendor_assignments
                    assigned_vendors:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/assigned_vendors
                        related: http://alever.lvh.me/api/v2/work_orders/60/assigned_vendors
                    invoices:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/invoices
                        related: http://alever.lvh.me/api/v2/work_orders/60/invoices
                    appointment:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/60/relationships/appointment
                        related: http://alever.lvh.me/api/v2/work_orders/60/appointment
                links:
                  first: http://alever.lvh.me/api/v2/work_orders?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/work_orders?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/work_orders/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show work order
      security:
      - bearer_auth: []
      tags:
      - Work Orders
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '62'
                  type: work_orders
                  links:
                    self: http://alever.lvh.me/api/v2/work_orders/62
                  attributes:
                    number: '62'
                    subject: Broken Refrigerator
                    description: Et cum a. Enim placeat consequatur. Minus asperiores
                    status: new
                    urgency: normal
                    cost: '0.00'
                    custom_data: {}
                    tags: []
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    closed_at:
                    approval_requested_at:
                    approved_at:
                    rejected_at:
                  relationships:
                    property:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/property
                        related: http://alever.lvh.me/api/v2/work_orders/62/property
                    unit:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/unit
                        related: http://alever.lvh.me/api/v2/work_orders/62/unit
                    assigned_employees:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/assigned_employees
                        related: http://alever.lvh.me/api/v2/work_orders/62/assigned_employees
                    vendor_assignments:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/vendor_assignments
                        related: http://alever.lvh.me/api/v2/work_orders/62/vendor_assignments
                    assigned_vendors:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/assigned_vendors
                        related: http://alever.lvh.me/api/v2/work_orders/62/assigned_vendors
                    invoices:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/invoices
                        related: http://alever.lvh.me/api/v2/work_orders/62/invoices
                    appointment:
                      links:
                        self: http://alever.lvh.me/api/v2/work_orders/62/relationships/appointment
                        related: http://alever.lvh.me/api/v2/work_orders/62/appointment
  "/api/v2/work_order_appointments":
    get:
      summary: list work order appointments
      security:
      - bearer_auth: []
      tags:
      - Work Order Appointments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '21'
                  type: work_order_appointments
                  links:
                    self: http://alever.lvh.me/api/v2/work_order_appointments/21
                  attributes:
                    scheduled_for: '2000-01-04T00:00:00.000-05:00'
                    window_hours: '2.0'
                    notify_owner: false
                    notify_tenant: false
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/work_order_appointments/21/relationships/work_order
                        related: http://alever.lvh.me/api/v2/work_order_appointments/21/work_order
                links:
                  first: http://alever.lvh.me/api/v2/work_order_appointments?page%5Bsize%5D=25
  "/api/v2/work_order_appointments/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show work order appointments
      security:
      - bearer_auth: []
      tags:
      - Work Order Appointments
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '22'
                  type: work_order_appointments
                  links:
                    self: http://alever.lvh.me/api/v2/work_order_appointments/22
                  attributes:
                    scheduled_for: '2000-01-04T00:00:00.000-05:00'
                    window_hours: '2.0'
                    notify_owner: false
                    notify_tenant: false
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    work_order:
                      links:
                        self: http://alever.lvh.me/api/v2/work_order_appointments/22/relationships/work_order
                        related: http://alever.lvh.me/api/v2/work_order_appointments/22/work_order
  "/api/v2/accounting/journals":
    get:
      summary: list journals
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1'
                  type: journals
                  links:
                    self: http://alever.lvh.me/api/v2/accounting/journals/1
                  attributes:
                    name: Sample Entity, LLC
                    closed_through: '1999-12-31'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at:
                links:
                  first: http://alever.lvh.me/api/v2/accounting/journals?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/accounting/journals?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/accounting/journals/{id}":
    parameters:
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show journal
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1'
                  type: journals
                  links:
                    self: http://alever.lvh.me/api/v2/accounting/journals/1
                  attributes:
                    name: Sample Entity, LLC
                    closed_through: '1999-12-31'
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at:
  "/api/v2/accounting/journals/{journal_id}/accounts":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    get:
      summary: list accounts
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Accounts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1910'
                  type: accounts
                  attributes:
                    gl_code: '4000'
                    name: Rent Income
                    description:
                    account_type: Revenue
                    category: Rent
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                links:
                  first: http://alever.lvh.me/api/v2/accounting/journals/123/accounts?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/accounting/journals/123/accounts?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/accounting/journals/{journal_id}/accounts/{id}":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show account
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Accounts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1911'
                  type: accounts
                  attributes:
                    gl_code: '4000'
                    name: Rent Income
                    description:
                    account_type: Revenue
                    category: Rent
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
  "/api/v2/accounting/journals/{journal_id}/amounts":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    get:
      summary: list amounts
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Amounts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '1357'
                  type: amounts
                  attributes:
                    direction: credit
                    amount: '1.0'
                - id: '1358'
                  type: amounts
                  attributes:
                    direction: debit
                    amount: '1.0'
                links:
                  first: http://alever.lvh.me/api/v2/accounting/journals/123/amounts?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/accounting/journals/123/amounts?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/accounting/journals/{journal_id}/amounts/{id}":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show amount
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Amounts
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '1360'
                  type: amounts
                  attributes:
                    direction: debit
                    amount: '1.0'
  "/api/v2/accounting/journals/{journal_id}/entries":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    get:
      summary: list entries
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Entries
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '679'
                  type: entries
                  attributes:
                    date: '2000-01-01'
                    description: Electrical repair
                    reference: R1234
                    document_type: invoice
                    closing_entry: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                links:
                  first: http://alever.lvh.me/api/v2/accounting/journals/123/entries?page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/accounting/journals/123/entries?page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/accounting/journals/{journal_id}/entries?include=amounts,amounts.account":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    get:
      summary: list entries with amounts and accounts
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Entries
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                - id: '680'
                  type: entries
                  attributes:
                    date: '2000-01-01'
                    description: Electrical repair
                    reference: R1234
                    document_type: invoice
                    closing_entry: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    amounts:
                      data:
                      - type: amounts
                        id: '1363'
                      - type: amounts
                        id: '1364'
                included:
                - id: '1363'
                  type: amounts
                  attributes:
                    direction: credit
                    amount: '1.0'
                  relationships:
                    account:
                      data:
                        type: accounts
                        id: '1918'
                    entry:
                      data:
                        type: entries
                        id: '680'
                - id: '1364'
                  type: amounts
                  attributes:
                    direction: debit
                    amount: '1.0'
                  relationships:
                    account:
                      data:
                        type: accounts
                        id: '1919'
                    entry:
                      data:
                        type: entries
                        id: '680'
                - id: '1918'
                  type: accounts
                  attributes:
                    gl_code: '5000'
                    name: Electrical
                    description:
                    account_type: Expense
                    category: Expense
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                - id: '1919'
                  type: accounts
                  attributes:
                    gl_code: '1000'
                    name: Operating
                    description:
                    account_type: Asset
                    category: Current Asset
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                links:
                  first: http://alever.lvh.me/api/v2/accounting/journals/123/entries?include=amounts%2Camounts.account&page%5Bnumber%5D=1&page%5Bsize%5D=25
                  last: http://alever.lvh.me/api/v2/accounting/journals/123/entries?include=amounts%2Camounts.account&page%5Bnumber%5D=1&page%5Bsize%5D=25
  "/api/v2/accounting/journals/{journal_id}/entries/{id}":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show entry
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Entries
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '681'
                  type: entries
                  attributes:
                    date: '2000-01-01'
                    description: Electrical expense
                    reference: R1234
                    document_type: invoice
                    closing_entry: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
  "/api/v2/accounting/journals/{journal_id}/entries/{id}?include=amounts,amounts.account":
    parameters:
    - name: journal_id
      in: path
      description: journal id
      required: true
      schema:
        type: integer
    - name: id
      in: path
      description: id
      required: true
      schema:
        type: integer
    get:
      summary: show entry with amounts and accounts
      security:
      - bearer_auth: []
      tags:
      - Accounting/Journals/{Id}/Entries
      responses:
        '200':
          description: successful
          content:
            application/vnd.api+json:
              example:
                data:
                  id: '682'
                  type: entries
                  attributes:
                    date: '2000-01-01'
                    description: Electrical expense
                    reference: R1234
                    document_type: invoice
                    closing_entry: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                  relationships:
                    amounts:
                      data:
                      - type: amounts
                        id: '1367'
                      - type: amounts
                        id: '1368'
                included:
                - id: '1367'
                  type: amounts
                  attributes:
                    direction: credit
                    amount: '1.0'
                  relationships:
                    account:
                      data:
                        type: accounts
                        id: '1922'
                    entry:
                      data:
                        type: entries
                        id: '682'
                - id: '1368'
                  type: amounts
                  attributes:
                    direction: debit
                    amount: '1.0'
                  relationships:
                    account:
                      data:
                        type: accounts
                        id: '1923'
                    entry:
                      data:
                        type: entries
                        id: '682'
                - id: '1922'
                  type: accounts
                  attributes:
                    gl_code: '5000'
                    name: Electrical
                    description:
                    account_type: Expense
                    category: Expense
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
                - id: '1923'
                  type: accounts
                  attributes:
                    gl_code: '1000'
                    name: Operating
                    description:
                    account_type: Asset
                    category: Current Asset
                    header:
                    contra: false
                    created_at: '2000-01-01T00:00:00.000-05:00'
                    updated_at: '2000-01-01T00:00:00.000-05:00'
servers:
- url: https://{defaultHost}
  variables:
    defaultHost:
      default: www.revela.co
