#!/usr/bin/env ruby

# Usage: ruby scripts/static/features.rb | tee features.tsv

# rubocop:disable Rails

require 'amazing_print'
require 'date'
require 'prism'

class FeatureVisitor < Prism::Visitor
  attr_reader :usages
  attr_accessor :file_path

  def initialize
    @usages = []
    super
  end

  def visit_call_node(node)
    case node
    in {
      receiver: { name: :Feature },
      name: :enabled?,
      arguments: { arguments: [{ unescaped: feature }, *] }
    }
      usages << FeatureUsage.new(feature, @file_path, node)
    else
      # Pass
    end

    super
  end

  FeatureUsage = Struct.new(:feature, :file_path, :node) do
    def committed_at
      output = `git blame --line-porcelain -L#{line_number},#{line_number} #{file_path}`

      committer_time = output.split("\n").find do |line|
        line.start_with?('committer-time')
      end

      return nil unless committer_time

      epoch = committer_time.split.last.to_i

      Time.at(epoch).to_date
    end

    def line_number
      node.location.start_line
    end
  end
end

visitor = FeatureVisitor.new

source_files = Dir.glob('**/*.{rb,haml}')

source_files.each do |file_path|
  visitor.file_path = file_path

  Prism.parse_file(file_path).value.accept(visitor)
end

puts %w[Feature Oldest Count Paths].join("\t")

visitor.usages.group_by(&:feature).each do |feature, usages|
  oldest = usages.filter_map(&:committed_at).min
  count = usages.count
  paths = usages.map(&:file_path).uniq.join(' ')

  puts [feature, oldest, count, paths].join("\t")
end

# rubocop:enable Rails
