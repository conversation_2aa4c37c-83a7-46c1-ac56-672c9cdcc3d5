#!/usr/bin/env ruby

selected = ARGV[0]

if selected.nil?
  warn 'Provide cop as command line argument'
  exit 1
end

path = File.expand_path('../../.rubocop_todo.yml', __dir__)

cops = {}

start = nil
cop = nil

lines = File.readlines(path)

lines.each.with_index do |line, index|
  if line.chomp.strip.empty? || index == lines.size - 1
    cops[cop] = [start, index] if cop

    # Reset
    start = nil
    cop = nil
  elsif line.start_with?(/[A-Z]/)
    # Identifier
    cop = line.delete(':').chomp.strip
  else
    # Starting comment
    start ||= index
  end
end

start, stop = cops.fetch(selected)

# Remove exclusion from .rubocop_todo.yml
contents = File.read(path)

File.open(path, 'w') do |f|
  contents.split("\n").each.with_index do |line, index|
    next if (start..stop).cover?(index)

    f.puts line
  end
end

system('bundle', 'exec', 'rubocop', '-A', '--only', selected)
