#!/usr/bin/env ruby

# rubocop:disable Layout/LineLength

TARGET_VERSION_NUMBER = '116'.freeze

require 'open-uri'
require 'json'

url = 'https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json'

json = URI.open(url).read

data = JSON.parse json

version_filter = ->(version) { version['version'].start_with?(TARGET_VERSION_NUMBER) }

target_version = data['versions'].reverse.find(&version_filter)

downloads = target_version['downloads']

platform_filter = ->(version) { version['platform'] == 'linux64' }

get_url = ->(download) { download.find(&platform_filter)['url'] }

target_chrome = get_url[downloads['chrome']]
target_chromedriver = get_url[downloads['chromedriver']]

[target_chrome, target_chromedriver].each do |download_url|
  filename = download_url.split('/').last

  puts "Downloading #{download_url}"

  system 'wget', '-P', '/tmp', download_url
  system 'unzip', '-d', '/opt', "/tmp/#{filename}"
  system 'rm', "/tmp/#{filename}"
end

system 'ln', '-s', '/opt/chrome-linux64/chrome', '/usr/bin/google-chrome'
system 'ln', '-s', '/opt/chromedriver-linux64/chromedriver', '/usr/bin/chromedriver'

# rubocop:enable Layout/LineLength
