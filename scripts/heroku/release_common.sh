#!/usr/bin/env zsh
set -e

BRANCH=master
STAGING=vast-beyond-79022
PRODUCTION=still-plateau-99181
COMMIT=$(git rev-parse --short --verify $BRANCH)

echo "Ensure that $COMMIT is up to date and deployed to $STAGING."

heroku pipelines:diff -a $STAGING

echo -n "Release $STAGING to $PRODUCTION? (y/n)"

read CONT
if [[ $CONT == "y" ]]; then
  heroku pipelines:promote -a $STAGING -t $PRODUCTION
else
  echo "Exiting.";
fi
