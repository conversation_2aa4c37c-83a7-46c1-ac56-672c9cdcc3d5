#!/usr/bin/env ruby

require 'aws-sdk-s3'
require 'date'
require 'fileutils'
require 'optparse'
require 'open3'
require 'io/console'
require 'debug'
require 'concurrent'

# Options and Defaults
options = {
  schemas: [],
  date: nil,
  download_path: 'backup_restore',
  database: nil,
  pg_host: nil,
  pg_port: nil,
  pg_user: nil,
  skip_data: false,
  exclude_tables: [],
  create: false
}

OptionParser.new do |opts|
  opts.banner = 'Usage: restore.rb [options]'

  opts.on('-s', '--schemas x,y,z', Array, 'Schemas to restore') { |s| options[:schemas] = s }
  opts.on('-d', '--date YYYY-MM-DD', 'Backup date') { |d| options[:date] = Date.parse(d) }
  opts.on('-p', '--path PATH', 'Download path') { |p| options[:download_path] = p }
  opts.on('-n', '--name DB_NAME', 'Target database name') { |n| options[:database] = n }
  opts.on('--host HOST', 'Postgres host') { |h| options[:pg_host] = h }
  opts.on('--port PORT', 'Postgres port') { |port| options[:pg_port] = port }
  opts.on('--user USER', 'Postgres user') { |u| options[:pg_user] = u }
  opts.on('--create',
          'Uses the create method in pg_restore to use the existing database name from the backup') do |v|
    options[:create] = v
  end
  opts.on('--skip-data', 'Skip data restore and only refresh materialized views') do |v|
    options[:skip_data] = v
  end
  opts.on('--exclude-tables x,y,z', Array, 'Tables to exclude during data restore') do |t|
    options[:exclude_tables] = t
  end
  opts.on('--aws-region REGION', 'AWS region (default: us-east-1)') { |r| options[:aws_region] = r }
  opts.on('--aws-profile PROFILE', 'AWS profile (default: default)') do |p|
    options[:aws_profile] = p
  end
  opts.on('--s3-bucket BUCKET', 'S3 bucket (default: revela-data-lake)') do |b|
    options[:s3_bucket] = b
  end
  opts.on('--password', 'Prompt for Postgres password (sets PGPASSWORD env var)') do
    print 'Enter Postgres password: '
    ENV['PGPASSWORD'] = STDIN.noecho(&:gets).chomp.tap { puts }
  end
end.parse!

schemas = options[:schemas]
backup_date = options[:date] || Date.today
download_path = options[:download_path]
database = options[:database] || "prod_#{backup_date.strftime('%Y_%m_%d')}"
pg_flags = []
pg_flags << "-h #{options[:pg_host]}" if options[:pg_host]
pg_flags << "-p #{options[:pg_port]}" if options[:pg_port]
pg_flags << "-U #{options[:pg_user]}" if options[:pg_user]
exclude_tables = options[:exclude_tables] || []

# Configuration
region = options[:aws_region] || ENV.fetch('AWS_REGION', 'us-east-1')
profile = options[:aws_profile] || ENV.fetch('AWS_PROFILE', 'default')
bucket = options[:s3_bucket] || ENV.fetch('S3_BACKUP_BUCKET', 'revela-data-lake')

s3 = Aws::S3::Client.new(profile: profile, region: region)
post_data_futures = []
max_concurrent_restores = 4 # Adjust this based on available resources
pool = Concurrent::FixedThreadPool.new(max_concurrent_restores)

def log(message)
  puts "[#{Time.now.strftime('%Y-%m-%d %H:%M:%S')}] #{message}"
end

BackupFile = Data.define(:key, :schemas)

SchemaList = Data.define(:key) do
  def schemas(s3, bucket)
    s3.get_object(bucket: bucket, key: key).body.read.split("\n")
  end

  def backup_file(s3, bucket)
    BackupFile.new(key: key.gsub('_schemas.txt', '.tar.gz'), schemas: schemas(s3, bucket))
  end
end

class BackupsFolder
  attr_reader :date, :s3, :bucket

  def initialize(date:, s3:, bucket:)
    @date = date
    @s3 = s3
    @bucket = bucket
  end

  def prefix
    "raw/prod/backups/#{date.strftime('%Y/%m/%d')}/"
  end

  def backup_files_for(schemas_to_restore)
    individual_files = individual_schema_backup_files.select do |file|
      (file.schemas & schemas_to_restore).any?
    end

    remaining_schemas = schemas_to_restore - individual_files.flat_map(&:schemas)

    batch_files = batch_schema_backup_files.select do |file|
      (file.schemas & remaining_schemas).any?
    end

    (individual_files + batch_files).uniq
  end

  def individual_schema_backup_files
    contents.filter_map do |entry|
      key = entry.key
      next unless key.end_with?('.tar.gz')
      next if key.include?('_batch') # skip batch files here

      parts = key.split('_')
      schema = parts[1] # Assume filename like dste7aoc3qt3s_<schema>_<timestamp>.tar.gz

      BackupFile.new(key: key, schemas: [schema])
    end
  end

  private

  def contents
    @contents ||= s3.list_objects_v2(bucket: bucket, prefix: prefix).contents
  end

  def schema_lists
    contents.filter_map do |entry|
      key = entry.key
      next unless key.end_with?('_schemas.txt')

      SchemaList.new(key: key)
    end
  end

  def batch_schema_backup_files
    schema_lists.map { |sl| sl.backup_file(s3, bucket) }
  end
end

def tune_postgres_for_restore(pg_flags)
  settings = {
    'max_wal_size' => '4GB',
    'checkpoint_timeout' => '30min',
    'checkpoint_completion_target' => '0.9',
    'wal_level' => 'minimal',
    'fsync' => 'off',
    'full_page_writes' => 'off',
    'synchronous_commit' => 'off'
  }

  settings.each do |param, value|
    cmd = ['psql', *pg_flags, '-d', 'postgres', '-c',
           "\"ALTER SYSTEM SET #{param} = '#{value}';\""].join(' ')
    stdout, stderr, status = Open3.capture3(cmd)
    puts "#{Time.now.iso8601} - Set #{param}=#{value}" unless status.success?
  end

  reload_cmd = ['psql', *pg_flags, '-d', 'postgres', '-c', '"SELECT pg_reload_conf();"'].join(' ')
  Open3.capture3(reload_cmd)
end

def reset_postgres_config(pg_flags)
  params = %w[
    max_wal_size
    checkpoint_timeout
    checkpoint_completion_target
    wal_level
    fsync
    full_page_writes
    synchronous_commit
  ]

  params.each do |param|
    cmd = ['psql', *pg_flags, '-d', 'postgres', '-c', "\"ALTER SYSTEM RESET #{param};\""].join(' ')
    stdout, stderr, status = Open3.capture3(cmd)
    if status.success?
      log "Reset #{param}"
    else
      log "Failed to reset #{param}: #{stderr.strip}"
    end
  end

  # Reload config
  reload_cmd = ['psql', *pg_flags, '-d', 'postgres', '-c', '"SELECT pg_reload_conf();"'].join(' ')
  stdout, stderr, status = Open3.capture3(reload_cmd)
  if status.success?
    log 'Postgres config reloaded'
  else
    log "Failed to reload config: #{stderr.strip}"
  end
end

# Restore Setup
unless options[:skip_data]
  log "Dropping and creating database: #{database} (using host: #{options[:pg_host]}, user: #{options[:pg_user]})"
  system("dropdb #{pg_flags.join(' ')} --if-exists \"#{database}\"")
  if options[:create]
    log 'Using --create flag, database will be created by pg_restore'
  else
    system("createdb #{pg_flags.join(' ')} \"#{database}\"") || abort("Failed to create database \"#{database}\"")
  end
  tune_postgres_for_restore(pg_flags)
end

folder = BackupsFolder.new(date: backup_date, s3: s3, bucket: bucket)
backup_files =
  if schemas.empty?
    folder.send(:individual_schema_backup_files) + folder.send(:batch_schema_backup_files)
  else
    folder.backup_files_for(schemas)
  end
FileUtils.mkdir_p(download_path)

Dir.chdir(download_path) do
  backup_files.each do |backup_file|
    key = backup_file.key
    target = File.basename(key)
    log "Downloading #{key} to #{target}..."
    next log 'Already exists, skipping.' if File.exist?(target)

    s3.get_object(bucket: bucket, key: key, response_target: target)
    log 'Done.'
  end

  backup_files.each do |backup_file|
    filename = File.basename(backup_file.key)
    folder_name = filename.split('.').first
    log "Extracting #{filename} to #{folder_name}..."
    system("tar -xzf #{filename}") || abort("Failed to extract #{filename}")

    next if options[:skip_data]

    # If --create is used, get the database name from the backup file
    current_database = if options[:create]
                         # First try to get it from the archive header
                         db_name = folder_name.split('_').first
                         if db_name
                           log "Using database name from backup: #{db_name}"
                           db_name
                         else
                           log "Warning: Could not determine database name from backup, using provided name: #{database}"
                           database
                         end
                       else
                         database
                       end
    connection_database = options[:create] ? 'postgres' : current_database

    # First restore all pre-data (schemas, types, functions) without filtering matviews
    log 'Restoring pre-data (schemas, types, functions) for all schemas...'
    create_flag = options[:create] ? '--create' : ''
    restore_pre_cmd = "pg_restore #{pg_flags.join(' ')} #{create_flag} --no-password --no-acl --no-owner --section=pre-data -d #{connection_database} #{folder_name}"
    system(restore_pre_cmd)

    # Then restore data at once, filtering out materialized views and limiting to specific schemas if requested
    schemas_to_restore = schemas.empty? ? backup_file.schemas : (schemas & backup_file.schemas)
    log "Restoring data for schemas: #{schemas_to_restore.join(', ')}"
    log "Excluding tables: #{exclude_tables.join(', ')}" if exclude_tables.any?

    list_file = 'all_schemas_toc.sql'
    full_list_cmd = "pg_restore #{pg_flags.join(' ')} -l #{folder_name} > #{list_file}"
    system(full_list_cmd)

    filtered_list = File.read(list_file).lines.reject do |line|
      # Skip comments and empty lines
      next true if line.start_with?(';') || line.strip.empty?

      # Skip materialized views
      next true if line.include?('MATERIALIZED VIEW')

      # Check for excluded tables
      next true if exclude_tables.any? { |t| schema_table.include?(".#{t}") }

      # Keep the line if it doesn't match any exclusion criteria
      false
    end

    filtered_file_list = "#{filename}_filtered.list"
    File.write(filtered_file_list, filtered_list.join)

    restore_data_cmd = "pg_restore #{pg_flags.join(' ')} -j 8 -L #{filtered_file_list} --no-password --no-acl --no-owner --section=data -d #{current_database} #{folder_name}"
    system(restore_data_cmd)
  end

  unless options[:skip_data]
    log 'Running VACUUM ANALYZE...'
    system("psql #{pg_flags.join(' ')} -d #{database} -c \"VACUUM ANALYZE;\"")
  end

  # Recreate materialized views by creating NO DATA then refreshing CONCURRENTLY
  log 'Recreating materialized views with NO DATA + CONCURRENT REFRESH...'
  backup_files.each do |backup_file|
    current_database = if options[:create]
                         # First try to get it from the archive header
                         db_name = File.basename(backup_file.key).split('_').first
                         if db_name
                           log "Using database name from backup: #{db_name}"
                           db_name
                         else
                           log "Warning: Could not determine database name from backup, using provided name: #{database}"
                           database
                         end
                       else
                         database
                       end
    backup_file.schemas.each do |schema|
      next if !schemas.empty? && !schemas.include?(schema)

      file_name = File.basename(backup_file.key).split('.').first

      ddl_file = "#{schema}_toc.sql"
      pg_restore_cmd = ['pg_restore', *pg_flags, '-s', '-n', schema, '-f', ddl_file, file_name]
      system(pg_restore_cmd.join(' '))

      next unless File.exist?(ddl_file)

      matview_ddls = File.read(ddl_file).split(";\n").select do |line|
        line =~ /CREATE MATERIALIZED VIEW/i
      end

      schemas_to_restore = schemas.empty? ? backup_file.schemas : (schemas & backup_file.schemas)
      schemas_to_restore.each do |schema|
        # Create filtered list first
        list_cmd = "pg_restore -l #{file_name} | grep -v 'MATERIALIZED VIEW' > #{schema}_filtered.list"
        system(list_cmd)

        # Create future for restore
        post_data_futures << Concurrent::Future.execute(executor: pool) do
          restore_cmd = "pg_restore #{pg_flags.join(' ')}  --no-password --no-acl --no-owner -n \"#{schema}\" --section=post-data -d #{current_database} #{file_name}"
          start = Time.now
          stdout, stderr, status = Open3.capture3(restore_cmd)
          if status.success? || stderr.include?('pg_restore: warning: errors ignored on restore:')
            log "Completed post-data restore for #{schema} in #{Time.now - start}s"
          else
            log "Failed post-data restore for #{schema}"
            log "Exit code: #{status.exitstatus}"
            log "Error message: #{stderr.strip}"
            log "Output: #{stdout.strip}" unless stdout.strip.empty?
          end
        end
      end

      threads = []
      matview_ddls.each do |_ddl|
        threads << Thread.new do
          _ddl.strip!
          # Handle both quoted and unquoted schema/view names
          # Matches: schema.view, "schema".view, schema."view", "schema"."view"
          view_name = _ddl[/CREATE MATERIALIZED VIEW\s+(?:(?:"[^"]+"|[^".]+)\.)?(?:"[^"]+"|[^"\s]+)/i,
                           0]
          unless view_name
            log "Could not extract view name from DDL: #{_ddl}..."
            next
          end

          # Strip the CREATE MATERIALIZED VIEW prefix
          view_name = view_name.sub(/CREATE MATERIALIZED VIEW\s+/i, '')

          # Parse schema and view parts, handling quotes
          schema_part, view_part = if view_name.include?('.')
                                     schema, view = view_name.split('.', 2)
                                     [schema.delete('"'), view.delete('"')]
                                   else
                                     ['public', view_name.delete('"')]
                                   end

          # Quote schema if it contains hyphens or special characters
          quoted_schema = schema_part = "\"#{schema_part}\""

          log "Refreshing #{quoted_schema}.#{view_part}..."
          refresh_cmd = ['psql', *pg_flags, '-d', current_database, '-c',
                         "'REFRESH MATERIALIZED VIEW #{quoted_schema}.#{view_part}'"].join(' ')

          start = Time.now
          stdout, stderr, status = Open3.capture3(refresh_cmd)
          if status.success?
            log "Refreshed #{view_part} in #{Time.now - start}s"
          else
            log "Failed to refresh #{quoted_schema}.#{view_part}"
            log "Exit code: #{status.exitstatus}"
            log "Error message: #{stderr.strip}"
            log "Output: #{stdout.strip}" unless stdout.strip.empty?
          end
        end
      end

      threads.each(&:join)

      log 'Refreshing indexes after materialized views...'
    end
  end
end

# Wait for all post-data restores to complete
post_data_futures.each(&:wait!)
pool.shutdown
pool.wait_for_termination

reset_postgres_config(pg_flags)
log 'Restore process complete.'
