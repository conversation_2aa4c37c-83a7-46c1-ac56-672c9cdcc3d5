#!/usr/bin/env ruby

# rubocop:disable Layout/LineLength, Rails, Style/HashSyntax

require 'date'
require 'fileutils'
require 'highline'
require 'tempfile'

module Backups
  module Cli
    def cli
      @cli ||= HighLine.new
    end
  end

  class Backup
    attr_reader :path

    def initialize(path:)
      @path = path
    end
  end

  class Settings
    include Cli

    attr_reader :app, :backup_id, :path, :database_name

    def initialize
      @app = ask_app
      @backup_id = ask_backup_id
      @path = ask_path
      @database_name = ask_database_name
    end

    private

    def ask_app
      cli.ask('Enter app') do |question|
        question.default = default_app
      end
    end

    def default_app
      'still-plateau-99181'
    end

    def ask_backup_id
      backup_id = cli.ask('Enter backup_id') do |question|
        question.default = 'none'
      end

      backup_id == 'none' ? nil : backup_id
    end

    def ask_path
      cli.ask('Enter backup path') do |question|
        question.default = default_path
      end
    end

    def default_path
      'latest.dump'
    end

    def ask_database_name
      cli.ask('Enter restore database name') do |question|
        question.default = default_database_name
      end
    end

    def default_database_name
      date = if File.exist?(@path)
               File.ctime(@path)
             else
               Date.today
             end

      date.strftime('prod_%Y_%m_%d')
    end
  end

  class Fetcher
    include Cli

    def initialize(settings:)
      @settings = settings
      @downloader = Downloader.new(settings:)
    end

    def fetch!
      if use_existing_backup?
        puts "Using existing #{path}."

        Backup.new(path: path)
      else
        clear_existing_backup!

        @downloader.download!
      end
    end

    private

    def existing_backup?
      File.exist?(path)
    end

    def use_existing_backup?
      return false unless existing_backup?

      prompt = "The file #{path} already exists. Do you want to replace it? (y/n)"

      answer = cli.ask(prompt) { |question| question.validate = /^(y|n)$/ }

      answer == 'n'
    end

    def clear_existing_backup!
      return unless existing_backup?

      puts "Removing #{path}."

      FileUtils.rm(path)
    end

    def path
      @settings.path
    end
  end

  class Downloader
    def initialize(settings:)
      @app = settings.app
      @backup_id = settings.backup_id
      @path = settings.path
    end

    def download!
      command = download_command

      puts "Running #{command}..."

      system(command)

      Backup.new(path: @path)
    end

    private

    def download_command
      "heroku pg:backups:download #{@backup_id} -a #{@app} -o #{@path}"
    end
  end

  class Database
    attr_reader :name

    def initialize(settings:)
      @name = settings.database_name
    end

    def create!
      command = "createdb #{name}"
      puts "Running #{command}..."
      system(command)
    end

    def exists?
      existing_database_names.include?(name)
    end

    private

    def existing_database_names
      `psql -lqt | cut -d \\| -f 1`.split("\n").map { |x| x.chomp.strip }
    end
  end

  class Restorer
    include Cli

    def initialize(database:, backup:)
      @database = database
      @backup = backup
    end

    def restore!
      if @database.exists?
        puts "Warning: #{@database.name} already exists."
      else
        @database.create!
      end

      command = restore_command
      puts "Running #{command}"
      system(command)
    end

    private

    def restore_command
      restore_method.command
    end

    def restore_method
      user = ENV.fetch('LOGNAME')
      database_name = @database.name
      backup_path = @backup.path

      restore_method_klass.new(user:, database_name:, backup_path:)
    end

    def restore_method_klass
      answer = cli.ask('Restore all data') do |question|
        question.default = 'yes'
      end

      if answer == 'yes'
        RestoreAll
      else
        RestoreSchemas
      end
    end

    class RestoreMethod
      def initialize(user:, database_name:, backup_path:)
        @user = user
        @database_name = database_name
        @backup_path = backup_path
      end
    end

    class ImportList
      def initialize(backup_path:)
        @backup_path = backup_path
        @contents = read_backup_contents
      end

      def path
        tempfile.path
      end

      def save!
        tempfile.write(@contents.join("\n"))
      end

      def filter_schemas!(schema_list)
        pattern = /#{schema_list.join('|')}/
        @contents = @contents.grep(pattern)
      end

      def filter_materialized_data!
        pattern = /MATERIALIZED VIEW DATA/
        @contents = @contents.grep_v(pattern)
      end

      private

      def tempfile
        @tempfile ||= Tempfile.new
      end

      def read_backup_contents
        command = "pg_restore -l #{@backup_path}"

        puts "Running #{command}..."

        `#{command}`.split("\n")
      end
    end

    class RestoreAll < RestoreMethod
      def command
        "pg_restore --no-acl --no-owner --verbose --clean -j 4 -U #{@user} -d #{@database_name} #{@backup_path}"
      end
    end

    class RestoreSchemas < RestoreMethod
      include Cli

      def command
        import_list = ImportList.new(backup_path: @backup_path)

        import_list.filter_schemas!(schema_list)

        import_list.filter_materialized_data! unless materialize_data?

        import_list.save!

        "pg_restore --no-acl --no-owner --verbose --clean -j 4 -U #{@user} -d #{@database_name} -L #{import_list.path} #{@backup_path}"
      end

      private

      def schema_list
        response = cli.ask('Enter a list of schemas separated by space or comma') do |question|
          question.default = 'public'
        end

        response.tr(',', ' ').split(/\s+/)
      end

      def materialize_data?
        response = cli.ask('Materialize view data?') do |question|
          question.default = 'yes'
        end

        response == 'yes'
      end
    end
  end
end

settings = Backups::Settings.new

backup = Backups::Fetcher.new(settings:).fetch!

database = Backups::Database.new(settings:)

restorer = Backups::Restorer.new(database:, backup:)

restorer.restore!

# rubocop:enable Layout/LineLength, Rails, Style/HashSyntax
