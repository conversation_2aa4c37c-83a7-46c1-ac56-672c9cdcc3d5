require 'amazing_print'
require 'date'
require 'debug'
require 'json'

APPROVED = '✅'.freeze
BLANK = '◽'.freeze

cutoff = DateTime.now - 20

json = `gh pr list --json number,title,author,reviews,createdAt,isDraft`

pull_requests = JSON.parse(json)

pull_requests.each do |pull_request|
  next if pull_request['isDraft']

  title = pull_request['title']
  number = pull_request['number']
  reviews = pull_request['reviews']
  created_at = pull_request['createdAt']
  author = pull_request['author']
  author_name = if author['name'] == ''
                  author['login']
                else
                  author['name']
                end

  next unless DateTime.parse(created_at) > cutoff

  approvals_count = reviews.count { |review| review['state'] == 'APPROVED' }

  approvals_display = case approvals_count
                      when 0
                        "#{BLANK}#{BLANK}"
                      when 1
                        "#{APPROVED}#{BLANK}"
                      else
                        "#{APPROVED}#{APPROVED}"
                      end

  date = DateTime.parse(created_at).strftime('%Y/%m/%d')

  puts "#{approvals_display}\t#{date} | #{number}: #{title} (#{author_name})"
end
