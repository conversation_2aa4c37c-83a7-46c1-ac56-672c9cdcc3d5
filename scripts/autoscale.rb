#!/usr/bin/env ruby

require 'English'
require 'json'

APP = 'still-plateau-99181'.freeze

PROFILES = {
  peak: {
    web: 5,
    worker: 3
  },
  off_peak: {
    web: 3,
    worker: 1
  },
  super_off_peak: {
    web: 2,
    worker: 1
  }
}.freeze

class DynoType
  attr_reader :type

  def initialize(type:, minimum_count:, maximum_count:)
    @type = type
    @minimum_count = minimum_count
    @maximum_count = maximum_count
  end

  def scale
    json_string = `heroku ps -a #{APP} --json`.chomp.strip

    unless $CHILD_STATUS.success?
      puts 'Unable to get current status'

      exit $CHILD_STATUS.exitstatus
    end

    json = JSON.parse(json_string)

    json.count { |process| process['type'] == @type.to_s }
  end

  def scale=(count)
    count = count.clamp(@minimum_count, @maximum_count)

    result = `heroku ps:scale -a #{APP} #{@type}=#{count}`.chomp.strip

    puts result

    exit $CHILD_STATUS.exitstatus unless $CHILD_STATUS.success?

    count
  end
end

web = DynoType.new(type: :web, minimum_count: 2, maximum_count: 5)
worker = DynoType.new(type: :worker, minimum_count: 1, maximum_count: 3)
dyno_types = [web, worker]

now = Time.now

def the_first?
  Time.now.day == 1
end

def the_tenth?
  Time.now.day == 10
end

def weekend?
  Time.now.saturday? || Time.now.sunday?
end

def weekday?
  !weekend?
end

# 7:30 AM to 6:30 PM
def on_peak?
  now = Time.now
  start = Time.new(now.year, now.month, now.day, 7, 30, 0)
  stop = Time.new(now.year, now.month, now.day, 18, 30, 0)
  now.between?(start, stop)
end

# 6:30 AM to 9:00 PM
def off_peak?
  now = Time.now
  start = Time.new(now.year, now.month, now.day, 6, 30, 0)
  stop = Time.new(now.year, now.month, now.day, 21, 0, 0)
  now.between?(start, stop)
end

def determine_profile_name
  if the_first? || the_tenth?
    # Peak day
    :peak
  elsif weekend?
    # Weekend
    :super_off_peak
  elsif on_peak?
    # On Peak
    :peak
  elsif off_peak?
    # Off Peak
    :off_peak
  else
    # Super Off Peak
    :super_off_peak
  end
end

profile_name = determine_profile_name

profile = PROFILES.fetch(profile_name)

changes = dyno_types.filter_map do |dyno|
  current_scale = dyno.scale
  expected_scale = profile.fetch(dyno.type)

  next if current_scale == expected_scale

  lambda do
    puts "Changing #{dyno.type} from #{current_scale} to #{expected_scale}"
    dyno.scale = expected_scale
  end
end

exit 0 unless changes.any?

puts "Current time is #{now}, profile is #{profile_name}"
changes.each(&:call)
