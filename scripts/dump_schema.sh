#!/bin/bash

# Uses pg_dump to export the schema given as the second argument from the
# database given as the first argument.
# Example: ./dump_schema.sh revela_development demo demo.sql

set -e

database=$1
schema=$2
output_file=$3

if [ -z $output_file ]; then
  echo "Usage: ./dump_schema.sh revela_development demo demo.sql"
  exit 1
fi

set -x

# Pass -a to dump data only without schema definitions

pg_dump -O -n $schema $database -f $output_file
