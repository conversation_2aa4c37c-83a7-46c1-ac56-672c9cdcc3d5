#!/usr/bin/env ruby

require 'fileutils'
require 'yaml'

match = ARGV[0]

FileUtils.cp 'swagger/v2/swagger.yaml', 'tmp/swagger.yaml' if match&.length

system('SWAGGER_DRY_RUN=0 RAILS_ENV=test bundle exec rails rswag')

exit unless match&.length

# Get new yaml
updated_yaml = YAML.safe_load File.read('swagger/v2/swagger.yaml')

# Replace original yaml
FileUtils.mv 'tmp/swagger.yaml', 'swagger/v2/swagger.yaml'

# Merge only matching paths
yaml = YAML.safe_load File.read('swagger/v2/swagger.yaml')

paths = (yaml['paths'].keys + updated_yaml['paths'].keys).uniq

paths = paths.select do |path|
  path.include?(match)
end

paths.each do |path|
  yaml['paths'][path] = updated_yaml['paths'][path]
end

# Save yaml
File.open('swagger/v2/swagger.yaml', 'w') do |f|
  f.puts yaml.to_yaml
end
