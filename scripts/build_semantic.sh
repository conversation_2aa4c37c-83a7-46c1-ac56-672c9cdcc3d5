# This checks an optional file containing semantic compilation date against the
# most recent change to either the yarn lockfile or semantic/ directory to
# determine if a compilation / recompilation should occur.

branch=`git rev-parse --abbrev-ref HEAD`

set -e

compile() {
  echo "Patching Fomantic UI"
  patch -p0 --forward < semantic/calendar.patch || true
  patch -p0 --forward < semantic/dropdown.patch || true
  patch -p0 --forward < semantic/tab.patch || true
  echo "Compiling Fomantic UI"
  gulp semantic-custom-theme
  gulp
}

if [ -x $branch ]; then
  echo "Unable to determine git branch"
  compile
  exit
fi

dir=node_modules/fomantic-ui/compiled_at
mkdir -p $dir
path="$dir/$branch"

update_date() {
  date +%s > $path
}

echo "Checking $path"

if [ -e $path ]; then
  compilation_date=`cat $path`
  changed_date=`git log -1 --format=%at semantic yarn.lock`

  echo "Relevant change to Semantic UI $changed_date, last compilation $compilation_date"

  if [ $compilation_date -gt $changed_date ]; then
    echo "Skipping"
    cp node_modules/fomantic-ui/dist/semantic.min.css vendor/assets/stylesheets/
    cp node_modules/fomantic-ui/dist/semantic.min.js vendor/assets/javascripts/
  else
    compile
    update_date
  fi
else
  compile
  update_date
fi
