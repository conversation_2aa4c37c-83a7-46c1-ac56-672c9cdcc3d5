# rubocop:disable Metrics/BlockLength

Rails.application.eager_load!

ENUM_NAMES = %w[
  business_type outcome preferred_contact_method reason role status
  termination_reason urgency
].freeze

STRING_ARRAY_NAMES = %w[
  amenities custom_data photo_urls tags
].freeze

JSON_ARRAY_NAMES = %w[
  applicants
].freeze

JSON_NAMES = %w[
  custom_data
]

klasses = Api::V2.constants.map do |constant|
  Api::V2.const_get(constant)
end

klasses += Api::V2::Accounting.constants.map do |constant|
  Api::V2::Accounting.const_get(constant)
end

resources = klasses.filter_map do |klass|
  klass if klass.is_a?(Class) && klass < JSONAPI::Resource
end

resources.sort_by! do |resource|
  resource.name.demodulize.gsub('Resource', '').underscore.pluralize
end

tables = ''
refs = ''

ENDPOINT_BLACKLIST = %w[
  employees
  insurance_policies
  resi_shares
].freeze

resources.each do |resource|
  relationships = resource._relationships
  attributes = resource._attributes

  table_name = resource.name.demodulize.gsub(
    'Resource', ''
  ).underscore.pluralize

  next if table_name.in?(ENDPOINT_BLACKLIST)

  tables += "Table #{table_name} {\n"
  tables += "  id integer [primary key]\n"

  tables += "  journal_id integer\n" if table_name == 'entries'

  tables += "  lease_id integer\n" if table_name == 'charges'

  relationships.each do |key, relationship|
    next unless relationship.is_a?(JSONAPI::Relationship::ToOne)

    column_name = "#{key}_id"

    sql_column = begin
      resource._model_name.safe_constantize.columns.find do |column|
        column.name == column_name
      end
    rescue StandardError
      nil
    end

    model_relationship = begin
      association_name = relationship.options[:relation_name] || key

      resource._model_name.safe_constantize.reflect_on_association(association_name)
    rescue StandardError
      nil
    end

    not_null = (
      sql_column&.null == false ||
      model_relationship&.options&.[](:optional) == false
    )

    type = 'integer'

    extras = ''
    extras += '[not null]' if not_null

    line = [column_name, type, extras].compact_blank.join(' ')

    tables += "  #{line}\n"
  end

  attribute_keys = attributes.keys

  # Prefer id, then associated ids sorted, then sorted attributes, then
  # created_at, then updated_at
  # attribute_keys.sort_by! do |key|
  #   case key.to_s
  #   when 'id' then [-2, key]
  #   when /_id/ then [-1, key]
  #   when 'created_at' then [1, key]
  #   when 'updated_at' then [2, key]
  #   else [0, key]
  #   end
  # end

  attribute_keys.each do |key|
    name = key.to_s

    next if name == 'id'
    next if name == 'property_id'
    next if name == 'unit_id'

    delegation = attributes[key][:delegate].presence&.to_s

    sql_column = begin
      resource._model_name.safe_constantize.columns.find do |column|
        column.name == (delegation || name)
      end
    rescue StandardError
      nil
    end

    sql_type = sql_column&.sql_type

    not_null = sql_column&.null == false

    not_null = true if name.in?(%w[created_at updated_at])

    type = case sql_type
           when 'character varying'
             'varchar'
           when /timestamp/
             'timestamp'
           when 'date'
             'date'
           when /boolean/
             'boolean'
           when /integer/, /bigint/
             'integer'
           when /double/, /numeric/
             'decimal'
           when /text/
             'text'
           when nil
             nil
           else
             fail "Unknown sql type '#{sql_type}'"
           end

    type ||= case name
             when /_at$/
               'datetime'
             when /date$/
               'date'
             when /amount/, /income/, /balance/, /deposit/, /rate/, /bathrooms/, /square_feet/, /overdue/, /thirty_days/, /sixty_days/, /ninety_days/, /ninety_one_plus_days/, /rent/, /charges/, /costs/
               'decimal'
             when /bedrooms/, 'floor'
               'integer'
             else
               'varchar'
             end

    type = 'string' if type == 'varchar'
    type = 'string' if name.in?(ENUM_NAMES)
    type = 'string[]' if name.in?(STRING_ARRAY_NAMES)
    type = 'json[]' if name.in?(JSON_ARRAY_NAMES)
    type = 'json' if name.in?(JSON_NAMES)
    type = type.chomp.strip

    extras = ''

    extras += '[not null]' if not_null

    line = [name, type, extras].compact_blank.join(' ')
    tables += "  #{line}\n"
  end
  tables += "}\n\n"

  refs += "Ref: entries.journal_id > journals.id\n" if table_name == 'entries'

  refs += "Ref: charges.lease_id > leases.id\n" if table_name == 'charges'

  relationships.each do |_key, relationship|
    next unless relationship.is_a?(JSONAPI::Relationship::ToOne)

    other_table_name = relationship.resource_types.first

    begin
      refs += "Ref: #{table_name}.#{relationship.foreign_key} > #{other_table_name}.id\n"
    rescue StandardError => e
      puts e
    end
  end
  refs += "\n"
end

File.open('./api_v2_schema.dbml', 'w') do |file|
  file.write(tables)
  file.write(refs)
end

# rubocop:enable Metrics/BlockLength
