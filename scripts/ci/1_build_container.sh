#!/usr/bin/env bash

set -e

GIT_BRANCH=$1
GIT_REVISION=$2

if [ -z $GIT_REVISION ]; then
  echo "Usage: $0 << pipeline.git.branch >> << pipeline.git.revision >>"
  exit 1
fi

AWS_ACCESS_KEY_ID=$ECR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=$ECR_AWS_SECRET_ACCESS_KEY
AWS_DEFAULT_REGION=us-east-1
DOCKER_REGISTRY_URL=256566291856.dkr.ecr.us-east-1.amazonaws.com/revela_web

aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $DOCKER_REGISTRY_URL

if aws ecr describe-images --region $AWS_DEFAULT_REGION --repository-name=revela_web --image-ids=imageTag=$GIT_REVISION; then
  echo "Image tagged $GIT_REVISION already exists."
else
  echo "Unable to find existing image for revision $GIT_BRANCH/$GIT_REVISION."

  docker pull $DOCKER_REGISTRY_URL:$GIT_BRANCH ||
  docker pull $DOCKER_REGISTRY_URL:latest

  echo "Building image."

  docker-compose --ansi never build --build-arg revision=$GIT_REVISION web

  # Tag Revision
  docker tag revela_web:latest $DOCKER_REGISTRY_URL:$GIT_REVISION
  docker push $DOCKER_REGISTRY_URL:$GIT_REVISION

  # Tag Branch
  docker tag revela_web:latest $DOCKER_REGISTRY_URL:$GIT_BRANCH
  docker push $DOCKER_REGISTRY_URL:$GIT_BRANCH

  # Tag Latest
  docker tag revela_web:latest $DOCKER_REGISTRY_URL:latest
  docker push $DOCKER_REGISTRY_URL:latest
fi
