#!/usr/bin/env bash

set -e

GIT_REVISION=$1

if [ -z $GIT_REVISION ]; then
  echo "Usage: $0 << pipeline.git.revision >>"
  exit 1
fi

mkdir -p test-results/rspec coverage
touch test-results/rspec/results.xml
chmod -R 777 test-results coverage

AWS_ACCESS_KEY_ID=$ECR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=$ECR_AWS_SECRET_ACCESS_KEY
AWS_DEFAULT_REGION=us-east-1
DOCKER_REGISTRY_URL=256566291856.dkr.ecr.us-east-1.amazonaws.com/revela_web

aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $DOCKER_REGISTRY_URL

docker pull $DOCKER_REGISTRY_URL:$GIT_REVISION
docker tag $DOCKER_REGISTRY_URL:$GIT_REVISION revela_web:latest
docker-compose pull postgres redis elasticsearch
