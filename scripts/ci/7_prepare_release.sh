#!/usr/bin/env bash

set -e

PRODUCTION_APP=still-plateau-99181
STAGING_APP=vast-beyond-79022

AWS_ACCESS_KEY_ID=$ECR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=$ECR_AWS_SECRET_ACCESS_KEY
AWS_DEFAULT_REGION=us-east-1
AWS_REGISTRY_URL=256566291856.dkr.ecr.us-east-1.amazonaws.com/revela_web
GIT_BRANCH=$1
GIT_REVISION=$2
HEROKU_REGISTRY_PASSWORD=$HEROKU_API_KEY
HEROKU_REGISTRY_USERNAME=$HEROKU_USERNAME
HEROKU_REGISTRY_URL="https://registry.heroku.com"

if [ -z $HEROKU_USERNAME ]; then
  echo "Please provide HEROKU_USERNAME."
  exit 1
fi

if [ -z $HEROKU_API_KEY ]; then
  echo "Please provide HEROKU_API_KEY."
  exit 1
fi

if [ -z $GIT_REVISION ]; then
  echo "Usage: $0 << pipeline.git.branch >> << pipeline.git.revision >>"
  exit 1
fi

case $GIT_BRANCH in
  master)
    APP=$PRODUCTION_APP
    ;;
  staging)
    APP=$STAGING_APP
    ;;
  *)
    echo "Invalid release branch $GIT_BRANCH"
    exit 1
    ;;
esac

echo "Using Heroku app $APP"

# Pull git revision container as local 'latest' tag.
# This could have be built from master or staging.
# Regardless, this will be tagged 'latest' and pushed to the heroku registry for $APP.

aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_REGISTRY_URL
docker pull $AWS_REGISTRY_URL:$GIT_REVISION
docker tag $AWS_REGISTRY_URL:$GIT_REVISION revela_web:latest

# Login to Heroku Registry
docker login -p $HEROKU_REGISTRY_PASSWORD -u $HEROKU_REGISTRY_USERNAME $HEROKU_REGISTRY_URL

set -x

# Release Container
docker build -t revela_release:latest -f scripts/heroku/Dockerfile.release .
docker tag revela_release:latest registry.heroku.com/$APP/release
docker push registry.heroku.com/$APP/release

# Web Container
docker build -t revela_web:latest -f scripts/heroku/Dockerfile.web .
docker tag revela_web:latest registry.heroku.com/$APP/web
docker push registry.heroku.com/$APP/web

# Worker Container
docker build -t revela_worker:latest -f scripts/heroku/Dockerfile.worker .
docker tag revela_worker:latest registry.heroku.com/$APP/worker
docker push registry.heroku.com/$APP/worker
