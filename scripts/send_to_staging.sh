#!/usr/bin/env bash

set -e

if ! [ -x "$(command -v git-graft)" ]; then
	echo "This command requires git-graft from git-extras. Exiting."
	exit 1
fi

CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

BRANCH=$1

if [ -z $BRANCH ]; then
	echo "Using current git branch '$CURRENT_BRANCH'. Supply an argument to override."
	BRANCH=$CURRENT_BRANCH
fi

echo "Send origin/$BRANCH to origin/staging? [y/n]"

read CONTINUE
if ! [ $CONTINUE == "y" ]; then
	echo "Exiting."
	exit 1
fi

set -x

# Fetch the most recent origin/staging
git fetch origin staging

# Update staging to point at origin/staging
git branch -f staging origin/staging

# Fetch the latest version of origin/$BRANCH
git fetch origin $BRANCH

# Merge origin/$branch into staging
git graft origin/$BRANCH staging

# Update origin/staging to staging, if we are up to date.
git push origin staging:staging
