# Revela

[![Circle CI](https://circleci.com/gh/Revela/Revela.svg?style=svg&circle-token=****************************************)](https://circleci.com/gh/Revela/Revela)

[![Code Climate](https://codeclimate.com/repos/57222d6e91a7273b85004611/badges/aa83bca7b692122777a0/gpa.svg)](https://codeclimate.com/repos/57222d6e91a7273b85004611/feed)

[![Test Coverage](https://codeclimate.com/repos/57222d6e91a7273b85004611/badges/aa83bca7b692122777a0/coverage.svg)](https://codeclimate.com/repos/57222d6e91a7273b85004611/coverage)

[![Issue Count](https://codeclimate.com/repos/57222d6e91a7273b85004611/badges/aa83bca7b692122777a0/issue_count.svg)](https://codeclimate.com/repos/57222d6e91a7273b85004611/feed)

## Installation

### Prerequisites
Install Postgresql, Elasticsearch, Redis, NodeJS, Yarn, Chrome Headless, Pdftk, Libreoffice

### Install Dependencies
```shell
# Install gems
bundle

# Install js dependencies and compile semantic-ui
yarn
```

### Prepare Database
You may need to create a postgres user `revela` with the ability to create databases.

Something like `$ createuser -ds revela` usually does the trick.

Then, `rails db:create db:migrate db:test:prepare` should create and migrate
both the development and test databases.

### Demo Seed File
Optionally load a simple preloaded environment:
```
rake db:seed:alever
```
(Alever is Revela backwards, our fake property management company).
This creates a few logins, all with password `TrustNo1!`:
- Admin User `<EMAIL>` for `/admin/login`
- Property Managers `<EMAIL>` and `<EMAIL>` for `/property_managers/login`
- Tenant `<EMAIL>` for `/tenants/login`

## Running Revela Locally
There is a foreman compatible `Procfile.dev` that can be started with
`bin/dev`. This will run the Rails webserver, sidekiq for processing background
jobs, and webpack in development mode for updates to compiled javascript and
CSS. Check `Procfile.dev` to see which processes are started.

## Contributing
We use a pretty standard GitHub workflow to close issues. When you are assigned
to or otherwise begin working on an issue, checkout a feature branch with a
similar name. That is, if you are working on '#48 Add Tenant Avatars', checkout
a branch called `48_tenant_avatars`. It is probably a good idea to periodically
rebase your branch against master so that the tests are running on the latest
code, even if there are no conflicts. This is only really needed before code
review or a merge. When you create a pull request from your branch, in the
comment it should include somewhere 'closes #48' so that the issue will be
automatically closed upon a successful merge. Although CI should catch test and
lint errors, you may want to run RSpec and some of the linter rake tasks (see
below) before requesting code review. Also, please review
[this article](https://chris.beams.io/posts/git-commit/) for advice on writing
good commit messages.

## Rake Tasks

Run RuboCop, haml-lint, scss-lint, and eslint on staged files before commits:
```
rake check
```

Run RuboCop on relevant ruby files:
```
rake rubocop
```

Run eslint Airbnb on relevant javascript files:
```
rake eslint
```

Run haml-lint on relevant haml files:
```
rake haml_lint
```

Run scss-lint on relevant scss files:
```
rake scss_lint
```

Run linters on files that are different than master:
```
rake check_branch
```

Generate ruby documentation with YARD:
```
rake doc
```

List feature flags in use throughout the app:
```
rake features
```

Run the Brakeman static security scanner
```
rake brakeman:run
```

## Apartment
We use [apartment](https://github.com/influitive/apartment) to switch to a
customer specific PostgreSQL schema based on the subdomain of the request.

### Using different subdomains locally
Locally, you can test different subdomains by using the `lvh.me` domain, which
is a public DNS entry that resolves to `127.0.0.1`. For example,
`alever.lvh.me:3000/` will resolve to a local rails server running on port
3000, and the `alever` schema will be loaded. As of writing, the `alever`
schema will be present after running the demo seed file. To test the normal
landing page and registration behaviour, you can visit `www.lvh.me:3000/`. For
convenience, there is an overridden configuration setting that will choose the
`alever` schema for `localhost:3000`, as this is what developers will want to
happen transparently 90% of the time.

### Accessing data in the Rails console
Notice that by using different schemas per customer, you have to switch to that
customers schema for any sql related functions (including ActiveRecord) to
return the data you expect. So, you almost always want to run, for example,
`Customer.find_by(subdomain: 'alever').activate!` before running any commands
so that the `search_path` is updated. This is equivalent to
`Apartment::Tenant.switch!('alever')`, as we use schema names that match the
customer subdomain. In development mode, `Customer.first.activate!` is usually
sufficient.

## Processing background jobs in development
Jobs in the application are processed by
[sidekiq](https://github.com/mperham/sidekiq). Sidekiq will be started via the
default development `Procfile.dev` when running `bin/dev`. You may view
sidekiq's logging of the jobs in the terminal, or by visiting the admin view at
`alever.lvh.me:3000/jobs`. To see what queues are configured, view
`config/sidekiq.yml`.

## Bullet
We use [Bullet](https://github.com/flyerhzm/bullet) to help check for
unoptimized queries. It is enabled in development, violations will appear in
the rails console and in `log/bullet.log`. To fail specs with violations,
enable bullet in `config/environments/test.rb`.


## Bummr
[Bummr](https://github.com/lpender/bummr) helps update Gemfile dependencies in
a safe yet automated way. We have a test runner script configured to fail fast
at `scripts/bummr-build.sh`, so `export BUMMR_TEST=./scripts/bummr-build.sh`
beforehand.

## Customer-specific Behaviors
Just as our architecture separates datastores by tenant, we also separate
config. The idea is to provide a thin, unified interface in all places where
customer-specific behavior is required. For example,
`CustomerSpecific::Behavior.member_ledgers_enabled?` returns `true` or `false`
based on what is configured in `config/customer_specific_behavior.yml`. Instead
of adding an `if` statment to govern customer-specific behavior, consider
adding config!
