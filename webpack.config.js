const webpack = require('webpack');

const withPolyfill = path => ['@babel/polyfill', path];

module.exports = {
  entry: {
    admin: withPolyfill('./frontend/admin-index.js'),
    apply: withPolyfill('./frontend/apply-index.js'),
    main: withPolyfill('./frontend/index.js'),
    owner: withPolyfill('./frontend/owner-index.js'),
    esignatures: withPolyfill('./frontend/electronic-signatures-index.js'),
    'invoice-processing': withPolyfill('./frontend/invoice-processing-index.js'),
    tenant: withPolyfill('./frontend/tenant-index.js'),
    vendor: withPolyfill('./frontend/vendor-index.js'),
    vendors: withPolyfill('./frontend/vendors-index.js'),
  },
  output: {
    path: `${__dirname}/app/assets/javascripts`,
    filename: '[name]-webpack-bundle.js',
    hashFunction: 'sha256'
  },
  module: {
    rules: [
      {
        test: /\.jsx?$/,
        exclude: /(node_modules)/,
        loader: 'babel-loader',
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|gif)$/,
        use: {
          loader: 'url-loader',
          options: {
            name: 'images/[name].[ext]',
          },
        },
      },
      {
        test: /\.m?js$/,
        resolve: {
          fullySpecified: false
        }
      },
    ],
  },
  resolve: {
    extensions: ['.js', '.jsx', '.js.jsx'],
  },
  plugins: [
    new webpack.IgnorePlugin({ resourceRegExp: /^\.\/locale$/, contextRegExp: /moment$/ }),
  ],
};
