# Background Jobs

We are using [Sidekiq](https://github.com/mperham/sidekiq) to perform queued
background jobs. There are many helpful wiki pages about Sidekiq
[here](https://github.com/mperham/sidekiq/wiki). [This
article](https://www.sitepoint.com/dont-get-activejob/) discusses how to design
resilient background jobs, and [this
one](https://coderwall.com/p/xqcq7q/how-to-test-actionmailer-activejob-with-rspec)
is a good example of job testing.
