# Unkown Facts

## Aesthetikx
- `npm` will not run postinstall hooks for GitHub dependencies, so compiled js has to be commited to forked and modified js repositories.
- Capybara will not find anchor tags without an `href` attribute for things like `click_link`.
- The name of the track Nlogax by Boards of Canada is thought to have been derived from the common mathematical expression `n(log_a(x)) = log_a(x^n)`.
- `#count`ing ActiveRecord results downstream of `GROUP BY` (e.g. `Invoice.unpaid`) returns a hash, so use `#length` to get the count.
- You cannot have a has many through a polymorphic join model without specifying a source type, so all targets cannot be fetched in ActiveRecord alone.
- Dwolla has a webhook url subscription limit of 10
- By default `has_one` relations are not validated upon parent save, unless `validate: true` flag is present on the association
- `jq` is cool (find first of each event topic and save as a fixture):

  ```shell
  for topic in $(jq -r 'map(.topic) | unique[]' dwolla_events.json);
  do jq --arg topic "$topic" 'map(select(.topic == $topic))[0]' dwolla_events.json
  > "spec/fixtures/dwolla_events/$topic.json"; done
  ```
- <PERSON><PERSON><PERSON>er `raise_delivery_errors` doesn't work for `deliver_later`
- You can use the `lvh.me` domain to test subdomains locally, e.g. `xyz.lvh.me:3000` still resolves to `localhost:3000`, but with a subdomain in the request header.
- You can order by an attribute on a joined table with a merge, such as `.merge(OtherTable.order(date: :desc))`.
- Capybara find `visible: false` is completely different than `visible: :hidden`, or even `visible: :all`. The former ignores visibility and matches based on `:all` (all children, not :all visibilities). That is, `visibile: :all` or `visible: :hidden` will seemingly fail to find invisible elements containing text, where `visible: false` will.
- Semantic ui calendar days cannot be `trigger(:clicked)`, it also requires `trigger(:mouseup)`.
- See what branches have been merged different than master, for example an old demo branch that needs updating, `diff -y <(git branch --merged master) <(git branch --merged demo)`
- For an :xpath query to work within `within` (hah) it must start with a `.`.
- You cannot order in sql and also use `find_each`, use `each` if order matters. (`Scoped order is ignored, it's forced to be batch order.`)
- `form_with` is remote true by default, pass local: true to opt out
- Renaming and uploading a pdf to s3:
  ```shell
  exiftool -Title="Sample Background Check" the_file.pdf
  aws s3 cp the_file.pdf s3://revela-doc/files/sample_background_check.pdf --grants read=uri=http://acs.amazonaws.com/groups/global/AllUsers
  ```
- Regenrating swagger documentation:
```shell
SWAGGER_DRY_RUN=0 rake rswag:specs:swaggerize
```
- When cloning large databases from template, `createdb -S FILE_COPY -T the_backup revela_development` is much faster on my machine as compared to the default `WAL_LOG` strategy.

## Taurus
### Elastic search errors in local

If you're just geting setup locally, one thing you'll notice after yopu run `foreman start -f Procfile.dev` is a slew of errors mentioning connection refusal on port `9200`. These errors result from the absence of an Elasticsearch service on your local machine. Elasticsearch is present in staging and production (of course) so that's why it's an error which only occurs locally.

OK so, unless you're enjoying seeing these errors in your local logs, there's an easy fix -- start Elasticsearch. Elastisearch wants to run inside Docker. If you don't know what Docker is yet, don't worry, you can learn that later:

Install Docker Desktop on your Mac:
```sh
brew install --cask docker
```

Run Docker Desktop with `command + space` and typing in Docker Desktop and clicking.

Run Elasticsearch:
```sh
docker-compose up -d elasticsearch
```

That's it! Poof! No more port `9200` connection refusals.
