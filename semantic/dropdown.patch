--- node_modules/fomantic-ui/src/definitions/modules/dropdown.js	2021-03-27 06:45:08.292000000 -0400
+++ node_modules/fomantic-ui/src/definitions/modules/dropdown.js	2021-03-30 07:39:47.753864709 -0400
@@ -984,7 +984,7 @@
           }
         },
 
-        forceSelection: function() {
+        forceSelection: function(userOnly) {
           var
             $currentlySelected = $item.not(className.filtered).filter('.' + className.selected).eq(0),
             $activeItem        = $item.not(className.filtered).filter('.' + className.active).eq(0),
@@ -993,12 +993,23 @@
               : $activeItem,
             hasSelected = ($selectedItem.length > 0)
           ;
-          if(settings.allowAdditions || (hasSelected && !module.is.multiple())) {
-            module.debug('Forcing partial selection to selected item', $selectedItem);
-            module.event.item.click.call($selectedItem, {}, true);
-          }
-          else {
-            module.remove.searchTerm();
+
+          var y = $item.filter(selector.addition);
+
+          if (userOnly) {
+            if (y.length) {
+              module.event.item.click.call(y.eq(0), {}, true);
+            } else {
+              module.remove.searchTerm();
+            }
+          } else {
+            if(settings.allowAdditions || (hasSelected && !module.is.multiple())) {
+              module.debug('Forcing partial selection to selected item', $selectedItem);
+              module.event.item.click.call($selectedItem, {}, true);
+            }
+            else {
+              module.remove.searchTerm();
+            }
           }
         },
 
@@ -1107,7 +1118,9 @@
               if(module.is.searchSelection() && !willRefocus) {
                 if(!itemActivated && !pageLostFocus) {
                   if(settings.forceSelection) {
-                    module.forceSelection();
+                    module.forceSelection(false);
+                  } else if(settings.allowAdditions){
+                    module.forceSelection(true);
                   } else if(!settings.allowAdditions){
                     module.remove.searchTerm();
                   }
@@ -2847,7 +2860,7 @@
             $('<option/>')
               .prop('value', escapedValue)
               .addClass(className.addition)
-              .html(value)
+              .html($.fn.dropdown.settings.templates.escape(value))
               .appendTo($input)
             ;
             module.verbose('Adding user addition as an <option>', value);
@@ -3933,7 +3946,7 @@
   hideDividers           : false,      // Whether to hide any divider elements (specified in selector.divider) that are sibling to any items when searched (set to true will hide all dividers, set to 'empty' will hide them when they are not followed by a visible item)
 
   placeholder            : 'auto',     // whether to convert blank <select> values to placeholder text
-  preserveHTML           : true,       // preserve html when selecting value
+  preserveHTML           : false,      // preserve html when selecting value
   sortSelect             : false,      // sort selection on init
 
   forceSelection         : true,       // force a choice on blur with search selection
@@ -4120,7 +4133,7 @@
   },
   escape: function(string, preserveHTML) {
     if (preserveHTML){
-      return string;
+      // return string; // Never preserveHTML, XSS
     }
     var
         badChars     = /[<>"'`]/g,
