--- node_modules/fomantic-ui/src/definitions/modules/calendar.js	2021-03-27 06:45:08.292000000 -0400
+++ node_modules/fomantic-ui/src/definitions/modules/calendar.js	2021-03-29 17:02:17.148123615 -0400
@@ -511,6 +511,7 @@
               $input.on('input' + eventNamespace, module.event.inputChange);
               $input.on('focus' + eventNamespace, module.event.inputFocus);
               $input.on('blur' + eventNamespace, module.event.inputBlur);
+              $input.on('click' + eventNamespace, module.event.inputClick);
               $input.on('keydown' + eventNamespace, module.event.keydown);
             } else {
               $container.on('keydown' + eventNamespace, module.event.keydown);
@@ -644,6 +645,9 @@
               selectionComplete = false;
             }
           },
+          inputClick: function () {
+            module.popup('show');
+          },
           class: {
             mutation: function(mutations) {
               mutations.forEach(function(mutation) {
