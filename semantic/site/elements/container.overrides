// Same width as ui text container, but without extra modifications
.ui.narrow.container {
  max-width: 700px !important;
}

// TODO: Remove this with 2.9, this is a backport from there.
// Begin backport

@wideRatio: 1.2;
@tabletWideWidth: (@tabletBreakpoint - (@tabletMinimumGutter * 2) - @scrollbarWidth) * @wideRatio;
@computerWideWidth: (@computerBreakpoint - (@computerMinimumGutter * 2) - @scrollbarWidth) * @wideRatio;
@largeMonitorWideWidth: (@largeMonitorBreakpoint - (@largeMonitorMinimumGutter * 2) - @scrollbarWidth) * @wideRatio;

@media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
  .ui.ui.ui.wide.container {
    width: @tabletWideWidth;
  }
}

@media only screen and (min-width: @computerBreakpoint) and (max-width: @largestSmallMonitor) {
  .ui.ui.ui.wide.container {
    width: @computerWideWidth;
  }
}

@media only screen and (min-width: @largeMonitorBreakpoint) {
  .ui.ui.ui.wide.container {
    width: @largeMonitorWideWidth;
  }
}

// End backport
