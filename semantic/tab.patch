--- node_modules/fomantic-ui/src/definitions/modules/tab.js	2021-09-21 10:01:14.921999000 -0400
+++ node_modules/fomantic-ui/src/definitions/modules/tab.js	2021-09-30 10:10:33.658219510 -0400
@@ -103,7 +103,9 @@
           if(settings.autoTabActivation && instance === undefined && module.determine.activeTab() == null) {
             module.debug('No active tab detected, setting first tab active', module.get.initialPath());
             module.changeTab(settings.autoTabActivation === true ? module.get.initialPath() : settings.autoTabActivation);
-          };
+          } else if (module.determine.activeTab() !== null) {
+            module.changeTab(module.determine.activeTab());
+          }
 
           module.instantiate();
         },
@@ -221,12 +223,16 @@
               tabPath = $(this).data(metadata.tab)
             ;
             if(tabPath !== undefined) {
-              if(settings.history) {
+              if(settings.history && settings.historyType === 'state') {
                 module.verbose('Updating page state', event);
                 $.address.value(tabPath);
               }
               else {
                 module.verbose('Changing tab', event);
+                var location = window.location.pathname + '#/' + tabPath;

+                Turbolinks.controller.replaceHistoryWithLocationAndRestorationIdentifier(

+                  location, Turbolinks.uuid()

+                );

                 module.changeTab(tabPath);
               }
               event.preventDefault();
@@ -688,6 +694,10 @@
           activeTab: function() {
             var activeTab = null;
 
+            if (window.location.hash.startsWith('#/')) {
+              return window.location.hash.replace('#/', '');
+            }
+
             $tabs.each(function(_index, tab) {
               var $tab = $(tab);
 
