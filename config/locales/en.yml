# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  rent_term: 'Rent'
  tenant_support_term: 'Tenant Relations'
  tenant_term: 'Tenant'
  lease_term: 'Lease'
  lease_agreement_term: 'Lease Agreement'
  views:
    pagination:
      first: "&laquo; First"
      last: "Last &raquo;"
      previous: "&lsaquo; Prev"
      next: "Next &rsaquo;"
      truncate: "&hellip;"
      helpers:
        page_entries_info:
          one_page:
            display_entries:
              zero: "No %{entry_name} found"
              one: "Displaying <b>1</b> %{entry_name}"
              other: "Displaying <b>all %{count}</b> %{entry_name}"
              more_pages:
                display_entries: "Displaying %{entry_name} <b>%{first}&nbsp;-&nbsp;%{last}</b> of <b>%{total}</b> in total"

    tenant: # Tenant Portal Translations
      agree_to_sms: 'I agree to receive text messages from %{customer_name}, Revela Inc., and its affiliates and subsidiaries. Message and data rates may apply.'
      maintenance:
        permission_to_enter: 'I give %{customer_name} staff and their vendors permission to enter my unit, even if I am not present.'

        survey:
          experience_rating: "How would you rate your recent experience with %{customer_name} regarding your recent maintenance request?"
          feedback: Do you have any additional feedback to help us improve our service?
          intro: We're always working to improve our tenant's experience, so we would love to hear what's working and how we can do better.
          quality_rating: How would you rate the quality of the work performed during your recent maintenance request?

    vendor:
      maintenance:
        proof_required_html: 'Note: %{customer_name} requires photos of work completed. Please take photos of completed work and upload them to the activity thread below prior to clicking &lsquo;Mark Completed.&rsquo; If you do not submit photos of complted work, invoices will not be paid.'

  activerecord:
    attributes:
      property:
        kind: Type
      charges:
        charge_amounts: Individual distribution
      inspection/report:
        kind: Inspection Type
        target: Inspection area
      invoice:
        accounting_debit_card_purchase: Debit card transaction
      maintenance_ticket/appointment:
        assignee: Appointment for
        scheduled_for: Date and time
      custom_forms/form:
        name: Form Title
      custom_forms/automation_settings/event_date_time/v1:
        event_at: Event Date & Time
    errors:
      models:
        bank_account:
          archiving:
            merchant_accounts: Bank accounts with active merchant accounts cannot be archived.
            application_deposit: Bank accounts configured as application fee deposit accounts cannot be archived.
            default_deposit: Bank accounts configured as default deposit accounts cannot be archived.
            default_withdrawal: Bank accounts configured as default withdrawal accounts cannot be archived.
        invoice:
          restrict_dependent_destroy:
            has_one: 'This invoice cannot be deleted because it is linked to a %{record}'
    models:
      inspection/report: Inspection
      inspection/template: Inspection Template
      zillow_claim: Zillow Property Adddress & Details
  activemodel:
    attributes:
      taxes/nelco_registration_form/address:
        region: State
  telephony:
    preamble:
      'This message is sent by Revela on behalf of %{customer_name}. Reply STOP to opt out.'
    collections:
      sms:
        preamble:
          'This message is sent by Revela on behalf of %{customer_name} regarding your past due balance. Reply STOP to opt out.'
        balance_notice:
          'You are more than 30 days past due on your balance owed. Please log in to your account here to make a payment: %{tenant_login_url}'
        balance_notice_chio_mu:
          'You are more than 30 days past due on your balance owed. Please log in to your account here to make a payment: %{tenant_login_url}. If you have questions about invoice charges, please contact the Chapter Executive Team, (i.e., the treasurer, president and personnel chair).'
      phone:
        message:
          'Hello - This call is initiated by Revela on behalf of %{customer_name} to collect a debt. You are more than 60 days past due on your outstanding balance. Please log in to your Revela account and submit a payment or set up a payment plan. Again, please log in to your %{customer_name} Revela account to submit a payment as soon as possible. If you have questions about this balance or anything else, please reach out via your portal after logging in or contact your landlord. Failure to submit payment may result in eviction, impact to your credit score, or other legal action to collect the balance. If you wish to opt out of future communication, please press 1. Thank you and have a great day!'
        message_sae:
          'Hello - This call is initiated by Revela on behalf of %{customer_name} to collect a debt. You are more than 60 days past due on your outstanding balance. Please log in to your Revela account and submit a payment or set up a payment plan. Again, please log in to your %{customer_name} Revela account to submit a payment as soon as possible. If you have questions about this balance or anything else, please reach out via your portal after logging in, call ************, or contact your landlord. That phone number again was ************. Failure to submit payment may result in eviction, impact to your credit score, or other legal action to collect the balance. If you wish to opt out of future communication, please press 1. Thank you and have a great day!'
        message_chio_mu:
          'Hello - This call is initiated by Revela on behalf of %{customer_name} to collect a debt. You are more than 45 days past due on your account. Please log in to your Revela account and submit a payment. If you have questions about this balance due, contact the Chapter Executive Team, (i.e., the treasurer, president and personnel chair). Again, please log  in to your %{customer_name} Revela account to submit a payment as soon as possible. Failure to submit payment may impact your membership benefits, your credit score, or result in legal action to collect the balance. If you wish to opt out of future communication, please press 1. Thank you.'
