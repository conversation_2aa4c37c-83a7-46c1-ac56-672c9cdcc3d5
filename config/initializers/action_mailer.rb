class ActionMailer::MailDeliveryJob
  # Retry before notifying of mailer delivery errors. This happens from time to
  # time when mailgun is experiencing issues.
  #
  # Retry defaults are wait: 3.seconds, attempts: 5, queue: nil, priority: nil
  #
  # We could rescue from other SMTP related errors, however this is the most
  # common one, and others typically signify an issue on our end, e.g. too many
  # recipients or no recipient.
  #
  # Note that we receive this exception from mail response 451 which is often
  # e.g. 'Internal server error'.
  retry_on Net::SMTPAuthenticationError
end
