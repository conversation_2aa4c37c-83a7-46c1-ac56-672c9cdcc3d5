JSONAPI.configure do |config|
  config.json_key_format = :underscored_key
  config.route_format = :underscored_key
  config.default_paginator = :'JSONAPI::CursorOrPaged'
  config.default_page_size = 25
  config.maximum_page_size = 250
end

module JSONAPI
  class BasicResource
    def self._resource_name_from_type(type)
      case type
      when 'api/v2/accounting/properties',
           'api/v2/accounting/property',
           'api/v2/owner_statement/properties',
           'api/v2/owner_statement/property'
        'Api::V2::PropertyResource'
      when 'api/v2/companies', 'api/v2/company'
        'Api::V2::EntityResource'
      when 'api/v2/accounting/leases', 'api/v2/accounting/lease'
        'Api::V2::LeaseResource'
      when 'api/v2/primary_tenants', 'api/v2/primary_tenant'
        'Api::V2::TenantResource'
      when 'api/v2/accounts', 'api/v2/account'
        'Api::V2::Accounting::AccountResource'
      else
        # Original implementation
        "#{type.to_s.underscore.singularize}_resource".camelize
      end
    end

    # rubocop:disable Layout/LineLength
    def self.join_polymorphic_has_one(records, relationship, related_resource_type, join_type, _options)
      resource_klass = relationship.parent_resource                                          # e.g. Api::V2::InvoiceResource
      resource_model = resource_klass._model_class                                           # e.g. Invoice
      resource_table = resource_model.arel_table                                             # e.g. Invoice.arel_table

      related_resource_klass = resource_klass.resource_klass_for(related_resource_type.to_s) # e.g. Api::V2::PropertyResource
      related_model = related_resource_klass._model_class                                    # e.g. Property
      related_table = related_model.arel_table                                               # e.g. Property.arel_table
      related_type_value = related_model.to_s                                                # e.g. 'Property'

      arel_join_type = case join_type
                       when :left
                         Arel::Nodes::OuterJoin
                       when :inner
                         Arel::Nodes::InnerJoin
                       else
                         fail "Unknown join type #{join_type}"
                       end

      relation_name = relationship.name                   # e.g. 'buyer'
      related_type_column_name = :"#{relation_name}_type" # e.g. :buyer_type
      related_id_column_name = :"#{relation_name}_id"     # e.g. :buyer_id

      related_type = resource_table[related_type_column_name]
      related_id = resource_table[related_id_column_name]

      condition = related_type.eq(related_type_value).and(related_id.eq(related_table[:id]))
      join = resource_table.join(related_table, arel_join_type).on(condition)

      records.joins(join.join_sources)
    end
    # rubocop:enable Layout/LineLength
  end

  ##
  # Updates first, next, last links to include scoped journal portion of path
  module ScopedLinksPatch
    def top_level_links
      links = super

      accounting_journal_regex = %r{/api/v2/accounting/(entries|accounts|amounts)}
      owner_statement_regex = %r{/api/v2/owner_statement/entries}

      matching_path_regex = Regexp.union(
        accounting_journal_regex, owner_statement_regex
      )

      return links unless links['first']&.match?(matching_path_regex)

      request_path = @options[:request].path

      links.transform_values do |value|
        value.gsub(matching_path_regex, request_path)
      end
    end
  end

  class ResponseDocument
    prepend ScopedLinksPatch
  end

  # This is a simple cursor pagination implementation that currently supports
  # records sorted ascending by primary key. Essentially the cursor for the
  # next page is the base 64 encoded primary key of the last returned record
  # for this page, so the records fetched for the next page are the ones with a
  # primary key higher than the cursor.
  #
  # When returning a full page of results, it is not aware of if there is a
  # next page or not, because we are 'upstream' of the jsonapi-resources
  # implementation so we would have to perform multiple queries to still return
  # a relation that jsonapi-resources expects. In some cases, then, the last
  # page will be empty.
  class CursorPaginator < JSONAPI::Paginator
    def initialize(params)
      super
      parse_page_size(params)
      parse_cursor(params)
    end

    def apply(relation, _order_options)
      primary_key = relation.primary_key.to_sym

      relation = relation.reorder(primary_key => :asc)

      relation = relation.where(relation.arel_table[primary_key].gt(@cursor)) if @cursor

      relation.limit(@page_size)
    end

    def links_page_params(page)
      next_cursor = find_next_cursor(page)

      links = {}

      links[:first] = { size: @page_size }

      links[:next] = { size: @page_size, cursor: encoded_cursor(next_cursor) } if next_cursor

      links
    end

    private

    def parse_page_size(params)
      provided_size = params&.dig(:size).presence

      size = provided_size&.to_i || JSONAPI.configuration.default_page_size

      @page_size = size.clamp(1..JSONAPI.configuration.maximum_page_size)
    end

    def parse_cursor(params)
      value = params&.dig(:cursor).presence

      @cursor = decoded_cursor(value) if value
    end

    def encoded_cursor(cursor)
      Base64.encode64(cursor.to_s)
    end

    def decoded_cursor(cursor)
      Base64.decode64(cursor)
    end

    def find_next_cursor(page)
      resource_set = page[:fetched_resources]

      resource_klass_to_resources_by_id = resource_set.resource_klasses

      primary_resources_by_id = resource_klass_to_resources_by_id.values.detect do |resources_by_id|
        resources_by_id.values.first[:primary]
      end

      return nil unless primary_resources_by_id

      fetched_ids = primary_resources_by_id.filter_map do |id, resource|
        id if resource[:primary]
      end

      fetched_ids.last if fetched_ids.size == @page_size
    end
  end

  class DynamicPaginator
    extend Forwardable

    def_delegators :@paginator, :apply, :links_page_params, :requires_record_count

    def initialize(params)
      @paginator = make_paginator(params)
    end

    private

    def make_paginator(params)
      paginator_klass(params).new(params)
    end

    def paginator_klass(params)
      fail NotImplementedError
    end
  end

  # This paginator prefers cursor pagination if available,
  # but still supports paged pagination if page[number] is provided.
  class CursorOrPagedPaginator < DynamicPaginator
    private

    def paginator_klass(params)
      if paged_pagination?(params)
        JSONAPI.const_get(:PagedPaginator)
      else
        JSONAPI.const_get(:CursorPaginator)
      end
    end

    def paged_pagination?(params)
      params&.dig(:number).present? || !cursor_pagination_available?
    end

    def cursor_pagination_available?
      Feature.enabled?(:api_v2_cursor_pagination, Customer.current)
    end
  end
end

# Allow request.parsed_body to work in request specs
if Rails.env.test?
  Rails.application.config.after_initialize do
    ActionDispatch::RequestEncoder.register_encoder(
      :api_json, response_parser: JSONAPI::MimeTypes.parser
    )
  end
end
