Time::DATE_FORMATS[:human_datetime] = lambda do |time|
  time.strftime("%B #{time.day.ordinalize}, %Y at %-l:%M %p")
end

Time::DATE_FORMATS[:human_datetime_long] = lambda do |time|
  time.strftime("%A, %B #{time.day.ordinalize}, %Y at %-l:%M %p")
end

Time::DATE_FORMATS[:human_datetime_long_with_timezone] = lambda do |time|
  time.strftime('%a %b %e %l:%M %p %Z')
end

human_date = lambda do |time|
  time.strftime("%B #{time.day.ordinalize}, %Y")
end
Date::DATE_FORMATS[:human_date] = human_date
Time::DATE_FORMATS[:human_date] = human_date

human_month_day = lambda do |time|
  time.strftime("%B #{time.day.ordinalize}")
end
Date::DATE_FORMATS[:human_month_day] = human_month_day
Time::DATE_FORMATS[:human_month_day] = human_month_day

human_date_long = lambda do |time|
  time.strftime("%A, %B #{time.day.ordinalize}, %Y")
end
Date::DATE_FORMATS[:human_date_long] = human_date_long
Time::DATE_FORMATS[:human_date_long] = human_date_long

human_date_short = lambda do |time|
  time.strftime('%b %d, %Y')
end
Date::DATE_FORMATS[:human_date_short] = human_date_short
Time::DATE_FORMATS[:human_date_short] = human_date_short

human_date_short_ordinalized = lambda do |time|
  time.strftime("%b #{time.day.ordinalize}, %Y")
end
Date::DATE_FORMATS[:human_date_short_ordinalized] = human_date_short_ordinalized
Time::DATE_FORMATS[:human_date_short_ordinalized] = human_date_short_ordinalized

Time::DATE_FORMATS[:human_time] = '%l:%M %p'
Time::DATE_FORMATS[:human_time_with_timezone] = '%l:%M %p %Z'

Date::DATE_FORMATS[:short_date] = Time::DATE_FORMATS[:short_date] = '%D'

Time::DATE_FORMATS[:export] = '%Y-%m-%d_%H-%M-%S'

Time::DATE_FORMATS[:short_datetime] = '%D at %-l:%M %p'

Time::DATE_FORMATS[:calendar_datetime] = '%B %-d, %Y %-H:%M %p'
