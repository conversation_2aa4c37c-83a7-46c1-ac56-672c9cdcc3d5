module Elasticsearch
  module Model
    module Response
      class Records
        def ids
          response.response['hits']['hits'].map do |hit|
            hit['_id'].split('-').last
          end
        end
      end
    end

    module Adapter
      module Multiple
        module Records
          def records
            records_by_type = __records_by_type

            records = response.response['hits']['hits'].map do |hit|
              records_by_type[__type_for_hit(hit)][hit[:_id].split('-').last]
            end

            records.compact
          end

          def __records_for_klass(klass, ids)
            numeric_ids = ids.map do |id|
              id.split('-').last
            end

            klass.where(id: numeric_ids)
          end

          def __ids_by_type
            ids_by_type = {}

            response.response['hits']['hits'].each do |hit|
              type = __type_for_hit(hit)
              ids_by_type[type] ||= []
              ids_by_type[type] << hit[:_id].split('-').last
            end
            ids_by_type
          end
        end
      end

      module ActiveRecord
        module Importing
          def __transform
            lambda do |model|
              {
                index: {
                  _id: "#{Customer.current_subdomain}-#{model.id}",
                  data: model.__elasticsearch__.as_indexed_json
                }
              }
            end
          end
        end
      end
    end
  end
end

if Rails.env.production?
  host = ENV['FOUNDELASTICSEARCH_URL'] || ENV['BONSAI_URL'] || ENV.fetch(
    'ELASTICSEARCH_URL', nil
  )
  Elasticsearch::Model.client = Elasticsearch::Client.new(
    host: host,
    transport_options: {
      request: {
        timeout: 15
      }
    }
  )

  # Not available as of 0.19
  # Elasticsearch::Model.settings[:inheritance_enabled] = true
else
  # bin/elasticsearch-users useradd revela -p password -r superuser

  options = {
    transport_options: {
      ssl: {
        verify: false # No need to verify localhost certificate
      }
    }
  }

  Elasticsearch::Model.client = Elasticsearch::Client.new(options)
end
