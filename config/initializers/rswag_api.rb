Rswag::Api.configure do |c|
  # Specify a root folder where Swagger JSON files are located This is used by
  # the Swagger middleware to serve requests for API descriptions NOTE: If
  # you're using rswag-specs to generate Swagger, you'll need to ensure that
  # it's configured to generate files in the same folder
  c.swagger_root = Rails.root.to_s + '/swagger'

  # Inject a lamda function to alter the returned Swagger prior to
  # serialization The function will have access to the rack env for the current
  # request For example, you could leverage this to dynamically assign the
  # "host" property
  c.swagger_filter = lambda { |swagger, env|
    force_ssl = Rails.application.config.force_ssl

    host = env['HTTP_HOST']

    scheme = force_ssl ? 'https' : 'http'

    swagger.deep_transform_values! do |value|
      if value.is_a?(String)
        value.gsub('http://alever.lvh.me', "#{scheme}://#{host}")
      else
        value
      end
    end

    swagger['servers'][0]['url'] = "#{scheme}://{defaultHost}"
    swagger['servers'][0]['variables']['defaultHost']['default'] = host
  }
end
