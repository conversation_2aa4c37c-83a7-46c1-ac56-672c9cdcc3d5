Rails.configuration.to_prepare do
  custom_serializers = [
    Background::Task::Serializer,
    Importers::V3::Result::Successful::Serializer,
    Importers::V3::Result::Unsuccessful::Serializer
  ]

  serializers = (Rails.application.config.active_job.custom_serializers ||= [])

  custom_serializers.each do |serializer|
    serializers << serializer unless serializers.include?(serializer)
  end
end
