Rails.configuration.to_prepare do
  Plutus.class_eval do
    extend Extensions::PlutusBasis
  end

  Plutus::Account.class_eval do
    include Extensions::PlutusAccountBasisSwitch
    include Extensions::PlutusAccountNormally

    belongs_to :parent, class_name: self.name

    has_one :budget_amount, dependent: :destroy

    has_one :bank_account, foreign_key: :ledger_account_id,
                           dependent: :restrict_with_error

    %i[charge credit].each do |kind|
      relation = "#{kind}_presets".to_sym

      has_many relation, inverse_of: :account,
                         foreign_key: 'account_id',
                         dependent: :restrict_with_error
    end

    has_many :lease_move_out_custom_damages,
             class_name: 'Lease::MoveOut::CustomDamage',
             inverse_of: :account,
             foreign_key: 'account_id',
             dependent: :restrict_with_error

    has_many :charge_schedule_entries, inverse_of: :account,
                                       class_name: 'ChargeSchedule::Entry',
                                       foreign_key: 'account_id',
                                       dependent: :restrict_with_error

    has_many :payable_line_items, class_name: 'LineItem',
                                  inverse_of: :payable_account,
                                  foreign_key: 'payable_account_id',
                                  dependent: :restrict_with_error

    has_many :receivable_line_items, class_name: 'LineItem',
                                     inverse_of: :receivable_account,
                                     foreign_key: 'receivable_account_id',
                                     dependent: :restrict_with_error

    has_many :payable_credit_notes,
             class_name: 'Payment',
             inverse_of: :payable_credit_note_account,
             foreign_key: 'payable_credit_note_account_id',
             dependent: :restrict_with_error

    has_many :receivable_credit_notes,
             class_name: 'Payment',
             inverse_of: :receivable_credit_note_account,
             foreign_key: 'receivable_credit_note_account_id',
             dependent: :restrict_with_error

    before_validation :normalize_header

    # Replace uniqueness constraint
    _validators.delete(:name)
    _validate_callbacks.each do |callback|
      if callback.filter.respond_to?(:attributes)
        callback.filter.attributes.delete(:name)
      end
    end
    validates :name, presence: true, uniqueness: {
      scope: %i[tenant_id tenant_type type category header]
    }
    validates :gl_code, presence: true
    validates :category, presence: true

    def self.inherited(subclass)
      super(subclass)
      subclass.include(Searchable::Index)
      subclass.index_fields = %i[name]
      subclass.instance_eval do
        def searchable_type
          'account'
        end
      end
    end

    def url
      Rails.application.routes.url_helpers
           .organization_chart_of_accounts_account_path(tenant_id, id)
    end

    def normalize_header
      self.header = header.presence
    end

    def report_balance(options = {})
      b = balance(options).to_i

      contra? ? -b : b
    end

    # Returns the debit balance of an account. In essence, this flips the
    # balance of credit normal accounts. This is useful for reports where the
    # total balance of accounts should sum to zero, e.g. trial balance et al.
    def debit_balance(options = {})
      b = Money.new(balance(options).to_i)

      if credit_normal?
        -b
      else
        b
      end
    end

    def display_name
      [gl_code, name].join(' - ')
    end

    alias auditable_name display_name
  end

  Plutus::Amount.class_eval do
    include Extensions::PlutusAmountTouchEntries
    include Extensions::PlutusLockedAmounts
    include Extensions::PlutusReconciledAmounts
    include Extensions::PlutusDepositBatchedAmounts
    include Extensions::PlutusAmountsNormalDirection

    belongs_to :reconciliation, class_name: 'BankAccount::Reconciliation',
                                inverse_of: :reconciled_amounts

    audited associated_with: :entry, unless: lambda { |amount|
      amount.entry.persisted_cash_basis_only?
    }

    def auditable_name
      side = credit? ? 'credit' : 'debit'
      amt = Money.new(amount).format
      acc = account.name

      "#{acc}/#{side}/#{amt}"
    end
  end

  module Plutus::AmountsExtension
    prepend Extensions::PlutusAmountsExtensionBalance
  end

  Plutus::Entry.class_eval do
    include Extensions::PlutusAssociatedEntries
    include Extensions::PlutusEntriesBasis
    include Extensions::PlutusEntriesContactName
    include Extensions::PlutusEntriesCopy
    include Extensions::PlutusEntriesDenormalizedReference
    include Extensions::PlutusEntriesKind
    include Extensions::PlutusEntriesLedger
    include Extensions::PlutusEntriesMigrationIgnoresLocks
    include Extensions::PlutusEntriesPropertyAllocation
    include Extensions::PlutusEntriesRecurrable
    include Extensions::PlutusEntriesReverseAutomatically
    include Extensions::PlutusLockedEntries
    include Extensions::PlutusReconciledEntries
    include Plutus::Entry::Exportable

    belongs_to :journal, class_name: 'Company', touch: :accounting_updated_at
    belongs_to :property
    belongs_to :unit
    belongs_to :lease_membership
    belongs_to :tenant

    has_one :lease, through: :lease_membership

    has_many_attached :attachments

    # For rails forms
    has_many :amounts, class_name: 'Plutus::Amount'
    accepts_nested_attributes_for :amounts

    audited except: %i[kind], unless: :persisted_cash_basis_only?
    has_associated_audits

    def accounts
      (credit_amounts + debit_amounts).map(&:account).uniq
    end

    def auditable_name
      id.to_s
    end

    def sorted_amounts
      [credit_amounts, debit_amounts].flatten.sort_by do |amount|
        amount.account.display_name
      end
    end

    def deposit_batch
      amounts.detect(&:deposit_batch)&.deposit_batch
    end

    def url
      Rails.application.routes.url_helpers.accounting_journal_entry_path(
        journal_id, id
      )
    end
  end
end
