# Be sure to restart your server when you modify this file.

# Add new inflection rules using the following format. Inflections
# are locale specific, and you may define rules for as many different
# locales as you wish. All of these examples are active by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.plural /^(ox)$/i, "\\1en"
#   inflect.singular /^(ox)en/i, "\\1"
#   inflect.irregular "person", "people"
#   inflect.uncountable %w( fish sheep )
# end

# These inflection rules are supported but not enabled by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.acronym "RESTful"
# end

ActiveSupport::Inflector.inflections(:en) do |inflect|
  inflect.irregular 'data', 'data'
  inflect.irregular 'chart_of_accounts', 'charts_of_accounts'
  inflect.irregular 'notice_of_non_renewal', 'notices_of_non_renewal'
  inflect.irregular 'was', 'were'
  inflect.irregular 'sms', 'sms'
  inflect.acronym 'CSV' # Comma Separated Values
  inflect.acronym 'FCM' # Firebase Cloud Messaging
  inflect.acronym 'FEMA' # Federal Emergency Management Agency
  inflect.acronym 'HUD' # Housing and Urban Development
  inflect.acronym 'OAuth' # Open Authorization
  inflect.acronym 'RBAC' # Role Based Access Control
  inflect.acronym 'REAMS' # Unclear
  inflect.acronym 'SFTP' # Secure File Transfer Protocol
  inflect.acronym 'SQL' # Structured Query Language
  inflect.acronym 'SSL' # Secure Sockets Layer
  inflect.acronym 'XLSX' # Excel Spreadsheet
end
