require 'doorkeeper/orm/active_record'

Doorkeeper.configure do
  # This block is used when user is logging in through the OAuth flow (e.g. Zapier OAuth setup)
  resource_owner_authenticator do
    current_property_manager || begin
      # We are not logged in, so set the target url and then warden is going to
      # send us to the login path. After login, we will redirect back to the,
      # uh, redirect url.
      session[:property_manager_target_url] = request.url

      warden.authenticate!
    end
  end

  # We are relying moreso on RBAC than OAuth scopes, so stick to obvious defaults.
  default_scopes :read, :write

  # Configure token expiration
  access_token_expires_in 2.hours

  # Enable refresh tokens
  use_refresh_token

  # Reuse existing access tokens instead of creating new ones
  # This can reduce database load and improve performance
  reuse_access_token

  # Use the Ruby class directly instead of string
  orm :active_record
end
