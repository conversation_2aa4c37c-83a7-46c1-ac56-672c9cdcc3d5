module Griddler
  module Mailgun
    class Adapter
      private

      # Modify original implementation to include recipient
      def to_recipients
        to_emails = [
          param_or_header(:To),
          params[:recipient]
        ].compact_blank.join(',')

        to_emails.split(',').map(&:strip)
      end
    end
  end
end

Griddler.configure do |config|
  config.email_service = :mailgun
end
