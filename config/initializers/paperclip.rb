if Rails.env.development? && !ENV['LOCAL_S3']&.to_s == 'true'
  Rails.application.config.to_prepare do
    module Paperclip
      module AlwaysUseFilesystem
        def define_on(klass, name, _options)
          # Ignore options, always use filesystem storage
          super(klass, name, storage: :filesystem)
        end
      end

      class HasAttachedFile
        class << self
          prepend(AlwaysUseFilesystem)
        end
      end
    end
  end
end
