class Redis
  class << self
    def new_redis_instance
      Redis.new(
        url: ENV['REDIS_URL'],
        driver: :ruby,
        ssl_params: { verify_mode: OpenSSL::SSL::VERIFY_NONE }
      )
    end

    def instance(&block)
      @instance ||= begin
        size = ENV.fetch('RAILS_MAX_THREADS', 5).to_i

        ConnectionPool.new(size: size, timeout: 5) do
          new_redis_instance
        end
      end

      @instance.with(&block)
    end
  end
end
