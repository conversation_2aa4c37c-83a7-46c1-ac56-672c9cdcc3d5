require 'action_text/content_helper'
require 'action_text/tag_helper'

# TODO: Remove with Rails 6.1.x
#
# This is used to fix host on blobs in rich texts in mailers.
#
# https://github.com/rails/rails/issues/35578#issuecomment-614246361
ActiveSupport.on_load(:action_mailer) do
  helper ActionText::Engine.helpers

  around_action do |_mailer, action|
    original_renderer = ActionText::Content.renderer
    ActionText::Content.renderer = self

    action.call
  ensure
    ActionText::Content.renderer = original_renderer
  end
end
