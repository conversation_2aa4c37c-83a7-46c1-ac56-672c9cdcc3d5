# TODO: This could go away if we switch to e.g. dart-sass-rails
#
# TODO: Rails 7.1 introduces Rails.env.local?
#
# Essentially sassc-rails for some reason re-implements SassCompressor
# differently than the one that comes with sprockets, and uses a random hash
# for a cache key, which means that caching never works for any sass assets
# between test runs.
if Rails.env.development? || Rails.env.test?
  module Sprockets
    # This implementation is copied from sprockets
    class SassCompressor
      def initialize(options = {})
        @options = {
          syntax: :scss,
          cache: false,
          read_cache: false,
          style: :compressed
        }.merge(options).freeze
        @cache_key = "#{self.class.name}:#{Autoload::Sass::VERSION}:#{VERSION}:#{DigestUtils.digest(options)}".freeze
      end
    end
  end
end
