remove_links = "
  const links = document.querySelectorAll('.paper a');

  links.forEach(link => {
      const element = document.createElement('span');
      element.textContent = link.textContent;
      link.parentNode.replaceChild(element, link);
  });
"

zoom_out_reports = "
  const printableReports = document.getElementsByClassName('printable report');

  if (printableReports.length === 1) {
    // Determine the width of one inch in pixels
    const inBlock = document.createElement('div');
    inBlock.id = 'in-block';
    inBlock.style.width = '1in';
    inBlock.style.height = '1in';
    document.body.appendChild(inBlock);
    const oneInchToPixels = inBlock.offsetWidth;
    document.body.removeChild(inBlock);

    // Available width in pixels, less margins
    const availableWidth = (8.5 - 0.5 - 0.5) * oneInchToPixels;

    // Zoom out tables that are larger than the available width
    const printableReport = printableReports[0];
    const tables = printableReport.getElementsByTagName('table');
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      const tableWidth = table.offsetWidth;
      const zoom = availableWidth / tableWidth;

      if (zoom < 1) {
        table.style.zoom = zoom;
      }
    }
  }
"

script = remove_links + zoom_out_reports

Grover.configure do |config|
  config.use_png_middleware = false
  config.use_jpeg_middleware = false
  config.use_pdf_middleware = true

  config.options = {
    emulate_media: 'print',
    format: 'letter',
    execute_script: script,
    margin: {
      top: '.5in',
      right: '.5in',
      bottom: '.5in',
      left: '.5in'
    }
  }
end

# In the test environment, we don't need to invoke grover and can instead
# return a sample PDF. This avoids the cost of invoking puppeteer /
# chromedriver, and allows the test suite to run on platforms not supported by
# puppeteer.
if Rails.env.test?
  class Grover
    def to_pdf
      sample_path = Rails.root.join('spec', 'fixtures', 'invoice.pdf')

      File.read(sample_path)
    end
  end
end
