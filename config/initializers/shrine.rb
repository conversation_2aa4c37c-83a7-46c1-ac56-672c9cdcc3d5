require 'shrine'

use_file_system = !Rails.env.production? && !(ENV['LOCAL_S3']&.to_s == 'true')

if use_file_system
  require 'shrine/storage/file_system'

  Shrine.storages = {
    cache: Shrine::Storage::FileSystem.new('public', prefix: 'uploads/cache'),
    store: Shrine::Storage::FileSystem.new('public', prefix: 'uploads')
  }

  Shrine.plugin :upload_endpoint
else
  require 'shrine/storage/s3'

  s3_options = {
    access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
    secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
    bucket: ENV.fetch('S3_UPLOADS_BUCKET'),
    region: 'us-east-1',
    public: true
  }

  Shrine.storages = {
    cache: Shrine::Storage::S3.new(prefix: 'cache', **s3_options),
    store: Shrine::Storage::S3.new(**s3_options)
  }

  Shrine.plugin :presign_endpoint, presign_location: lambda { |request|
    [
      # Environemnt likely is production here,
      # but keeping this in case we are testing s3 locally.
      ("#{Rails.env}_uploads" unless Rails.env.production?),
      Customer.current_subdomain,
      SecureRandom.hex,
      request.params['filename']
    ].compact.join('/')
  }
end

Shrine.plugin :activerecord
Shrine.plugin :determine_mime_type, analyzer: :marcel
Shrine.plugin :tempfile
Shrine.plugin :derivatives, create_on_promote: true
Shrine.plugin :instrumentation if Rails.env.development?
