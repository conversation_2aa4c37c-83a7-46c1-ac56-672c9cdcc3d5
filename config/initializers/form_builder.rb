class ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
  def semantic_date_field(method, options = {}, calendar_options = {})
    data = { init: false, options: { type: 'date', **calendar_options } }
    data.merge!(options[:data] || {})
    input_class = options[:input_class] || 'ui left icon input'
    @template.content_tag :div, class: 'ui calendar', data: data do
      @template.content_tag :div, class: input_class do
        options[:placeholder] ||= begin
          default_placeholder = 'Type or pick a date'
          if calendar_options.dig(:type) == 'datetime'
            default_placeholder += ' and time'
          end
          default_placeholder
        end

        options[:autocomplete] = 'off'

        icon = @template.content_tag(:i,
                                     nil,
                                     class: 'calendar alternate outline icon')

        field = @template.text_field(@object_name,
                                     method,
                                     objectify_options(options))

        icon + field
      end
    end
  end

  def semantic_dropdown(method, choices, options = {}, html_options = {})
    default_html_options = {
      class: 'ui dropdown',
      data: {
        init: false,
        options: {
          fullTextSearch: true,
          searchSelection: 'text'
        }
      }
    }

    options[:selected] ||= @object.try(method)

    @template.select(
      @object_name, method, choices, options,
      default_html_options.deep_merge(html_options)
    )
  end

  def no_or_explain_field(method, value, options = {}, html_options = {})
    default_html_options = {
      class: 'ui no-or-explain dropdown',
      data: {
        init: false
      }
    }

    existing_value = value || @object&.try(method) || 'No'
    options[:selected] ||= 'yes' unless ['No', nil].include?(existing_value)

    dropdown = select(
      nil,
      [['No', 'no'], ['Yes', 'yes']],
      options,
      default_html_options.merge(html_options)
    )

    textfield = @template.text_field(@object_name, method, {
      class: 'no-or-explain',
      placeholder: 'Please explain...',
      value: existing_value
    })

    dropdown + textfield
  end

  def upload_field(method, options = {})
    @template.content_tag :div, class: 'ui fluid upload action input',
                                data: { init: false } do

      file_opts = objectify_options(options)

      @template.file_field(@object_name, method, file_opts) +
        @template.content_tag(:button, class: 'ui disabled button') do
          @template.content_tag(:i, nil, class: 'up arrow icon') +
            'Upload'
        end
    end
  end

  def money_field(method, options = {})
    default_html_options = { data: { init: false } }

    options[:value] ||= begin
      amount = @object.send(method) if @object.respond_to?(method)

      if amount.is_a?(String)
        Monetize.parse(amount).format
      elsif amount.is_a?(Money) && amount.nonzero?
        amount.format
      else
        ''
      end
    end

    unless options[:class].to_s.include?('money input')
      options[:class] = [options[:class], 'money input'].compact.join(' ')
    end

    @template.text_field(
      @object_name, method, default_html_options.deep_merge(options)
    )
  end

  def semantic_search_field(method, options = {})
    icon = options[:icon].presence || 'search'

    url = options.delete(:url) || '/api/v1/search?q={query}'

    side = options.delete(:side) || :left

    default_html_options = {
      class: 'ui search',
      data: {
        init: false,
        options: {
          minCharacters: 0,
          searchOnFocus: true,
          apiSettings: {
            url: url
          }
        }
      }
    }

    existing = @object.send(method) if @object.respond_to?(method)
    text = existing&.to_s
    sgid = existing&.to_sgid&.to_s

    input_class = side.to_s == 'left' ? 'ui left icon input' : 'ui icon input'

    @template.content_tag(:div, default_html_options.deep_merge(options)) do
      @template.content_tag(:div, class: input_class) do
        @template.text_field_tag(
          nil, text,
          placeholder: options[:placeholder],
          class: 'prompt'
        ) + @template.content_tag(:i, nil, class: "#{icon} icon") +
          @template.hidden_field(@object_name, method, value: sgid)
      end
    end
  end

  def semantic_search_toggle(method, options = {})
    search_options = options.delete(:search_options) || []

    default_html_options = {
      class: 'ui search',
      data: {
        controller: 'semantic-search-toggle',
        semantic_search_toggle_target: 'search'
      }
    }

    existing = @object.send(method) if @object.respond_to?(method)
    text = existing&.to_s
    sgid = existing&.to_sgid&.to_s
    dropdown_value = search_options.find { |opt| opt[:klass] == existing&.class&.name }&.dig(:value) || search_options.first.dig(:value)

    input_class = 'ui right labeled input'

    @template.content_tag(:div, default_html_options.deep_merge(options)) do
      @template.content_tag(:div, class: input_class) do
        @template.text_field_tag(
          nil, text,
          placeholder: options[:placeholder],
          class: 'prompt',
        ) +
          @template.hidden_field(@object_name, method, value: sgid) +

          @template.content_tag(:div, class: 'ui dropdown label') do
            @template.content_tag(:div, dropdown_value || '', class: 'text') +
              @template.content_tag(:i, nil, class: 'dropdown icon') +
              @template.content_tag(:div, class: 'menu') do
                first = search_options.shift
                html = @template.content_tag(:div, first[:value], class: 'item', data: { url: first[:url], semantic_search_toggle_target: 'item' })
                search_options.each { |opt| html += @template.content_tag(:div, opt[:value], class: 'item', data: { url: opt[:url], semantic_search_toggle_target: 'item' }) }
                html
              end
          end
      end
    end
  end

  def markup_field(method, options = {})
    kind_val = @object&.try("#{method}_kind") || 'percent'
    raw_sym = "#{method}_raw".to_sym
    val = @object&.try(raw_sym) || ''

    klass = if options[:small]
              'ui action fluid transparent input'
            else
              'ui action input'
            end
    input_style = 'text-align: right;'
    input_style += 'margin-right: 0.5em;' if options[:small]

    @template.content_tag(:div, class: klass) do
      id = SecureRandom.alphanumeric(10)
      hidden = @template.hidden_field(@object_name,
                                      "#{method}_kind",
                                      id: id,
                                      class: 'markup-kind input',
                                      value: kind_val)
      markup = @template.text_field(@object_name,
                                    raw_sym,
                                    class: 'markup input',
                                    value: val,
                                    style: input_style,
                                    placeholder: options[:placeholder])

      icon = @template.content_tag(:i, nil, class: 'small hourglass icon')
      button_class = 'ui basic disabled icon markup-kind button'
      button_class += ' mini' if options[:small]
      button_style = 'border-radius: 2px;' if options[:small]
      button = @template.content_tag(:button,
                                     type: 'button',
                                     data: { field: id },
                                     class: button_class,
                                     style: button_style) do
                                       icon
                                     end

      hidden + markup + button
    end
  end

  def tags_field(allow_additions: true)
    class_name = @object.class.name

    class_name = @object.model.class.name if class_name.ends_with?('Decorator')

    available_tags = TagsQuery.new.search.for_type(class_name)

    selected = @object&.tags&.pluck(:tag)

    placeholder = if allow_additions
                    'Type to search or enter new tags'
                  else
                    'Type to search for tags'
                  end

    semantic_dropdown \
      :tags,
      available_tags,
      { selected: selected },
      multiple: true,
      data: {
        options: {
          multiple: true,
          allowAdditions: allow_additions,
          forceSelection: false,
          placeholder: placeholder
        }
      },
      class: 'ui multiple search selection dropdown'
  end

  def uppy_dashboard_field(method, options = {})
    default_id = [@object_name, method, 'uppy', 'dashboard'].join('_')

    id = options.fetch(:id, default_id)

    data = {
      controller: 'uppy-dashboard',
      uppy_dashboard_debug_value: options.fetch(:debug, Rails.env.development?),
      uppy_dashboard_use_s3_value: options.fetch(:use_s3, Rails.env.production?),
      uppy_dashboard_use_active_storage_value: options.fetch(:use_active_storage, false),
      uppy_dashboard_active_storage_attribute_value: "#{@object_name}[#{method}][]",
      uppy_dashboard_collection_name_value: "#{@object_name}[#{method}_attributes]",
      uppy_dashboard_attribute_name_value: 'upload',
      uppy_dashboard_restrictions_value: {
        maxFileSize: options.fetch(:max_file_size, 10.megabytes),
        maxTotalFileSize: options.fetch(:max_total_file_size, 25.megabytes),
        maxNumberOfFiles: options.fetch(:max_number_of_files, 15),
        allowedFileTypes: Array(options.fetch(:accept, 'image/*'))
      },
      uppy_dashboard_note_value: options.fetch(:note, 'Images only, maximum of 15 files totaling 25 MB'),
      uppy_dashboard_height_value: options.fetch(:height, 325),
      uppy_dashboard_webcam_modes_value: options.fetch(:webcame_modes, ['picture'])
    }

    @template.content_tag :div, nil, id:, data:
  end
end

class ActionView::Helpers::Tags::Label::LabelBuilder
  prepend (
    Module.new do
      def translation
        super.titleize
      end
    end
  )
end
