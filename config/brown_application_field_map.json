[{"form_key": "first_name", "pdf_key": "First", "label": "First Name"}, {"form_key": "last_name", "pdf_key": "Last", "label": "Last Name"}, {"form_key": "middle_name", "pdf_key": "Middle Initial", "label": "Middle Name"}, {"form_key": "phone", "pdf_key": "Tel", "label": "Phone Number"}, {"form_key": "email", "pdf_key": "Email", "label": "Email"}, {"form_key": "street_address", "pdf_key": "Street"}, {"form_key": "city", "pdf_key": "City", "label": "City"}, {"form_key": "state", "pdf_key": "State"}, {"form_key": "zip", "pdf_key": "Postal Code"}, {"form_key": "country", "pdf_key": "Country"}, {"form_key": "academic_department", "pdf_key": "Academ ic Dept"}, {"form_key": "start_date", "pdf_key": "Group5", "type": "radio", "radios": [{"form_key": "june_2", "pdf_key": "Choice1"}, {"form_key": "july_1", "pdf_key": "Choice2"}, {"form_key": "august_1", "pdf_key": "Choice3"}]}, {"form_key": "gender", "pdf_key": "Group5", "type": "radio", "radios": [{"form_key": "male", "pdf_key": "Choice4"}, {"form_key": "female", "pdf_key": "Choice5"}]}, {"form_key": "roommate", "pdf_key": "Group7", "type": "radio", "radios": [{"form_key": "a", "pdf_key": "Choice8"}, {"form_key": "b", "pdf_key": "Choice9"}]}, {"form_key": "furnishings", "pdf_key": "Group8", "type": "radio", "radios": [{"form_key": "yes", "pdf_key": "Choice6"}, {"form_key": "no", "pdf_key": "Choice7"}]}]