# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = 'https://www.revela.co'

if Rails.env.production?
  bucket = 'revela-public'

  SitemapGenerator::Sitemap.sitemaps_host = "https://#{bucket}.s3.amazonaws.com"

  SitemapGenerator::Sitemap.public_path = 'tmp/sitemap'

  SitemapGenerator::Sitemap.adapter = SitemapGenerator::AwsSdkAdapter.new(
    bucket,
    aws_access_key_id: ENV['AWS_ACCESS_KEY_ID'],
    aws_secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
    aws_region: 'us-east-1'
  )
end

SitemapGenerator::Sitemap.create do
  # Put links creation logic here.
  #
  # The root path '/' and sitemap index file are added automatically for you.
  # Links are added to the Sitemap in the order they are specified.
  #
  # Usage: add(path, options={})
  #        (default options are used if you don't specify)
  #
  # Defaults: :priority => 0.5, :changefreq => 'weekly',
  #           :lastmod => Time.now, :host => default_host
  #
  # Examples:
  #
  # Add '/articles'
  #
  #   add articles_path, :priority => 0.7, :changefreq => 'daily'
  #
  # Add all articles:
  #
  #   Article.find_each do |article|
  #     add article_path(article), :lastmod => article.updated_at
  #   end

  add '/security'
  add '/contact'
  add '/blog', changefreq: 'daily'
  add '/company'
  add '/values'
  add '/privacy'
  add '/services'
  add '/training'
  add '/research'
  add '/case_studies'
  add '/university'
  add '/careers', changefreq: 'daily'

  BoringScience::Article.published.where(blog: 'revela').find_each do |article|
    add "/blog/#{article.slug}", lastmod: article.updated_at
  end
end
