require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Revela; end

class Revela::Application < Rails::Application
  # Settings in config/environments/* take precedence over those specified here.
  # Application configuration should go into files in config/initializers
  # -- all .rb files in that directory are automatically loaded.

  config.load_defaults '6.1'

  # Set Time.zone default to the specified zone and make Active Record
  # auto-convert to this zone.
  # Run "rake -D time" for a list of tasks for finding time zone names. Default
  # is UTC.
  config.time_zone = 'Eastern Time (US & Canada)'

  # The default locale is :en and all translations from config/locales/*.rb,yml
  # are auto loaded.
  # config.i18n.load_path += Dir[
  #   Rails.root.join('my', 'locales', '*.{rb,yml}').to_s
  # ]
  config.i18n.enforce_available_locales = true
  config.i18n.default_locale = :en
  config.i18n.fallbacks.map = {
    adpi: :greek_housing,
    greek_housing: :en,
    home_owners_association: :en
  }

  # Use haml for template generation
  config.generators do |g|
    g.template_engine :haml
  end

  # Load lib directory
  config.eager_load_paths << Rails.root.join('lib')

  # Ignore lib/rubocop directory
  Rails.autoloaders.main.ignore(Rails.root.join('lib/rubocop'))

  # Load extra app folders
  %w[forms importers exporters queries policies services validators].each do |folder|
    config.eager_load_paths << Rails.root.join('app', folder, '{*/}')
  end

  # Load view component from rspec
  config.view_component.preview_paths << \
    Rails.root.join('spec/components/previews')

  # Use sidekiq for ActiveJob
  config.active_job.queue_adapter = :sidekiq

  # CORS
  config.middleware.insert_before 0, Rack::Cors do
    allow do
      origins '*'
      resource '/api/v1/listings.json', headers: :any, methods: %i[get]
      resource '/api/v1/floorplans.json', headers: :any, methods: %i[get]
    end

    allow do
      origins /^null$/
      resource '/api/v2/*', headers: :any, methods: %i[get], credentials: false
      resource '/api/documentation/*', headers: :any, methods: %i[get], credentials: false
    end
  end

  config.middleware.use Grover::Middleware, except: [%r{^/rails}, %r{^/api}]

  # Don't wrap invalid fields until we can do this with semantic
  config.action_view.field_error_proc = proc { |html_tag, _instance| html_tag }

  # Automatic ids on labels and inputs from form_with
  config.action_view.form_with_generates_ids = true

  config.active_record.belongs_to_required_by_default = false

  config.action_view.form_with_generates_remote_forms = false

  # https://github.com/rails/sprockets-rails/pull/489
  config.assets.resolve_assets_in_css_urls = false

  config.view_component.default_preview_layout = 'component_preview'

  config.action_mailer.deliver_later_queue_name = 'within_one_hour'

  config.customer_specific_behavior = config_for(:customer_specific_behavior)

  damage_waiver_defaults = config_for(:damage_waiver_defaults)
  damage_waiver_defaults[:alever] = damage_waiver_defaults.values.first
  damage_waiver_defaults[:staging] = damage_waiver_defaults.values.first
  config.damage_waiver_defaults = damage_waiver_defaults


  config.insurance = config_for(:insurance)

  config.x.rbac.permissions = config_for('rbac/permissions')
end
