:queues:
  # Queues for worst-case-acceptable-latency requirements.
  - within_one_minute
  - within_one_hour
  - within_one_day

  # Queues for global throttling.
  # Use for tasks that cannot safely run at the same time.
  - global_isolation
  - report_isolation

  # Queue for per-process throttling.
  # Useful for limiting CPU intensive tasks per worker process.
  - process_isolation

  # Queue for cron related scheduling.
  - cron

  # Integration specific queues.
  # Useful for controlling throughput based on what the integration can handle.
  - inspectify
  - mercury
  - reams
  - sierra_leone
  - zeamster

  # Default queue.
  # Prefer a more specific queue, but do not remove in case jobs do not specify a queue.
  - default

  # Historical and categorical queues.
  # Prefer a worst-case-acceptable-latency-queue.
  - importers
  - mailers
  - reports
  - search

  # Rails provided queues.
  - action_mailbox_routing
  - action_mailbox_incineration
  - active_storage_analysis
  - active_storage_purge

# These limits prevent more than N jobs from running at the same time globally.
:limits:
  global_isolation: 1
  inspectify: 1
  mercury: 1
  reams: 1
  report_isolation: 1
  sierra_leone: 1
  zeamster: 3

# These limits prevent more than N jobs from running at the same time per worker process.
:process_limits:
  process_isolation: 1
  reports: 8
  within_one_day: 1
  within_one_hour: 4

:scheduler:
  :schedule:
    ten_jobs:
      cron: '*/10 * * * * UTC' # Every 10 minutes
      class: Cron::RunEveryTenMinutesJob
    sixty_jobs:
      cron: '3 * * * * UTC' # Every 1 hour, 3 minutes after the top of the hour
      class: Cron::RunEveryHourJob
    fourteen_forty_jobs:
      cron: '3 5 * * * UTC' # Every day at 5:03 UTC
      class: Cron::RunEveryDayJob
