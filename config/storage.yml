local:
  service: S3
  access_key_id: <%= ENV['AWS_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['AWS_SECRET_ACCESS_KEY'] %>
  region: us-east-1
  bucket: <%= ENV['S3_ACTIVE_STORAGE_BUCKET'] %>

test:
  service: Disk
  root: <%= Rails.root.join('tmp/storage') %>

amazon:
  service: S3
  access_key_id: <%= ENV['AWS_ACCESS_KEY_ID'] %>
  secret_access_key: <%= ENV['AWS_SECRET_ACCESS_KEY'] %>
  region: us-east-1
  bucket: <%= ENV['S3_ACTIVE_STORAGE_BUCKET'] %>
