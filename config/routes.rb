Rails.application.routes.draw do
  extend Routes::LandingPage

  # Archived Customers
  get '/archived' => 'customers#archived', as: :archived_customer

  # Customer Registration
  namespace :customer_setup do
    get :welcome
    resources :administrators, only: %i[index create]
    resource :client_entity, as: :company, only: %i[show create update]
    resource :chart_of_accounts, only: %i[show create]
    %i[
      entity_directories
      owner_directories
      properties
      units
      rent_rolls
      lease_charges
      tenant_directories
      vendor_directories
      general_ledgers
      beginning_balances
      work_orders
      employees
      bank_accounts
    ].each do |route|
      resources route, only: %i[index create] do
        collection do
          get :template
          get :sample
        end
      end
    end
    get :review
    get :finish
  end

  # Management

  devise_scope :property_manager do
    patch '/property_managers/confirmation' =>
    'property_managers/confirmations#update', as:
      :update_property_manager_confirmation
  end

  devise_for :property_managers,
             path_names: { sign_in: 'login' },
             controllers: {
               confirmations: 'property_managers/confirmations',
               passwords: 'property_managers/passwords',
               registrations: 'property_managers/registrations',
               sessions: 'property_managers/sessions'
             }

  get '/search' => 'search#search'
  resources :notifications, only: %i[index show] do
    get :mark_all_as_seen, on: :collection
    patch :update_preferences, on: :collection
  end
  get :whats_new, to: 'whats_new#index', as: :whats_new

  # Attachments
  post '/attachments/:attachable_sgid', to: 'attachments#create'
  delete '/attachments/:attachable_sgid/:id', to: 'attachments#destroy'

  # handled by cloudflare redeem_attachment worker route
  get '/cf/redeem_attachment/:filename', as: :cloudflare_redeem_attachment, to: 'attachments#dummy'

  get '/attachments/:attachable_sgid/signed_url',
      to: 'attachments#signed_upload_url'

  concern :approving do
    member do
      get :request_approval
      get :approve
    end
  end

  concern :archiving do
    member do
      get :archive
      get :unarchive
    end
  end

  concern :batch_archiving do
    collection do
      get :archive
      get :unarchive
    end
  end

  concern :bulk_archiving do
    collection do
      post :bulk_archive
      get :bulk_archive_modal
      get :bulk_unarchive
    end
  end

  concern :account_management do
    member do
      get :send_invite
      get :send_password_reset
    end
  end

  concern :has_deposit_account do
    resources :payment_methods, only: %i[create destroy]
  end

  concern :electronic_signable do
    member do
      delete :destroy_document
      post :request_signatures
      get :cancel_signatures
      get :resend_signature
    end
  end

  concern :contact do
    member do
      # TODO: remove
      get :mockup
    end
  end

  concern :document_template_parent do
    resources :document_templates, only: %i[new create]
  end

  namespace :management, path: '/manage', as: nil do
    post :help, to: 'help#create', format: :json

    resources :parking_lots do
      resources :parking_reservations
      resources :parking_spaces, as: :spaces, only: %i[index destroy] do
        post :import, on: :collection
      end
      member do
        get :new_import
        post :import
      end
    end

    get '/properties/new', to: 'property_setups#new', as: :new_property
    post '/properties', to: 'property_setups#create', as: :properties
    resources :properties, except: %i[new create destroy] do
      member do
        post :offer_refinance
        get :prepare_archive
        post :archive
        get :unarchive
        get :export
        patch :custom_status, to: 'properties#update_custom_status'
      end

      resources :floorplans, except: %i[index show]

      resources :tenant_ledger_imports, only: %i[new create show]

      resources :transfers, controller: :property_transfers,
                            only: %i[new create]

      resource :setup, controller: 'property_setups' do
        patch :update_information
        get :land
        patch :update_land
        %i[buildings floorplans units leases].each do |route|
          resource route, only: :show, module: 'property_setups' do
            post :import
          end
        end
        resource :floorplan, only: %i[show update],
                             module: 'property_setups',
                             controller: 'floorplan'
        resource :occupancy, only: %i[show update], module: 'property_setups'
        get :review
        get :finish
      end

      resource :document_hierarchy, module: 'properties', only: :show do
        get :export_folder
      end

      resources :documents, module: 'properties', only: %i[create destroy]
    end

    resources :portfolios do
      get :export, on: :member
      resources :default_onboardings, only: %i[create] do
        collection do
          delete :destroy
        end
      end
      resources :onboarding_auto_assignment, only: [] do
        collection do
          get :batch_turn_on_modal
          post :batch_turn_on
          post :batch_turn_off
          delete :batch_turn_off
        end
      end
    end
    resources :tenants, except: %i[new create destroy],
                        concerns: %i[
                          account_management
                          contact
                          document_template_parent
                        ] do
      get :batch_invite, on: :collection
      resources :payment_methods, only: %i[new create destroy]
      resources :scheduled_payments, except: :index
      resources :payment_plans, only: %i[show destroy]
      get :seven_day_notice, on: :member
      resources :evictions, only: %i[create new]
      resources :refunds, only: %i[new create]
      resources :deposit_transfers, only: %i[new create]
      member do
        get :transactions

        get :charge, to: 'charges#new'
        get 'charge/presets', to: 'charges#presets'
        post :charge, to: 'charges#create'

        get :credit, to: 'credits#new'
        get 'credit/presets', to: 'credits#presets'
        post :credit, to: 'credits#create'
      end
      resource :documents, only: :show
      resources :utility_transfers, only: :show
      resources :tenant_merges, as: :merges, only: %i[index new create]
      get :locations, on: :collection, to: '/scopes#locations'
    end
    get '/todos/:id/toggle' => 'todos#toggle', as: :toggle_todo
    get '/todos/:id/unassign' => 'todos#unassign', as: :unassign_todo

    resources :onboardings, except: %i[edit update], concerns: %i[archiving bulk_archiving] do
      member do
        get :assignees
        delete :unassign
        get :default_properties
        post :copy
      end
      get :selectable_properties, on: :collection
      resources :assignments, only: %i[new create index], controller: 'onboardings/assignments' do
        collection do
          get :assign
          post :submit_assign
          get '/new/bulk_assign_modal' => 'onboardings/assignments#bulk_assign_modal', as: :bulk_assign_modal
          get '/assign/bulk_assign_modal' => 'onboardings/assignments#bulk_assign_modal', as: :assign_bulk_assign_modal
          get :bulk_unassign_modal
          post '/new/bulk_assign' => 'onboardings/assignments#bulk_assign', as: :bulk_assign
          post :bulk_unassign
          post :property_contacts
        end
        get :unassign_modal, on: :member
      end
      resources :auto_assignments, only: %i[index], controller: 'onboardings/auto_assignments' do
        collection do
          post :batch_turn_on
          post :batch_turn_off
          post :submit_auto_assignments
          get :manage
        end
      end
      resources :modules, only: :show, module: :onboardings, param: :module_id
    end
    # These are for onboarding setup wizard, hence the token param. The rest should go in above
    resources :onboardings, only: %i[edit update], param: :token do
      get 'module_selection'
      post 'module_selection' => 'onboardings#process_module_selection'
      resources :modules, only: %i[edit update], module: :onboardings, param: :module_id
    end

    resources :units do
      resources :downtimes, only: %i[create destroy],
                            controller: 'units/downtimes' do
        get :terminate, on: :member
      end

      member do
        get :transactions
        get :prepare_archive
        post :archive
        get :unarchive
      end
    end
    resources :vehicles, only: :show
    resources :vendors,
              concerns: %i[archiving has_deposit_account contact] do
      member do
        get :invite
        get :invoices
        post :send_insurance_reminder
      end
      resources :invites, only: :create, module: :vendors
      resource :sku_list, only: %i[create destroy show], module: :vendors
      resources :contracts, only: %i[create update],
                            module: :vendors,
                            concerns: :archiving
    end
    resources :owners, except: :destroy,
                       concerns: %i[contact document_template_parent] do
      # Doesn't use the account management concern because the workflows are
      # a bit different.
      member do
        get :send_confirmation_instructions
        get :send_password_reset

        get :prepare_archive
        post :archive
        get :unarchive
      end
    end
    namespace :owners do
      post :send_invite
    end

    resources :companies, except: :destroy,
                          as: :manage_companies,
                          concerns: :has_deposit_account do
      member do
        get 'setup'
        post 'finalize'

        get :prepare_archive
        post :archive
        get :unarchive
      end
      resources :month_end_reviews, only: %i[new create] do
        get :summary, on: :collection
        get :transactions, on: :collection
      end
      resource :management_contract, concerns: :electronic_signable,
                                     only: %i[show edit update] do
        resources :electronic_documents, module: :management_contracts,
                                         only: %i[new create] do
          get :metadata_fields, on: :collection
        end
      end
      resources :schedule_es, controller: 'companies/schedule_es',
                              only: %i[new create]
      resources :owner_contribution_requests,
                controller: 'companies/owner_contribution_requests',
                only: %i[create destroy]
      resources :owner_contributions,
                controller: 'companies/owner_contributions',
                only: :create
      resources :owner_credits,
                controller: 'companies/owner_credits',
                only: :create
    end
    resources :management_fees, only: :create
    resources :disbursements, only: :create
    resources :bulk_property_balance_transfers, only: :create

    resources :addresses, only: %i[create update]

    resources :tags, only: :index

    resources :calendar_events, only: %i[create destroy index], format: :json

    get :modal_search, to: 'modal_search#index', format: :json
    resources :linkages, only: %i[index destroy]
    get 'audit_logs/:id', to: 'audit_logs#show', as: :audit_logs

    namespace :approvals do
      resources :change_requests, only: :create do
        patch :complete, on: :member
      end
    end

    resources :rooms, only: %i[create update], concerns: :archiving

    resource :masquerade, only: %i[create destroy]
  end

  extend Routes::Api::V1
  extend Routes::Api::V1::Tenant
  extend Routes::Api::V2
  extend Routes::Apply
  extend Routes::Assurant
  extend Routes::Development
  extend Routes::Forms
  extend Routes::LandlordVerification
  extend Routes::Lending
  extend Routes::Morocco
  extend Routes::PortfolioSetup
  extend Routes::Operations
  extend Routes::Opportunities
  extend Routes::Owners
  extend Routes::RiskRelease
  extend Routes::Settings
  extend Routes::Telephony
  extend Routes::Tenants
  extend Routes::Users
  extend Routes::Webhooks
  extend Routes::Vendors

  authenticate(:admin_user) { extend Routes::Registration }

  # Maintenance
  namespace :maintenance do
    resources :tickets, except: :edit do
      get :available, on: :collection
      get :service_areas, on: :collection
      get :service_area, on: :collection
      get :vendors, on: :collection
      get :assignable_employees, on: :collection
      get :assignees, on: :collection
      resources :vendor_assignments, only: %i[create destroy] do
        get :vendor_status, on: :collection
      end
      resources :bid_requests, controller: 'tickets/bid_requests', only: :create do
        resources :invites, controller: 'tickets/bid_requests/invites', only: [] do
          member do
            get :resend
          end
        end
      end
      resources :bids, controller: 'tickets/bids', only: [:show, :index] do
        member do
          patch :approve
          patch :reject
        end
      end
      resources :comments, controller: 'tickets/comments',
                           only: %i[create destroy]
      resources :messages, controller: 'tickets/messages', only: :create
      resources :estimates, controller: 'tickets/estimates',
                            only: %i[create update destroy]
      resources :billings, controller: 'tickets/billings', only: :create
      resources :receipts, only: :create
      resources :attachments, controller: 'tickets/attachments',
                              only: :destroy, param: :attachment_sgid do
        collection do
          get :grid
          get :list
          get :download_all
        end
      end

      member do
        get :overview, to: 'tickets#show'
        get :close
        get :confirm_close
        get :open
        get :resolve
        get :create_child
        get :associate_tenant
        post :comment
      end
      controller 'tickets/batch_actions' do
        collection do
          get :archive
          get :assign
          get :tag
          get :reopen
          get :resolve
          get :close
          get :urgency
        end
      end
      resource :defer, controller: 'tickets/defers',
                       only: %i[create update destroy]
      resource :appointment, controller: 'tickets/appointments',
                             only: %i[create update destroy]
      resource :sidebar_balances, controller: 'tickets/sidebar_balances',
                                  only: :show
    end
    resources :estimates do
      member do
        patch :toggle_item_amounts
        post :project
        post :maintenance_ticket
        post :owner_approval
      end
    end
    resources :tags, except: %i[edit show] do
      post :upload, on: :collection
    end
    get :service_areas, to: '/scopes#service_areas'
  end

  # Leasing
  namespace :leasing do
    get '/leases/available_units.json', to: 'leases#available_units'
    get '/leases/available_floorplans.json', to: 'leases#available_floorplans'
    get '/leases/applicants.json', to: 'leases#applicants'
    get '/leases/check_availability.json', to: 'leases#check_availability'
    get '/leases/charge_presets.json', to: 'leases#charge_presets'
    resources :guest_cards, except: %i[destroy index], concerns: :archiving
    resources :tours, only: %i[new create]
    resources :leads, only: %i[index show edit update]
    resources :applications, only: %i[show update], concerns: :archiving do
      resources :transfers, only: %i[new create], module: :applications
      resources :screenings, only: :create, module: :applications
      member do
        patch :request_payment
      end
      resources :background_checks, only: [], module: :applications do
        member do
          get :retry
        end
      end
      resources :income_verifications, only: [], module: :applications do
        member do
          get :cancel
          get :complete
          get :resend
        end
      end
      resources :landlord_verifications, only: :show, module: :applications do
        member do
          get :skip
        end
      end
      resources :screenings, only: :show, module: :applications, controller: 'saferent/screenings'
      resource :scorecard, only: :show, module: :applications
      resource :adjudication, only: %i[show destroy], module: :applications do
        member do
          patch :approve
          patch :reject
          post :resend
        end
      end
    end
    resources :leases, concerns: %i[archiving electronic_signable] do
      resources :move_ins, only: %i[new create]
      resources :move_outs, only: %i[destroy new create update] do
        member do
          get :dates
          get :termination
          get :forwarding_addresses
          get :assessment
          get :damages
          get :review
          patch :execute
          get :reopen
        end

        resources :damages, controller: 'move_outs/damages',
                            only: %i[create destroy] do
          post :add, on: :collection
        end
      end
      resources :itemized_damages, only: :show
      resources :unit_transfers, only: %i[create new]
      resource :notice_of_non_renewal, only: %i[create destroy]
      resources :lease_documents, only: %i[new create] do
        get :metadata_fields, on: :collection
      end
    end
    get 'bookings', to: 'bookings#index'
    get '/bookings/amenities', to: 'bookings#amenities'
    resources :lease_application_invites, only: %i[create new]
    namespace :pipeline do
      # TODO: Remove with legacy pipeline
      with_options constraints: { format: /(html|xlsx)/ } do
        resources :leads, only: :index, concerns: :batch_archiving
        resources :applications, only: :index, concerns: :batch_archiving do
          get :waitlist, on: :collection
        end
        resources :unsubmitted_applications, only: :index do
          post :send_reminder, on: :member
        end
        resources :waitlists, only: :index, concerns: :batch_archiving
        resources :expirings, only: :index
      end
    end
    resource :pipeline, only: [] do
      %i[leads applicants expiring waitlist].each do |path|
        get path, constraints: { format: :json }
      end
    end
    resources :waitlist_entries, only: %i[create destroy]
    resources :parking_allocations, only: :update
    resources :commercial_leases, only: %i[new create]
    namespace :agreements, as: :agreement do
      resources :types, controller: 'agreement_types'
    end
    resources :agreements, except: :destroy,
                           concerns: %i[archiving electronic_signable],
                           path: 'agreements/:type' do
      get :charge, on: :collection, to: 'agreements/charges#new'
      post :charge, on: :collection, to: 'agreements/charges#create'
      get :credit, on: :collection, to: 'agreements/credits#new'
      post :credit, on: :collection, to: 'agreements/credits#create'
      get :batch_invite, on: :collection
      get :batch_add_tag, on: :collection
      get :batch_remove_tag, on: :collection
      resources :electronic_documents, module: :agreements,
                                       only: %i[new create] do
        get :metadata_fields, on: :collection
      end
    end
  end

  # Accounting
  namespace :accounting do
    get :summary

    # Pulse
    resources :pulse, only: :index

    resources :revenues, only: [:new, :create] do
      collection do
        get :accounts
      end
    end

    # Payables
    namespace :payables do
      # Needed so /accounting/payables works without adding /invoices
      root to: 'invoices#index', as: ''
      resources :invoices do
        member do
          get :approve
          get :unapprove
          get :batch
          get :unbatch
          get :void
          get :owner_visibility
          post :mark_paid
          get :pdf_attachment
        end
        collection do
          get :batch_approve
          get :batch_destroy
          get :batch_add_to_batch
          get :batch_download
        end
      end
      resources :payments, only: %i[new create] do
        post :safe_create
        get :payees, on: :collection
      end
      post 'payments/safe_to_create', to: 'payments#safe_to_create'
      resources :batches do
        member do
          get :approve
          get :checks
          patch :bank_accounts
          patch :payment_methods
          patch :run
          patch :safe_run
        end
      end
    end

    # Receivables
    namespace :receivables do
      root to: 'invoices#index', as: ''
      resources :income, only: %i[new create show] do
        get :payers, on: :collection
      end
      resources :invoices do
        member do
          get :waive
          get :unwaive
          get :void
          post :mark_paid
          get :send_reminder
        end
        collection do
          get :batch_destroy
          get :batch_download
        end
        resource :terminal_checkouts, only: :create
      end
    end

    # Yearly Progress Graphs
    get :yearly_progress, to: 'yearly_progress#yearly_progress'

    # InvoiceTargets
    get '/payable', to: 'invoice_targets#payable'
    get '/receivable', to: 'invoice_targets#receivable'
    get '/targets', to: 'invoice_targets#targets'

    # Budget
    resources :budgets do
      get :template, on: :collection
    end

    # Payments
    resources :payments do
      get :payers, on: :collection
      get :apply_ledger, on: :collection

      resource :apply, only: %i[create destroy],
                       controller: 'payments/applies'

      member do
        get :refund
        get :void
        get :check
      end
    end

    # Electronic tenant payments from employees
    resources :electronic_payments, controller: 'payments/electronic_payments',
                                    only: %i[new create]

    # Cards
    resources :cards, only: :show, param: :card

    # Journals
    resources :journals, only: %i[index show edit update] do
      patch :lock, on: :member
      resources :year_closings, only: %i[new create], module: :journals
      resources :accounts, only: %i[index show]
      resources :entries do
        resource :recurring_schedule,
                 only: %i[create destroy],
                 controller: 'entries/recurring_schedules' do
                   get :preview, on: :collection
                 end
      end
      resource :trial_balance_import, only: %i[new show create]
      resource :general_ledger_import, only: %i[new create]
      resource :clearing, only: %i[new create], module: :journals
      resource :bill_detail_import, only: %i[show create], module: :journals
      resource :entry_import, only: %i[show create], module: :journals do
        get :template
        get :sample
      end
      resources :funds_transfers, only: %i[index new create]
      collection do
        post 'bulk_lockings/new' => 'journals/bulk_lockings#new'
        post 'bulk_lockings' => 'journals/bulk_lockings#create', as: :bulk_lock
      end
    end

    resources :statements, except: :edit do
      member do
        get :approve
        get :regenerate
      end
    end

    # Recurring Invoice Schedules
    resources :invoices, only: [] do
      resource :recurring_schedule,
               only: %i[create update destroy],
               controller: 'invoices/recurring_schedules' do
                 get :preview, on: :collection
               end
    end
  end

  namespace :taxes do
    resources :submissions, only: [:show] do
      member do
        put :update_adjustment
        get :resend_email, to: 'submissions#show_resend_email'
        post :resend_email
      end
    end

    resources :batch_groups, only: [:show, :destroy] do
      member do
        post :file
        post :evict
      end
    end

    resources :ten_ninety_nines, only: [:index, :show] do
      put :update_adjustment, on: :member
      collection do
        post :register
        post :skip
        post :unskip
        post :review
      end
    end
  end

  # Organization
  namespace :organization do
    resources :companies

    resources :forms, only: %i[index show create update edit],
                      concerns: %i[archiving bulk_archiving] do
      member do
        post :clone
        post :publish
      end

      resources :fields, only: [:create, :update], module: :forms
      resources :settings, only: :update, module: :forms
    end

    resources :employees, except: :destroy, concerns: %i[archiving contact] do
      get :target, on: :collection
      member do
        get :audits
        get :send_password_reset
      end
    end

    # Bank Accounts
    resources :bank_accounts, except: :destroy, concerns: :archiving do
      member do
        # Show page tag links
        get :reconciliation_history_tab
        get :batches_tab
        get :transactions_tab
        get :ut_activity_tab
        get :account_number
        get :routing_number
        get :voided_check
      end

      post :callback, on: :collection
      resources :reconciliations,
                except: :index,
                controller: '/accounting/bank_reconciliations' do
                  member do
                    get :deposits
                    get :withdrawals
                    get :reopen
                    post :reconcile
                    post :unreconcile
                  end
                end
      resources :deposit_batches, only: %i[show create update destroy],
                                  controller: '/accounting/deposit_batches' do
        member do
          patch :batch
          patch :unbatch
          patch :submit
        end
      end

      resources :transfers,
                controller: '/accounting/bank_accounts/transfers',
                only: %i[new create]
    end

    # Charts of Accounts
    resources :charts_of_accounts do
      member do
        get :upload, to: 'charts_of_accounts#new_upload'
        patch :upload
        get :configure
        get :suggested_gl_code
      end

      resources :accounts do
        resources :merges, only: %i[new create], module: :accounts
      end
    end

    # Documents
    resources :documents, only: %i[create destroy index new] do
      get '/signed_url' => 'documents#signed_upload_url', on: :collection
    end
    namespace :documents do
      resource :sample_filling, only: %i[new create]
      resources :templates, only: %i[index new create destroy] do
        get :sample, on: :member
      end
    end

    resources :charge_codes, only: :index

    resources :configurations, except: :edit do
      get :overview
      get :applications
      get :screening
      get :leases
      get :collections
      get :inspections
      get :maintenance

      resources :late_fees, except: :show,
                            controller: 'configurations/late_fees'
      resources :charge_presets, except: :show,
                                 controller: 'configurations/charge_presets'
      resources :credit_presets, except: :show,
                                 controller: 'configurations/credit_presets'
      resources :payment_plan_presets,
                except: :show,
                controller: 'configurations/payment_plan_presets'
      resources :lease_terms, only: %i[new create edit update destroy],
                              controller: 'configurations/lease_terms'
    end

    resources :roles
    resources :data_imports, only: :index do
      get :archive, on: :member
    end
    namespace :data_imports do
      %i[
        beginning_balances
        bill_detail
        employee_directories
        entity_directory
        general_ledger
        guest_cards
        inspection_templates
        inspections
        loan_history
        lease_charges
        maintenance_tickets
        managed_entity_balances
        management_contract_accounts
        management_contracts
        member_directories
        merchant_accounts
        metadata
        owner_directory
        paid_invoices
        prepared_disbursements
        property_directory
        rent_roll
        tenant_directory
        tenant_invoices
        tenant_payments
        unit_directory
        unpaid_invoices
        vendor_directory
      ].each do |route|
        resource route, only: %i[show create] do
          get :template
          get :sample
        end
      end
    end

    # Unit
    namespace :tunisia, path: :ut do
      resources :application_forms, only: %i[new create]
      resources :user_tokens, only: %i[new create] do
        get :clear, on: :collection
      end
      resources :internal_transfers, only: [:new, :create] do
        post :review, on: :collection
      end
      post 'internal_transfers/new', to: 'internal_transfers#new'
      resources :bank_accounts, only: %i[new create] do
        resources :cards, only: %i[new create]
        resources :card_activities, only: %i[index edit update], concerns: :bulk_archiving do
          get :review, on: :member
          delete :remove_attachment, on: :member
          get :matching_invoices, on: :member
          post :missing_receipt, on: :member
          post :post_transactions, on: :collection
          collection do
            patch :bulk_update
          end
        end
        member do
          resources :authorized_users, only: %i[index new create destroy]
          get :statements
          get :statement
        end
      end
      resources :bank_verification_letters, only: :show
    end
  end

  # Reporting
  namespace :reports do
    resources :email_schedules, only: %i[index create] do
      get :sample, on: :member
      get :recipients, on: :collection
      get :batch_destroy, on: :collection
    end
    resources :accounting_contexts, only: :index do
      get :accounts, on: :collection
    end
    resources :packet_templates, except: :show do
      get :filters, on: :collection
    end
    resources :packets, only: :create
    resources :bulk_ledgers, only: %i[new create]

    # TODO: Replace these with v3 versions. These routes are redefined below
    # during the catelogue block, but rails matches eagerly so the routes are
    # unused.
    get '/rent-schedule' => 'rent_schedule#show'

    Reports::Catalog.new(user: nil).all.each do |entry| # rubocop:disable Rails/FindEach
      get entry[:slug], to: 'v3/report#show'
    end

    resources :requests, only: %i[create show destroy], param: :uuid do
      get :rendered, on: :member
    end
  end
  resources :reports, param: :name, only: %i[index show] do
    member do
      get :favorite
      get :unfavorite
    end
  end

  # Marketing
  namespace :marketing do
    resources :listings do
      member do
        patch :publish
        patch :unpublish
        patch :reorder
      end
    end
  end

  namespace :zillow do
    resources :claims, only: %i[create edit update] do
      get :search, on: :collection
    end
  end

  # Messaging
  namespace :messaging do
    resources :contacts, only: :index
    resources :chats, only: %i[index show], param: :name
    resources :broadcasts, only: %i[index show new create] do
      get :targets, on: :collection
    end
    namespace :emails do
      resources :receipts, only: :index
    end
    resources :emails, except: %i[edit update] do
      collection do
        get :read
        get :unread
        post :broadcast
      end
      get :thread_previous, on: :member
    end
    resources :sms, only: %i[index show new create]

    resources :templates, path: 'templates/:kind', except: :show do
      collection do
        get :batch_destroy
      end
    end

    resources :contact_groups do
      resources :memberships, only: %i[index create destroy],
                              controller: :contact_group_memberships do
        collection do
          post :import_contacts
          get :remove_contact_group_memberships
        end
      end
      collection do
        get :template
        get :sample
        post :recipients
      end
    end
  end

  # Contacts
  resources :contacts, only: [], module: :contacts do
    resources :timeline_events, only: %i[index create destroy]
    resources :documents, only: %i[create destroy]
  end

  # Demo pages
  namespace :demo do
    get 'apply' => 'apply#apply'
    get 'listings' => 'listings#listings'
  end

  # ActiveAdmin
  devise_for :admin_users, ActiveAdmin::Devise.config
  ActiveAdmin.routes(self)

  authenticate :admin_user do
    # Flipper
    constraints Flipper::CanAccessFlipperUI do
      mount Flipper::UI.app(Flipper) => '/flipper'
    end

    # Sidekiq
    require 'sidekiq/web'
    require 'sidekiq-scheduler/web'
    mount Sidekiq::Web => '/jobs'
  end

  if Rails.env.production? || ENV['LOCAL_S3']&.to_s == 'true'
    mount Shrine.presign_endpoint(:cache) => '/s3/params'
  else
    mount Shrine.upload_endpoint(:cache) => '/upload'
  end


  # RentLinx
  namespace :listings do
    get :rent_linx, to: 'rent_linx#index', constraints: { format: :xml }
    get :zillow, to: 'zillow#index', constraints: { format: :xml }
    get :hotpads, to: 'hotpads#index', constraints: { format: :xml }
  end

  # Hosted listings page
  resources :listings, only: %i[index show]

  # External plugin for new guest card
  get '/guests', to: 'guest_cards#plugin', as: :guests_frame
  resources :guest_cards, only: %i[new create], path: :guests do
    get :success, on: :collection
    get :sources, on: :collection
  end

  # External plugin for reservations
  get '/reserve/:unit_id/new', to: 'reservations#new',
                               as: :new_hosted_reservation
  get '/reserve/:unit_id/checkout', to: 'reservations#checkout',
                                    as: :checkout_hosted_reservation
  post '/reserve/:unit_id', to: 'reservations#create',
                            as: :hosted_reservations

  # Hosted electronic signatures page
  resources :electronic_signatures, only: %i[show update], param: :uuid do
    get :document, on: :member
  end

  # Blog
  mount BoringScience.blog, at: '/blog', as: :revela_blog,
                            options: { blog: 'revela', title: 'Revela Blog' }

  resources :member_onboardings, path: :members, only: %i[new create] do
    collection do
      get :document
      get :sign
      post :sign, action: :submit
      get :success
    end
  end

  # Invoice processing
  namespace :invoice_processing do
    root to: redirect('/invoice_processing/emails')
    resources :emails, only: :index
    get :statistics, controller: :emails

    resources :emails, only: :show, path: 'emails/:customer_subdomain' do
      member do
        get :claim
        get :complete
        patch :reject
        post :process_invoice
      end
    end
  end

  namespace :sftp_to_go do
    post :webhooks
  end

  # OAuth
  constraints lambda { |req|
    !CustomSubdomainElevator.excluded_subdomains.include?(req.subdomain)
  } do
    use_doorkeeper
  end

  namespace :api do
    namespace :zapier do
      get 'oauth/me', to: 'oauth#me'
      resources :guest_cards, only: [:create]
      resources :contact_timeline_entries, only: [:create]
      resources :contacts, only: [:index]
    end
  end

  # Swagger
  mount Rswag::Api::Engine => '/api/documentation'

  authenticate :property_manager do
    mount Rswag::Ui::Engine => '/api/documentation'
  end
end
