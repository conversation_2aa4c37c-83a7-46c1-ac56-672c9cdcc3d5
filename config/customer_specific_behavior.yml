shared:
  accounting_pulse_additional_cards:
    adpi: ['Unapproved Invoices']
  agreements_larger_page_size?:
    - adpi
    - adpi-sandbox
    - barrister
    - chio-beta-beta
    - chio-eta
    - chio-mu
    - chio-psi
    - chio-psi-delta
    - chio-sigma-beta
    - chiohc-msu
    - dphie
    - esponda
    - ghm
    - ghm-sandbox
    - sae
    - sae-sandbox
    - sigep
    - trisigma
    - wrp
    - zbt-rho
  archive_email_addresses?:
    - adpi
    - adpi-sandbox
    - staging
  accounting_receipt_required?:
    - adpi
    - adpi-sandbox
    - staging
  auto_enroll_damage_waiver?:
    - staging
    - ghm
    - ghm-sandbox
  collection_letter_filename:
    chio-mu: 'collections/communications/chio_mu_letter_template'
  datestamp_inspection_response_photos?:
    - staging
    - alever
    - omnia
    - omnia-sandbox
  disable_based_on_role_name?:
    - ghm
    - ghm-sandbox
    - sae
    - sae-sandbox
    - cod
  hubspot_support_form?:
    - adpi
    - adpi-sandbox
    - staging
  importers_member_directory_max_row_limit:
    adpi: 25000
    adpi-sandbox: 25000
    alever: 25000
    staging: 25000
  importers_member_directory_remove_invalid_phones?:
    - adpi
    - adpi-sandbox
  importers_unattended_import_notificant_ids:
    adpi: [1]
    adpi-sandbox: [1]
    staging: [1]
  invoice_forwarding_receivable_account_match?:
    - 1872-properties
    - 1872-sandbox
    - ghm
    - ghm-sandbox
    - sae
    - sae-sandbox
    - wrp
  inspectify_inspection_type:
    alever: 'home_inspection'
    staging: 'home_inspection'
    omnia: 'occupancy_check'
    omnia_sandbox: 'home_inspection'
  inspection_integrations_slack_channel:
    omnia: '#project-omnia-notifications'
  inspection_integrations_slack_notify:
    omnia: ['U06C2ET99M0']
  invoice_due_today_reminder_email?:
    - adpi
    - ghm
    - ghm-sandbox
    - sae
    - sae-sandbox
    - staging
  invoice_overdue_email?:
    - chio-beta-beta
    - chio-mu
    - chio-sigma-beta
    - dphie
    - ghm
    - ghm-sandbox
    - kappasig-gammarho
    - sae
    - sae-sandbox
    - staging
  lease_application_required_residence_history_years:
    moulton: 1
  lease_application_skip_emergency_contact_email?:
    - moulton
  ledger_statements_mail_from_management_company?:
    - jlgardel
  lending_loans_enabled?:
    - alever
    - encoredev
    - evergreen
    - princeton
    - staging
    - wrp
    - zetasigma
  lending_loans_property_loan_index?:
    - alever
    - cod
    - staging
  listing_availability_disregards_reservations_and_applications?:
    - shamrock
    - ag-mgmt
    - harvest
  listing_availability_only_respects_approved_applications?:
    - gebraelmgmt
    - senihmgt
    - encoredev
    - mph-sandbox
    - marketplacehomes
    - uniland
    - sailboatbay
    - axumdev
    - feil
    - guardian
    - moulton
    - countryside
    - castro-felipe
    - garden-lofts
    - snowplace
  maintenance_surveys_disabled?:
    - jlgardel
  marketing_tab_hidden?:
    - adpi
    - adpi-sandbox
    - brown
  member_ledgers_enabled?:
    - adpi
    - adpi-sandbox
    - chio-beta-beta
    - chio-eta
    - chio-eta-alpha
    - chio-eta-kappa
    - chio-mu
    - chio-psi
    - chio-psi-delta
    - chio-sigma-beta
    - chio-tau-alpha
    - chiohc-msu
    - dphie
    - ghm
    - ghm-sandbox
    - kappasig-gammaomicron
    - kappasig-gammarho
    - sae
    - sae-sandbox
    - sigep
    - trisigma
    - zbt-rho
  member_status_tags:
    adpi: ['Alpha', 'Delta', 'Temporarily Off Campus', 'Flex', 'EFML', 'Graduate',
          'Left School / Transfer', 'Membership Cancellation', 'Alpha Release', 'Affiliate']
    staging: ['Alpha', 'Delta', 'Temporarily Off Campus', 'Flex', 'EFML', 'Graduate',
          'Left School / Transfer', 'Membership Cancellation', 'Alpha Release', 'Affiliate']
    adpi-sandbox: ['Alpha', 'Delta', 'Temporarily Off Campus', 'Flex', 'EFML', 'Graduate',
          'Left School / Transfer', 'Membership Cancellation', 'Alpha Release', 'Affiliate']
  owner_portal_hide_dashboard_balance?:
    - evergreen
    - gebrael
    - gebraelmgmt
    - marketplacehomes
    - mavenprops
    - mph-sandbox
    - pmi
  payable_invoice_default_net_days:
    gebraelmgmt: 0
    jlgardel: 15
  property_scoped_tags?:
    - adpi
    - adpi-sandbox
    - staging
  reports_add_property_region_filters?:
    - evergreen
  reports_add_owner_filters?:
    - marketplacehomes
    - mph-sandbox
    - staging
  reports_budget_variance_use_annual_budget?:
    - jlgardel
  reports_budget_variance_use_date_range_filter?:
    - adpi
    - jlgardel
  reports_general_ledger_export_include_account_column?:
    - adpi
    - adpi-sandbox
    - marketplacehomes
    - mph-sandbox
    - staging
  reports_general_ledger_export_include_lease_ledger_id_column?:
    - marketplacehomes
    - mph-sandbox
  reports_general_ledger_export_include_portfolio_column?:
    - gebraelmgmt
    - marketplacehomes
    - mph-sandbox
  require_completed_member_profile?:
    - adpi
    - adpi-sandbox
    - barrister
    - chio-beta-beta
    - chio-eta
    - chio-eta-kappa
    - chio-mu
    - chio-psi
    - chio-psi-delta
    - chio-sigma-beta
    - chiohc-msu
    - dphie
    - ghm
    - ghm-sandbox
    - kappasig-gammaomicron
    - sae
    - sae-sandbox
    - sigep
    - trisigma
    - wrp
    - xo-sandbox
    - zbt-rho
    - staging
  skip_first_months_rent_revenue?:
    - senihmgt
    - crg
  tunisia_debit_card_design:
    adpi: 'custom_adpi'
    ghm: 'logo_sae'
    sae: 'logo_sae'
    us: 'custom_adpi'
  tunisia_deposit_product:
    adpi: 'fraternal_medium'
    barrister: 'fraternal_medium'
    chio-eta: 'fraternal_medium'
    chio-mu: 'fraternal_medium'
    chio-tau-alpha: 'fraternal_small'
    crg: 'business_small'
    encoredev: 'business_medium'
    garden-lofts: 'business_small'
    gebraelmgmt: 'business_large'
    ghm: 'fraternal_medium'
    jlgardel: 'business_large'
    mino: 'business_medium'
    sae: 'fraternal_medium'
    us: 'business_small'
  work_order_sidebar_billing_rates:
    evergreen: ['0', '50', '75']
    rest-assured: ['0', '25', '30', '35', '40', '45', '50', '55', '70', '75', '80']
    staging: ['0', '50', '75']
  xlsx_loan_values?:
    - cod
