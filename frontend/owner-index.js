// ActionText
require("trix")
require("@rails/actiontext")

// Stimulus
import 'element-closest';

import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';

const stimulus = StimulusApplication.start();
const stimulusControllers = require.context(
  './stimulus/',
  true,
  /\.\/(owners|reports).*\.js$/
);
stimulus.load(stimulusDefinitions(stimulusControllers));

import 'jquery-tablesort';

import qs from 'qs';
window.qs = qs;

import React from 'react';
window.React = React;

import ReactDOM from 'react-dom';
window.ReactDOM = ReactDOM;

import DataTable from './table/data_table.jsx';
window.DataTable = DataTable;

import AccountingContextSelector from './reports/accounting_context_selector';
window.AccountingContextSelector = AccountingContextSelector;

import Reports from './reports';
window.reports = Reports;

// Action Cable
import App from './channels/owner_app';
window.App = App;
