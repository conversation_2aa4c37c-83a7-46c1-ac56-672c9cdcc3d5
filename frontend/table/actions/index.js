export const addColumn = (key, text) => (
  {
    type: 'ADD_COLUMN',
    key,
    text,
  }
);

export const removeColumn = key => (
  {
    type: 'REMOVE_COLUMN',
    key,
  }
);

export const setColumns = columns => (
  {
    type: 'SET_COLUMNS',
    columns,
  }
);

export const clickColumn = key => (
  {
    type: 'TOGGLE_COLUMN_DIRECTION',
    key,
  }
);

export const filterColumn = (key, text) => (
  {
    type: 'FILTER_COLUMN',
    key,
    text,
  }
);

export const aliasColumn = (key, alias) => (
  {
    type: 'ALIAS_COLUMN',
    key,
    alias,
  }
);

export const toggleFilter = () => (
  {
    type: 'TOGGLE_FILTER',
  }
);

export const setEdit = edit => (
  {
    type: 'SET_EDIT',
    edit,
  }
);

export const clickPage = page => (
  {
    type: 'CHANGE_PAGE',
    page,
  }
);

export const updatePageCount = pages => (
  {
    type: 'UPDATE_PAGE_COUNT',
    pages,
  }
);

export const setData = data => (
  {
    type: 'SET_DATA',
    data,
  }
);
