const table = (state = { page: 0, pages: 1, perPage: 10, filter: false, edit: false, data: [] }, action) => {
  switch (action.type) {
    case 'CHANGE_PAGE':
      return _.assign({}, state, { page: action.page });
    case 'UPDATE_PAGE_COUNT':
      const pages = action.pages;
      const page = Math.min(state.page, pages - 1);
      return _.assign({}, state, { page, pages });
    case 'TOGGLE_FILTER':
      return _.assign({}, state, { filter: !state.filter });
    case 'SET_EDIT':
      return _.assign({}, state, { edit: action.edit });
    case 'SET_DATA':
      return _.assign({}, state, { data: action.data });
    default:
      return state;
  }
};

export default table;
