const th = (state = {}, action) => {
  switch (action.type) {
    case 'ADD_COLUMN':
      return { key: action.key, text: action.text };

    case 'FILTER_COLUMN':
      if (state.key !== action.key) {
        return state;
      }

      let filter = null;
      if (action.text.length > 0) {
        filter = action.text.toLowerCase();
      }

      return _.assign({}, state, { filter });

    case 'TOGGLE_FILTER':
      return _.assign({}, state, { filter: null });

    case 'ALIAS_COLUMN':
      if (state.key !== action.key) {
        return state;
      }

      const alias = action.alias;

      return _.assign({}, state, { alias });

    case 'TOGGLE_COLUMN_DIRECTION':
      let direction;

      if (state.key !== action.key) {
        direction = null;
      } else {
        direction = state.direction || 'descending';
        direction = (direction === 'ascending') ? 'descending' : 'ascending';
      }

      return _.assign({}, state, { direction });

    default:
      return state;
  }
};

const thead = (state = [], action) => {
  const pass = state.map(t => th(t, action));

  switch (action.type) {
    case 'ADD_COLUMN':
      return [...state, th(undefined, action)];
    case 'REMOVE_COLUMN':
      return state.filter(column => column.key !== action.key);
    case 'SET_COLUMNS':
      return action.columns;
    case 'TOGGLE_FILTER':
      return pass;
    case 'FILTER_COLUMN':
      return pass;
    case 'ALIAS_COLUMN':
      return pass;
    case 'TOGGLE_COLUMN_DIRECTION':
      return pass;
    default:
      return state;
  }
};

export default thead;
