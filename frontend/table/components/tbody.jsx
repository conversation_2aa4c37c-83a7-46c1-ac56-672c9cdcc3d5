import natsort from 'natsort';

import ConnectedFilterRow from '../containers/connected_filter_row.js';

import { formatMoney } from '../../util/money.jsx';

const TBody = ({
  columns,
  page,
  perPage,
  pageCountUpdated,
  filter,
  data,
}) => {
  // Sort
  let sortedData = data;

  const sorter = natsort();

  columns.forEach(column => {
    if (column.direction !== null) {
      const key = column.key;

      sortedData = sortedData.sort((a, b) => {
        let av = a[key];
        let bv = b[key];

        if (typeof av === 'object') {
          if (av.sort) {
            av = parseInt(av.sort, 10);
            bv = parseInt(bv.sort, 10);
          } else if (av.type === 'currency') {
            av = parseInt(av.cents, 10);
            bv = parseInt(bv.cents, 10);
          } else {
            av = av.text;
            bv = bv.text;
          }
        }

        const v = sorter(av, bv);

        if (column.direction === 'descending') {
          return -v;
        }

        return v;
      });
    }
  });

  // Filter
  const filteredData = sortedData.filter(entry => {
    let pass = true;

    columns.forEach(column => {
      if (pass && column.filter) {
        let value = entry[column.key];
        if (typeof value === 'object') {
          if (value.type === 'currency') {
            value = value.cents.toString();
          } else {
            value = value.text.toLowerCase();
          }
        } else {
          value = value.toString().toLowerCase();
        }

        if (!value.includes(column.filter)) {
          pass = false;
        }
      }
    });

    return pass;
  });

  const pages = Math.max(Math.ceil(filteredData.length / perPage), 1);

  pageCountUpdated(pages);

  // Paginate
  const pageData = _.take(_.drop(filteredData, page * perPage), perPage);

  const dataRows = pageData.map(entry => {
    const tds = columns.map(({ key }) => {
      const value = entry[key];
      if (typeof value === 'object') {
        switch (value.type) {
          case 'label':
            return (
              <td key={key} style={{ padding: '.5em' }}>
                <div
                  className={`ui small center aligned ${value.color} label`}
                  style={{ width: '100%' }}
                >
                  {value.text}
                </div>
              </td>
            );
          case 'currency':
            return (
              <td key={key}>
                {formatMoney(value.cents / 100)}
              </td>
            );
          default:
            return (
              <td key={key}>
                {value.url && <a href={value.url}>{value.text}</a>}
                {!value.url && value.text}
              </td>
            );
        }
      }

      return <td key={key} title={value}>{value}</td>;
    });

    return (
      <tr key={entry.id}>
        {tds}
      </tr>
    );
  });

  // Pad last page
  let padCount;
  padCount = perPage - pageData.length;
  padCount = Math.max(0, padCount);

  const padRows = _.fill(Array(padCount), 0).map((_, i) => (
    <tr key={`pad-${i}`}>
      <td colSpan={columns.length}>
        <span style={{ visibility: 'hidden' }}>
          0
        </span>
      </td>
    </tr>
  ));

  const rows = [...dataRows, ...padRows];

  const filterRow = filter ? <ConnectedFilterRow /> : null;

  return (
    <tbody>
      {filterRow}
      {rows}
    </tbody>
  );
};

export default TBody;
