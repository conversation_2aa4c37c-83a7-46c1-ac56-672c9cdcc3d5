import PropTypes from 'prop-types';

const FilterRow = ({
  columns,
  onColumnFilter,
}) => {
  const tds = columns.map(column => (
    <td key={column.key}>
      <div className="ui filter input">
        <input
          type="text"
          onChange={(e) => onColumnFilter(column.key, e.target.value) }
        />
      </div>
    </td>
  ));

  return (
    <tr>
      {tds}
    </tr>
  );
};

FilterRow.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string,
  })),
  onColumnFilter: PropTypes.func.isRequired,
};

export default FilterRow;
