import Th from './th';
import EditableTH from './editable_th';

const THead = ({
  columns,
  edit,
  onColumnClick,
  onColumnAlias,
  onColumnRemove,
}) => {
  const items = columns.map(({ key, text, direction, alias, width }) => {
    if (edit) {
      return (
        <EditableTH
          key={key}
          onAlias={value => onColumnAlias(key, value)}
          onRemove={() => onColumnRemove(key)}
          text={text}
          alias={alias}
        />
      );
    }

    return (
      <Th
        key={key}
        onClick={() => onColumnClick(key)}
        text={(alias && alias.length > 0) ? alias : text}
        sortDirection={direction}
        width={width}
      />
    );
  });

  return (
    <thead>
      <tr>
        {items}
      </tr>
    </thead>
  );
};

export default THead;
