import PropTypes from 'prop-types';

const EditableTH = ({
  text,
  alias,
  onAlias,
  onRemove,
}) => (
  <th>
    <div className="ui input">
      <input
        type="text"
        placeholder={text}
        value={alias}
        onChange={(e) => onAlias(e.target.value)}
      />
    </div>
    <button
      className="ui right floated mini circular icon button"
      onClick={() => onRemove()}
      style={{ margin: '-10px -6px 0 0' }}
    >
      <i className="remove icon" />
    </button>
  </th>
);

EditableTH.propTypes = {
  alias: PropTypes.string.isRequired,
  onAlias: PropTypes.func.isRequired,
  onRemove: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired,
};

export default EditableTH;
