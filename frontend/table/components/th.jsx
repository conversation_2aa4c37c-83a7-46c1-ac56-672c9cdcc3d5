import { func, string } from 'prop-types';
import classNames from 'classnames';

/*
 * A <th> that properly sets sorted class
 */
const Th = ({ text, sortDirection, onClick, width }) => (
  <th
    onClick={onClick}
    className={classNames({
      [`sorted ${sortDirection}`]: sortDirection,
      [`${width} wide`]: width,
    })}
  >
    {text}
  </th>
);

Th.propTypes = {
  onClick: func.isRequired,
  sortDirection: string.isRequired,
  text: string.isRequired,
  width: string,
};

Th.defaultProps = {
  width: null,
};

export default Th;
