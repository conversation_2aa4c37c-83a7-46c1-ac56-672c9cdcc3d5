import PropTypes from 'prop-types';

import PageButtons from '../../pagination/page_buttons';

const TFoot = ({ columnCount, onPageClick, page, pages }) => (
  <tfoot>
    <tr>
      <th colSpan={columnCount} className="center aligned">
        <PageButtons page={page} pages={pages} onChangePage={onPageClick} />
      </th>
    </tr>
  </tfoot>
);

TFoot.propTypes = {
  columnCount: PropTypes.number.isRequired,
  onPageClick: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  pages: PropTypes.number.isRequired,
};

export default TFoot;
