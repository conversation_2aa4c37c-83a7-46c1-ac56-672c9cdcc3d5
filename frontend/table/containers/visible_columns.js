import { map } from 'lodash';
import { connect } from 'react-redux';
import THead from '../components/thead';
import { clickColumn, aliasColumn, removeColumn } from '../actions/index';

const mapStateToColumnProps = (state, { widths = {} }) => {
  const columns = map(state.thead, column => (
    { ...column, width: widths[column.key] }
  ));

  return {
    columns,
    edit: state.table.edit,
  };
};

const mapDispatchToColumnProps = dispatch => ({
  onColumnClick: key => {
    dispatch(clickColumn(key));
  },
  onColumnAlias: (key, alias) => {
    dispatch(aliasColumn(key, alias));
  },
  onColumnRemove: key => {
    dispatch(removeColumn(key));
  },
});

const VisibleColumns = connect(
  mapStateToColumnProps,
  mapDispatchToColumnProps,
)(THead);

export default VisibleColumns;
