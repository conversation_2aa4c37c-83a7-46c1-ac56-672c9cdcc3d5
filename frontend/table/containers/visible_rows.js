import { connect } from 'react-redux';
import TBody from '../components/tbody.jsx';

import { updatePageCount } from '../actions/index.js';

const mapStateToRowProps = state => ({
  columns: state.thead,
  page: state.table.page,
  perPage: state.table.perPage,
  filter: state.table.filter,
  data: state.table.data,
});

const mapDispatchToRowProps = dispatch => ({
  pageCountUpdated: (pages) => {
    dispatch(updatePageCount(pages));
  },
});

const VisibleRows = connect(
  mapStateToRowProps,
  mapDispatchToRowProps
)(TBody);

export default VisibleRows;
