import { connect } from 'react-redux';
import TFoot from '../components/tfoot.jsx';
import { clickPage } from '../actions/index.js';

const mapStateToFootProps = state => ({
  columnCount: state.thead.length,
  page: state.table.page,
  pages: state.table.pages,
});

const mapDispatchToFootProps = dispatch => ({
  onPageClick: (i) => {
    dispatch(clickPage(i));
  },
});

const ConnectedFoot = connect(
  mapStateToFootProps,
  mapDispatchToFootProps
)(TFoot);

export default ConnectedFoot;
