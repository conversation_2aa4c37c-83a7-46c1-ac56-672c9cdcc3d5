import { connect } from 'react-redux';

import FilterRow from '../components/filter_row.jsx';

import { filterColumn } from '../actions/index.js';

const mapStateToFilterProps = state => ({
  columns: state.thead,
});

const mapDispatchToFilterProps = dispatch => ({
  onColumnFilter: (key, text) => {
    dispatch(filterColumn(key, text));
  },
});

const ConnectedFilterRow = connect(
  mapStateToFilterProps,
  mapDispatchToFilterProps
)(FilterRow);

export default ConnectedFilterRow;
