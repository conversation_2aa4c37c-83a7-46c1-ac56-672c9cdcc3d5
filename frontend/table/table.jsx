import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import VisibleColumns from './containers/visible_columns';
import VisibleRows from './containers/visible_rows';
import ConnectedFoot from './containers/connected_foot';

import { toggleFilter } from './actions/index';

const Table = props => {
  const filterText = props.filter ?
    'Clear' : 'Filter';

  return (
    <div>
      <div className="ui two column padded grid" style={{ minHeight: '2.5em' }}>
        <div className="column" style={{ padding: 0 }}>
          {props.title}
        </div>
        <div className="right aligned column" style={{ padding: 0 }}>
          <button
            className="ui tiny compact button"
            onClick={props.onClickFilter}
            style={{ borderRadius: '4px 4px 0px 0px', position: 'absolute', bottom: 0, right: '10px' }}
          >
            <i className="ui filter icon" />
            {filterText}
          </button>
        </div>
      </div>
      <div>
        <table className={props.className}>
          <VisibleColumns widths={props.widths} />
          <VisibleRows />
          <ConnectedFoot />
        </table>
      </div>
    </div>
  );
};

Table.propTypes = {
  filter: PropTypes.bool.isRequired,
  onClickFilter: PropTypes.func.isRequired,
  className: PropTypes.string.isRequired,
};

Table.defaultProps = {
  filter: false,
  className: 'ui fixed single line sortable selectable unstackable celled table',
};

const mapStateToProps = state => ({
  data: state.data,
  filter: state.filter,
});

const mapDispatchToProps = dispatch => ({
  onClickFilter: () => (
    dispatch(toggleFilter())
  ),
});

const ConnectedTable = connect(
  mapStateToProps,
  mapDispatchToProps,
)(Table);

export default ConnectedTable;
