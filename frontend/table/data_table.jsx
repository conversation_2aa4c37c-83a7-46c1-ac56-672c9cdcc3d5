import PropTypes from 'prop-types';
/*
 * Top level table component
 */

import { Component } from 'react';
import { Provider } from 'react-redux';

import Table from './table.jsx';

import store from '../store.js';

import { clickColumn, setColumns, setData, setEdit } from './actions/index.js';

const computeColumns = data => (
  _.keys(data[0]).map(entry => ({
    key: entry,
    text: _.startCase(entry),
  })).filter(k => k.key !== 'id')
);

class DataTable extends Component {
  constructor(props) {
    super(props);
    this.state = {};

    store.getState().table.perPage = props.perPage;
  }

  componentWillMount() {
    this.updateStore(this.props);
  }

  componentWillReceiveProps(props) {
    this.updateStore(props);
  }

  updateStore(props) {
    const columns = computeColumns(props.data);

    store.dispatch(setData(props.data));
    store.dispatch(setEdit(props.edit));
    store.dispatch(setColumns(columns));

    if (columns.length > 0) {
      if (props.sort === 'ascending') {
        store.dispatch(clickColumn(columns[0].key));
      } else if (props.sort === 'descending') {
        store.dispatch(clickColumn(columns[0].key));
        store.dispatch(clickColumn(columns[0].key));
      }
    }
  }

  render() {
    const title = (typeof this.props.title === 'string') ? (
      <h2 className="ui header">{this.props.title}</h2>
    ) : this.props.title;

    return (
      <Provider store={store}>
        <Table
          title={title}
          className={this.props.className}
          widths={this.props.widths}
        />
      </Provider>
    );
  }
}

DataTable.propTypes = {
  className: PropTypes.string,
  data: PropTypes.array.isRequired,
  perPage: PropTypes.number.isRequired,
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  sort: PropTypes.string,
};

DataTable.defaultProps = {
  perPage: 10,
  sort: 'ascending'
};

export default DataTable;
