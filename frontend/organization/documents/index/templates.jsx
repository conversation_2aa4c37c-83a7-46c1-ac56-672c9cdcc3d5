import { Component } from 'react';
import { PropTypes } from 'prop-types';
import { forEach, map, reject } from 'lodash';
import Dropzone from 'react-dropzone';

import AttachmentStore from '../../../attachments/attachment_store';
import Document from './document';

const style = {
  padding: '1em',
  borderRadius: 4,
  flex: '1 1 auto',
  margin: '1em',
  transition: 'border .2s',
};

class Templates extends Component {
  constructor(props) {
    super(props);

    this.state = { hovering: false, documents: [] };
    this.store = new AttachmentStore('/organization/documents', { onProgress: this.onProgress });
    this.canCreateDocument = props.canCreateDocument;
    $.get('/organization/documents.json', documents => this.setState({ documents }));
  }

  canCreateDocument = true;

  onProgress = (file, progress) => {
    const { documents } = this.state;

    this.setState({
      documents: map(documents, document => {
        if (document.filename === file.name) {
          return { ...document, progress };
        }

        return document;
      }),
    });
  }

  onDrop = files => {
    const { documents } = this.state;

    forEach(files, file => {
      documents.push({
        filename: file.name,
        size: file.size,
        progress: 0,
      });

      this.store.createResource(file)
        .then(data => this.handleFileUploaded(file, data));
    });

    this.setState({ documents, hovering: false });
  }

  onDragEnter = () => this.setState({ hovering: true });

  onDragLeave = () => this.setState({ hovering: false });

  onDelete = attachment => {
    this.store.destroyResource(attachment)
      .then(() =>
        this.setState({ documents: reject(this.state.documents, { id: attachment.id }) })
      );
  }

  handleFileUploaded = (upload, file) => {
    const { documents } = this.state;

    this.setState({
      documents: map(documents, document => {
        if (document.filename === file.filename) {
          return { ...document, ...file, progress: undefined };
        }

        return document;
      }),
    });
  }

  render() {
    const border = this.state.hovering ? '1px solid black' : '1px dashed gray';

    return (
      <div style={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column' }}>
        <Dropzone
          id="template-dropzone"
          disableClick
          onDrop={this.onDrop}
          onDragEnter={this.onDragEnter}
          onDragLeave={this.onDragLeave}
          ref={node => { this.dropzoneRef = node; }}
          style={{ ...style, border }}
        >
          {this.canCreateDocument &&
            <button className="right floated ui button" type="button" onClick={() => this.dropzoneRef.open()}>
              <i className="ui upload icon" />
              Add Template
            </button>
          }
          <div className="ui cards">
            {map(this.state.documents, doc => <Document onClick={() => this.props.setSelectedDocument(doc)} document={doc} />)}
          </div>
        </Dropzone>
      </div>
    );
  }
}

Templates.propTypes = {
  setSelectedDocument: PropTypes.func.isRequired,
};

export default Templates;
