import { Component } from 'react';
import { PropTypes } from 'prop-types';
import { findDOMNode } from 'react-dom';

import { name, type } from '../../../util/document_utils';

const headerStyle = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
};

class Document extends Component {
  componentDidMount() {
    this.progress({ percent: 0 });
  }

  componentWillReceiveProps(props) {
    if (props.document.progres !== this.props.document.progress) {
      const { progress, size } = props.document;

      if (progress) {
        const percent = (progress / size) * 100;
        this.progress({ percent });
      }
    }
  }

  progress(...args) {
    $(findDOMNode(this)).find('.ui.progress').progress(...args);
  }

  render() {
    const { document, onClick } = this.props;

    const image = (document.content_type || '').includes('image');

    const src = image ? 'https://www.semantic-ui.com/images/wireframe/white-image.png'
                      : 'https://www.semantic-ui.com/images/wireframe/paragraph.png';

    const width = image ? '60px' : '100px';

    return (
      <a
        className="ui card"
        onClick={onClick}
        style={{ width: '130px', height: '180px' }}
        title={document.filename}
      >
        {document.progress !== undefined &&
          <div className="top attached ui progress">
            <div className="bar" />
          </div>
        }
        <div className="content">
          <div className="header" style={headerStyle}>
            {name(document)}
          </div>
          <div className="meta">{type(document)}</div>
          <div className="center aligned description">
            <img src={src} width={width} role="presentation" />
          </div>
        </div>
      </a>
    );
  }
}

Document.propTypes = {
  document: PropTypes.shape({
    filename: PropTypes.string.isRequired,
    progress: PropTypes.number,
    size: PropTypes.number,
  }).isRequired,
  onClick: PropTypes.func,
};

export default Document;
