import { PropTypes } from 'prop-types';
import { branch, nest, renderComponent } from 'recompose';

import DocumentInfo from './document_info';

const style = {
  width: '320px',
  borderLeft: '1px solid #D4D4D5',
  padding: '1em',
  flex: '0 0 auto',
};

const InfoPane = ({ children }) => (
  <div style={style}>
    {children}
  </div>
);

InfoPane.propTypes = {
  children: PropTypes.element.isRequired,
  canDownloadDocument: PropTypes.bool,
  canDestroyDocument: PropTypes.bool,
};

const Message = () => (
  <div className="ui message">
    Select a document on the left to see more information about it.
  </div>
);

const withMessage = branch(
  props => !props.document,
  renderComponent(Message),
);

const Inner = withMessage(DocumentInfo);

export default nest(InfoPane, Inner);
