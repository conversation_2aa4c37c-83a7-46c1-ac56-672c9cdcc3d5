import { PropTypes } from 'prop-types';
import { withState } from 'recompose';

import ActionBar from '../../../action_bar/action_bar';

import Templates from './templates';
import InfoPane from './info_pane';

const Documents = ({ canCreateDocument, canDestroyDocument, canDownloadDocument, selectedDocument, setSelectedDocument }) => (
  <div>
    <ActionBar title="Document Management" />
    <div className="action-container">
      <div style={{ display: 'flex', flex: '1 0 auto' }}>
        <Templates setSelectedDocument={setSelectedDocument} canCreateDocument={canCreateDocument} />
        <InfoPane document={selectedDocument} canDestroyDocument={canDestroyDocument} canDownloadDocument={canDownloadDocument} />
      </div>
    </div>
  </div>
);

Documents.propTypes = {
  canCreateDocument: PropTypes.bool,
  canDownloadDocument: PropTypes.bool,
  canDestroyDocument: PropTypes.bool,
  selectedDocument: PropTypes.any,
  setSelectedDocument: PropTypes.func.isRequired,
};

export default withState('selectedDocument', 'setSelectedDocument')(Documents);
