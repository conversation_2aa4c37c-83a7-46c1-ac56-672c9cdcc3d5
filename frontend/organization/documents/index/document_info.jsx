import { PropTypes } from 'prop-types';
import { get } from 'lodash';
import moment from 'moment';

import { name, type, size } from '../../../util/document_utils';

const uploadDate = document => moment(document.created_at).format('l');

const headerStyle = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
};

const DocumentInfo = ({ document, canDestroyDocument, canDownloadDocument }) => (
  <div>
    <h2 className="ui header" title={document.filename} style={headerStyle}>
      {name(document)}
      <div className="sub header">
        {type(document)}, {size(document)}
      </div>
    </h2>

    <div style={{ display: 'flex' }}>
      {canDownloadDocument &&
        <a className="ui small fluid compact button" href={document.url} target="_blank">
          <i className="ui download icon" />
          Download
        </a>
      }
      {canDestroyDocument &&
        <form action={`/organization/documents/${document.id}`} method="post" style={{ flex: '1 0 auto' }}>
          <input name="_method" value="delete" type="hidden" />
          <input
            type="hidden"
            name="authenticity_token"
            value={$('meta[name=csrf-token]').attr('content')}
          />
          <button
            type="submit"
            className="ui small fluid compact button"
            data-confirm="Are you sure?"
          >
            <i className="ui trash icon" />
            Delete
          </button>
        </form>
      }
    </div>

    <div className="ui four stackable tiny vertical statistics" style={{ flexDirection: 'column', alignItems: 'center', padding: '2em 1em' }}>
      <div className="statistic">
        <div className="value">{get(document, 'uploaded_by.name', 'Unknown')}</div>
        <div className="label">Uploaded By</div>
      </div>
      <div className="statistic">
        <div className="value">{uploadDate(document)}</div>
        <div className="label">Date Uploaded</div>
      </div>
      <div className="statistic">
        <div className="value">
          {document.last_used_at && moment(document.last_used_at).format('l') || 'Never'}
        </div>
        <div className="label">Last Used</div>
      </div>
      <div className="statistic">
        <div className="value">{document.use_count}</div>
        <div className="label">Use Count</div>
      </div>
    </div>
  </div>
);

DocumentInfo.propTypes = {
  document: PropTypes.object.isRequired,
  canDestroyDocument: PropTypes.bool,
  canDownloadDocument: PropTypes.bool,
};

export default DocumentInfo;
