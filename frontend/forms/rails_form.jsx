import PropTypes from 'prop-types';
import { Component } from 'react';

/*
 * Renders a <form> tag that automatically adds the current pages csrf-token as
 * a hidden input field for use with rails controllers.
 */
class RailsForm extends Component {
  static propTypes = {
    action: PropTypes.string.isRequired,
    className: PropTypes.string.isRequired,
    children: PropTypes.node.isRequired,
    id: PropTypes.string.isRequired,
    multipart: PropTypes.bool.isRequired,
    patch: PropTypes.bool.isRequired,
  };

  static defaultProps = {
    className: 'ui form',
    multipart: false,
    patch: false,
  };

  constructor(props) {
    super(props);
    this.state = { authenticityToken: undefined };
  }

  componentWillMount() {
    const authenticityToken = $('meta[name=csrf-token]').attr('content');
    this.setState({ authenticityToken });
  }

  render() {
    const { authenticityToken } = this.state;
    const { action, className, children, id, multipart, patch } = this.props;

    const patchInput = patch ? (
      <input type="hidden" name="_method" value="patch" />
    ) : null;

    return (
      <form
        acceptCharset="UTF-8"
        action={action}
        className={className}
        encType={multipart ? 'multipart/form-data' : undefined}
        id={id}
        method="post"
      >
        <input
          type="hidden"
          name="authenticity_token"
          value={authenticityToken}
        />
        {patchInput}
        {children}
      </form>
    );
  }
}

export default RailsForm;
