import { func, string } from 'prop-types';
import { withState } from 'recompose';

import Search from '../../components/search';

const TenantSearchField = ({ className, label, name, tenant, setTenant }) => (
  <div className={className}>
    <label>{label}</label>
    <Search
      icon="user icon"
      onChange={t => setTenant(t)}
      placeholder="Tenant"
      url="/api/v1/search?q={query}&all=true"
    />
    {tenant && <input type="hidden" name={name} value={tenant.id} />}
  </div>
);

TenantSearchField.propTypes = {
  className: string.isRequired,
  label: string.isRequired,
  name: string.isRequired,
  setTenant: func.isRequired,
};

TenantSearchField.defaultProps = {
  className: 'ui required field',
  label: 'Tenant',
};

export default withState('tenant', 'setTenant')(TenantSearchField);
