import PropTypes from 'prop-types';
import { Component } from 'react';

import Dropzone from 'react-dropzone';

import PhotoAttachment from './photo_attachment';
import AttachmentStore from '../../attachments/attachment_store';

class PhotoAttachmentsArea extends Component {

  static propTypes = {
    photos: PropTypes.array,
  };

  constructor(props) {
    super(props);
    this.state = { photos: props.photos || [] };
    this.onDrop = this.handleDrop.bind(this);
    this.onRemove = this.handleRemovePhoto.bind(this);
    this.store = new AttachmentStore('/api/v1/photos');
  }

  getStore() {
    return this.store;
  }

  handleDrop(files) {
    this.uploads = _.map(files, (file) => (
      { name: file.name, size: file.size, loaded: 0 }
    ));

    _.each(files, file => {
      this.getStore().createResource(file, { onProgress: this.handleProgress.bind(this) })
      .then((data) => {
        this.handleResourceCreated(file, data);
      })
      .fail((xhr) => {
        this.handleError(file, xhr);
      });
    });
  }

  handleProgress(file, loaded) {
    const upload = _.find(this.uploads, { name: file.name });
    if (upload) { upload.loaded = loaded; }

    const current = _.sumBy(this.uploads, 'loaded');
    const total = _.sumBy(this.uploads, 'size');
    const percentLoaded = Math.round((current / total) * 100);

    this.setState({ percentLoaded });
  }

  handleResourceCreated(file, photo) {
    this.uploads.splice(this.uploads.indexOf(file.name), 1);
    if (!this.uploads.length) {
      this.setState({ percentLoaded: null });
    }

    this.handleCreatePhoto(photo);
  }

  handleError(file, xhr) {
    this.setState({ errors: `Failed to upload ${file.name}` });
  }

  handleCreatePhoto(photo) {
    this.setState({ photos: $.merge(this.state.photos, [photo]) });
  }

  handleRemovePhoto(photo) {
    // Remove from photos
    const newPhotos = _.filter(this.state.photos, p => p.url !== photo.url);

    this.setState({ photos: newPhotos });
  }

  renderInputs() {
    return _.map(this.state.photos, photo => (
      <input key={photo.id} type="hidden" name="attachments[]" value={photo.url} />
    ));
  }

  render() {
    const photoItems = _.map(this.state.photos, (photo) => (
      <PhotoAttachment
        key={photo.id}
        photo={photo}
        onRemovePhoto={this.onRemove}
      />
    ));

    const instructions = this.state.photos.length === 0 ? (
      <div>Drop files here or click to upload</div>
    ) : null;

    return (
      <div>
        <Dropzone
          className="dropzone"
          onDrop={this.onDrop}
          style={{
            height: '150px',
            borderWidth: 1,
            borderColor: '#666',
            borderStyle: 'dashed',
            borderRadius: 5,
            marginBottom: '1em',
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {instructions}
          {photoItems}
        </Dropzone>
        {this.renderInputs()}
      </div>
    );
  }
}

export default PhotoAttachmentsArea;
