import $ from 'jquery'

function refreshInputState() {
  const input = $(this).parents('.field').find('input.no-or-explain')

  if ($(this).val() === 'yes') {
    input.val('')
    input.show()
  } else {
    input.hide()
    input.val('No')
  }
}

$(document).on('turbolinks:load', () => {
  $('select.no-or-explain').off('change')

  $('select.no-or-explain').on('change', refreshInputState)
  $('select.no-or-explain').each(refreshInputState)
  $('select.no-or-explain').attr('name', null)
})
