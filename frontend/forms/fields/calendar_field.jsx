import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';

class CalendarField extends Component {
  static propTypes = {
    id: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    name: PropTypes.string,
    placeholder: PropTypes.string,
    options: PropTypes.object.isRequired,
    className: PropTypes.string.isRequired,
    defaultValue: PropTypes.any,
    onChange: PropTypes.func, // Optional callback function
    startCalendar: PropTypes.string, // Optional id of another calendar for start of range
    endCalendar: PropTypes.string, // Optional id of another calendar for end of range
  }

  static defaultProps = {
    options: { type: 'date' },
    className: 'ui four wide calendar field',
  }

  componentDidMount() {
    const options = _.assign({}, this.props.options);

    const { startCalendar, endCalendar } = this.props;

    if (startCalendar) {
      options.startCalendar = $(startCalendar).parent().parent();
    }

    if (endCalendar) {
      options.endCalendar = $(endCalendar).parent().parent();
    }

    options.onChange = this.handleChange;

    this.calendar(options);

    const value = this.value(this.props);
    if (value) {
      this.setValue(value);
    }
  }

  componentWillReceiveProps(props) {
    const newValue = this.value(props);
    const oldValue = this.value(this.props);
    if (newValue !== oldValue) {
      this.setValue(newValue);
    }
  }

  handleChange = date => {
    const { onChange } = this.props;
    if (onChange) onChange(date);
  };

  value = props => props.value;

  setValue = date => this.calendar('set date', date, true, false);

  calendar(...args) {
    $(this.node).calendar(...args);
  }

  clear() {
    this.calendar('clear');
  }

  render() {
    const { id, label, name, placeholder, defaultValue } = this.props;
    return (
      <div className={this.props.className}>
        <label htmlFor={id}>
          {label}
        </label>
        <div className="ui fluid left icon input" ref={n => this.node = n}>
          <input
            type="text"
            id={id}
            placeholder={placeholder}
            name={name}
            defaultValue={defaultValue}
          />
          <i className="calendar alternate outline icon" />
        </div>
      </div>
    );
  }
}

export default CalendarField;
