import PropTypes from 'prop-types';
import { Component } from 'react';

import Thumbnail from '../../attachments/thumbnail.jsx';

class PhotoAttachment extends Component {
  constructor(props) {
    super(props);
    this.state = { };
  }

  handleRemove() {
    this.props.onRemovePhoto(this.props.photo);
  }

  render() {
    const { photo } = this.props;
    const { filename, url, id, content_type } = photo;
    console.log(photo);

    return (
      <div className="ui image" style={{ margin: '0.5em' }}>
        <a className="ui red right corner label" onClick={e => {
          e.cancelBubble = true;
          if (e.stopPropagation) e.stopPropagation();
          this.handleRemove();
        }}
        >
          <i className="remove icon"></i>
        </a>
        <Thumbnail attachment={photo} />
      </div>
    );
  }
}

PhotoAttachment.propTypes = {
  onRemovePhoto: PropTypes.func.isRequired,
};

export default PhotoAttachment;
