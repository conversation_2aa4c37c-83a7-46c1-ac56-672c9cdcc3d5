import PropTypes from 'prop-types';
import { Component } from 'react';
import { without } from 'lodash';

import { formatMoney, forceTwoDecimal, sanitizeMoney } from '../../util/money.jsx';

class MoneyField extends Component {
  constructor(props) {
    super(props);
    this.state = { amount: props.amount || '', focused: false };
  }

  componentWillReceiveProps(props) {
    if ((props.amount !== undefined) && (props.amount !== this.state.amount)) {
      this.setState({ amount: props.amount });
    }
  }

  format(amount) {
    return formatMoney(sanitizeMoney(amount), false, false, false);
  }

  handleChange(e) {
    const amount = forceTwoDecimal(e.target.value);
    this.setState({ amount });

    const change = this.props.onChange || this.props.input.onChange;

    if (change) {
      change(sanitizeMoney(amount) || '0');
    }
  }

  render() {
    const { allowNil, id, label, name, input } = this.props;
    const { amount, focused } = this.state;
    const displayEmpty = allowNil && amount === ''

    const value = (focused || displayEmpty) ? amount : this.format(amount);

    const change = this.handleChange.bind(this);
    return (
      <div className={this.props.className}>
        <label htmlFor={id}>
          {label}
        </label>
        <input
          id={id}
          name={name}
          onBlur={() => this.setState({ focused: false })}
          onChange={change}
          onFocus={() => this.setState({ focused: true })}
          type="text"
          value={value}
          {...without(input, 'value')}
        />
      </div>
    );
  }
}

MoneyField.propTypes = {
  allowNil: PropTypes.boolean,
  className: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  input: PropTypes.object.isRequired,
  label: PropTypes.string.isRequired,
  name: PropTypes.string,
  onChange: PropTypes.func,
};

MoneyField.defaultProps = {
  className: 'required field',
  input: {},
};

export default MoneyField;
