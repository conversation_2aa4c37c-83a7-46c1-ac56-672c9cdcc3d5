import PropTypes from 'prop-types';
import { Component } from 'react';

const availableAmenities = [
  'Air Conditioning',
  'Dryer',
  'Fireplace',
  'Free Parking',
  'Gym',
  'Heating',
  'Pool',
  'Television',
  'Washer',
];

class AmenitiesInput extends Component {
  constructor(props) {
    super(props);
    this.state = { list: _.join(props.amenities, ', ') };
  }

  selectedAmenities() {
    return _(this.state.list)
      .split(',')
      .map(_.trim)
      .reject(_.isEmpty)
      .map(_.startCase)
      .uniq()
      .value();
  }

  render() {
    const { list } = this.state;

    const checkboxes = _.map(availableAmenities, amenity => {
      const value = _(this.selectedAmenities()).includes(amenity);

      const onChange = () => {
        if (value) {
          const newList = _(list)
            .replace(new RegExp(amenity, 'ig'), '')
            .replace(/(^( ?,+)|(, )+$)/, '')
            .replace(/,( ,)+/, ',');

          this.setState({ list: newList });
        } else {
          this.setState({ list: `${list}${list && ', '}${amenity}` });
        }
      };

      return (
        <div className="field">
          <div className="ui checkbox" key={amenity}>
            <input type="checkbox" checked={value} value={value} onChange={onChange} />
            <label>{amenity}</label>
          </div>
        </div>
      );
    });

    const listInput = (
      <input
        type="text"
        value={list}
        onChange={e => this.setState({ list: e.target.value })}
      />
    );

    const hiddenFields = _.map(this.selectedAmenities(), amenity => (
      <input
        type="hidden"
        name={this.props.name}
        key={amenity}
        value={amenity}
      />
    ));

    return (
      <div>
        <div className="grouped fields">
          {checkboxes}
        </div>
        <i>You can add more comma separated amenities here</i>
        {listInput}
        {hiddenFields}
      </div>
    );
  }
}

AmenitiesInput.propTypes = {
  amenities: PropTypes.array.isRequired,
  name: PropTypes.string.isRequired,
};

export default AmenitiesInput;
