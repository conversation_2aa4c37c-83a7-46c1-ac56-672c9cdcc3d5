import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import PropTypes from 'prop-types';

class Checkbox extends Component {
  componentDidMount() {
    const { input: { onChange, value } } = this.props;

    this.checkbox({
      onChecked() {
        onChange(true);
      },
      onUnchecked() {
        onChange(false);
      },
    });

    if (value === true) {
      this.checkbox('check');
    } else {
      this.checkbox('uncheck');
    }
  }

  componentWillReceiveProps(props) {
    const { input: { value } } = this.props;
    const { input: { value: newValue } } = props;

    if (value !== newValue) {
      if (newValue === true) {
        this.checkbox('check');
      } else {
        this.checkbox('uncheck');
      }
    }
  }

  checkbox = (...args) => $(findDOMNode(this)).checkbox(...args);

  render() {
    const { className, label, input, checkbox_value, ...rest } = this.props;

    return (
      <div className={className}>
        <input type="checkbox" {...input} {...rest} value={checkbox_value} />
        <label htmlFor={rest.id}>{label}</label>
      </div>
    );
  }
}

Checkbox.propTypes = {
  className: PropTypes.string.isRequired,
  input: PropTypes.object.isRequired,
  label: PropTypes.string.isRequired,
};

Checkbox.defaultProps = {
  className: 'ui checkbox',
  input: {},
};

export default Checkbox;
