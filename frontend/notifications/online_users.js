import uuid from 'uuid';
import { each, reject, includes } from 'lodash';
import { register<PERSON>and<PERSON> } from './notify';

class OnlineUsers {
  constructor() {
    registerHandler({
      id: uuid.v4(),
      kind: 'users_online',
      handle: this.handle,
    });

    registerHandler({
      id: uuid.v4(),
      kind: 'user_online',
      handle: this.handle,
    });

    registerHandler({
      id: uuid.v4(),
      kind: 'user_offline',
      handle: this.handle,
    });

    this.onlineUsers = [];
    this.subscribers = [];
  }

  subscribe = subscriber => {
    this.subscribers.push(subscriber);
  }

  unsubscribe = subscriber => {
    this.subscribers = reject(this.subscribers, s => s === subscriber);
  }

  handle = event => {
    if (event.kind === 'users_online') {
      this.onlineUsers = event.data;
    } else if (event.kind === 'user_offline') {
      this.onlineUsers = reject(this.onlineUsers, i => i === event.data);
    } else if (event.kind === 'user_online') {
      this.onlineUsers = [...this.onlineUsers, event.data];
    }
    each(this.subscribers, subscriber => subscriber.onlineUsersChanged(this.onlineUsers));
  }

  isOnline = userId => includes(this.onlineUsers, userId);
}

export default OnlineUsers;
