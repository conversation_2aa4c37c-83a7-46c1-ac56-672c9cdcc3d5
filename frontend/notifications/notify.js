// https://developer.mozilla.org/en-US/docs/Web/API/notification

import { filter, reject, some } from 'lodash';

let handlers = [];

const registerHandler = handler => handlers.push(handler);

const unregisterHandler = handler => {
  handlers = reject(handlers, { id: handler.id });
};

const consumed = event => {
  const matching = filter(handlers, { kind: event.kind });
  return some(matching, handler => handler.handle(event));
};

const show = event => {
  let description = event.description;
  let icon = '/images/logo_orange.png';
  let link = event.link;
  let title = event.title;

  if (event.kind === 'message') {
    title = event.title;
    description = event.body;
    icon = event.avatar_url;
    link = '/messaging/chats';
  }

  if (!title) {
    return; // Unknown notification
  }

  const options = {
    body: description,
    icon,
  };

  const notification = new Notification(title, options);

  if (link) {
    notification.onclick = e => {
      e.preventDefault();
      notification.close();
      Turbolinks.visit(link);
    };
  }

  setTimeout(notification.close.bind(notification), 5000);

  // Update notification icon
  if (event.kind !== 'message') {
    $('.notification.icon').addClass('active');
  }
};

const notify = event => {
  if (consumed(event)) {
    // Some handler took the event
    return;
  } else if (!('Notification' in window)) {
    // This browser does not support desktop notifications
  } else if (Notification.permission === 'granted') {
    // If it's okay let's create a notification
    show(event);
  } else if (Notification.permission !== 'denied') {
    // Otherwise, we need to ask the user for permission
    Notification.requestPermission(permission => {
      // If the user accepts, let's create a notification
      if (permission === 'granted') {
        show(event);
      }
    });
  }
};

export { notify, registerHandler, unregisterHandler };
