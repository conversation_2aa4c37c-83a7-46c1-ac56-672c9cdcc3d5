import PropTypes from 'prop-types';
import { Component } from 'react';

import ControlsBox from './controls_box.jsx';
import ToggleButton from './toggle_button.jsx';

class FilterBox extends Component {
  constructor(props) {
    super(props);
    this.state = { visible: false };
  }

  render() {
    const { visible } = this.state;

    return (
      <div className="filter box">
        <ControlsBox visible={visible}>
          {this.props.children}
        </ControlsBox>
        <div className="ui container" style={{ position: 'relative' }}>
          <ToggleButton
            visible={visible}
            onClick={() => this.setState({ visible: !visible })}
          />
        </div>
      </div>
    );
  }
}

FilterBox.propTypes = {
  children: PropTypes.node.isRequired,
};

export default FilterBox;
