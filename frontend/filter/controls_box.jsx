import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';

class ControlsBox extends Component {
  constructor(props) {
    super(props);
    this.state = { open: false };
  }

  componentDidMount() {
    const node = findDOMNode(this);
    $(node).accordion({
      duration: 200,
      onOpening: () => this.setState({ open: true }),
      onClose: () => this.setState({ open: false }),
    });
    this.updateAccordion();
  }

  componentDidUpdate() {
    this.updateAccordion();
  }

  updateAccordion() {
    const action = this.props.visible ? 'open' : 'close';
    const node = findDOMNode(this);
    $(node).accordion(action, 0);
  }

  render() {
    const onClass = this.state.open ? 'on' : 'off';

    return (
      <div className={`ui accordion ${onClass}`}>
        <div className="title" style={{ padding: 0 }} />
        <div className="content">
          <div className="filter controls">
            <div className="ui container">
              {this.props.children}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

ControlsBox.propTypes = {
  children: PropTypes.node.isRequired,
  visible: PropTypes.bool.isRequired,
};

export default ControlsBox;
