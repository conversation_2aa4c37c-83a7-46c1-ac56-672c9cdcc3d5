import PropTypes from 'prop-types';

const ToggleButton = ({ visible, onClick }) => {
  const onClass = visible ? 'on' : 'off';

  return (
    <button
      className={`ui compact tiny bottom attached ${onClass} filter button`}
      onClick={onClick}
    >
      <i className="ui filter icon" />
      Filter
    </button>
  );
};

ToggleButton.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default ToggleButton;
