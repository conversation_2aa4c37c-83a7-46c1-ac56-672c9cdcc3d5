import { Component } from 'react';
import PropTypes from 'prop-types';
import { findDOMNode } from 'react-dom';
import moment from 'moment';
import URI from 'urijs';

class TurbolinksDateSelector extends Component {
  componentDidMount() {
    const node = findDOMNode(this);

    const { calendarType } = this.props;

    $(node).calendar({ type: calendarType, onChange: this.change });

    const url = this.url();

    const { queryKey } = this.props;

    const { [queryKey]: existing } = url.search(true);

    if (existing) {
      const date = moment(existing).toDate();
      console.log('existing date', date);
      $(node).calendar('set date', date, true, false);
    }
  }

  change = date => {
    const url = this.url();

    const { queryKey } = this.props;

    const search = url.search(true);
    url.search({ ...search, [queryKey]: moment(date).format() });

    Turbolinks.visit(url.toString());
  };

  url = () => new URI(window.location.href);

  render() {
    return (
      <div className="ui four wide calendar field">
        <div className="ui fluid left icon input" style={{ width: '175px', margin: '0 1em' }}>
          <input
            type="text"
            placeholder={this.props.placeholder}
          />
          <i className="calendar alternate outline icon" />
        </div>
      </div>
    );
  }
}

TurbolinksDateSelector.propTypes = {
  queryKey: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  calendarType: PropTypes.string.isRequired,
};

TurbolinksDateSelector.defaultProps = {
  queryKey: 'date',
  placeholder: 'Date',
  calendarType: 'date',
};

export default TurbolinksDateSelector;
