import { Component } from 'react';
import URI from 'urijs';

import PropertyOrPortfolioDropdown from '../selectors/property_or_portfolio_dropdown';

class TurbolinksPortfolioSelector extends Component {
  change = portfolio => {
    const url = this.url();

    const search = url.search(true);
    url.search({ ...search, portfolio });

    Turbolinks.visit(url.toString());
  };

  url = () => new URI(window.location.href);

  render() {
    return (
      <PropertyOrPortfolioDropdown
        ref="selector"
        onChange={this.change}
      />
    );
  }
}

export default TurbolinksPortfolioSelector;
