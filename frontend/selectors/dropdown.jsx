import PropTypes from 'prop-types';
import classNames from 'classnames';
import { Component } from 'react';
import { isArray, filter, includes, find, forEach, map } from 'lodash';

class Dropdown extends Component {
  constructor(props) {
    super(props);
    this.state = {
      items: props.items || [],
      value: null
    };
  }

  componentDidMount() {
    const onChange = this.handleChange.bind(this);

    const { allowAdditions, fullTextSearch } = this.props;

    const options = {
      allowAdditions,
      fullTextSearch,
      onChange,
      match: 'text',
      silent: true,
      ...this.props.options,
    };

    $(this.node).dropdown(options);

    this.fetchItems(this.props.url);
    if (this.props.items) {
      this.automaticSelection();
    }
  }

  componentWillReceiveProps(props) {
    if (props.url !== this.props.url) {
      this.setState({ items: [] });
      this.clear();
      this.fetchItems(props.url);
    }

    if (props.items && props.items !== this.props.items) {
      this.setState({ items: props.items }, () => this.automaticSelection());
    }

    if (props.value === null) {
      this.clear();
    }
  }

  componentWillUnmount() {
    if (this.ajax) this.ajax.abort();
  }

  setDefault() {
    const selection = this.props.defaultSelection;

    if (selection) {
      this.state.defaultSelection = null;
      setTimeout(() => {
        if (this.props.multiple) {
          forEach(selection, item => (
            this.dropdown('set selected', item)
          ));
        } else {
          this.dropdown('set selected', selection);
        }
        this.handleChange(selection);
      }, 0);
      return true;
    }

    return false;
  }

  dropdown = (...args) => $(this.node).dropdown(...args);

  // Optionally selects the first item based on selectFirst prop
  selectFirst() {
    const { selectFirst } = this.props;

    if (selectFirst === 'always' || (selectFirst === 'single' && this.state.items.length === 1)) {
      setTimeout(() => {
        const selection = this.value(this.state.items[0]);
        this.dropdown('set selected', selection);
        this.handleChange(selection);
      }, 0);
    }
  }

  clear() {
    this.dropdown('clear');
  }

  value(item) {
    return item[this.props.valueField];
  }

  text(item) {
    return item[this.props.textField];
  }

  icon(item) {
    return item.icon;
  }

  automaticSelection() {
    if (!this.setDefault()) {
      this.selectFirst();
    }
  }

  fetchItems(url) {
    if (url) {
      if (this.ajax) this.ajax.abort();

      this.ajax = $.get({
        url,
        success: (items) => this.setState({ items }, this.automaticSelection),
      });
    }
  }

  addItems(value) {
    let items = this.state.items.slice();

    const addValue = (val) => {
      if (items.findIndex(it => this.value(it) === val) >= 0) {
        return;
      }

      let tmp = {};
      tmp[this.props.textField] = value;
      tmp[this.props.valueField] = value;
      items.push(tmp);
    }

    if (this.props.multiple) {
      value.toString().split(',').forEach(addValue);
    } else {
      addValue(value.toString());
    }

    if (items.length !== this.state.items.length) {
      this.setState({ items });
    }

    return items;
  }

  handleChange(value) {
    let items = this.state.items.slice();

    if (this.props.allowAdditions) {
      items = this.addItems(value);
    }

    if (this.props.multiple) {
      const v = isArray(value) ?
        map(value, v => v.toString()) : value.toString().split(',');
      items = filter(items, i => includes(v, this.value(i).toString()));

      this.setState({ value: items.map(it => this.value(it)) || null });

      if (this.props.onChange) {
        this.props.onChange(items);
      }
    } else {
      const v = value.toString();
      const item = find(items, i => this.value(i).toString() === v);

      this.setState({ value: item ? this.value(item) : null });

      if (this.props.onChange) {
        this.props.onChange(item);
      }
    }
  }

  inputs() {
    const { id, multiple, name } = this.props
    const { value } = this.state

    if (multiple && value) {
      return value.map((v, i) => (
        <input key={i} id={id} value={v} name={name} type="hidden" />
      ));
    } else if (!multiple) {
      return (<input id={id} value={value || ''} name={name} type="hidden" />);
    }

    return null;
  }

  render() {
    let options = map(this.state.items, item => {
      const icon = this.icon(item);
      const value = this.value(item);
      const text = this.text(item);
      return (
        <div key={value} className="item" data-value={value}>
          {icon && <i className={`${icon} icon`}></i>}
          {text}
        </div>
      );
    });

    if (this.props.addExplicitDefaultTextOption) {
      options = [
        (
          <div key={''} className="item" data-value={''}>
            {this.props.defaultText}
          </div>
        ),
        ...options,
      ];
    }

    const { className, name, fluid, multiple } = this.props;

    return (
      <div
        className={classNames(className, { fluid, multiple })}
        ref={d => this.node = d}
      >
        {this.inputs()}
        <i className="dropdown icon" />
        <div className="default text">{this.props.defaultText}</div>
        <div className="menu">
          {options}
        </div>
      </div>
    );
  }
}

Dropdown.propTypes = {
  allowAdditions: PropTypes.bool.isRequired,
  className: PropTypes.string,
  defaultSelection: PropTypes.any,
  defaultText: PropTypes.string.isRequired,
  fluid: PropTypes.bool.isRequired,
  fullTextSearch: PropTypes.bool.isRequired,
  id: PropTypes.string,
  items: PropTypes.array, // Optional array of items
  multiple: PropTypes.bool.isRequired,
  name: PropTypes.string,
  onChange: PropTypes.func, // Optional change callback
  options: PropTypes.object.isRequired, // Extra options
  selectFirst: PropTypes.string.isRequired,
  textField: PropTypes.string.isRequired,
  url: PropTypes.string, // Optional data url
  valueField: PropTypes.string.isRequired,
};

Dropdown.defaultProps = {
  allowAdditions: false,
  className: 'ui search selection dropdown',
  defaultText: '',
  fluid: false,
  fullTextSearch: true,
  multiple: false,
  options: {},
  selectFirst: 'never',
  textField: 'name',
  valueField: 'id',
};

export default Dropdown;
