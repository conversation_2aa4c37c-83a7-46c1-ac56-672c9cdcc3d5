import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import { map } from 'lodash';

const propertiesUrl = '/manage/properties.json';
const portfoliosUrl = '/manage/portfolios.json';

class PropertyOrPortfolioDropdown extends Component {
  static propTypes = {
    id: PropTypes.string,
    name: PropTypes.string,
    onChange: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    $.get(propertiesUrl, properties => this.setState({ properties }));
    $.get(portfoliosUrl, portfolios => this.setState({ portfolios }));

    this.node = findDOMNode(this);

    const onChange = this.handleChange.bind(this);

    $(this.node).dropdown({
      allowReselection: true,
      fullTextSearch: true,
      match: 'text',
      placeholder: 'value',
      onChange,
    });
  }

  handleChange(targetId) {
    const { onChange } = this.props;
    if (onChange) {
      onChange(targetId);
    }
  }

  render() {
    const { properties, portfolios } = this.state;

    const options = [];

    const makeOption = type => item => {
      const value = `${type}-${item.id}`;
      return (
        <div
          className="item"
          data-value={value}
          key={value}
        >
          {item.name}
        </div>
      );
    };

    const makeHeader = (text, icon) => (
      <div className="header" key={text}>
        <i className={`ui ${icon} icon`} />
        {text}
      </div>
    );

    const makeDivider = i => <div key={`divider-${i}`} className="divider" />;

    if (properties && portfolios) {
      options.push(makeHeader('Properties', 'cube'));
      options.push(makeDivider(0));
      options.push(map(properties, makeOption('property')));

      options.push(makeHeader('Portfolios', 'cubes'));
      options.push(makeDivider(1));
      options.push(map(portfolios, makeOption('portfolio')));
    }

    const { name, id } = this.props;

    return (
      <div className="ui search selection dropdown">
        <input id={id} name={name} type="hidden" />
        <i className="dropdown icon" />
        <div className="default text">All Properties</div>
        <div className="menu">
          <div className="item" data-value="">All Properties</div>
          {options}
        </div>
      </div>
    );
  }
}

export default PropertyOrPortfolioDropdown;
