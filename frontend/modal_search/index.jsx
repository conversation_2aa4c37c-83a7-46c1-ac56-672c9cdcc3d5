import classNames from 'classnames';
import moment from 'moment';
import { Component } from 'react';
import { arrayOf, func, string } from 'prop-types';
import { debounce, map, startCase, truncate } from 'lodash';

class ModalSearch extends Component {
  static propTypes = {
    onSelect: func.isRequired,
    subheader: string,
    title: string.isRequired,
    types: arrayOf(string).isRequired,
  };

  static defaultProps = {
    onSelect: () => {},
    subheader: '',
  };

  constructor(props) {
    super(props);

    this.state = { loading: false, query: '', results: {} };

    this.debouncedFetch = debounce(
      this.fetchResults, 200, { leading: true, trailing: true },
    );
  }

  handleQueryChanged = ({ target: { value: query } }) => {
    this.setState({ query }, this.debouncedFetch);
  }

  handleItemSelected = item => {
    this.props.onSelect(item);
    $(this.modal).modal('hide');
  }

  fetchResults = () => {
    const { props: { types }, state: { query } } = this;

    this.setState({ loading: true });

    if (this.xhr) {
      this.xhr.abort();
    }

    this.xhr = $.get(
      '/manage/modal_search.json',
      { query, types },
      results => this.setState({ results, loading: false }),
    );
  }

  render() {
    const {
      props: { subheader, title, types },
      state: { loading, query, results },
    } = this;

    return (
      <div ref={modal => this.modal = modal} className="ui small modal" id="modal-search">
        <h2 className="ui header">
          {title}
          <div className="sub header">
            {subheader}
          </div>
        </h2>
        <div className="content">
          <div className={classNames('ui fluid icon input', { loading })}>
            <input
              type="text"
              placeholder="Search..."
              onChange={this.handleQueryChanged}
            />
            <i className="search icon" />
          </div>
          {map(types, type => {
            const items = results[type] || [];

            const disabled = items.length === 0;

            const titlePrefix = query ? 'Matching' : 'Recent';

            return (
              <div key={type} style={{ marginTop: '1em' }}>
                <h4 className={classNames('ui', { disabled }, 'header')}>
                  {titlePrefix} {startCase(type)}
                  <div className="sub header">
                    {disabled && 'No results'}
                  </div>
                </h4>
                <div className="ui divided selection list">
                  {map(items, item => (
                    <a
                      key={item.id}
                      tabIndex="0"
                      className="item"
                      onClick={() => this.handleItemSelected(item)}
                      onKeyDown={e => e.which === 13 && this.handleItemSelected(item)}
                    >
                      <div className="right floated content">
                        {moment(item.date).fromNow()}
                      </div>
                      <div className="content">
                        <div className="header">
                          {item.name}
                        </div>
                        <div className="description">
                          {truncate(item.description || 'No description', { length: 80 })}
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
        <div className="actions">
          <div className="ui cancel button">
            Cancel
          </div>
        </div>
      </div>
    );
  }
}

export default ModalSearch;
