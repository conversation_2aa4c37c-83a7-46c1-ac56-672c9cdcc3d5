import PropTypes from 'prop-types';
import moment from 'moment';

const Document = ({ document, onRemove }) => (
  <a
    className="paper document card"
    href={document.url}
    title={document.title}
  >
    <div className="content">
      {document.id && (
        <i
          className="ui remove icon"
          onClick={e => { e.preventDefault(); onRemove(document); }}
        />
      )}
      <div
        className="header"
        style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {document.title}
      </div>
      <div className="meta">
        <i>{document.date && moment(document.date).format('ll')}</i>
      </div>
      <div className="description">
        <img
          src="http://www.semantic-ui.com/images/wireframe/paragraph.png"
          width="100px"
          role="presentation"
        />
      </div>
    </div>
  </a>
);

Document.propTypes = {
  document: PropTypes.object.isRequired,
  onRemove: PropTypes.func.isRequired,
};

export default Document;
