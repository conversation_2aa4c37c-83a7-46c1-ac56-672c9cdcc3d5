import PropTypes from 'prop-types';
import { Component } from 'react';
import { concat, reject } from 'lodash';

import AttachmentStore from '../../attachments/attachment_store';
import Documents from './documents';
import DocumentAttachmentArea from './document_attachment_area';

class DocumentsArea extends Component {

  static propTypes = {
    documents: PropTypes.array.isRequired,
    attachable_sgid: PropTypes.string.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = { documents: props.documents };

    const attachmentsUrl = `/attachments/${props.attachable_sgid}`;
    this.store = new AttachmentStore(attachmentsUrl);
  }

  onDocumentAdded = doc =>
    this.setState({ documents: concat(this.state.documents, doc) });

  onDocumentRemoved = doc =>
    this.setState({ documents: reject(this.state.documents, { url: doc.url }) });

  handleRemoveDocument = doc =>
    this.store.destroyResource(doc).then(() => this.onDocumentRemoved(doc));

  render() {
    const { documents } = this.state;

    return (
      <div className="documents area">
        <Documents
          documents={documents}
          handleRemoveDocument={this.handleRemoveDocument}
        />
        <DocumentAttachmentArea
          store={this.store}
          onDocumentAdded={this.onDocumentAdded}
        />
      </div>
    );
  }
}

export default DocumentsArea;
