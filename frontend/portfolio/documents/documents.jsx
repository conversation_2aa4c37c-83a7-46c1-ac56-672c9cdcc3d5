import PropTypes from 'prop-types';
import { map } from 'lodash';

import Document from './document';

const Documents = ({ documents, handleRemoveDocument }) => (
  <div className="ui cards">
    {map(documents, (doc, i) =>
      <Document key={i} document={doc} onRemove={handleRemoveDocument} />)}
  </div>
);

Documents.propTypes = {
  documents: PropTypes.array.isRequired,
  handleRemoveDocument: PropTypes.func.isRequired,
};

export default Documents;
