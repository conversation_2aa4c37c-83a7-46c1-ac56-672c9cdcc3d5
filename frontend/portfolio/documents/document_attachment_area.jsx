import PropTypes from 'prop-types';

import PhotoAttachmentArea from '../../forms/fields/photo_attachments_area';

class DocumentAttachmentArea extends PhotoAttachmentArea {

  static propTypes = {
    onDocumentAdded: PropTypes.func.isRequired,
    store: PropTypes.object.isRequired,
  };

  getStore() {
    return this.props.store;
  }

  handleCreatePhoto(photo) {
    this.props.onDocumentAdded({ ...photo, title: photo.filename });
  }
}

export default DocumentAttachmentArea;
