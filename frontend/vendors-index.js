// ActionText
require("trix")
require("@rails/actiontext")

// Magnific popup
require('imports-loader?define=>false&exports=>false!../node_modules/magnific-popup/dist/jquery.magnific-popup.js');
require('../node_modules/magnific-popup/dist/magnific-popup.css');

// Stimulus
import 'element-closest';

import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';

// Action Cable
import App from './channels/vendors_app';
window.App = App;

const stimulus = StimulusApplication.start();
stimulus.load(
  stimulusDefinitions(
    require.context(
      './stimulus/',
      true,
      /\.\/(vendors|timeline|attachments_bar).*\.js$/
    )
  )
);
