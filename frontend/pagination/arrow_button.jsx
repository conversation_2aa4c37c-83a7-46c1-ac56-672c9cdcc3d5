import PropTypes from 'prop-types';

const ArrowButton = ({
  direction,
  enabled,
  onClick,
}) => (
  <a
    className={`${enabled ? '' : 'disabled '}icon item`}
    onClick={enabled ? onClick : null}
  >
    <i className={`${direction} chevron icon`} />
  </a>
);

ArrowButton.propTypes = {
  direction: PropTypes.string.isRequired,
  enabled: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default ArrowButton;
