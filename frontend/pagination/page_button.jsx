import PropTypes from 'prop-types';

const PageButton = ({ page, active, onClick }) => (
  <a
    className={`${active ? 'active' : ''} item`}
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '3em',
    }}
    onClick={onClick}
  >
    {page + 1}
  </a>
);

PageButton.propTypes = {
  active: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
};

export default PageButton;
