import PropTypes from 'prop-types';

import ArrowButton from './arrow_button';
import PageButton from './page_button';

const ellipses = key => (
  <a key={key} className="ui disabled item">...</a>
);

const PageButtons = ({ page, pages, onChangePage }) => {
  const pageButton = i => (
    <PageButton
      key={i}
      page={i}
      active={page === i}
      onClick={() => onChangePage(i)}
    />
  );

  const collapseLeft = pages > 5 && page > 4;
  const collapseRight = pages > 5 && page < pages - 5;

  const left = [];
  const right = [];

  left.push(
    <ArrowButton
      key="left"
      direction="left"
      enabled={page > 0}
      onClick={() => onChangePage(page - 1)}
    />,
  );
  if (collapseLeft) {
    left.push(pageButton(0));
    left.push(ellipses('start'));
  }

  if (collapseRight) {
    right.push(ellipses('end'));
    right.push(pageButton(pages - 1));
  }
  right.push(
    <ArrowButton
      key="right"
      direction="right"
      enabled={page < pages - 1}
      onClick={() => onChangePage(page + 1)}
    />,
  );

  let start = 0;
  if (collapseLeft && !collapseRight) {
    start = pages - 7;
  } else if (collapseLeft) {
    start = Math.max(3, page - 2);
  }

  let end = pages - 1;
  if (collapseLeft && collapseRight) {
    end = page + 2;
  } else if (collapseRight) {
    end = start + 6;
  }

  const center = [];
  for (let i = start; i <= end; i += 1) {
    center.push(pageButton(i));
  }

  return (
    <div className="ui pagination menu">
      {left}
      {center}
      {right}
    </div>
  );
};

PageButtons.propTypes = {
  page: PropTypes.number.isRequired,
  pages: PropTypes.number.isRequired,
  onChangePage: PropTypes.func.isRequired,
};

export default PageButtons;
