import PropTypes from 'prop-types';
import { Component } from 'react';
import uuid from 'uuid';

import PageButtons from './page_buttons.jsx';

class Paginate extends Component {
  constructor(props) {
    super(props);

    this.state = {
      pages: Math.ceil(props.children.length / props.perPage),
      page: 0,
    };

    this.changePage = this.handleChangePage.bind(this);
  }

  handleChangePage(page) {
    this.setState({ page });
  }

  blank() {
    return (
      <div
        key={uuid.v4()}
        style={{
          height: this.props.blankHeight,
        }}
      />
    );
  }

  render() {
    const { className, children, perPage } = this.props;
    const { page, pages } = this.state;

    const drop = page * perPage;

    const entries = _(children)
      .drop(drop)
      .take(perPage)
      .value();

    const pad = perPage - (children.length - drop);

    for (let i = 0; i < pad; i++) {
      entries.push(this.blank());
    }

    return (
      <div className={className} >
        {entries}
        <div
          style={{
            marginTop: '0.5em',
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <PageButtons
            onChangePage={this.changePage}
            page={page}
            pages={pages}
          />
        </div>
      </div>
    );
  }
}

Paginate.propTypes = {
  blankHeight: PropTypes.string,
  children: PropTypes.arrayOf(PropTypes.node).isRequired,
  className: PropTypes.string,
  perPage: PropTypes.number.isRequired,
};

Paginate.defaultProps = {
  perPage: 10,
};

export default Paginate;
