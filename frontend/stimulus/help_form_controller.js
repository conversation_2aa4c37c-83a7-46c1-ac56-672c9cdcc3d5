import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'back', 'submit', 'cancel', // Buttons
    'categories', 'form', 'success', // Pages
    'categoryField', 'errorCodeField', 'reportIssueField', // Fields
  ];

  static validations = {
    description: ['empty'],
  };

  initialize() {
    $('#help-button').click(event => {
      event.preventDefault();
      const options = { inverted: true };
      this.reset();
      $('.ui.help.modal').modal(options).modal('show');
    });

    $(this.backTarget).click(() => this.reset());
  }

  reset() {
    $(this.categoriesTarget).show();
    $(this.formTarget).hide();
    $(this.errorCodeFieldTarget).hide();
    $(this.reportIssueFieldTarget).hide();
    $(this.successTarget).hide();
    $(this.formTarget).form('reset');
    $(this.backTarget).hide();
    $(this.submitTarget).show();
    $(this.cancelTarget).text('Cancel');
  }

  categorize(event) {
    const category = event.currentTarget.dataset.value;

    this.categoryFieldTarget.value = category;
    $(this.categoriesTarget).attr('style', 'display: none !important;');
    $(this.formTarget).show();
    $(this.submitTarget).removeClass('disabled');
    $(this.backTarget).show();

    if (category === 'reporting') {
      $(this.reportIssueFieldTarget).show();
    } else if (category === 'error') {
      $(this.errorCodeFieldTarget).show();
    }
  }

  submit() {
    $(this.formTarget).form('validate form');
  }

  failure() {
    // TODO
    $(this.submitTarget).removeClass('loading', 'disabled');
  }

  success() {
    // TODO
    $(this.submitTarget).hide();
    $(this.formTarget).hide();
    $(this.backTarget).hide();
    $(this.successTarget).show();
    $(this.submitTarget).removeClass('loading', 'disabled');
    $(this.cancelTarget).text('Close');
  }

  connect() {
    this.reset();

    $(this.formTarget).form({
      on: 'submit',
      inline: true,
      fields: this.constructor.validations,
      onSuccess: () => {
        $(this.submitTarget).addClass('loading', 'disabled');

        const data = new FormData(this.formTarget);

        $.ajax({
          type: 'POST',
          url: '/manage/help',
          data,
          cache: false,
          contentType: false,
          processData: false,
          success: () => this.success(),
          error: () => this.failure(),
        });
      },
    });
  }
}
