import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['accordion', 'button'];

  connect() {
    $(this.accordionTarget).accordion({ duration: 150 });
  }

  disconnect() {
    // Show accordion for turbolinks cache
    $(this.accordionTarget).removeClass('off').addClass('on');
    $(this.accordionTarget).find('.content').addClass('active');
    $(this.accordionTarget).find('.filter.controls').removeClass('hidden').addClass('visible');
    $(this.buttonTarget).removeClass('off').addClass('on');
  }

  toggle() {
    $(this.accordionTarget).accordion('toggle', 0);
    $(this.accordionTarget).toggleClass('on');
    $(this.buttonTarget).toggleClass('on');
    $(this.buttonTarget).blur();
  }
}
