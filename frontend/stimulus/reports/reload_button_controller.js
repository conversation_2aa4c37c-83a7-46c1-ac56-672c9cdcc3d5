import { Controller } from 'stimulus';

export default class extends Controller {
  connect() {
    const target = $(this.element);

    target.off('click').click(() => {
      window.showReportLoading();
      const query = qs.parse(window.location.search.replace('?', ''));
      const path = window.location.pathname;
      const queryParams = '?' + qs.stringify(query);
      window.fetchReport(path + '.js' + queryParams);

      target.addClass('disabled');

      setTimeout(() => {
        target.removeClass('disabled');
      }, 5000);
    });
  }
}
