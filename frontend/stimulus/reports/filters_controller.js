import moment from 'moment';
import { Controller } from 'stimulus';

export default class extends Controller {
  connect() {
    const preview = document.documentElement.hasAttribute("data-turbolinks-preview");

    this.notTurbolinksPreview = !preview;

    if (this.notTurbolinksPreview) {
      // This exists because of AccountingContextSelector react component
      window.reportSetFilter = (key, value) => { this.setFilter(key, value) };

      initializeSemanticFields();

      this.setupDropdowns();
      this.setupCalendars();
      this.setupSearch();
    }
  }

  disconnect() {
    if (this.notTurbolinksPreview) {
      // Navigating away from report, cancel request

      // TODO: Move up to a parent controller
      window.reports.loader._cancelPendingRequest();
    }
  }

  reloadAccountsDropdown(contextId) {
    const dropdown = $(this.element).find('#account_id');

    if (dropdown.length) {
      // Disable
      dropdown.closest('.field').addClass('disabled');

      // Clear
      dropdown.dropdown('change values', []);

      // Reload
      const url = '/reports/accounting_contexts/accounts.json?context_id=' + contextId;
      $.get(url, accounts => {
        dropdown.dropdown('change values', accounts);

        // Enable
        $(this.element).find('#account-id-field').removeClass('disabled');
      });
    }
  }

  setFilter(key, value) {
    if (key === 'context_id') {
      this.reloadAccountsDropdown(value);
    }

    const query = qs.parse(window.location.search.replace('?', ''));

    if (!query.filters) {
      query.filters = {};
    }

    query.filters[key] = value;

    const path = window.location.pathname;
    const queryParams = '?' + qs.stringify(query);

    // Display Loading
    window.showReportLoading();

    // Load Report
    window.fetchReport(path + '.js' + queryParams);

    // Update Buttons
    $('.ui.button#report-pdf').attr('href', path + '.js' + queryParams + '&download_format=pdf');
    $('.ui.button#report-xlsx').attr('href', path + '.js' + queryParams + '&download_format=xlsx');

    // Update URL
    Turbolinks.controller.replaceHistoryWithLocationAndRestorationIdentifier(
      path + queryParams, Turbolinks.uuid()
    );
  };

  setupDropdowns() {
    const setFilter = this.setFilter.bind(this);

    const dropdownChange = function (value, text, $choice) {
      let select = $(this).closest('select');

      if (select.length === 0) {
        select = $(this).find('select');
      }

      const key = select.attr('name');
      setFilter(key, value);
    };

    $(this.element)
      .find(':not([data-react-class] *).ui.dropdown')
      .dropdown('setting', 'onChange', dropdownChange);
  }

  setupCalendars() {
    const setFilter = this.setFilter.bind(this);

    const calendarChange = function (value) {
      const calendar = $(this);

      const key = calendar.find('input').attr('name');

      if (calendar.data('options').type === 'date' && value instanceof Date) {
        value = moment(value).format('YYYY-MM-DD');
      }

      setFilter(key, value);
    };

    $(this.element)
      .find('.ui.calendar[data-init]')
      .calendar('setting', 'onChange', calendarChange)
      .attr('data-init', true);
  }

  setupSearch() {
    const setFilter = this.setFilter.bind(this);

    const searchChange = function (value) {
      setFilter('tenant_id', value.id);
    }

    $(this.element).find('.ui.search')
      .search({
        minCharacters: 0,
        searchOnFocus: true,
        apiSettings: {
          url: '/api/v1/search?q={query}'
        },
        onSelect: searchChange,
      });
  }
}
