import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'buyerSearch',
    'sellerSearch',
    'buyerLeaseMembership',
    'buyerUnit',
    'sellerLeaseMembership',
    'sellerUnit',
  ];

  connect() {
    const self = this;

    // Setup buyer / seller change form reload
    const reloadForm = this.reloadForm;
    [this.buyerSearchTarget, this.sellerSearchTarget].forEach(target => {
      $(target).find('input[type=hidden]').on('change', function () {
        reloadForm($(self.element));
      });
    });

    // Setup lease chain reload
    const setupLeaseReload = function (target) {
      $(target).dropdown('setting', 'onChange', function (newVal) {
        reloadForm($(self.element));
      });
    };

    if (this.hasBuyerLeaseMembershipTarget) {
      setupLeaseReload(this.buyerLeaseMembershipTarget);
    }

    if (this.hasSellerLeaseMembershipTarget) {
      setupLeaseReload(this.sellerLeaseMembershipTarget);
    }

    if (this.hasBuyerUnitTarget) {
      setupLeaseReload(this.buyerUnitTarget);
    }

    if (this.hasSellerUnitTarget) {
      setupLeaseReload(this.sellerUnitTarget);
    }
  }

  reloadForm = form => {
    const formvalues = form.serialize();

    if (formvalues) {
      $.get(`${window.location.pathname}.js?${formvalues}`);
    }
  }
}
