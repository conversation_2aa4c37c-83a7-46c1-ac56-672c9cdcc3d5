import moment from 'moment';
import { forEach, last, map, split, toFinite } from 'lodash';
import { Controller } from 'stimulus';
import { formatMoney } from '../util/money';

export default class extends Controller {
  static targets = [
    'invoiceDate',
    'dueDate',
    'netDays',
    'lineItem',
    'lineItems',
  ];

  connect() {
    this.journalId = this.element.dataset.journalId;
    this.accountingSide = this.element.dataset.accountingSide;
    this.accountValues = null;

    this.prepareAccountingContextChanges();

    this.afterAddItem();

    $(this.lineItemsTarget).on('cocoon:after-insert', (e, task) => {
      task.show(150).find('input.quantity').val(null);
      if (this.accountValues) {
        const dropdown = task.find('.ui.dropdown');
        this.setAccountValues(dropdown, this.accountValues);
      }
      this.afterAddItem();
    });

    $(this.lineItemsTarget).on('cocoon:before-insert', (e, task) => {
      task.hide();
    });

    $(this.dueDateTarget).calendar({
      type: 'date',
      onChange: dueDate => {
        this.computeNetD(this.getPostDate(), dueDate);
      },
    });

    $(this.invoiceDateTarget).calendar({
      type: 'date',
      onChange: invoiceDate => {
        // $(this.dueDateTarget).calendar('setting', 'minDate', date);

        this.computeNetD(invoiceDate, this.getDueDate());
      },
    });

    this.computeNetD(this.getPostDate(), this.getDueDate());
    this.computeTotals();
    this.prepareFormValidation();
  }

  getDueDate = () => $(this.dueDateTarget).calendar('get date');

  getPostDate = () => $(this.invoiceDateTarget).calendar('get date');

  computeNetD = (invoiceDate, dueDate) => {
    let net = 30;

    if (invoiceDate && dueDate) {
      net = moment(dueDate).diff(moment(invoiceDate), 'days');
    }

    $(this.netDaysTarget).html(`Net ${net}`);
  }

  prepareAccountDropdowns = () => {
    $('.ui.floating.dropdown#account').dropdown({
      forceSelection: true,
      fullTextSearch: true,
      onChange(value, text) {
        const name = last(split(text, ' - ')) || 'Description';
        $(this).closest('.line-item').find('.description').attr('placeholder', name);
        $(this).find('.account-id').val(value);
      },
    });
  }

  removeItem = event => {
    event.preventDefault();
    const computeTotals = this.computeTotals;
    $(event.target).closest('.line-item').slideUp(150, function () {
      const lineItemIndex = $(this).data('index');

      // If this is a persisted line item, add the _destroy flag for
      // accepts_nested_attributes_for removal
      if (lineItemIndex !== undefined) {
        const html = `<input type="hidden" name="invoice[line_items_attributes][${lineItemIndex}][_destroy]" value="true"></input>`;
        const form = $(this).closest('form');
        $(html).appendTo(form);
      }

      $(this).remove();
      computeTotals();
    });
  }

  forwardItem = event => {
    event.preventDefault();
    const item = $(event.target).closest('.line-item');
    $(item).find('.row.controls').hide();
    $(item).find('.row.forwarding').show();

    this.computeTotals();

    $(item).find('.search').search({
      type: 'category',
      selectFirstResult: true,
      cache: false,
      apiSettings: {
        url: '/accounting/targets.json?q={query}',
      },
      onSelect(result) {
        $(this).find('input.forward-id').val(result.sgid);
      },
    });
  }

  afterAddItem = () => {
    this.prepareAccountDropdowns();
    window.initializeSemanticFields();

    setTimeout(() => {
      $('.line-items input.money.input').blur(this.computeTotals);
      $('.line-items input.quantity.input').change(this.computeTotals);
      $('.line-items input.markup.input').change(this.computeTotals);
      $('.line-items input.markup-kind.input').change(this.computeTotals);

      const randId = Math.random().toString(36).slice(2);
      $('.line-items .line-item')
        .last()
        .find('.markup-kind.input').attr('id', randId);
      $('.line-items .line-item')
        .last()
        .find('.markup-kind.button').data('field', randId);
    }, 10);
  }

  computeTotals = () => {
    let subtotal = 0;
    let subtotalRecoup = 0;

    this.lineItemTargets.forEach(target => {
      const amount = $(target).find('input.money.input').val();
      const quantity = $(target).find('input.quantity.input').val() || 1;
      const cleanedAmount = amount.replace('$', '').replace(/,/g, '');
      const cents = Math.round(parseFloat(cleanedAmount, 10) * 100);
      const total = quantity * cents;
      subtotal += total;
      const formatted = formatMoney(total, true);
      $(target).find('.total-amount').html(formatted);

      if ($(target).find('.forwarding').is(':visible')) {
        const markupInput = $(target).find('input.markup.input');
        const markupKindInput = $(target).find('input.markup-kind.input');
        const markupValue = toFinite(markupInput.val()) || 0;
        let markupCents = 0;

        if (markupKindInput.val() === 'fixed' && markupValue) {
          markupCents = markupValue * 100;
        } else if (markupValue || markupInput.val() == '0') {
          markupCents = (total * (markupValue / 100));
        } else {
          markupCents = total * 0; // 0% default markup
        }

        const recoup = total + markupCents;
        subtotalRecoup += recoup;
        const formattedRecoup = formatMoney(recoup, true);
        $(target).find('.total-recoup-amount').html(formattedRecoup);
      }
    });

    const formatted = formatMoney(subtotal, true);
    const formattedRecoup = formatMoney(subtotalRecoup, true);

    $(this.element).find('.subtotal-amount b').html(formatted);
    $(this.element).find('.recoup-subtotal-amount b').html(formattedRecoup);
  }

  prepareFormValidation = () => {
    $('form#invoice_form').form({
      on: 'submit',
      inline: true,
      fields: {
        invoice_buyer: ['empty'],
        invoice_seller: ['empty'],
        invoice_post_date: ['empty'],
        invoice_physical_date: ['empty'],
        invoice_due_date: ['empty'],
        invoice_description: ['empty'],
      },
    });
  }

  setupAccountingContextChanges = () => {
    let journalSearch
    if (this.accountingSide === 'receivables') {
      journalSearch = $('#invoice_seller').closest('.ui.search');
    } else {
      journalSearch = $('#invoice_buyer').closest('.ui.search');
    }

    const existingOnSelect = journalSearch.search('setting', 'onSelect');

    if (!existingOnSelect) {
      return;
    }

    journalSearch.search('setting', 'onSelect', context => {
      existingOnSelect(context);

      if (context.journal_id != this.journalId) {
        this.journalId = context.journal_id;
        this.reloadAccounts(this.journalId);
      }
    });
  }

  prepareAccountingContextChanges = () => {
    $(document).on('turbolinks:load', this.setupAccountingContextChanges);
    this.setupAccountingContextChanges();
  }

  reloadAccounts = journalId => {
    const dropdowns = $('.ui.floating.dropdown#account');

    dropdowns.addClass('disabled').dropdown('clear');

    $.get(`/accounting/journals/${journalId}/accounts.json?invoiceable=true`, accounts => {
      this.accountValues = accounts;

      this.setAccountValues(dropdowns, accounts);

      dropdowns.removeClass('disabled');
    });
  }

  setAccountValues = (dropdowns, accounts) => {
    let string = '';

    forEach(accounts, account => {
      string += `<div class="item" data-value="${account.id}">${account.display_name}</div>`;
    });

    dropdowns.find('.menu .scrolling.menu').html(string);
  };
}
