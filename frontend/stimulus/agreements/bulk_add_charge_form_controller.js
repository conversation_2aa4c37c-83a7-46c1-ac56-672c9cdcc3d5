import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'invoiceDescription',
    'postDate', 'dueDate',
    'lineItems',
    'installments'
  ];

  connect() {
    this.setupLineItems();

    this.setupInstallments();

    this.userSetInvoiceDescription = false;

    this.invoiceDescriptionTarget.addEventListener('input', () => {
      this.userSetInvoiceDescription = true;
    });

    $(this.dueDateTarget).calendar('setting', 'onChange', () => {
      this.userSetDueDate = true;
    });

    $('.ui.checkbox').checkbox();
  }

  setupLineItems() {
    $(this.lineItemsTarget).on('cocoon:before-insert', (e, item) => {
      item.hide();
    });

    $(this.lineItemsTarget).on('cocoon:after-insert', (e, item) => {
      this.afterAddLineItem(item);
    });

    $(this.lineItemsTarget).on('cocoon:after-remove', (e) => {
      this.afterRemoveLineItem();
    });

    const firstItem = $(this.element).find('.nested-fields')[0];
    this.setupChargePresetDropdown(firstItem);
  }

  setupInstallments() {
    $(this.installmentsTarget).on('cocoon:before-insert', (e, item) => {
      item.hide();
    });

    $(this.installmentsTarget).on('cocoon:after-insert', (e, item) => {
      this.afterAddInstallment(item);
    });

    $(this.installmentsTarget).on('cocoon:after-remove', (e) => {
      this.afterRemoveInstallment();
    });
  }

  resize = () => window.dispatchEvent(new Event('resize')); // Trigger modal dimmer scrollbar

  afterAddLineItem(item) {
    this.afterAdd(item);
    this.setupChargePresetDropdown(item);
    this.updateRemoveLineItemButtons();
  }

  afterAddInstallment(item) {
    this.afterAdd(item);
    this.updateRemoveInstallmentButtons();
  }

  afterAdd(item) {
    item.slideDown(100);
    initializeSemanticFields();
    this.resize();
  }

  afterRemoveLineItem() {
    this.updateRemoveLineItemButtons();
    this.afterRemove();
  }

  afterRemoveInstallment() {
    this.updateRemoveInstallmentButtons();
    this.afterRemove();
  }

  afterRemove() {
    this.resize();
  }

  setupChargePresetDropdown(item) {
    $(item).find('.charge-preset-dropdown').dropdown('setting', 'onChange', (id, text) => {
      if (id === '0') {
        // Custom
        this.showAccountField(item);
        this.setDefaults(item, {
          description: null,
          amount: null,
        });
      } else {
        // Preset
        this.hideAccountField(item);

        const presetData = JSON.parse(this.element.dataset.chargePresets);

        const preset = presetData[id];

        this.setDefaults(item, {
          description: preset.name,
          amount: preset.amount,
        });

        this.chargePresetSelected(preset);
      }
    });
  }

  showAccountField(item) {
    $(item).find('#account-field').show();
  }

  hideAccountField(item) {
    $(item).find('#account-field').hide();
  }

  setDefaults(item, { description, amount }) {
    $(item).find('#description-field input').val(description);
    $(item).find('#amount-field input').val(amount).data('user-input', amount);
  }

  chargePresetSelected(preset) {
    const oneLineItem = $(this.lineItemsTarget).children().length === 1;

    if (!oneLineItem) {
      return;
    }

    const currentInvoiceDescription = this.invoiceDescriptionTarget.value;
    if (!this.userSetInvoiceDescription || !currentInvoiceDescription) {
      this.invoiceDescriptionTarget.value = preset.name;
    }

    if (this.hasDueDateTarget) {
      const currentDueDate = $(this.dueDateTarget).calendar('get date');
      if (!this.userSetDueDate || !currentDueDate) {
        const postDate = $(this.postDateTarget).calendar('get date') || new Date();

        const dueDate = new Date(postDate);
        dueDate.setDate(dueDate.getDate() + preset.net_d);

        // No update to differentiate between user set and preset set
        $(this.dueDateTarget).calendar('set date', dueDate, true, false);
      }
    }
  }

  updateRemoveLineItemButtons() {
    const buttons = $(this.element).find('#line-items .icon.button');

    if (buttons.length > 1) {
      buttons.removeClass('disabled');
    } else {
      buttons.addClass('disabled');
    }
  }

  updateRemoveInstallmentButtons() {
    const buttons = $(this.element).find('#installments .icon.button');

    if (buttons.length > 1) {
      buttons.removeClass('disabled');
    } else {
      buttons.addClass('disabled');
    }
  }
}
