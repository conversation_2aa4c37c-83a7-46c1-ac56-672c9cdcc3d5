import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'breed',
    'color',
    'kind',
    'kindDetail'
  ]

  connect() {
    const self = this

    $(this.kindTarget).on('change', function() {
      self.refresh()
    })

    this.refresh()
  }

  refresh() {
    const newVal = $(this.kindTarget).find('.ui.dropdown').dropdown('get value')

    if (newVal === 'other') {
      $(this.breedTarget).hide()
      $(this.breedTarget).find('input').val('')
      $(this.colorTarget).hide()
      $(this.colorTarget).find('input').val('')
      $(this.kindDetailTarget).show()
    } else {
      $(this.kindDetailTarget).hide()
      $(this.kindDetailTarget).find('input').val('')
      $(this.breedTarget).show()
      $(this.colorTarget).show()
    }
  }
}
