import { Controller } from 'stimulus';

import { DirectUpload } from '@rails/activestorage';

import Dropzone from 'dropzone';
import 'dropzone/dist/dropzone.css';
import 'dropzone/dist/basic.css';

Dropzone.autoDiscover = false;

class DirectUploadController {
  constructor(source, file, url) {
    this.directUpload = new DirectUpload(file, url, this);
    this.file = file;
    this.source = source;
  }

  start() {
    this.directUpload.create((error, blob) => {
      this.hiddenInput = this.createHiddenInput();

      if (error) {
        this.removeHiddenInput();
        this.emitDropzoneError(error);
      } else {
        this.hiddenInput.value = blob.signed_id;
        this.file.signed_blob_id = blob.signed_id;
        this.emitDropzoneSuccess();
      }
    });
  }

  createHiddenInput() {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = this.source.inputTarget.name;
    this.source.inputTarget.parentNode.insertBefore(
      input, this.source.inputTarget.nextSibling,
    );
    return input;
  }

  removeHiddenInput() {
    this.hiddenInput.parentNode.removeChild(this.hiddenInput);
  }

  directUploadWillStoreFileWithXHR(xhr) {
    this.bindProgressEvent(xhr);
    this.emitDropzoneUploading();
  }

  bindProgressEvent(xhr) {
    this.xhr = xhr;
    this.xhr.upload.addEventListener('progress', event => {
      this.uploadRequestDidProgress(event);
    });
  }

  uploadRequestDidProgress(event) {
    const progress = event.loaded / event.total * 100;
    const element = this.file.previewTemplate.querySelector('.dz-upload');
    element.style.width = `${progress}%`;
  }

  emitDropzoneUploading() {
    this.file.status = Dropzone.UPLOADING;
    this.source.dropzone.emit('processing', this.file);
  }

  emitDropzoneError(error) {
    this.file.status = Dropzone.ERROR;
    this.source.dropzone.emit('error', this.file, error);
    this.source.dropzone.emit('complete', this.file);
  }

  emitDropzoneSuccess() {
    this.file.status = Dropzone.SUCCESS;
    this.source.dropzone.emit('success', this.file);
    this.source.dropzone.emit('complete', this.file);
  }
}

export default class extends Controller {
  static targets = ['input'];

  connect() {
    this.initializeDropzone();
    this.hideFileInput();
    this.bindEvents();
  }

  hideFileInput() {
    // Hide activestorage input
    this.inputTarget.disabled = true;
    this.inputTarget.style.display = 'none';
  }

  initializeDropzone() {
    this.dropzone = new Dropzone(this.element, this.dropzoneOptions);

    $(this.element)
      .find('.dz-button')
      .text('Drop Files Here or Click to Upload');
  }

  bindEvents() {
    this.dropzone.on('addedfile', file => {
      setTimeout(() => {
        if (file.accepted) {
          const upload = new DirectUploadController(this, file, this.url);
          upload.start();
        } else {
          console.log('not accepted');
        }
      }, 20);
    });

    this.dropzone.on('removedfile', file => {
      file.directUploadController.removeHiddenInput();
    });

    this.dropzone.on('processing', () => {
      if (this.submitButton) {
        this.submitButton.disabled = true;
      }
    });

    this.dropzone.on('queuecomplete', () => {
      if (this.submitButton) {
        this.submitButton.disabled = false;
      }
    });
  }

  get url() {
    return this.inputTarget.getAttribute('data-direct-upload-url');
  }

  get headers() {
    const meta = document.head.querySelector('meta[name="csrf-token"]');

    const token = meta.getAttribute('content');

    return { 'X-CSRF-Token': token };
  }

  get dropzoneOptions() {
    return {
      url: this.url,
      headers: this.headers,
      autoQueue: false,
    };
  }

  get form() {
    return this.element.closest('form');
  }

  get submitButton() {
    return this.form.querySelector('input[type=submit], button[type=submit]');
  }
}
