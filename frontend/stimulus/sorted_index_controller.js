import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'column',
  ];

  connect() {
    $(this.columnTargets).click(function (event) {
      event.preventDefault();

      const column = $(this).data('sort-column');
      const direction = $(this).hasClass('ascending') ? 'descending' : 'ascending';

      const params = new URLSearchParams(document.location.search);
      params.set('sort[column]', column);
      params.set('sort[direction]', direction);
      params.delete('page');
      const url = `${document.location.pathname}?${params.toString()}`;

      Turbolinks.visit(url, { action: 'replace' });
    });
  }
}
