import { Controller } from 'stimulus';

/*
 * This controller is used to reload dynamic metadata fields based on changes
 * to selected document templates.
 */
export default class extends Controller {
  static targets = ['submit', 'dynamicMetadata'];

  connect() {
    if (this.hasDynamicMetadataTarget) {
      this.metadataXhr = null;
      this.setupCheckboxes();
      this.reloadDynamicMetadata();
    }
  }

  setupCheckboxes() {
    $(this.element).find('.checkbox').checkbox({
      onChange: () => {
        this.reloadDynamicMetadata();
      },
    });
  }

  reloadDynamicMetadata() {
    if (this.metadataXhr) {
      this.metadataXhr.abort();
    }

    this.submitTarget.classList.add('disabled');

    const formData = $(this.element).serialize();

    const url = window.location.toString().replace('/new', '/metadata_fields') + '?' + formData;

    $.get(url, html => {
      $(this.dynamicMetadataTarget).replaceWith(html);
      this.submitTarget.classList.remove('disabled');
    });
  }
}
