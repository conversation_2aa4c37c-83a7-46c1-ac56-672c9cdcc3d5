import { Controller } from 'stimulus';

/*
 * This controller is used to toggle the visibility of the explicit date input
 * based on the accounting date setting on the dates page of the move out
 * proccess.
 */
export default class extends Controller {
  static targets = ['input', 'settingDropdown', 'explicitDate'];

  connect() {
    // TODO: Fluid dropdown should have 100% width search input
    setTimeout(() => {
      $(this.settingDropdownTarget).parent().find('input').attr('style', 'width: 100%;');
    }, 0);

    const current = $(this.settingDropdownTarget).dropdown('get value');

    this.toggleVisibility(current);

    $(this.settingDropdownTarget).dropdown('setting', 'onChange', (value) => {
      this.toggleVisibility(value);
    });
  }

  toggleVisibility(value) {
    console.log({ value });

    if (value === 'explicit_date') {
      $(this.inputTarget).addClass('left action')
      $(this.explicitDateTarget).show();
    } else {
      $(this.inputTarget).removeClass('left action')
      $(this.explicitDateTarget).hide();
    }
  }
}
