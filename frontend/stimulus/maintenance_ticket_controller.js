import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'description', 'editDescription', 'serviceArea', 'urgency', 'tags',
    'employee', 'addEstimate', 'attachReceipt', 'attachInvoice',
    'billingRate', 'materialsMarkup',
  ];

  connect() {
    this.setupServiceAreaDropdown();
    this.setupTagsDropdown();
    this.setupUrgencyDropdown();
    this.setupEmployeeDropdown();
    this.setupEstimateModal();
    this.setupInvoiceModal();
    this.setupBillingRate();
    this.setupMaterialsMarkup();
  }

  // User clicked edit button
  edit(event) {
    event.preventDefault();

    $(this.editDescriptionTarget).show();
    $(this.descriptionTarget).hide();

    // autofocus on end of subject input
    const subjectBox = $('input#maintenance_ticket_subject');
    const subject = subjectBox.val();
    subjectBox.val('').val(subject).focus();
  }

  // User clicked cancel edit button
  cancelEdit() {
    $(this.descriptionTarget).show();
    $(this.editDescriptionTarget).hide();
  }

  setupServiceAreaDropdown() {
    $(this.serviceAreaTarget).dropdown({
      action: 'select',
      fullTextSearch: true,
      match: 'text',
      onShow() {
        $.get('/maintenance/tickets/service_areas.json', values => {
          const html = $.fn.dropdown.settings.templates.menu(
            { values },
            { ...$.fn.dropdown.settings.fields, value: 'id' },
            $.fn.dropdown.settings.preserveHTML,
            $.fn.dropdown.settings.className,
          );

          $(this).find('.menu .menu').html(html);
        });
      },
      onChange(regarding) {
        $(this).find('.cog.icon').addClass('loading');

        const data = {
          maintenance_ticket: { regarding },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }

  setupUrgencyDropdown() {
    $(this.urgencyTarget).dropdown({
      action: 'select',
      onChange(urgency) {
        $(this).find('.cog.icon').addClass('loading');

        const data = {
          maintenance_ticket: { urgency },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }

  setupTagsDropdown() {
    $(this.tagsTarget).dropdown({
      action(text, id, element) {
        $(element).find('.icon').toggleClass('check');
      },
      onHide() {
        $(this).find('.cog.icon').addClass('loading');

        const tags = [''];

        $(this).find('.check.icon').parent('.item').each(function () {
          tags.push($(this).data('value'));
        });

        const data = {
          maintenance_ticket: { tags },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }

  setupEmployeeDropdown() {
    $(this.employeeTarget).dropdown({
      action(text, id, element) {
        if ($(element).find('.icon').hasClass('check')) {
          $(this).find('.icon').removeClass('check');
        } else {
          $(this).find('.icon').removeClass('check');
          $(element).find('.icon').addClass('check');
        }
      },
      onHide() {
        $(this).find('.cog.icon').addClass('loading');

        const assignmentId = $(this).data('assignmentId');

        let userId = null;

        $(this).find('.check.icon').parent('.item').each(function () {
          userId = $(this).data('value');
        });

        const attributes = userId ? {
          // Assign employee
          user_id: userId,
        } : {
          // Remove assignment
          id: assignmentId, _destroy: true,
        };

        const data = {
          maintenance_ticket: { assignment_attributes: attributes },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }

  setupEstimateModal() {
    $(this.addEstimateTarget).click(event => {
      event.preventDefault();

      $('.ui.modal#add-estimate')
        .modal(window.modalDefaults)
        .modal('setting', 'autofocus', false)
        .modal('show');
    });
  }

  setupInvoiceModal() {
    $(this.attachInvoiceTarget).click(event => {
      event.preventDefault();

      $('.ui.modal#attach-invoice')
        .modal(window.modalDefaults)
        .modal('setting', 'autofocus', false)
        .modal('show');
    });
  }

  setupBillingRate() {
    if (!this.hasBillingRateTarget) {
      return;
    }

    $(this.billingRateTarget).dropdown({
      action: 'select',
      allowAdditions: true,
      hideAdditions: false,
      onChange(billing_rate) {
        $(this).find('.cog.icon').addClass('loading');

        const data = {
          maintenance_ticket: { billing_rate },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }

  setupMaterialsMarkup() {
    if (!this.hasMaterialsMarkupTarget) {
      return;
    }

    $(this.materialsMarkupTarget).dropdown({
      action: 'select',
      allowAdditions: true,
      hideAdditions: false,
      onChange(materials_markup_percentage) {
        $(this).find('.cog.icon').addClass('loading');

        const data = {
          maintenance_ticket: { materials_markup_percentage },
        };

        $.ajax({
          url: window.location,
          method: 'PATCH',
          data,
        });
      },
    });
  }
}
