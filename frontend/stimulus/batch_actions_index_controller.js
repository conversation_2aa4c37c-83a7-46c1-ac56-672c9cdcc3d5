import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'button',
    'dropdown'
  ];

  connect() {
    const performBatchAction = this.performBatchAction;

    $(this.buttonTargets).click(function (event) {
      const element = this;
      const batchConfirm = this.dataset.batchConfirm;

      if (batchConfirm && !confirm(batchConfirm)) {
        return;
      }

      const action = this.dataset.batchAction;
      performBatchAction(action, event);
    });

    $(this.dropdownTargets).dropdown({
      fullTextSearch: true,
      onChange(value) {
        const dropdown = this;

        const specifiedAction = dropdown.dataset.batchAction;

        let action = undefined;
        let params = {};

        if (specifiedAction) {
          action = specifiedAction;
          const parameterName = dropdown.dataset.batchActionParam;
          params[parameterName] = value;
        } else {
          action = value;
        }

        performBatchAction(action, null, params);
      },
    });
  }

  performBatchAction = (action, event, extraParams = {}) => {
    if (!action) {
      return;
    }

    const params = new URLSearchParams();

    const multiSelection = this.element['multi-selection-index'];

    if (multiSelection.isOverselection()) {
      params.append('overselection', true);
      const search = window.location.search;
      const filters = qs.parse(search, { ignoreQueryPrefix: true }).filters || {};
      for (const key in filters) {
        params.append(`filters[${key}]`, filters[key]);
      }
    } else {
      const ids = this.element['multi-selection-index'].selectedRowIds();

      for (const id of ids) {
        params.append('selected_ids[]', id);
      }
    }

    for (const key in extraParams) {
      params.set(key, extraParams[key]);
    }

    const basePath = this.element.dataset.basePath;
    const url = `${basePath}/${action}?${params.toString()}`;


    if (event && event.target.dataset.actionMethod === 'post') {
      $.post(url);
    } else if (action.endsWith('js')) {
      $.get(url);
    } else {
      Turbolinks.visit(url, { action: 'replace' });
    }
  }
}
