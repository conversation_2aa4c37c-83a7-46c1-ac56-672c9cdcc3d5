import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['quoteHandle'];

  connect() {
    this.prepareQuoteExpanding();
  }

  prepareQuoteExpanding() {
    const quotes = this.element.querySelectorAll('blockquote, .gmail_quote, .yahoo_quoted');

    for (let i = 0; i < quotes.length; i += 1) {
      const quote = quotes[i];

      // Insert a handle before each quote
      const handle = document.createElement('a');
      handle.classList.add('quote-handle');
      handle.innerHTML = '(Show Quoted Text)';
      handle.href = '#';
      handle.dataset['messaging--email-target'] = 'quoteHandle';

      quote.parentNode.insertBefore(handle, quote);
    }

    this.prepareQuoteHandles();
  }

  prepareQuoteHandles() {
    this.quoteHandleTargets.forEach(handle => {
      handle.addEventListener('click', function (event) {
        event.preventDefault();
        const quote = this.nextSibling;

        if (quote.classList.contains('expanded')) {
          handle.innerHTML = '(Show Quoted Text)';
        } else {
          handle.innerHTML = '(Hide Quoted Text)';
        }

        this.nextSibling.classList.toggle('expanded');
      });
    });
  }
}
