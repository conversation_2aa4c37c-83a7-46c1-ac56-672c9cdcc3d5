import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['email', 'showPrevious', 'replyButton'];

  connect() {
    this.prepareShowPrevious();
    this.prepareReplyButton();
  }

  prepareShowPrevious() {
    if (this.hasShowPreviousTarget) {
      this.showPreviousTarget.addEventListener('click', event => {
        event.preventDefault();

        const url = `${window.location.pathname}/thread_previous`;

        $.get(url, html => {
          $(this.showPreviousTarget).replaceWith(html);
        });
      });
    }
  }

  prepareReplyButton() {
    this.replyButtonTarget.addEventListener('click', event => {
      event.preventDefault();

      const emails = this.emailTargets;
      const email = emails[emails.length - 1];

      const emailData = {
        subject: email.dataset.emailSubject,
        replyingToId: email.dataset.emailReplyingToId,
      };

      $('.tray-window.compose-email')[0]['tray-window'].prepareReply(emailData);
      $('.tray-window.compose-email')[0]['tray-window'].open();
    });
  }
}
