import { Controller } from 'stimulus';

export default class extends Controller {
  connect() {
    this.element.documentHierarchyController = this;

    const unloaded = this.element.dataset.unloaded === 'true';

    if (unloaded) { // Initial load
      this.fetchRootFolder();
    }

    this.setup();
  }

  fetchRootFolder() {
    this.fetchFolder('/');
  }

  fetchFolder(path) {
    const url = `${this.documentsHierarchyBasePath}?path=${path}`;

    if (this.xhr) {
      this.xhr.abort();
    }

    this.xhr = $.get(url, html => {
      $(this.element).replaceWith(html);

      this.setup();
    });
  }

  setup() {
    this.setupLinks();
    this.setupTablesort();
  }

  setupLinks() {
    const links = $(this.element).find('a[data-path]');

    links.click(event => {
      event.preventDefault();

      // Display loading spinner
      $(event.currentTarget)
        .find('.ui.icon')
        .removeClass('folder')
        .addClass('loading spinner');

      const path = event.currentTarget.dataset.path;

      this.fetchFolder(path);
    });
  }

  setupTablesort() {
    $(this.element).find('.sortable.table').tablesort();
  }

  get documentsHierarchyBasePath() {
    return this.element.dataset.documentsHierarchyPath;
  }
}
