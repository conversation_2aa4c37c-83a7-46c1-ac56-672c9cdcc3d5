import { Controller } from 'stimulus';

export default class extends Controller {
  fields = {};
  static targets = [
    'field'
  ];

  connect() {
    this.fieldTargets.forEach(fieldElement => {
      this.fields[fieldElement.dataset.field] = fieldElement;
    });
  }

  selectTemplate = ({ target: { dataset: { prefills } } }) => {
    if (prefills == undefined) {
      console.error('Clicked element does not have a prefills data attribute.');
      return;
    }

    for (let [field, prefill] of Object.entries(JSON.parse(prefills))) {
      $(this.fields[field]).find('trix-editor,input,textarea').val(prefill).trigger('input');
    }
  };
}
