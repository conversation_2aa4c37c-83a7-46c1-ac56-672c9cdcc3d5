import { Controller } from 'stimulus';
import { every, some } from 'lodash';

export default class extends Controller {
  static targets = [
    'allReportsCheckbox',
    'reportCategoryCheckbox',
    'reportCheckbox',
    'permissionNamespace',
    'permissionComponent',
    'permissionAction'
  ];

  connect() {
    this.prepareReportCheckboxes();
  }

  prepareReportCheckboxes() {
    const categoryCheckboxes = this.reportCategoryCheckboxTargets;
    const reportCheckboxes = this.reportCheckboxTargets;

    $(this.allReportsCheckboxTarget).checkbox({
      onChange: () => {
        this.updateAllReportsState();
      },
    });
    this.updateAllReportsState();

    // Change report checkboxes from category changes
    $(categoryCheckboxes).checkbox({
      onChange() {
        const checked = $(this.parentElement).checkbox('is checked');
        const category = this.parentElement.dataset.category;

        const action = checked ? 'set checked' : 'set unchecked';

        $(reportCheckboxes).filter(`[data-category="${category}"]`).checkbox(action);
      },
    });

    const updateCategoryCheckboxStates = this.updateCategoryCheckboxStates.bind(this);

    // Change category checkboxes from report changes
    $(reportCheckboxes).checkbox({
      onChange() {
        const category = this.parentElement.dataset.category;

        updateCategoryCheckboxStates(category);
      },
    });

    // Initially set category checkboxes
    $(categoryCheckboxes).each(function () {
      const category = this.dataset.category;
      updateCategoryCheckboxStates(category);
    });
  }

  togglePermissionNamespace(event) {
    const namespace = event.target.dataset.namespace;
    const checked = event.target.checked;

    this.permissionComponentTargets.forEach((element) => {
      if (element.dataset.namespace === namespace) {
        element.checked = checked;
      }
    });

    this.permissionActionTargets.forEach((element) => {
      if (element.dataset.namespace === namespace) {
        element.checked = checked;
      }
    });
  }

  togglePermissionComponent(event) {
    const namespace = event.target.dataset.namespace;
    const component = event.target.dataset.component;
    const checked = event.target.checked;

    this.permissionActionTargets.forEach((element) => {
      if (element.dataset.namespace === namespace && element.dataset.component === component) {
        element.checked = checked;
      }
    });
  }

  // Disable or enable reports checkbox area based on all access checkbox
  updateAllReportsState() {
    const checked = $(this.allReportsCheckboxTarget).checkbox('is checked');

    const targets = $('#reports-checkboxes .field');

    if (checked) {
      targets.addClass('disabled');
    } else {
      targets.removeClass('disabled');
    }
  }

  updateCategoryCheckboxStates(category) {
    const filter = `[data-category="${category}"]`;
    const categoryCheckbox = $(this.reportCategoryCheckboxTargets).filter(filter);
    const categoryReportCheckboxes = $(this.reportCheckboxTargets).filter(filter);

    if (every(categoryReportCheckboxes, checkbox => $(checkbox).checkbox('is unchecked'))) {
      $(categoryCheckbox).checkbox('set unchecked');
    } else if (some(categoryReportCheckboxes, checkbox => $(checkbox).checkbox('is unchecked'))) {
      $(categoryCheckbox).checkbox('set indeterminate');
    } else {
      $(categoryCheckbox).checkbox('set checked');
    }
  }
}
