import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'search',
    'item',
  ];

  connect() {
    if (this.hasItemTarget) {
      this.prepareSearch(this.itemTarget.dataset.url);
    }

    $(this.element).find('.ui.dropdown').dropdown({
      selectOnKeydown: false,
      onChange: (_value, _text, $element) => {
        this.updateSearchUrl($element.data('url'));
      },
    });
  }

  prepareSearch(url) {
    $(this.searchTarget)
      .search({
        type: 'category',
        selectFirstResult: true,
        minCharacters: 0,
        searchOnFocus: true,
        showNoResults: true,
        cache: false,
        apiSettings: {
          url,
        },
        onSelect: item => {
          $(this.searchTarget).find('input[type=hidden]').val(item.sgid || item.id).trigger('change');
        },
      });

    $(this.searchTarget).find('input.prompt').on('input', () => {
      if ($(this.searchTarget).find('input.prompt').val().trim() === '') {
        $(this.searchTarget).find('input[type=hidden]').val('').trigger('change');
      }
    });
  }

  updateSearchUrl(url) {
    $(this.searchTarget).find('input.prompt').val('');
    $(this.searchTarget).search('destroy');
    this.prepareSearch(url);
  }
}
