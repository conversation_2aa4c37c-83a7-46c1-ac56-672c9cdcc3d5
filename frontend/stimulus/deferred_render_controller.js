import { Controller } from 'stimulus';

export default class extends Controller {
  static values = {
    on: String,   // Either 'load' or 'show'
    path: String, // A path or URL to fetch
  }

  connect() {
    if (this.onValue === 'show') {
      this.setupReplaceOnShow();
    } else {
      this.setupReplaceOnLoad();
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  setupReplaceOnLoad() {
    // Immediately load
    this.loadAndReplace();
  }

  setupReplaceOnShow() {
    const options = {
      root: this.element.parentElement,
    };

    const handler = (entries, observer) => {
      entries.forEach(entry => {
        if (entry.intersectionRatio > 0) {
          this.loadAndReplace();
          observer.disconnect();
        }
      });
    };

    this.observer = new IntersectionObserver(handler, options);

    this.observer.observe(this.element);
  }

  loadAndReplace() {
    $.get(this.pathValue, html => {
      $(this.element).replaceWith(html);
      ReactRailsUJS.mountComponents();
    });
  }
}
