import { Controller } from 'stimulus';
import { throttle } from 'lodash';
import 'pdfjs-dist/web/pdf_viewer.css';

export default class extends Controller {
  connect() {
    const url = this.element.dataset.url;

    this.pdf = null;

    this.previousWidth = this.element.clientWidth;

    pdfjsLib.getDocument(url).promise.then(pdf => {
      this.pdf = pdf;
      this.displayPdf();
    });

    window.addEventListener('resize', () => this.windowResized());
  }

  clearContents() {
    while (this.element.firstChild) {
      this.element.firstChild.remove();
    }
  }

  displayPdf() {
    this.loadPage(this.pdf, 1);
  }

  loadPage(pdf, number) {
    if (number <= pdf.numPages) {
      pdf.getPage(number).then(page => {
        if (number === 1) {
          // Clear Loading Page
          this.clearContents();
        }

        // Add paper element
        const paper = document.createElement('paper');
        paper.className = 'pdf paper';
        this.element.appendChild(paper);

        // Create canvas element
        const canvas = document.createElement('canvas');

        // Determine render scale
        const devicePixelRatio = window.devicePixelRatio || 1;
        const paperWidth = paper.clientWidth;
        const pdfWidth = page.getViewport({ scale: 1 }).width;
        const scale = (paperWidth / pdfWidth);
        const viewport = page.getViewport({ scale });
        const canvasContext = canvas.getContext('2d');

        // Render to canvas
        canvas.height = viewport.height * devicePixelRatio;
        canvas.width = viewport.width * devicePixelRatio;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.width = `${viewport.width}px`;
        canvasContext.scale(devicePixelRatio, devicePixelRatio);
        const renderContext = { canvasContext, viewport };
        page.render(renderContext);

        // Add canvas to paper
        paper.appendChild(canvas);

        // Load next page
        this.loadPage(pdf, number + 1);

        const textLayer = document.createElement('div');
        textLayer.className = 'textLayer';
        textLayer.style.left = `${canvas.offsetLeft}px`;
        textLayer.style.top = `${canvas.offsetTop}px`;
        textLayer.style.height = `${canvas.offsetHeight}px`;
        textLayer.style.width = `${canvas.offsetWidth}px`;
        paper.appendChild(textLayer);

        // Render text content
        page.getTextContent().then(textContent => {
          pdfjsLib.renderTextLayer({
            textContent,
            container: textLayer,
            viewport,
            textDivs: [],
          });
        });
      });
    }
  }

  redraw = throttle(() => {
    this.previousWidth = this.element.clientWidth;
    this.clearContents();
    this.displayPdf(this.pdf);
  }, 100);

  windowResized() {
    if (this.pdf && this.element.clientWidth !== this.previousWidth) {
      this.redraw();
    }
  }
}
