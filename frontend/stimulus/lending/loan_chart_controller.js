import { Controller } from 'stimulus';
import { each, map } from 'lodash';

export default class extends Controller {
  connect() {
    const loanId = this.element.dataset.loanId;

    const url = `/reports/amortization-schedule.json?filters[loan_id]=${loanId}`;

    $.get(url, this.parseReport.bind(this));
  }

  parseReport(report) {
    const data = map(report.rows, row => {
      return {
        date: Date.parse(row.cells[0].value),
        principal_balance: row.cells[5].value.cents / 100.0,
        paid_principal: row.cells[6].value.cents / 100.0,
        paid_interest: row.cells[7].value.cents / 100.0,
      };
    });

    this.loadChart(data);
  }

  loadChart(data) {
    const margin = {
      top: 16,
      bottom: 48,
      left: 72,
      right: 4,
    };

    const height = 250 - margin.top - margin.bottom;
    const width = 800 - margin.left - margin.right;

    const w = width + margin.left + margin.right;
    const h = height + margin.top + margin.bottom;

    d3.select(this.element).selectAll('svg').remove();

    const svg = d3.select(this.element)
      .append('svg')
      .attr('viewBox', `0 0 ${w} ${h}`)
      .append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    const x = d3.time.scale()
      .domain(d3.extent(data, function (d) { return d.date }))
      .range([0, width]);

    svg
      .append('g')
      .attr('class', 'axis')
      .attr('transform', `translate(0, ${height})`)
      .call(d3.svg.axis().orient('bottom').scale(x).ticks(5));

    const y = d3.scale.linear()
      .domain([0, d3.max(data, function(d) { return d.principal_balance; })])
      .range([height, 0]);

    svg
      .append('g')
      .attr('class', 'axis')
      .call(
        d3.svg.axis().orient('left').scale(y).ticks(5)
      );

    const principalBalanceLine = d3.svg.line()
      .x(function(d) { return x(d.date); })
      .y(function(d) { return y(d.principal_balance); })
      .interpolate('cardinal');

    const paidPrincipalLine = d3.svg.line()
      .x(function(d) { return x(d.date); })
      .y(function(d) { return y(d.paid_principal); })
      .interpolate('cardinal');

    const paidInterestLine = d3.svg.line()
      .x(function(d) { return x(d.date); })
      .y(function(d) { return y(d.paid_interest); })
      .interpolate('cardinal');

    const colors = [
      '#9fd5b3',
      '#23a696',
      '#004445',
    ];

    const lines = [
      {
        name: 'Balance',
        color: colors[0],
        func: principalBalanceLine,
      },
      {
        name: 'Paid Principal',
        color: colors[1],
        func: paidPrincipalLine,
      },
      {
        name: 'Paid Interest',
        color: colors[2],
        func: paidInterestLine,
      },
    ];

    lines.forEach((line, i) => {
      svg
        .append('path')
        .datum(data)
        .attr('fill', 'none')
        .attr('stroke', line.color)
        .attr('stroke-width', 2)
        .attr('d', function(d) { return line.func(data); });

      svg
        .append('text')
        .attr('x', [4, 64, 158][i])
        .attr('y', 230)
        .style('fill', line.color)
        .text(line.name);
    });

    svg.append('line')
      .attr('x1', x(new Date()))
      .attr('y1', 0)
      .attr('x2', x(new Date()))
      .attr('y2', height)
      .style('stroke-width', 1)
      .style('stroke', 'gray')
      .style('stroke-dasharray', '5, 5')
      .style('fill', 'none');
  }
}
