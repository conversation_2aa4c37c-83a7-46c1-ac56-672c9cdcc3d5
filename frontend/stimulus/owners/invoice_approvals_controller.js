import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['table', 'button'];

  connect() {
    this.headCheckbox.checkbox({
      onChange: () => {
        const checked = this.headCheckbox.checkbox('is checked');

        if (checked) {
          this.rowCheckboxes.checkbox('set checked');
        } else {
          this.rowCheckboxes.checkbox('set unchecked');
        }

        this.updateCounts();
      },
    });

    this.rowCheckboxes.checkbox({
      onChange: () => {
        this.updateCounts();
      },
    });
  }

  updateCounts() {
    let total = 0;
    let checked = 0;

    this.rowCheckboxes.each(function () {
      total += 1;

      if ($(this).checkbox('is checked')) {
        checked += 1;
      }
    });

    if (checked === 0) {
      this.headCheckbox.checkbox('set unchecked');
    } else if (checked === total) {
      this.headCheckbox.checkbox('set checked');
    } else {
      this.headCheckbox.checkbox('set indeterminate');
    }

    $(this.buttonTarget).html(this.buttonText(checked));

    if (checked > 0) {
      $(this.buttonTarget).removeClass('disabled');
    } else {
      $(this.buttonTarget).addClass('disabled');
    }
  }

  buttonText(checkedCount) {
    return `Approve ${checkedCount} Invoice${checkedCount === 1 ? '' : 's'}`;
  }

  get headCheckbox() {
    return $(this.tableTarget).find('thead .ui.checkbox');
  }

  get rowCheckboxes() {
    return $(this.tableTarget).find('tbody .ui.checkbox');
  }
}
