import DocumentsDropzoneController from '../documents_dropzone_controller';

export default class extends DocumentsDropzoneController {
  associateUpload(file) {
    const signedBlobId = file.signed_blob_id;

    const propertyId = this.element.dataset.propertyId;

    const url = `/manage/properties/${propertyId}/documents?signed_blob_id=${signedBlobId}`;

    $.post(url);
  }

  bindEvents() {
    super.bindEvents();

    this.dropzone.on('queuecomplete', () => {
      const hierarchy = $(this.element)
        .closest('[data-controller="document-hierarchy"]')[0]
        .documentHierarchyController;

      hierarchy.fetchFolder('/uploads');
    });
  }
}
