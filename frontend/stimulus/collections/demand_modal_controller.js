import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'demandModal',
    'demandModalHeader',
    'demandModalSubheader',
    'demandModalContent',
    'batchDemandLetterButton',
  ];

  connect() {
    this.batchDemandLetterButtonTarget.onclick = () => {
      const index = this.element['multi-selection-index'];

      const ids = index.selectedRowIds();

      const overselection = index.isOverselection();

      const count = overselection ? this.overselectionFilteredCount(index) : ids.length;

      this.showDemandModal(ids, overselection, count);

      return false;
    };
  }

  clickDemandLetterLink(event) {
    event.preventDefault();

    const id = event.target.dataset.id;

    this.showDemandModal([id], false, 1);
  }

  showDemandModal(ids, overselection, count) {
    const plural = count !== 1;

    let title = 'Send or Print Demand Letter';
    if (plural) {
      title += 's';
    }
    this.demandModalHeaderTarget.innerHTML = title;

    let subheader = `${count} Lease`;
    if (plural) {
      subheader += 's';
    }
    this.demandModalSubheaderTarget.innerHTML = subheader;

    this.demandModalContentTarget.innerHTML = '<div class="ui centered active inline loader" />';

    const filters = qs.parse(
      window.location.search, { ignoreQueryPrefix: true },
    ).filters || {};

    $.get(
      '/operations/collections/demand_letter/new',
      { ids, overselection, filters },
      html => { this.demandModalContentTarget.innerHTML = html; },
    );

    $(this.demandModalTarget)
      .modal({ ...modalDefaults, detachable: false })
      .modal('show');
  }

  overselectionFilteredCount(index) {
    return parseInt(index.filteredCount, 10);
  }
}
