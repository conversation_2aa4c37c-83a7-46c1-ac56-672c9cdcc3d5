import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'searchInput',
  ];

  connect() {
    let showingSearch = false;

    let xhr = null;

    const uiSearchInput = this.searchInputTarget.parentNode;

    $(this.searchInputTarget).on('keyup', function () {
      const value = this.value;

      const searchBar = this;

      const modal = $('#search-modal');

      if (value.length) {
        if (!showingSearch) {
          uiSearchInput.style.zIndex = 1001;
          showingSearch = true;

          modal
            .modal(modalDefaults)
            .modal('setting', 'autofocus', false)
            .modal('setting', 'onHidden', function () {
              showingSearch = false;
              uiSearchInput.style.zIndex = null;
            });

          // Prevent blurring search bar
          modal.modal('save').focus = function () {};

          modal.modal('show');
        }
      } else {
        modal.modal('hide');
      }

      $('#search-modal #term-live').html(this.value);

      // Cancel existing requests
      if (xhr) {
        xhr.abort();
      }

      xhr = $.get(`/search.js?search=${this.value}`);
    });
  }
}
