import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['code', 'digit'];

  connect() {
    this.digitTargets[0].focus();

    const digitCount = 6;

    const updateCode = () => {
      this.codeTarget.value = this.digitTargets.map(digit => digit.value).join('');
    }

    this.digitTargets.forEach((digit, index) => {
      digit.addEventListener('keydown', event => {
        const backspace = event.keyCode == 8;
        const number = event.keyCode >= 48 && event.keyCode <= 57;
        const numpad = event.keyCode >= 96 && event.keyCode <= 105;
        const allowed = backspace || number || numpad;

        // Only allow numbers, numpad, backspace
        if (!allowed) {
          event.preventDefault();
        }

        // Prevent more than one digit
        if (digit.value.length == 1 && event.keyCode != 8) {
          event.preventDefault();
        }

        updateCode();

        const enter = event.keyCode == 13;
        const completed = this.digitTargets.every(digit => digit.value.length == 1);
        if (enter && completed) {
          Rails.fire(this.element, 'submit');
        }
      });

      // Move to next or previous input
      digit.addEventListener('keyup', event => {
        if (digit.value.length == 1 && index < digitCount - 1) {
          this.digitTargets[index + 1].focus();
        } else if (digit.value.length == 0 && index > 0) {
          this.digitTargets[index - 1].focus();
        }

        updateCode();
      });
    });
  }
}
