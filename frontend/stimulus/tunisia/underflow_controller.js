import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['unitUiElement'];

  connect() {
    this.unitUiElementTarget.addEventListener('unitOnLoad', e => {
      const shadowTable = this.shadowRoot
        .querySelector('.multiple-cards-table');
      if (shadowTable) {
        shadowTable.style.paddingBottom = '15rem';
      }
      return e;
    });
  }

  get shadowRoot() {
    return this.unitUiElementTarget.
      shadowRoot.children[0];
  }
}
