import { Controller } from 'stimulus';
import { isEmpty, isString } from 'lodash';

export default class extends Controller {
    static targets = ['unitUi', 'placeholder', 'guarded'];

    static values = {
      companyId: String,
      initialToken: String,
    };

    connect() {
      this.expired = !isString(this.initialTokenValue) || isEmpty(this.initialTokenValue);
      if(this.expired) { this.unauthed(); }
      $(this.unitUiTargets).on('unitOnLoad', e => {
        if (e.detail.errors && e.detail.errors[0].status === '401') {
          this.unauthed();
        }
        return e;
      });
      $(window).on('tunisia:updated', this.onUpdatedToken.bind(this));
    }


    unauthed() {
      this.expired = true;
      $(this.guardedTarget).hide();
      $(this.placeholderTarget).show();
    }

    authed() {
      this.expired = false;
      $(this.guardedTarget).show();
      $(this.placeholderTarget).hide();
    }

    onUpdatedToken(event) {
      if (event.detail.company_id.toString() !== this.companyIdValue) {
        return event;
      }

      this.unitUiTargets.forEach( target => {
        target.setAttribute('customer-token', event.detail.token);
        target.dispatchEvent(new CustomEvent('unitRequestRefresh'))
      });
      this.authed();
    }

    validate(_) {
      if (this.expired === true) {
        this.summonModal();
      }
    }

    summonModal(event) {
      if (event) {
        event.preventDefault();
      }

      window.tunisia_customer_tokens.summonModal();
    }
}
