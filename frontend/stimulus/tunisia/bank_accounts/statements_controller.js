import { Controller } from 'stimulus';
import { isEmpty, isString } from 'lodash';

export default class extends Controller {
  static targets = ['form','results','modal'];

  static values = {
    bankAccountId: String,
  };

  connect() {
    this.resetStatementModalListeners();
    $('#statement_modal').modal();
  }

  getStatements(e) {
    e.preventDefault();
    const statements_url = this.formTarget.action;
    const statements_params = $(this.formTarget).serialize()
    $.get(`${statements_url}?${statements_params}`, html => {
      $(this.resultsTarget).html(html);
      this.resetStatementModalListeners();
    });
  }

  resetStatementModalListeners() {
    $(this.resultsTarget).find('a.view').toArray().forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        this.loadsPath(e.target.href);
        $('#statement_modal').modal(modalDefaults).modal('show');
      })
    });
  }

  // mimics loads_path in modal
  loadsPath(path) {
    $.get(path, html => {
      const iframe = $('#statement_modal #statement_iframe')[0];

      iframe.srcdoc = html;

      iframe.onload = () => {
        const contentHeight = iframe.contentWindow.document.body.scrollHeight;

        // 50 px is to handle padding added by Unit
        iframe.style.height = `${contentHeight + 50}px`;
      };
    })
  }
}
