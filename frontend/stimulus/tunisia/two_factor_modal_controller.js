import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['contents'];

  static values = {
    companyId: Number,
    newTokenPath: String,
  };

  reload(event) {
    event.preventDefault();

    $(this.contentsTarget).html(
      '<div class="ui very basic vertical segment"><div class="ui active centered loader"></div></div>'
    );

    // Was this from the 'Resend' link?
    const resend = event.type == 'click' ? 'true' : 'false';

    $.get(`${this.newTokenPathValue}?company_id=${this.companyIdValue}&resend=${resend}`, html => {
      $(this.contentsTarget).html(html);
    });
  }

  success(ajaxSuccess) {
    const tokenResponse = JSON.parse(ajaxSuccess.detail[2].responseText);
    window.tunisia_customer_tokens.setToken(tokenResponse);
  }
}
