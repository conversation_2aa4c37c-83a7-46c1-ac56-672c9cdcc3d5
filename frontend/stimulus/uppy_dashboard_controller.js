import { Controller } from 'stimulus';

import ActiveStorageUpload from '@excid3/uppy-activestorage-upload';
import AwsS3 from '@uppy/aws-s3';
import Core from '@uppy/core';
import Dashboard from '@uppy/dashboard';
import Webcam from '@uppy/webcam';
import XHRUpload from '@uppy/xhr-upload';

import '@uppy/core/dist/style.min.css';
import '@uppy/dashboard/dist/style.min.css';
import '@uppy/webcam/dist/style.min.css';

import DisableSubmitPlugin from '../uppy/disable_submit_plugin';
import FomanticIconPlugin from '../uppy/fomantic_icon_plugin';
import HiddenInputsPlugin from '../uppy/hidden_inputs_plugin';

export default class extends Controller {
  static values = {
    attributeName: String,
    collectionName: String,
    debug: Boolean,
    note: String,
    restrictions: Object,
    useS3: Boolean,
    webcamModes: Array,
    useActiveStorage: Boolean,
    activeStorageAttribute: String,
    height: Number,
  };

  connect() {
    const uppy = new Core({
      id: this.element.id,
      autoProceed: false,
      debug: this.debugValue,
      restrictions: this.restrictionsValue,
    });

    uppy.use(Dashboard, {
      target: this.element,
      inline: true,
      height: this.heightValue,
      proudlyDisplayPoweredByUppy: false,
      doneButtonHandler: null,
      note: this.noteValue,
    });

    uppy.use(Webcam, {
      modes: this.webcamModesValue,
      mirror: false,
      videoConstraints: {
        facingMode: {
          ideal: 'environment',
        },
        width: {
          max: 1920,
        },
        height: {
          max: 1920,
        },
      },
    });

    if (this.useS3Value) {
      uppy.use(AwsS3, {
        endpoint: '/',
      });
    } else if (this.useActiveStorageValue) {
      uppy.use(ActiveStorageUpload, {
        directUploadUrl: document.querySelector("meta[name='direct-upload-url']").getAttribute('content')
      });
    } else {
      uppy.use(XHRUpload, {
        endpoint: '/upload',
      });
    }

    uppy.use(DisableSubmitPlugin, {
      target: this.element,
    });

    uppy.use(FomanticIconPlugin, {
      target: this.element,
      icons: {
        'MyDevice': 'folder open outline',
        'Webcam': 'camera',
      }
    });

    uppy.use(HiddenInputsPlugin, {
      target: this.element,
      useS3: this.useS3Value,
      useActiveStorage: this.useActiveStorageValue,
      collectionName: this.collectionNameValue,
      attributeName: this.attributeNameValue,
      activeStorageAttribute: this.activeStorageAttributeValue,
    });

    this.uppy = uppy;
  }

  disconnect() {
    if (this.uppy) {
      this.uppy.destroy();
    }
  }
}
