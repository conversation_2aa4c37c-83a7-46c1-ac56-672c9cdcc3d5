import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['search', 'item'];

  connect() {
    this.prepareSearch();
  }

  prepareSearch() {
    const items = this.itemTargets;

    $(this.searchTarget).val('').focus();

    $(this.searchTarget).keyup(function () {
      const term = $(this).val().replace(/\s/g, '').toLowerCase();

      items.forEach(item => {
        if (term.length) {
          const name = item.dataset.name.replace(/\s/g, '').toLowerCase();

          if (name.includes(term)) {
            item.style.display = 'list-item';
          } else {
            item.style.display = 'none';
          }
        } else {
          item.style.display = 'list-item';
        }
      });
    });
  }

  toggleFavorite(event) {
    event.preventDefault();

    const target = event.currentTarget;

    const report = $(target).parent();

    if (report.hasClass('favorite')) {
      this.unfavorite(report);
    } else {
      this.favorite(report);
    }
  }

  favorite(report) {
    report.addClass('favorite');
    $.get(`${report.attr('href')}/favorite`);
  }

  unfavorite(report) {
    report.removeClass('favorite');
    $.get(`${report.attr('href')}/unfavorite`);
  }
}
