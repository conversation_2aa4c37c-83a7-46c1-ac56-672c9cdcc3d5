import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'amountField',
    'paymentMethod',
    'referenceLabel',
  ]

  connect() {
    // setTimeout(() => { // Wait for money field initialization before focus
    //   $(this.amountFieldTarget).focus();
    // }, 0);

    let lastAmountValue;

    $(this.amountFieldTarget).on('keyup change', event => {
      const value = event.target.value;

      if (lastAmountValue !== value) {
        lastAmountValue = value;
        $(document).trigger('payment-form.reloadLedger');
      }
    });

    if (this.hasPaymentMethodTarget) {
      // Set initial reference label
      this.updateReferenceLabel(this.paymentMethodTarget.value);

      // Update reference label upon payment method change
      $(this.paymentMethodTarget).dropdown('setting', 'onChange', method => {
        this.updateReferenceLabel(method);
      });
    }
  }

  updateReferenceLabel(method) {
    if (method === 'check') {
      this.referenceLabelTarget.innerHTML = 'Check Number';
    } else if (method === 'credit') {
      this.referenceLabelTarget.innerHTML = 'Last Four';
    } else {
      this.referenceLabelTarget.innerHTML = 'Reference Number';
    }
  }
}
