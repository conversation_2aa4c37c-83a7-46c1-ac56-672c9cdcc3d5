import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'payerSearch',
    'payeeSearch',
    'payerLeaseChain',
    'payeeLeaseChain',
  ];

  connect() {
    // Setup payer / payee change form reload
    const reloadForm = this.reloadForm;

    const searchTargets = [];

    if (this.hasPayerSearchTarget) {
      searchTargets.push(this.payerSearchTarget);
    }

    if (this.hasPayeeSearchTarget) {
      searchTargets.push(this.payeeSearchTarget);
    }

    searchTargets.forEach(target => {
      $(target).find('input[type=hidden]').on('change', function () {
        reloadForm($(this).closest('form'));
      });
    });

    // Setup lease chain reload
    const setupLeaseReload = function (target) {
      setTimeout(() => {
        $(target).dropdown('setting', 'onChange', function () {
          reloadForm($(this).closest('form'));
        });
      }, 0);
    };

    if (this.hasPayerLeaseChainTarget) {
      setupLeaseReload(this.payerLeaseChainTarget);
    }

    if (this.hasPayeeLeaseChainTarget) {
      setupLeaseReload(this.payeeLeaseChainTarget);
    }
  }

  reloadForm = form => {
    const formvalues = form.serialize();
    $.get(`${window.location.pathname}.js?${formvalues}`);
  }
}
