import { Controller } from 'stimulus';
import { filter, map } from 'lodash';

export default class extends Controller {
  static targets = [
    'allCheckBox',
    'invoiceCheckBox',
  ];

  connect() {
    const reload = this.reloadLedger.bind(this);

    $(document).on('payment-form.reloadLedger', reload);

    const $checkboxes = map(
      this.invoiceCheckBoxTargets,
      checkbox => $(checkbox),
    );

    $(this.allCheckBoxTarget).checkbox({
      onChange: () => {
        const checked = $(this.allCheckBoxTarget).checkbox('is checked');

        $checkboxes.forEach($checkbox => {
          if (checked) {
            $checkbox.checkbox('set checked');
          } else {
            $checkbox.checkbox('set unchecked');
          }
        });

        reload();
      },
    });

    $checkboxes.forEach($checkbox => {
      $checkbox.checkbox({
        onChange: () => {
          this.updateAllCheckbox($checkboxes);
          reload();
        },
      });
    });

    this.updateAllCheckbox($checkboxes);
  }

  disconnect() {
    $(document).off('payment-form.reloadLedger');
  }

  reloadLedger = () => {
    if (this.xhr) {
      this.xhr.abort();
    }

    const form = $(this.element).closest('form');

    const formvalues = form.serialize();

    let path = window.location.pathname;

    if (!path.includes('electronic_payments')) {
      path = path.replace('new', 'apply_ledger');
    }

    this.xhr = $.get(`${path}.js?${formvalues}`);
  }

  updateAllCheckbox($checkboxes) {
    const total = $checkboxes.length;

    const checked = filter(
      $checkboxes,
      $checkbox => $checkbox.checkbox('is checked'),
    ).length;

    const $allcheckbox = $(this.allCheckBoxTarget);

    if (checked === 0) {
      $allcheckbox.checkbox('set unchecked');
    } else if (total === checked) {
      $allcheckbox.checkbox('set checked');
    } else {
      $allcheckbox.checkbox('set indeterminate');
    }
  }
}
