import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['preview'];

  connect() {
    setTimeout(() => {
      this.prepareListeners();
    }, 10);
  }

  prepareListeners() {
    $(this.element).find('.ui.calendar').calendar('setting', 'onChange', () => {
      this.reloadPreview();
    });

    $(this.element).find('.ui.dropdown').dropdown('setting', 'onChange', () => {
      this.reloadPreview();
    });

    $(this.element).find('input').on('input', () => {
      this.reloadPreview();
    });
  }

  reloadPreview() {
    if (this.xhr) {
      this.xhr.abort();
    }

    this.displayLoader();

    const previewUrl = this.element.dataset.previewUrl;

    const params = $(this.element).serialize();

    const url = `${previewUrl}?${params}`;

    this.xhr = $.get(url, html => {
      this.replacePreview(html);
    });
  }

  displayLoader() {
    $(this.previewTarget)
      .find('.ui.list')
      .replaceWith('<div class="ui active loader"></div>');
  }

  replacePreview(html) {
    $(this.previewTarget).replaceWith(html);
  }
}
