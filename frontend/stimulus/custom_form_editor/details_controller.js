import { Controller } from 'stimulus';
import { find, map } from 'lodash';

export default class extends Controller {
  static targets = ['detailsPanel', 'canvasPanel', 'detailsColumn'];

  connect() {
    this.refresh();
  }

  refresh(event) {
    this.activeElement = document.querySelector('.cf-element.active');
    if(this.activeElement) {
      this.summon();
    }
  }

  activate(click) {
    this.dismiss(click);
    this.activeElement = click.currentTarget.closest('.cf-element');
    this.summon();
  }

  summon() {
    this.activeElement.classList.add('active');
    this.detailsColumnTarget.classList.add('active')
    this.canvasPanelTarget.classList.replace('twelve', 'eight')
    this.detailsPanelTarget.innerHTML = this.activeElement.querySelector('.details-form').innerHTML
    window.initializeSemanticFields();
  }

  dim(event) {
    this.element.dispatchEvent(new CustomEvent('dim'));
    return event;
  }

  delete() {
    this.activeElement && this.activeElement.querySelector('.trash').dispatchEvent(new CustomEvent('click'));
  }

  dismiss(click) {
    click.preventDefault();
    this.activeElement?.classList.remove('active');
    this.detailsColumnTarget.classList.remove('active')
    this.canvasPanelTarget.classList.replace('eight', 'twelve')
  }

  replaceForm = (ajax_success) => {
    this.activeElement.querySelector('.details-form').innerHTML = ajax_success.body
    return ajax_success
  }

  handleError = (ajax_error) => {
    this.detailsPanelTarget.innerHTML = ajax_error.detail[0].querySelector('.details-form').innerHTML;
    window.initializeSemanticFields();
    return ajax_error;
  }
}
