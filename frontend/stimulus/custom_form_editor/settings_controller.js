import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['availabilityDates', 'betweenDates', 'always', 'endDate', 'tab', 'alwaysAvailable'];

  static values = {
    always: Boolean
  }

  betweenDates = (e) => {
    e.preventDefault();
    this.alwaysTarget.classList.remove('active');
    this.betweenDatesTarget.classList.add('active');
    this.alwaysValue = false;
    this.availabilityDatesTarget.style.display = 'block';
    this.alwaysAvailableTarget.style.display = 'none';
  }

  always = (e) => {
    e.preventDefault();
    this.betweenDatesTarget.classList.remove('active');
    this.alwaysTarget.classList.add('active');
    this.alwaysValue = true;
    this.availabilityDatesTarget.style.display = 'none';
    this.alwaysAvailableTarget.style.display = 'block';
  }

  dim(event) {
    this.element.dispatchEvent(new CustomEvent('dim'));
    return event;
  }

  handleError(ajax_error) {
    this.tabTarget.innerHTML = ajax_error.detail[0].querySelector('body').innerHTML;
    window.initializeSemanticFields();
    return ajax_error;
  }

  beforeSubmit(submit) {
    if(this.alwaysValue === true) {
      this.endDateTarget.querySelector('input').value = null;
    }
    return submit;
  }
}
