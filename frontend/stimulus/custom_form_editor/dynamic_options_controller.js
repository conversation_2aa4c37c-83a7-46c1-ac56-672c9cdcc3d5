import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['optionsForm', 'optionsTemplate', 'option'];

  initialize() {
    if (this.optionsFormTarget.dataset.count === '0' && !this.hasVisibleOption)
      this.addBlankOption();
  }

  addOption({ target }) {
    let content = this.optionsTemplateTarget.innerHTML.replace(/TEMPLATE_RECORD/g, new Date().getTime());
    let insertLocation = target.closest('.optionable-option');
    insertLocation.insertAdjacentHTML('afterend', content);
    this.setIndexes();
  }

  removeOption({ target }) {
    let option = target.closest('.optionable-option');
    option.querySelector("input[name*='_destroy']").value = true;
    option.style.display = 'none';
    if (!this.hasVisibleOption)
      this.addBlankOption();
    this.setIndexes();
  }

  setIndexes() {
    this.optionTargets.forEach((option, index) => {
      option.querySelector("input[name*='index']").value = index;
    });
  }

  addBlankOption() {
    let content = this.optionsTemplateTarget.innerHTML.replace(/TEMPLATE_RECORD/g, new Date().getTime());
    this.optionsFormTarget.insertAdjacentHTML('beforeend', content);
  }

  get hasVisibleOption() {
    let visibleOption = this.optionTargets.find(option => $(option).is(":visible"));
    return !!visibleOption;
  }
}
