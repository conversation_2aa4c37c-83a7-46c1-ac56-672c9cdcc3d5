import { Controller } from 'stimulus';
import * as dragula from 'dragula/dist/dragula';

export default class extends Controller {
  static targets = ['canvas'];
  static values = {loading: Boolean, formId: Number};

  initialize() {
    this.loading = false;
    this.drake = dragula({
      copy: function(el, source) {
        return source.classList.contains('cf-toolkit-src');
      },
      accepts: function(el, target, _source, sibling) {
        const drop_in_toolkit = (target.classList.contains('cf-toolkit-src'));
        const nested_rows = (el.classList.contains('cf-row-element') && target.classList.contains('cf-row'))
        const max_row = (target.classList.contains('cf-row') && target.children.length > 4)
        return !(drop_in_toolkit ||
          nested_rows ||
          max_row)
      },
      isContainer: function(el) {
        return el.classList.contains('cf-toolkit-src') ||
          el.id === 'cf-dropzone' ||
          el.classList.contains('cf-row');
      },
      invalid: function(el) {
        return el.classList.contains('cf-row-fixed') ||
          el.classList.contains('cf-fixed');
      },
      removeOnSpill: false
    });

    this.drake.on('drop', (el, target) => {
      const inCanvas = target && target.matches('.cf-row, #cf-dropzone');
      const emptyRow = el.classList.contains('cf-row-element') &&
        el.querySelectorAll('.cf-row *').length === 0
      if (inCanvas && !emptyRow) {
        setTimeout((function () {
          this.update();
        }).bind(this), 10);
      }
      return el;
    });
  }

  deleteRow(click) {
    const elt = click.target.closest('.cf-row-element');
    const rowElements = [...elt.querySelectorAll('.cf-element')];
    if (rowElements.length > 0) {
      rowElements.forEach(childElt => {
        this.markDeleted(childElt);
      });
    } else {
      elt.remove();
    }
    this.update();
  }

  markDeleted(elt) {
    elt.dataset.deleted = (elt.dataset.removable && elt.dataset.removable.toLowerCase() === "true")
  }

  delete(click) {
    click.stopPropagation();
    const elt = click.currentTarget.closest('.cf-element')
    this.markDeleted(elt);
    this.update();
  }

  update() {
    this.element.dispatchEvent(new CustomEvent('dim'));

    const dropResult = this.withPositions((elt, row, order) => {
      elt.dataset.row = row;
      elt.dataset.order = order;
      return {
        id: parseInt(elt.dataset.id) || null,
        order: parseInt(elt.dataset.order),
        row: parseInt(elt.dataset.row),
        deleted: (elt.dataset.deleted && elt.dataset.deleted.toLowerCase() === "true") || false,
        removable: (elt.dataset.removable && elt.dataset.removable.toLowerCase() === "true") || false,
        element_type: elt.dataset.elementType,
        element: elt.dataset.element
      }
    });
    $.ajax({
      url: `/organization/forms/${this.formIdValue}`,
      type: 'PUT',
      data: JSON.stringify({fields: dropResult}),
      contentType: 'application/json'
    });
  }

  withPositions(cb) {
    const topLevel = [...document.querySelectorAll('#cf-dropzone > .cf-element, #cf-dropzone > .cf-row-element, #cf-dropzone > .cf-section')];
    const cfElementRows = topLevel.map((cfElementOrContainer) => {
        if (cfElementOrContainer.classList.contains('cf-element')) {
          return [cfElementOrContainer]
        } else {
          return [...cfElementOrContainer.querySelectorAll('.cf-element')]
        }
    });

    var row = -1;
    var order = -1;
    const entries = []

    for(const cfElementRow of cfElementRows) {
      order = -1;
      const allDeleted = cfElementRow.every(cfElement => {
        return cfElement.dataset.deleted &&
          cfElement.dataset.deleted.toLowerCase() === "true"
      }) && cfElementRow.length > 0;

      if (allDeleted === false) {
        row = row + 1;
      }

      for(const cfElement of cfElementRow) {
        const deleted =  cfElement.dataset.deleted &&
          cfElement.dataset.deleted.toLowerCase() === "true";
        if (!deleted) {
          order = order + 1;
        }
        entries.push(cb(cfElement, row, order));
      }
    }
    return entries;
  }

  disconnect() {
    this.drake && this.drake.destroy();
    super.disconnect()
  }
}
