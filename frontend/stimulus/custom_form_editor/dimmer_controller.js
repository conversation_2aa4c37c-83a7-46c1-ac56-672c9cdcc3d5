import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['dimmer'];
  static values =  {formId: Number};

  connect() {
    if (this.getScroll()) {
      this.element.parentElement.scrollTo(
        0,
        this.getScroll(),
      );
    }


    const params = new URLSearchParams(window.location.search);
    const gidFromParams = params.has('activeItemGid') ? params.get('activeItemGid') : this.getActiveItemGid()
    if (gidFromParams) {
      this.setActiveItemGid(gidFromParams);
    }
    this.refreshActiveItem();
  }

  refreshActiveItem() {
    if(this.getActiveItemGid()) {
      const activeElement = document.querySelector(`#cf-canvas [data-gid='${this.getActiveItemGid()}']`)
      if (activeElement) {
        activeElement.classList.add('active');
      }

      this.element.dispatchEvent(new CustomEvent('refresh'));
    }
  }

  getActiveItemKey() {
    return `custom_form_${this.formIdValue}_active_item_gid`
  }

  getActiveItemGid() {
    return window.localStorage.getItem(this.getActiveItemKey())
  }

  setActiveItemGid(gid) {
    window.localStorage.setItem(this.getActiveItemKey(), gid)
  }

  getScrollKey() {
    return `custom_form_${this.formIdValue}_scroll`
  }

  getScroll() {
    const scrollItem  = window.localStorage.getItem(this.getScrollKey())
    if (scrollItem) {
      return Number(scrollItem)
    } else {
      return null;
    }
  }

  setScroll(scroll) {
    window.localStorage.setItem(this.getScrollKey(), scroll.scrollY)
  }


  dim(event) {
    const activeElement = document.querySelector('.cf-element.active');
    if (activeElement) {
      this.setActiveItemGid(activeElement.dataset.gid)
    }

    this.setScroll({ scrollY: this.element.parentElement.scrollTop });
    this.dimmerTarget.classList.add('active-scroll-preserving-dimmer', 'active');
    return event;
  }

  undim(event = null) {
    this.dimmerTarget.classList.remove('active-scroll-preserving-dimmer', 'active');
  }

}
