import { Controller } from 'stimulus';

import { v4 as uuidv4 } from 'uuid';

export default class extends Controller {
  connect() {
    this.taskId = uuidv4();
    this.addHiddenInput();
    this.followTask();
  }

  addHiddenInput() {
    const taskIdInput = document.createElement('input');
    taskIdInput.type = 'hidden';
    taskIdInput.name = 'background_task_id';
    taskIdInput.value = this.taskId;
    this.element.append(taskIdInput);
  }

  followTask() {
    window.followDownloadId(this.taskId);
  }
}
