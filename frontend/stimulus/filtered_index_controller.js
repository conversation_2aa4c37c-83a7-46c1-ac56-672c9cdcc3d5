import { Controller } from 'stimulus';
import { debounce } from 'lodash';

export default class extends Controller {
  static targets = [
    'calendar',
    'dropdown',
    'search',
    'standardSearch',
    'clearButton',
    'dropdownSelection',
    'dropdownSearchSelection',
  ];

  turbolinksSearch = (key, value) => {
    const searchParams = new URLSearchParams(window.location.search);
    const current = searchParams.get(key);

    if (value !== (current || '')) {
      // Preserve scroll
      const elements = document.querySelectorAll('[data-turbolinks-preserve-scroll]');
      elements.forEach(element => {
        Turbolinks.scroll.top = element.scrollTop;
      });

      if (value && value.length) {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }

      let url = window.location.pathname;

      const searchString = searchParams.toString();

      if (searchString.length) {
        url = `${url}?${searchString}`;
      }

      Turbolinks.visit(url, { action: 'replace' });
    }
  }

  searchParams = () => new URLSearchParams(window.location.search);

  prepareDropdowns() {
    this.dropdownTargets.forEach(dropdown => {
      const name = dropdown.dataset.name;
      const key = `filters[${name}]`;

      $(dropdown).dropdown({
        fullTextSearch: true,
        searchSelection: 'text',
        onChange: value => {
          this.turbolinksSearch(key, value);
        },
      });

      const currentValue = this.searchParams().get(key);

      if (currentValue) {
        $(dropdown).dropdown('set selected', currentValue);
      } else {
        $(dropdown).dropdown('restore placeholder text');
      }
    });
  }

  prepareStandardSearch() {
    const delay = parseInt(
      $(this.element).data('search-debounce-delay') || '100'
    );

    const debouncedSearch = debounce(this.turbolinksSearch, delay);

    if (this.hasStandardSearchTarget) {
      const key = 'filters[search]';

      $(this.standardSearchTarget).on('keyup', function () {
        debouncedSearch(key, this.value);
      });

      const $openSearchDropdown = $('.menu.visible .search input');

      if ($openSearchDropdown.length) {
        return;
      }

      $(this.standardSearchTarget).focus(() => {
        const length = this.standardSearchTarget.value.length;
        this.standardSearchTarget.setSelectionRange(length, length);
        $(this.standardSearchTarget).off('focus');
      }).focus();
    }
  }

  prepareCalendars() {
    const dateOptions = { year: 'numeric', month: 'short', day: 'numeric' };

    this.calendarTargets.forEach(calendar => {
      const name = calendar.dataset.name;
      const key = `filters[${name}]`;

      $(calendar).calendar({
        type: 'date',
        popupOptions: {
          variation: 'basic',
          position: 'bottom center',
          duration: 75,
        },
        formatter: {
          date(date, settings) {
            return date.toLocaleDateString('en-US', dateOptions);
          },
        },
        onHide: () => {
          const date = $(calendar).calendar('get date');

          if (date) {
            this.turbolinksSearch(key, date.toISOString());
          } else {
            this.turbolinksSearch(key, '');
          }
        },
      });
    });
  }

  prepareSearches() {
    this.searchTargets.forEach(search => {
      const url = search.dataset.url;
      const key = `filters[${search.dataset.name}]`;
      const resultKey = search.dataset.result_key || 'sgid';

      $(search).search({
        type: 'category',
        selectFirstResult: true,
        minCharacters: 1,
        searchOnFocus: true,
        showNoResults: true,
        apiSettings: {
          url,
        },
        onSelect: result => {
          this.turbolinksSearch(key, result[resultKey]);
        },
      });
    });
  }

  prepareClearButton() {
    if (this.searchParams().toString()) {
      $(this.clearButtonTarget).removeClass('disabled');
    } else {
      $(this.clearButtonTarget).addClass('disabled');
    }

    $(this.clearButtonTarget).click(() => {
      if (this.hasStandardSearchTarget) {
        // Force clear search typing, as it is ignored by turbolinks.
        this.standardSearchTarget.value = '';
      }

      // Clear search target inputs
      this.searchTargets.forEach(target => {
        $(target).search('set value', '');
        $(target).find('.results').remove();
        $(target).removeClass('focus');
      });

      // Clear calendar inputs
      this.calendarTargets.forEach(target => {
        $(target).calendar('clear');
      });
    });
  }

  prepareDropdownSelections() {
    this.dropdownSelections = {};
    const searchParams = this.searchParams();

    this.dropdownSelectionTargets.forEach(dropdown => {
      const name = dropdown.dataset.name;
      const value = searchParams.get(`filters[${name}]`);
      this.dropdownSelections[name] = {};

      if (value) {
        value.split(',').forEach(item => {
          this.dropdownSelections[name][item] = true;
        });
      }
    });
  }

  selectItem({ target }) {
    const $dropdown = $(target).closest('.multiple-selection');
    const $checkbox = $(target).closest('.ui.checkbox');
    const value = $checkbox.find('input[type=checkbox]').data('value');

    // The checkbox will be checked after this code is run. So an unchecked checkbox represents the desire to include the filter.
    if (!$checkbox.hasClass('checked')) {
      this.dropdownSelections[$dropdown.data('name')][value] = true;
    } else {
      delete this.dropdownSelections[$dropdown.data('name')][value];
    }

    if ($dropdown.find('.scrolling.menu').length) {
      localStorage.setItem('scrollPosition', $dropdown.find('.scrolling.menu').scrollTop());
    }

    this.turbolinksSearch(`filters[${$dropdown.data('name')}]`, Object.keys(this.dropdownSelections[$dropdown.data('name')]).join(','));
  }

  prepareDropdownSearchSelections() {
    this.dropdownSearchSelections = {};
    const searchParams = this.searchParams();

    this.dropdownSearchSelectionTargets.forEach(search => {
      const url = search.dataset.url;
      const name = search.dataset.name;
      const key = `filters[${name}]`;
      const resultKey = search.dataset.resultKey || 'sgid';
      const value = searchParams.get(key);
      const searchType = search.dataset.searchType;

      this.dropdownSearchSelections[name] = {};

      if (value) {
        value.split(',').forEach(item => {
          $(`[data-value="${item}"]`).closest('.ui.checkbox').checkbox('check');
          const id = $(`[data-value="${item}"]`).data('id');
          const klass = $(`[data-value="${item}"]`).data('klass');
          this.dropdownSearchSelections[name][`${klass}-${id}`] = item;
        });

        $(search).find('[data-none-selected]').hide();
        $(search).find('[data-selected]').show();
      }

      $(search).search({
        type: searchType,
        selectFirstResult: true,
        minCharacters: 1,
        searchOnFocus: true,
        showNoResults: true,
        apiSettings: {
          url,
        },
        onSelect: result => {
          if (this.dropdownSearchSelections[name][`${result.klass}-${result.id}`]) {
            $(search).find('input.prompt').val('');
            return;
          }

          this.dropdownSearchSelections[name][`${result.klass}-${result.id}`] = result[resultKey];
          this.turbolinksSearch(key, Object.values(this.dropdownSearchSelections[name]).join(','));
          $(search).find('input.prompt').val('');
          this.addDropdownSelectedRow(result, search);
        },
      });
    });
  }

  addDropdownSelectedRow(result, search) {
    const resultKey = search.dataset.result_key || 'sgid';
    let $row = $($('#dropdown-search-row-template').html().replace('[[NAME]]', result.title));

    $row.find('input').attr('data-id', result.id);
    $row.find('input').attr('data-value', result[resultKey]);
    $row.find('input').attr('data-klass', result.klass);
    $row.on('click', this.toggleDropdownSelectedRow.bind(this, { target: $row }));

    $(search).find('[data-selected]').append($row);
    $(search).find('[data-none-selected]').hide();
  }

  toggleDropdownSelectedRow({ target }) {
    const $row = $(target).closest('.ui.checkbox');
    const $search = $row.closest('.dropdown');
    const name = $search.data('name');
    const id = $row.find('input').data('id');
    const klass = $row.find('input').data('klass');

    this.buildRowHashFromParams($search);

    if (this.dropdownSearchSelections[name][`${klass}-${id}`]) {
      delete this.dropdownSearchSelections[name][`${klass}-${id}`];
      $row.checkbox('uncheck');
    } else {
      this.dropdownSearchSelections[name][`${klass}-${id}`] = $row.find('input').data('value');
      $row.checkbox('check');
    }

    this.turbolinksSearch(`filters[${name}]`, Object.values(this.dropdownSearchSelections[name]).join(','));
  }

  buildRowHashFromParams($search) {
    const searchParams = this.searchParams();

    const name = $search.data('name');
    const key = `filters[${name}]`;
    const value = searchParams.get(key);

    this.dropdownSearchSelections[name] = {};

    if (value) {
      value.split(',').forEach(item => {
        const id = $(`[data-value="${item}"]`).data('id');
        const klass = $(`[data-value="${item}"]`).data('klass');
        this.dropdownSearchSelections[name][`${klass}-${id}`] = item;
      });
    }
  }


  connect() {
    this.prepareCalendars();
    this.prepareDropdowns();
    this.prepareSearches();
    this.prepareStandardSearch();
    this.prepareClearButton();
    this.prepareDropdownSelections();
    this.prepareDropdownSearchSelections();
  }
}
