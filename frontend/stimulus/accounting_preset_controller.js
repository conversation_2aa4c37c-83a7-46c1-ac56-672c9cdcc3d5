import moment from 'moment';
import { Controller } from 'stimulus';
import { find, map } from 'lodash';

import { formatMoney } from '../util/money';

export default class extends Controller {
  static targets = ['context', 'preset', 'amount', 'description', 'date'];

  static validations = {
    preset: ['empty'],
    date: ['empty'],
    description: ['empty'],
    amount: ['empty'],
  };

  connect() {
    this.presets = JSON.parse(this.element.dataset.presets);

    $(this.contextTarget).dropdown();
    $(this.contextTarget).parent().dropdown({ onChange: this.changeLedgerContext });

    $(this.presetTarget).dropdown();
    $(this.presetTarget).parent().dropdown({ onChange: this.changePreset });

    this.addValidations();
  }

  changeLedgerContext = ledgerContextGid => {
    $(this.presetTarget).parent().dropdown('restore defaults');
    this.clearInputs();
    this.presets = [];

    $(this.presetTarget).parent().addClass('loading');

    if (ledgerContextGid) {
      this.fetchPresets(ledgerContextGid);
    }
  }

  fetchPresets = ledgerContextGid => {
    const params = { ledger_context_gid: ledgerContextGid };

    $.get(this.constructor.presetsUrl, params, presets => {
      $(this.presetTarget).parent().removeClass('loading');
      this.presets = presets;
      const values = [
        { name: 'Select', value: '' },
        ...map(presets, i => ({ name: i.name, value: i.id })),
      ];
      $(this.presetTarget).parent().dropdown({ values, onChange: this.changePreset });
      this.clearInputs();
    });
  }

  changePreset = presetId => {
    const id = parseInt(presetId, 10);
    const preset = find(this.presets, { id });

    if (!preset) {
      this.clearInputs();
      return;
    }

    const { name: description, amount_cents: cents, net_d: days } = preset;

    this.descriptionTarget.value = description;
    this.amountTarget.value = formatMoney(cents, true);
    $(this.amountTarget).trigger('blur');

    if (typeof this.dueTarget === 'function') {
      const date = moment(this.dateTarget.value);
      date.add(days, 'days');
      this.dueTarget.value = date.format('LL');
    }
  }

  clearInputs = () => {
    this.descriptionTarget.value = '';
    this.amountTarget.value = '';
    $(this.amountTarget).trigger('blur');
  }

  addValidations = () => {
    $(this.element).find('form').form({
      on: 'submit',
      inline: true,
      fields: this.constructor.validations,
    });
  }
}
