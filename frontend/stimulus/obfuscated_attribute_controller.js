import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['value', 'eyeball'];

  static values = { url: String }

  connect() {
    this.obfuscatedValue = this.valueTarget.textContent;
    this.isRevealed = false;
  }

  toggleView(event) {
    event.preventDefault();

    if (this.ajax) {
      this.ajax.abort();
    }

    if (this.isRevealed) {
      this.obfuscate();
    } else {
      this.reveal();
    }
  }

  obfuscate() {
    this.valueTarget.textContent = this.obfuscatedValue;
    this.eyeballTarget.classList.remove('slash');

    this.isRevealed = false;
  }

  reveal() {
    this.ajax = $.get(this.urlValue, value => {
      this.valueTarget.textContent = value;
      this.eyeballTarget.classList.add('slash');

      this.isRevealed = true;
    });
  }
}
