import { Controller } from 'stimulus';

export default class extends Controller {
  searchParams = () => new URLSearchParams(window.location.search);

  prepareDropdown() {
    const $dropdown = $(this.element);
    $dropdown.find('.ui.checkbox').checkbox();

    this.tryHideSelectedItems();

    const name = $dropdown.data('name');
    const value = this.searchParams().get(`filters[${name}]`);

    if (value) {
      $dropdown.find('.selected-items').show();

      const items = value.split(',');

      $dropdown.find('input').each((_, element) => {
        $(element).closest('.ui.checkbox').checkbox('uncheck');
      });

      items.forEach(item => {
        $dropdown.find(`input[data-value='${item}']`).closest('.ui.checkbox').checkbox('check');
      });

      $dropdown.find('.text').removeClass('default');
      $dropdown.addClass('filtered');
      $dropdown.find('.selected.count').text(items.length);
    } else {
      $dropdown.find('.text').addClass('default');
      $dropdown.removeClass('filtered');
      $dropdown.find('.ui.checkbox').checkbox('uncheck');
    }

    if ($dropdown.hasClass('visible')) {
      this.active = true;
      this.closeListener = this.closeDropdown.bind(this);
      this.escapeListener = this.escapeDropdown.bind(this);

      $dropdown.find('> .menu').off('animationend');
      this.focusSearch();

      const lastScrollPosition = localStorage.getItem('scrollPosition');
      const $scrollMenu = $dropdown.find('.scrolling.menu');

      if (lastScrollPosition && $scrollMenu.length) {
        $scrollMenu.scrollTop(lastScrollPosition);
        localStorage.removeItem('scrollPosition');
      }
    }
  }

  reorderItems() {
    const $dropdown = $(this.element);
    const deselectedItems = $dropdown.find('.selected-items .ui.checkbox:not(.checked), [data-selected] .ui.checkbox:not(.checked)');
    const selectedItems = $dropdown.find('.menu > .ui.checkbox.checked');

    deselectedItems.each((_, element) => {
      const $checkbox = $(element);
      const $hidden = $dropdown.find(`.menu > .ui.checkbox input[data-value="${$checkbox.find('input').data('value')}"]`);
      $hidden.closest('.ui.checkbox').show();
      $hidden.closest('.ui.checkbox').checkbox('uncheck');
      $checkbox.remove();
    });

    $dropdown.find('.selected-items').empty();

    selectedItems.each((_, element) => {
      const $checkbox = $(element);
      $checkbox.checkbox('check');
      $checkbox.hide();

      const $clone = $checkbox.clone();
      $dropdown.find('.selected-items').append($clone);
      $clone.show();
    });

    this.tryHideSelectedItems();
  }

  selectItem({ target }) {
    const $dropdown = $(this.element);
    const $checkbox = $(target).closest('.ui.checkbox');
    const checkedCount = $dropdown.find('.menu > .ui.checkbox.checked').length;

    // The checkbox will be checked after this code is run. So an unchecked checkbox represents the desire to check it.
    if (!$checkbox.hasClass('checked')) {
      $dropdown.find('.text').removeClass('default');
      $dropdown.addClass('filtered');
      $dropdown.find('.selected.count').text(checkedCount + 1);
      $dropdown.find('.selected-items').show();
    } else {
      $dropdown.find('.selected.count').text(checkedCount - 1);

      if ((checkedCount - 1) === 0) {
        $dropdown.removeClass('filtered');
        $dropdown.find('.text').addClass('default');
      }
    }
  }

  openDropdown({ target }) {
    if ($(target).is('.dropdown > .menu') || $(target).closest('.dropdown > .menu').length > 0) {
      return;
    }

    const $dropdown = $(this.element);
    const withinMenu = $(target).closest('.ui.multiple-selection .menu').length > 0;
    const $menu = $dropdown.find('> .menu');
    const $checkboxTarget = $(target).closest('.ui.checkbox');
    const justRemoved = $checkboxTarget.get(0) === (this.$justRemoved ? this.$justRemoved.get(0) : undefined);

    if (this.active && !withinMenu && ($checkboxTarget.length === 0 || !justRemoved)) {
      this.closeDropdown({ target }, true);
      return;
    }

    this.reorderItems();
    this.active = true;
    $dropdown.addClass('active visible');

    $menu.one('animationend', () => {
      $menu.removeClass('animating slide down in');
      $menu.addClass('finished');
      this.focusSearch();
    });

    $menu.removeClass('hidden');
    $menu.addClass('visible animating slide down in');

    setTimeout(() => {
      this.closeListener = this.closeDropdown.bind(this);
      this.escapeListener = this.escapeDropdown.bind(this);
      document.addEventListener('click', this.closeListener);
      document.addEventListener('keydown', this.escapeListener);
    }, 0);
  }

  escapeDropdown({ key }) {
    if (key === 'Escape' && this.active) {
      this.closeDropdown({ target: $(this.element) }, true);
    }
  }

  closeDropdown({ target }, force = false) {
    if (!this.active) return;

    const $dropdown = $(this.element);
    const $checkboxTarget = $(target).closest('.ui.checkbox');
    const justRemoved = $checkboxTarget.get(0) === (this.$justRemoved ? this.$justRemoved.get(0) : undefined);

    if (force || (!$dropdown.is(target) && !$dropdown.has(target).length && ($checkboxTarget.length === 0 || !justRemoved))) {
      document.removeEventListener('click', this.closeListener);
      document.removeEventListener('keydown', this.escapeListener);

      $dropdown.find('> .menu').one('animationend', () => {
        $dropdown.find('> .menu').removeClass('visible animating slide down out');
        $dropdown.find('> .menu').addClass('hidden');
        this.active = false;
        this.clearSearch();
      });

      $dropdown.removeClass('active visible');
      $dropdown.find('> .menu').removeClass('finished');
      $dropdown.find('> .menu').addClass('animating slide down out');
    }
  }

  tryHideSelectedItems() {
    const $dropdown = $(this.element);
    const $selectedItems = $dropdown.find('.selected-items, [data-selected]');

    if ($selectedItems.children().length === 0) {
      $selectedItems.hide();
      $dropdown.find('[data-none-selected]').show();
    }
  }

  prepareSearch() {
    const $search = $(this.element).find('.search.input input');

    if ($search.length < 0) {
      return;
    }

    this.items = {}

    const $items = $(this.element).find('.menu > .ui.checkbox');
    $items.each((_, element) => {
      const $item = $(element);
      this.items[$item.text().trim()] = $item;
    });

    $search.on('input', this.filterItems.bind(this));
  }

  filterItems() {
    const $search = $(this.element).find('.search.input input');
    const searchString = $search.val();
    const regex = new RegExp(searchString, 'i');

    Object.keys(this.items).forEach(item_name => {
      const $item = this.items[item_name];

      if (item_name.match(regex)) {
        let previouslySelected = false;

        $(this.element).find('.menu > .selected-items .ui.checkbox').each((_, element) => {
          const $child = $(element);

          if ($child.text().trim() === item_name) {
            previouslySelected = true;
          }
        });

        if ($item.hasClass('checked') || previouslySelected) {
          return;
        }

        $item.show();
      } else {
        $item.hide();
      }
    });
  }

  clearSearch() {
    const $search = $(this.element).find('.search input');

    if (!$search.length) {
      return;
    }

    $search.val('');
    this.filterItems();
  }

  focusSearch() {
    const $search = $(this.element).find('.search input');

    if (!$search.length) {
      return;
    }

    $search.focus();
  }

  connect() {
    this.prepareDropdown();
    this.prepareSearch();
  }
}
