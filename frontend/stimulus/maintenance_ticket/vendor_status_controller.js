import { Controller } from "stimulus"

class VendorStatusController extends Controller {
  static targets = [ "banner" ]

  static values = {
    vendorStatusPath: String
  }

  updateVendorStatus({ target: { value } }) {
    const vendor_id = value;

    if (vendor_id === '') {
      $(this.bannerTarget).html(null);
      return;
    }

    $.get({
      url: `${this.vendorStatusPathValue}?vendor_id=${vendor_id}`,
      success: (response) => {
        $(this.bannerTarget).html(response);
      }
    })
  }
}

export default VendorStatusController;
