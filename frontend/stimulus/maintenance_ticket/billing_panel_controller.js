import { Controller } from 'stimulus';
import { every, some } from 'lodash';

import { formatMoney } from '../../util/money';

class BillingPanelController extends Controller {
  static targets = [
    'allInvoicesCheckbox',
    'allLaborItemsCheckbox',
    'invoiceCheckbox',
    'invoiceTotalAmount',
    'itemCheckbox',
    'itemRow',
    'laborItemCheckbox',
    'laborItemRow',
    'laborTotalAmount',
    'totalAmount',
  ];

  connect() {
    this.prepareCheckboxes();
    this.refreshAmounts();
  }

  // Establishes nested callback hierarchy of all -> invoice -> item checkboxes.
  prepareCheckboxes() {
    const invoiceCheckboxChanged = this.invoiceCheckboxChanged.bind(this);
    const itemCheckboxChanged = this.itemCheckboxChanged.bind(this);
    const laborItemCheckboxChanged = this.laborItemCheckboxChanged.bind(this);

    if (this.hasAllInvoicesCheckboxTarget) {
      $(this.allInvoicesCheckboxTarget).checkbox({
        onChecked: () => this.allInvoicesCheckboxChanged(true),
        onUnchecked: () => this.allInvoicesCheckboxChanged(false),
      });
    }

    $(this.invoiceCheckboxTargets).checkbox({
      onChecked() { invoiceCheckboxChanged(this, true); },
      onUnchecked() { invoiceCheckboxChanged(this, false); },
    });

    $(this.itemCheckboxTargets).checkbox({
      onChecked() { itemCheckboxChanged(this); },
      onUnchecked() { itemCheckboxChanged(this); },
    });

    if (this.hasAllLaborItemsCheckboxTarget) {
      $(this.allLaborItemsCheckboxTarget).checkbox({
        onChecked: () => this.allLaborItemsCheckboxChanged(true),
        onUnchecked: () => this.allLaborItemsCheckboxChanged(false),
      });
    }

    $(this.laborItemCheckboxTargets).checkbox({
      onChecked() { laborItemCheckboxChanged(this); },
      onUnchecked() { laborItemCheckboxChanged(this); },
    });
  }

  allInvoicesCheckboxChanged(checked) {
    const action = checked ? 'check' : 'uncheck';
    $(this.invoiceCheckboxTargets).checkbox(action);
  }

  invoiceCheckboxChanged(input, checked) {
    const invoiceId = $(input).closest('tr').data('invoiceId');

    const action = checked ? 'checked' : 'unchecked';

    $(this.itemCheckboxTargets).filter(function () {
      return $(this).closest('tr').data('invoiceId') === invoiceId;
    }).checkbox(`set ${action}`);

    this.refreshRootCheckboxState();
  }

  itemCheckboxChanged(input) {
    const invoiceId = $(input).closest('tr').data('invoiceId');
    this.refreshInvoiceCheckboxState(invoiceId);
  }

  /*
   * Update invoice checkbox based on line item states
   */
  refreshInvoiceCheckboxState(invoiceId) {
    const itemCheckboxStates = $(this.itemCheckboxTargets).filter(function () {
      return invoiceId === $(this).closest('tr').data('invoiceId');
    }).map(function () {
      return $(this).closest('.ui.checkbox').checkbox('is checked');
    }).get();

    const parent = $(this.invoiceCheckboxTargets).filter(function () {
      return invoiceId === $(this).closest('tr').data('invoiceId');
    });

    if (every(itemCheckboxStates)) {
      parent.checkbox('set checked');
    } else if (some(itemCheckboxStates)) {
      parent.checkbox('set indeterminate');
    } else {
      parent.checkbox('set unchecked');
    }

    this.refreshRootCheckboxState();
  }

  /*
   * Update root checkbox based on all item checkboxes
   */
  refreshRootCheckboxState() {
    const allCheckboxStates = $(this.itemCheckboxTargets).map(function () {
      return $(this).closest('.ui.checkbox').checkbox('is checked');
    }).get();

    const root = $(this.allInvoicesCheckboxTarget);

    if (every(allCheckboxStates)) {
      root.checkbox('set checked');
    } else if (some(allCheckboxStates)) {
      root.checkbox('set indeterminate');
    } else {
      root.checkbox('set unchecked');
    }

    this.refreshAmounts(); // All checkbox change paths end here
  }

  markupValueChanged() {
    this.refreshAmounts();
  }

  laborChanged() {
    this.refreshAmounts();
  }

  markupTypeChanged(event) {
    const element = event.currentTarget;

    // Change icon
    const icon = $(element).find('i');
    icon.toggleClass('percent').toggleClass('dollar sign');

    // Change hidden input
    const input = $(element).siblings('input:hidden:first');
    if (icon.hasClass('percent')) {
      input.val('percent');
    } else {
      input.val('fixed');
    }

    this.refreshAmounts();
  }

  refreshAmounts() {
    let totalCents = 0;
    let sectionTotal = 0;

    $(this.itemRowTargets).each(function () {
      const amount = $(this).data('amountCents');

      let markup = 0;

      const value = $(this).find('input.markup').val();
      const percentMode = $(this).find('i').hasClass('percent');

      if (percentMode) {
        const pct = (parseFloat(value, 10) || 0) / 100;
        markup = parseInt(pct * amount, 10);
      } else {
        markup = parseInt((parseFloat(value, 10) || 0) * 100, 10);
      }

      let total = amount + markup;

      if (!$(this).find('.ui.checkbox').checkbox('is checked')) {
        total = 0;
      }

      const disp = formatMoney(total, true, false, false);
      $(this).find('.subtotal').html(disp);

      sectionTotal += total;
    });

    if (this.hasInvoiceTotalAmountTarget) {
      $(this.invoiceTotalAmountTarget)
        .html(formatMoney(sectionTotal, true, false, false));
    }
    totalCents += sectionTotal
    sectionTotal = 0

    $(this.laborItemRowTargets).each(function () {
      let subtotal = 0;

      if ($(this).find('.ui.checkbox').checkbox('is checked')) {
        let hours = parseFloat($(this).find('input.hours').val());
        let rate = $(this).find('input.rate').attr('data-cents');

        subtotal = Math.round(hours * rate) || 0;
        sectionTotal += subtotal
      }

      const disp = formatMoney(subtotal, true, false, false);
      $(this).find('td.subtotal').html(disp);
    });

    if (this.hasLaborTotalAmountTarget) {
      $(this.laborTotalAmountTarget)
        .html(formatMoney(sectionTotal, true, false, false));
    }

    totalCents += sectionTotal
    if (this.hasTotalAmountTarget) {
      $(this.totalAmountTarget)
        .html(formatMoney(totalCents, true, false, false ));
    }
  }

  allLaborItemsCheckboxChanged(checked) {
    const action = checked ? 'check' : 'uncheck';
    $(this.laborItemCheckboxTargets).checkbox(action);
  }

  laborItemCheckboxChanged(input) {
    const states = $(this.laborItemCheckboxTargets).map(function () {
      return $(this).closest('.ui.checkbox').checkbox('is checked');
    }).get();

    const root = $(this.allLaborItemsCheckboxTarget);

    if (every(states)) {
      root.checkbox('set checked');
    } else if (some(states)) {
      root.checkbox('set indeterminate');
    } else {
      root.checkbox('set unchecked');
    }
  }

  laborRateChanged(event) {
    const rate = $(event.target).data('value');
    const formatted = formatMoney(rate, true, false, false);

    $(event.target).parents('td')
                   .children('input[type="hidden"]')
                   .first()
                   .attr('data-cents', rate)
                   .val(formatted);

    this.laborChanged();
  }
}

export default BillingPanelController;
