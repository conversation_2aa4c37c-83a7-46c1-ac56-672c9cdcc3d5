import { Controller } from 'stimulus';

class NewController extends Controller {

  static values = {
    assignableEmployeesPath: String,
    availableRegardingPath: String,
    serviceAreaPath: String,
  }

  static targets = ['assignableField', 'assignableDropdown', 'regardingSearch', 'regarding']

  connect() {
    this.prepareSearch();
    this.prepareAssigned();

    this.onRegardingChange(this.regardingTarget.value);
  }

  prepareAssigned() {
    this.$assignedDropdown = $(this.assignableDropdownTarget).dropdown({
      action: 'activate',
      onChange: () => this.assignableFieldTarget.classList.remove('error')
    });
  }

  prepareSearch() {
    $(this.regardingSearchTarget).search({
      type: 'category',
      selectFirstResult: true,
      cache: false,
      apiSettings: {
        url: `${this.availableRegardingPathValue}.json?search=true&q={query}`,
      },
      onSelect: this.onRegardingChange.bind(this),
    });
  }

  onRegardingChange(regarding) {
    if (regarding && regarding.sgid) {
      this.regardingTarget.value = regarding.sgid;
      this.element.classList.add('loading')
      $.when(
        this.loadServiceAreaSidebar(regarding.sgid),
        this.updateAssignedUserDropdown(regarding.sgid)
      ).done(() => this.element.classList.remove('loading'))
    }
  }

  updateAssignedUserDropdown(sgid) {
    return $.get({
      url: `${this.assignableEmployeesPathValue}?sgid=${sgid}`,
      success: (assign_options) => {
        const allowed_values = assign_options.map( opt => opt.value);
        const current_value = this.$assignedDropdown.dropdown('get value')
        const current_name = this.$assignedDropdown.dropdown('get text')
        const valid_user = allowed_values.includes(current_value);

        this.$assignedDropdown.dropdown('change values', assign_options);

        if (valid_user) {
          this.assignableFieldTarget.classList.remove('error');
          this.$assignedDropdown.dropdown('set selected', [current_value]);
        } else {
          this.assignableFieldTarget.classList.add('error');
          this.$assignedDropdown.dropdown('set text',
            `${current_name} is not on this service area.`)
        }
      }
    })
  }

  loadServiceAreaSidebar(sgid) {
    return $.get(`${this.serviceAreaPathValue}?sgid=${sgid}`);
  }
}

export default NewController;
