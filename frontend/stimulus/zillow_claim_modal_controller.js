import { Controller } from 'stimulus';
import { renderUjsErrors } from '../util/render_ujs_errors';

export default class extends Controller {

  static values = {
    lockedPropertyId: Number,
    parentSelector: String,
    multifamilyRequired: <PERSON><PERSON><PERSON>,
  };

  static targets = ['multifamilyRadio']

  async connect() {
    this.form$ = $('#zillow_claim_form').form();

    if($(this.multifamilyRadioTarget).prop('checked')) {
      this.multiUnitSelected();
    }
  }

  multiUnitSelected(_) {
    $('#zillow_claim_hotpads_unit_number').val('')
    $('#zillow_claim_hotpads_unit_number').prop('disabled', true);
    this.singleMultiChosen();
  }

  singleUnitSelected(_) {
    $('#zillow_claim_hotpads_unit_number').prop('disabled', false);
    this.singleMultiChosen();
  }

  singleMultiChosen() {
    $(this.element).find('.single-multi input[hidden]')
      .val($(this.element).find('.single-multi input[checked]').val())
    $(this.element).find('.single-multi input').each(el => $(el).prop('disabled', true))
    $(this.element).find('.single-multi .ui.checkbox').each((i, el) => $(el).addClass('disabled'));

    $('.after-grouping .dimmer').removeClass('active');
  }

  unchooseSingleMulti() {
    $(this.element).find('.single-multi input[hidden]').val(null);
    $(this.element).find('.single-multi input').each(el => $(el).prop('disabled', false))
    $(this.element).find('.single-multi .ui.checkbox').each((i, el) => $(el).removeClass('disabled'));
    $('.after-grouping .dimmer').addClass('active');
  }

  addClaimError = (ajaxError) => {
    const claimJson = JSON.parse(ajaxError.detail[2].responseText);
    renderUjsErrors('#zillow_claim_form', claimJson.errors);
  }

  addClaim = (ajaxSuccess) => {
    this.form$.form('reset');
    this.unchooseSingleMulti();
    document.querySelector(this.parentSelectorValue)
      .dispatchEvent(
        new CustomEvent(
          'claimCreated',
          { detail: JSON.parse(ajaxSuccess.detail[2].responseText ) }
        )
      )
    return ajaxSuccess;
  }
}
