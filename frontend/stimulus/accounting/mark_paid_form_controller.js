import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['kindDropdown', 'accountField'];

  connect() {
    $(this.kindDropdownTarget).dropdown('setting', 'onChange', () => {
      this.updateAccountDropdown();
    });

    this.updateAccountDropdown();
  }

  updateAccountDropdown() {
    const kindValue = $(this.kindDropdownTarget).dropdown('get value');

    if (kindValue.endsWith('_balance')) {
      $(this.accountFieldTarget).slideUp(101);
    } else {
      $(this.accountFieldTarget).slideDown(100);
    }
  }
}
