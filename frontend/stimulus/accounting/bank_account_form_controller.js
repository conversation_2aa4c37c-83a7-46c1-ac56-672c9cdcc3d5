import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['ownerDropdown'];

  connect() {
    setTimeout(() => { // Wait for initial initialization
      $(this.ownerDropdownTarget).dropdown('setting', 'onChange', () => {
        // Reload Form
        const event = document.createEvent('Event');
        event.initEvent('submit', true, true);
        this.element.dispatchEvent(event);
      });
    }, 0);
  }
}
