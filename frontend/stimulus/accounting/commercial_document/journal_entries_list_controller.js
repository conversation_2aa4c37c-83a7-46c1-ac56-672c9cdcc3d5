import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'accrualButton', 'cashButton',
    'accrualEntries', 'cashEntries'
  ];

  connect() {
    this.showAccrualBasis();
  }

  showAccrualBasis(event) {
    if (event) {
      event.preventDefault();
    }

    $(this.cashButtonTarget).removeClass('active');
    $(this.cashEntriesTarget).hide();

    $(this.accrualButtonTarget).addClass('active');
    $(this.accrualEntriesTarget).show();
  }

  showCashBasis(event) {
    if (event) {
      event.preventDefault();
    }

    $(this.accrualButtonTarget).removeClass('active');
    $(this.accrualEntriesTarget).hide();

    $(this.cashButtonTarget).addClass('active');
    $(this.cashEntriesTarget).show();
  }
}
