import { Controller } from 'stimulus';

export default class extends Controller {
  static values = {
    bankAccountId: Number,
    reconciliationId: Number
  };

  reconcileVisible(event) {
    event.preventDefault();

    const url = `${this.reconciliationPath}/reconcile`;

    $.post(url, this.visiblePostData, () => {
      this.checkVisibleCheckboxes();
    });
  }

  unreconcileVisible(event) {
    event.preventDefault();

    const url = `${this.reconciliationPath}/unreconcile`;

    $.post(url, this.visiblePostData, () => {
      this.uncheckVisibleCheckboxes();
    });
  }

  checkVisibleCheckboxes() {
    $(this.element).find('.active.tab .checkbox').each(function() {
      $(this).checkbox('set checked', false);
    });
  }

  uncheckVisibleCheckboxes() {
    $(this.element).find('.active.tab .checkbox').each(function() {
      $(this).checkbox('set unchecked', false);
    });
  }

  get visiblePostData() {
    return {
      amount_ids: this.visibleAmountIds,
      deposit_ids: this.visibleDepositIds,
    }
  }

  get visibleAmountIds() {
    const visibleCheckboxes = $(this.element).find('.active.tab .amount-checkbox input');

    return visibleCheckboxes.map(function() { return this.value }).get();
  }

  get visibleDepositIds() {
    const visibleCheckboxes = $(this.element).find('.active.tab .deposit-checkbox input');

    return visibleCheckboxes.map(function() { return this.value }).get();
  }

  get reconciliationPath() {
    return `/organization/bank_accounts/${this.bankAccountIdValue}/reconciliations/${this.reconciliationIdValue}`;
  }
}
