import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['journal', 'revenueAccount', 'bankAccount'];
  
  connect() {
    this.initializeDropdowns();
    this.setupFormValidation();

    if (this.journalTarget.value) {
      this.loadAccounts();
    }
  }

  initializeDropdowns() {
    $(this.journalTarget).dropdown();
    $(this.revenueAccountTarget).dropdown();
    $(this.bankAccountTarget).dropdown();
  }

  journalChanged() {
    this.clearAccountDropdowns();
    this.loadAccounts();
  }

  clearAccountDropdowns() {
    $(this.revenueAccountTarget).dropdown('clear');
    $(this.bankAccountTarget).dropdown('clear');
  }

  refreshDropdowns() {
    $(this.revenueAccountTarget).dropdown('refresh');
    $(this.bankAccountTarget).dropdown('refresh');
  }

  setupFormValidation() {
    $.fn.form.settings.rules.notZero = function(value) {
      const cleanValue = value.replace(/[$,]/g, '');
      const numericValue = parseFloat(cleanValue);
      return !isNaN(numericValue) && numericValue > 0;
    };
  
    $(this.element).form({
      on: 'submit',
      fields: {
        'entry[date]': ['empty'],
        'entry[description]': ['empty'],
        'entry[amount]': ['empty', 'notZero'],
        'entry[journal_id]': ['empty'],
        'entry[revenue_account_id]': ['empty'],
        'entry[bank_account_id]': ['empty']
      },
      prompt: {
        notZero: 'Amount must be greater than zero'
      }
    });
  }

  async loadAccounts() {
    const journalId = this.journalTarget.value;

    const response = await fetch(`/accounting/revenues/accounts?journal_id=${journalId}`);
    const data = await response.json();

    this.populateAccountDropdown(
      this.revenueAccountTarget, 
      data.revenue_accounts, 
      'Select Revenue Account',
      (account) => `${account.gl_code} - ${account.name}`
    );
    
    this.populateAccountDropdown(
      this.bankAccountTarget, 
      data.bank_accounts, 
      'Select Deposit Bank Account',
      (account) => {
        const accountNumber = account.account_number || '';
        const last4 = accountNumber.slice(-4);
        return `${account.name} (*${last4})`;
      }
    );
    
    this.refreshDropdowns();
  }

  populateAccountDropdown(target, accounts, placeholder, textFormatter) {
    target.innerHTML = `<option value="">${placeholder}</option>`;
    accounts.forEach(account => {
      const option = document.createElement('option');
      option.value = account.id;
      option.textContent = textFormatter(account);
      target.appendChild(option);
    });
  }
}
