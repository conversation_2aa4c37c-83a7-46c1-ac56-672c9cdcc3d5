import { Controller } from 'stimulus';
import qs from 'qs';

export default class extends Controller {
  static targets = [
    'payerSearch', 'payeeSearch',
    'paymentKind',
    'withdrawalAccount', 'depositAccount',
    'memo', 'date',
    'invoiceCheckbox',
    'submitButton'
  ];

  // Payer
  get $payerSearch() {
    return $(this.payerSearchTarget);
  }

  get payerSgid() {
    return this.$payerSearch.find('input[type=hidden]').val();
  }

  set payerSgid(value) {
    this.$payerSearch.find('input[type=hidden]').val(value);
  }

  // Payee
  get $payeeSearch() {
    return $(this.payeeSearchTarget);
  }

  get payeeSgid() {
    return this.$payeeSearch.find('input[type=hidden]').val();
  }

  set payeeSgid(value) {
    this.$payeeSearch.find('input[type=hidden]').val(value);
  }

  // Payment Method
  get $paymentKindDropdown() {
    return $(this.paymentKindTarget.closest('.ui.dropdown'));
  }

  get paymentKind() {
    return this.$paymentKindDropdown.dropdown('get value');
  }

  // Withdrawl Bank Account
  get $withdrawalAccountDropdown() {
    return $(this.withdrawalAccountTarget.closest('.ui.dropdown'));
  }

  get withdrawalBankAccountId() {
    return this.$withdrawalAccountDropdown.dropdown('get value');
  }

  // Deposit Bank Account
  get $depositAccountDropdown() {
    return $(this.depositAccountTarget.closest('.ui.dropdown'));
  }

  get depositBankAccountId() {
    return this.hasDepositAccountTarget ? this.$depositAccountDropdown.dropdown('get value') : undefined;
  }

  // Memo
  get memo() {
    return this.memoTarget.value;
  }

  // Date (conditional)
  get date() {
    return this.hasDateTarget ? $(this.dateTarget).find('input').val() : undefined;
  }

  // Selected Invoices
  get selectedInvoiceIds() {
    const selectedIds = this.invoiceCheckboxTargets
                            .filter(checkbox => checkbox.checked)
                            .map(checkbox => checkbox.value);

    // Return array format that qs.stringify handles correctly
    return selectedIds.length > 0 ? selectedIds : [];
  }

  disableSubmit() {
    $(this.submitButtonTarget).prop('disabled', true);
  }

  reloadForm() {
    this.disableSubmit();

    // Get current form values
    const params = {
      payer_sgid: this.payerSgid,
      payee_sgid: this.payeeSgid,
      kind: this.paymentKind,
      withdrawal_bank_account_id: this.withdrawalBankAccountId,
      deposit_bank_account_id: this.depositBankAccountId,
      description: this.memo,
      date: this.date,
      invoice_ids: this.selectedInvoiceIds
    };

    // Remove undefined / empty values
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
        delete params[key];
      }
    });

    const options = qs.stringify(params, {
      addQueryPrefix: true,
      arrayFormat: 'brackets'
    });

    const url = window.location.pathname + options;

    Turbolinks.controller.replaceHistoryWithLocationAndRestorationIdentifier(url, Turbolinks.uuid());

    if (this.xhr) {
      this.xhr.abort();
    }

    this.xhr = $.get(url);
  }

  connect() {
    requestAnimationFrame(() => { // Wait for automatic initialization
      this.$paymentKindDropdown.dropdown('setting', 'onChange', (value) => {
        this.$paymentKindDropdown.addClass('loading');
        this.reloadForm();
      });

      this.$payerSearch.search('setting', 'onSelect', (result) => {
        this.$payerSearch.addClass('loading');
        this.payerSgid = result.sgid;
        this.reloadForm();
      });

      this.$payeeSearch.search('setting', 'onSelect', (result) => {
        this.$payeeSearch.addClass('loading');
        this.payeeSgid = result.sgid;
        this.reloadForm();
      });

      this.$withdrawalAccountDropdown.dropdown('setting', 'onChange', (value) => {
        this.$withdrawalAccountDropdown.addClass('loading');
        this.reloadForm();
      });

      if (this.hasDepositAccountTarget) {
        this.$depositAccountDropdown.dropdown('setting', 'onChange', (value) => {
          this.$depositAccountDropdown.addClass('loading');
          this.reloadForm();
        });
      }

      // Setup invoice checkboxes
      this.invoiceCheckboxTargets.forEach(checkbox => {
        $(checkbox).on('change', () => {
          this.reloadForm();
        });
      });
    });
  }
}
