import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'periodSelector',
    'unpaidExpensesTable',
    'transfersTable',
    'disbursementDropdown',
    'disbursementInput',
    'transactionsModal',
    'transactionsModalHeader',
  ];

  connect() {
    this.preparePeriodSelector();

    this.prepareExpensesCheckboxes();

    $(this.disbursementDropdownTarget).dropdown('setting', 'onChange', () => {
      this.refreshAccounting();
    });

    $('#review_form').on('ajax:success', () => {
      this.prepareDisbursementInputs();
      this.prepareTransfersCheckboxes();
    });

    this.refreshAccounting();
  }

  preparePeriodSelector() {
    $(this.periodSelectorTarget).calendar({
      type: 'month',
      onChange(date) {
        const params = new URLSearchParams(window.location.search);
        params.set('review[period]', date.toISOString());
        const url = `${window.location.pathname}?${params.toString()}`;
        Turbolinks.visit(url);
      },
    });
  }

  prepareExpensesCheckboxes() {
    this.unpaidExpensesTableTarget.selectionChangedCallback = () => {
      this.refreshAccounting();
    };
  }

  prepareTransfersCheckboxes() {
    this.transfersTableTarget.selectionChangedCallback = () => {
      this.refreshAccounting();
    };
  }

  prepareDisbursementInputs() {
    $(this.disbursementInputTargets).off('change').on('change', () => {
      this.refreshAccounting();
    });
  }

  refreshAccounting() {
    Rails.fire(this.element, 'submit');
  }

  cashInTransactions(event) {
    this.showTransactions(event, 'Cash In');
  }

  cashOutTransactions(event) {
    this.showTransactions(event, 'Cash Out');
  }

  showTransactions(event, direction) {
    const name = $(event.target).closest('tr').data('propertyName');

    const transactionsUrl = $(event.target).attr('href');

    event.preventDefault();

    const header = $('#month-end-transactions-modal .header');
    header.find('.direction').text(direction);
    header.find('.name').text(name);

    const contentDiv = $('#month-end-transactions-modal').find('.content');

    contentDiv.html('<div class="ui placeholder segment"><div class="ui active loader"></div></div>');

    $('#month-end-transactions-modal')
      .modal(window.modalDefaults)
      .modal('show');

    $.get(transactionsUrl, html => contentDiv.html(html));
  }
}
