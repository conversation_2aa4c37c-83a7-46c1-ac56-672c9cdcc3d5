import { values } from 'lodash';
import { Controller } from 'stimulus';

export default class extends Controller {
  connect() {
    window.initializeSemanticFields();
    this.nextIndex = this.element.querySelectorAll('.charge-membership-fields').length;
  }

  remove(event) {
    event.preventDefault();

    const charge_membership_field = event.target.closest('.charge-membership-fields');
    charge_membership_field.remove();
  }

  add(event) {
    event.preventDefault();

    const kindContainer = event.target.closest('.kind');
    const newCharge = kindContainer.querySelector('template').content.cloneNode(true).firstElementChild;
    newCharge.innerHTML = newCharge.innerHTML.replace(/NEW_RECORD/g, this.nextIndex);
    kindContainer.firstElementChild.appendChild(newCharge);

    this.nextIndex += 1;
    
    initializeSemanticFields();
    $('.ui.checkbox').checkbox();
  }

  set_monthly(event) {
    event.preventDefault();

    const button = $(event.target);
    const charge_membership_fields = button.closest('.charge-membership-fields');

    button.closest('.field').find('.recurring').val(true);
    button.closest('.field').find('.button').removeClass('active');
    button.addClass('active');

    charge_membership_fields.find('.entire-term').show();
    
    charge_membership_fields.find('.due-date-section').hide();
    this.clearDueDateFields(charge_membership_fields);
  }

  set_one_time(event) {
    event.preventDefault();

    const button = $(event.target);
    const charge_membership_fields = button.closest('.charge-membership-fields');

    button.closest('.field').find('.recurring').val(false);
    button.closest('.field').find('.button').removeClass('active');
    button.addClass('active');

    const start_end_date = charge_membership_fields.find('.start-end-date');
    start_end_date.hide();
    start_end_date.find('.ui.calendar').calendar('clear');

    const entire_term = charge_membership_fields.find('.entire-term');
    entire_term.hide();
    entire_term.find('input').prop('checked', true);
    
    charge_membership_fields.find('.due-date-section').show();
    
    const dueDateField = charge_membership_fields.find('input[name*="due_date"]');
    const netDField = charge_membership_fields.find('input[name*="net_d"]');
    
    if (!dueDateField.val() && !netDField.val()) {
      this.updateDueDateToggle(charge_membership_fields, 'net-d');
    }
  }

  set_due_date(event) {
    event.preventDefault();

    const button = $(event.target);
    const charge_membership_fields = button.closest('.charge-membership-fields');
    
    this.updateDueDateToggle(charge_membership_fields, 'due-date');
  }

  set_net_d(event) {
    event.preventDefault();

    const button = $(event.target);
    const charge_membership_fields = button.closest('.charge-membership-fields');
    
    this.updateDueDateToggle(charge_membership_fields, 'net-d');
  }

  updateDueDateToggle(container, type) {
    container.find('.due-date-toggle .button').removeClass('active');
    container.find(`.${type}-btn`).addClass('active');

    if (type === 'due-date') {
      container.find('.due-date-field').show();
      container.find('.net-d-field').hide();
      container.find('input[name*="net_d"]').val('');
    } else {
      container.find('.due-date-field').hide();
      container.find('.net-d-field').show();
      container.find('.due-date-field .ui.calendar').calendar('clear');
    }
  }

  clearDueDateFields(container) {
    container.find('.due-date-field .ui.calendar').calendar('clear');
    container.find('input[name*="net_d"]').val('');
  }

  toggle_entire_term(event) {
    const checkbox = event.target;
    const start_end_date = $(checkbox).closest('.charge-membership-fields').find('.start-end-date');

    if (checkbox.checked) {
      start_end_date.hide();
      start_end_date.find('.ui.calendar').calendar('clear');
    } else {
      start_end_date.show();
    }
  }
}
