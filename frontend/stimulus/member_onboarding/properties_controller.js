import { Controller } from "stimulus"
import { debounce } from "lodash"

export default class extends Controller {

  connect() {
    this.$sidebar = $('#properties-sidebar').sidebar({ transition: 'overlay' });

    $('.main').on('click', '.properties-sidebar-button', this.show.bind(this));

    this.$sidebarSelectionGroupName = this.$sidebar.find('.selection-group-name')
    this.$sidebarSelectionGroupMemberSearch = this.$sidebar.find('input.selection-group-member-search')
    this.$sidebarSelectionGroupMembers = this.$sidebar.find('.selection-group-members')
    this.currentPage = 1
    this.$sidebarSelectionGroupMemberSearch.on('keyup', debounce(this.onSearchKeyUp.bind(this), 300))
  }

  show(event) {
    this.sidebarContentURL = event.target.href;
    const onboardingId = event.target.dataset.onboardingId;
    const onboardingName = event.target.dataset.onboardingName;
    const onboardingPortfolioId = event.target.dataset.onboardingPortfolioId;
    const onboardingPortfolioName = event.target.dataset.onboardingPortfolioName;
    const onboardingPropertiesCount = event.target.dataset.onboardingPropertiesCount;
    const newVersion = event.target.dataset.newVersion;
    
    event.preventDefault();
    $.get(this.sidebarContentURL, html => {
      $('#properties-sidebar .selection-group-members')[0].innerHTML = html;
      
      // TODO: Remove this legacy version block
      if (newVersion == 'false') {
        const baseUrl = window.location.origin;
        const portfolioUrl = `${baseUrl}/manage/portfolios/${onboardingPortfolioId}`;
        const portfoliosLink = document.getElementById('portfolios-link');
        const propertiesLink = document.getElementById('properties-link');
        portfoliosLink.textContent = onboardingPortfolioName;
        portfoliosLink.href = portfolioUrl;
        propertiesLink.href = portfolioUrl;
        this.$sidebar.find('.selection-group-name').text(`Defaults for ${onboardingName}`);
      } else {
        this.$sidebar.find('.selection-group-name').text(`${onboardingName} Properties`);
      }
      
      const propertiesCountContainer = document.getElementById('properties-count-container');
    
      propertiesCountContainer.innerHTML = `<strong>${onboardingPropertiesCount}</strong>`;
      
      this.bindPaginationEvents()
    });
    this.$sidebar.sidebar('show');
  }

  onSearchKeyUp(event) {
    this.searchTerm = event.target.value
    this.refreshSubSelections({ searchTerm: this.searchTerm, page: this.currentPage })
  }

  onPaginationClick(event) {
    event.preventDefault()

    const $link = $(event.target)

    if ($link.hasClass('left')) {
      this.currentPage -= 1
    } else if ($link.hasClass('right')) {
      this.currentPage += 1
    } else {
      const unparsed = $link.attr('href').split('?');
      if (unparsed.length > 1) {
        const params = new URLSearchParams($link.attr('href').split('?')[1])
        this.currentPage = parseInt(params.get('page'))
      } else {
        this.currentPage = null
      }
    }

    this.refreshSubSelections({ searchTerm: this.searchTerm, page: this.currentPage })
  }

  refreshSubSelections({ searchTerm, page }, callback = () => {}) {
    const url = new URL(this.sidebarContentURL)
    const params = url.searchParams

    if (searchTerm) {
      params.append('search_term', searchTerm)
    }

    if (page) {
      params.append('page', page)
    }

    $.get(url.toString(), (html) => {
      this.$sidebarSelectionGroupName.text(this.selectedGroupName)
      this.$sidebarSelectionGroupMembers.html(html)
      this.bindPaginationEvents()
      callback()
    })
  }

  bindPaginationEvents() {
    this.$sidebar.find('.pagination a.item').on('click', this.onPaginationClick.bind(this));
  }
}
