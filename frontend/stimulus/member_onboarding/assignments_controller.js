import { Controller } from "stimulus"
import SidebarSelectorController from '../sidebar_selector_controller';

export default class extends SidebarSelectorController {
  static values = {
    existingDefaults: Array
  }
  connect() {
    super.connect();
    this.$dropDown.dropdown('setting', 'action', this.dropdownAction.bind(this))
  }

  dropdownAction(text, value) {
    if(this.existingDefaultsValue.includes(value)) {
      $('#existing-default .content .warn').text(
        `Assigning members to this onboarding will override the default onboarding for ${text}.`);
      $('#existing-default').modal({
        onApprove: (function (dropdownValue, _$element,) {
          this.$dropDown.dropdown('set selected', [dropdownValue])
        }).bind(this, value)
      }).modal('show');
    } else {
      this.$dropDown.dropdown('set selected', [value])
    }
  }
}
