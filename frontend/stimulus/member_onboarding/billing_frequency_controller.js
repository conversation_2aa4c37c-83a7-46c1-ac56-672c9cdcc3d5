import { Controller } from "stimulus";

export default class extends Controller {
  static targets = ["twoInstallments"]

  static values = {
    frequency: String
  }

  connect() {
    if (this.frequencyValue === '') return

    if (this.frequencyValue == 'one_time') {
      this.setOneTime();
    } else {
      this.setTwoInstallments();
    }
  }

  setOneTime() {
    const twoInstallmentsTarget = $(this.twoInstallmentsTarget)
    twoInstallmentsTarget.find('input').val(null)
    twoInstallmentsTarget.hide();
  }

  setTwoInstallments() {
    $(this.twoInstallmentsTarget).show();
  }
}
