import { Controller } from 'stimulus';
import * as dragula from 'dragula/dist/dragula';


export default class extends Controller {
  static targets = ['container', 'textTemplate', 'dropdownTemplate', 'descriptionTemplate'];

  initialize() {
    this.drake = dragula([this.containerTarget], {
      moves: function (el, _source, handle) {
        const question =
          el.classList.contains('question-container') ? el : handle.closest('.question-container');
        return question && !(question.classList.contains('disabled'));
      },
      slideFactorY: 300,
      removeOnSpill: false,
      revertOnSpill: true,
    });
  }

  remove(event) {
    event.target.closest('.question-container').remove();
  }

  addTextField(event) {
    const newQuestion = this.textTemplateTarget.content.cloneNode(true).firstElementChild;

    this.containerTarget.appendChild(newQuestion);
  }

  addDropdown(event) {
    const newQuestion = this.dropdownTemplateTarget.content.cloneNode(true).firstElementChild;

    this.containerTarget.appendChild(newQuestion);
  }

  addDescription(event) {
    const newQuestion = this.descriptionTemplateTarget.content.cloneNode(true).firstElementChild;

    this.containerTarget.appendChild(newQuestion);
  }

  disconnect() {
    this.drake && this.drake.destroy();
    super.disconnect()
  }
}
