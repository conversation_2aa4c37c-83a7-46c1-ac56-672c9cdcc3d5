import { Controller } from "stimulus"

export default class extends Controller {
  static targets = ["link"]

  copyLinkToClipboard(event) {
    event.preventDefault();
    
    const linkURL = this.data.get("url"); 
    navigator.clipboard.writeText(linkURL).then(() => {
      this.linkTarget.textContent = 'Link Copied';
    }).catch(err => {
      console.error('Failed to copy the link: ', err);
    });
  }
}
