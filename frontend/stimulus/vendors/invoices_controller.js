import { Controller } from 'stimulus';
import { toInteger, toNumber, uniqueId } from 'lodash';

import { formatMoney } from '../../util/money';

export default class extends Controller {
  static targets = [
    'lineItem',
  ];

  lineItemChanged() {
    this.refresh();
  }

  refresh() {
    const toCents = (val) => {
      if (!val) {
        return 0;
      }

      return toInteger(toNumber(val.replace(/\$/, '')) * 100);
    }

    let totalCents = 0

    $(this.lineItemTargets).each(function() {
      const unitPriceCents = toCents($(this).find('.unit-price input').val());
      const quantity = $(this).find('.quantity input').val() || 0;

      totalCents += unitPriceCents * quantity
      const total = formatMoney(unitPriceCents * quantity, true);

      $(this).find('.subtotal div').text(total);
    });

    $('#invoice-total').text(formatMoney(totalCents, true));
  }

  removeLineItem(event) {
    event.preventDefault();
    const toRemove = $(event.target).data('ident');
    $(`.fields#${toRemove}`).remove();
    this.refresh();
  }

  addLabor(event) {
    event.preventDefault();
    this.addField('#section-labor', 'Hours', 'Rate');
  }

  addMaterial(event) {
    event.preventDefault();
    this.addField('#section-material');
  }

  addOther(event) {
    event.preventDefault();
    this.addField('#section-other');
  }

  addField(section, quantityLabel = 'Quantity', unitPriceLabel = 'Unit Price') {
    const existing = $(`${section} .fields`).size();
    const ident = uniqueId('line_item')

    if (existing > 0) {
      $(section).append(`<div class="ui divider mobile-only"></div>`);
    }

    $(section).append(`
      <div id="${ident}" class="fields" data-vendors--invoices-target="lineItem">
        <div class="four wide description required field">
          <label>Description</label>
          <input
            type="text"
            name="invoice[line_items_attributes][][description]"
          >
        </div>

        <div class="four wide quantity required field">
          <label>${quantityLabel}</label>
          <input
            type="number"
            name="invoice[line_items_attributes][][quantity]"
            data-action="change->vendors--invoices#lineItemChanged"
          >
        </div>

        <div class="four wide unit-price required field">
          <label>${unitPriceLabel}</label>
          <input
            class="money input"
            name="invoice[line_items_attributes][][unit_price]"
            data-action="change->vendors--invoices#lineItemChanged"
            data-init="false"
          >
        </div>

        <div class="three wide subtotal field">
          <label>Subtotal</label>
          <div style="padding-top: 9px;">$0.00</div>
        </div>

        <div class="one wide remove field">
          <div
            class="ui basic icon button"
            data-action="click->vendors--invoices#removeLineItem"
            data-ident="${ident}"
            style="margin-top: 1.8em; margin-bottom: 0.3em;"
          >
            <i class="remove icon" data-ident="${ident}"></i>
          </div>
        </div>
      </div>
    `);

    $('.money.input[data-init=false]').each(function() {
      $(this).makeMoney();
      $(this).data('init', 'true');
    });

    $(`${section} .description.field input`).last().focus();
  }
}
