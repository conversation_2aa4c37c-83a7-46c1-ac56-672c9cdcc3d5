import { Controller } from 'stimulus';
import { castArray, filter, every, map, partition, some } from 'lodash';

export default class extends Controller {
  static targets = [
    'masterCheckBox', 'rowCheckBox',
    'noSelectionMenu', 'withSelectionMenu',
    'selectionCount', 'overselectionToggle',
    'searchInput',
    'row',
  ];

  connect() {
    this.element[this.identifier] = this;

    const rowCheckBoxes = this.rowCheckBoxTargets;
    const masterCheckBoxes = this.masterCheckBoxTargets;

    const updateCount = this.updateCount.bind(this);
    const changedCallback = this.changedCallback.bind(this);

    this.visibleCount = this.rowCheckBoxTargets.length;
    this.filteredCount = this.element.dataset.filteredCount;
    this.overselection = false;

    $(this.masterCheckBoxTargets).checkbox({
      onChecked() {
        $(rowCheckBoxes).checkbox('set checked');
        $(masterCheckBoxes).checkbox('set checked');
        updateCount();
        changedCallback();
      },
      onUnchecked() {
        $(rowCheckBoxes).checkbox('set unchecked');
        $(masterCheckBoxes).checkbox('set unchecked');
        updateCount();
        changedCallback();
      },
    });

    const updateMasterState = this.updateMasterState.bind(this);

    $(this.rowCheckBoxTargets).checkbox({
      onChecked() {
        updateMasterState();
        changedCallback();
      },
      onUnchecked() {
        updateMasterState();
        changedCallback();
      },
    });

    if (this.hasSearchInputTarget) {
      $(this.searchInputTarget).keyup(event => {
        const value = event.target.value;
        this.applySearch(value);
      });
    }

    this.updateMasterState();
  }

  updateMasterState() {
    const results = $(this.rowCheckBoxTargets).checkbox('is checked');

    if (every(castArray(results))) {
      $(this.masterCheckBoxTargets).checkbox('set checked');
    } else if (some(results)) {
      $(this.masterCheckBoxTargets).checkbox('set indeterminate');
    } else {
      $(this.masterCheckBoxTargets).checkbox('set unchecked');
    }

    this.updateCount();
  }

  updateCount() {
    if (this.hasSelectionCountTarget) {
      const selectedCount = this.selectedRows().length;
      const selectedAllVisible = selectedCount === this.visibleCount;

      if (!selectedAllVisible) {
        this.overselection = false;
      }

      const count = this.overselection ? this.filteredCount : selectedCount;

      $(this.selectionCountTarget).html(`${count} Selected`);

      if (this.hasOverselectionToggleTarget) {
        this.updateOverselection(selectedAllVisible);
      }

      if (count === 0) {
        $(this.noSelectionMenuTarget).show();
        $(this.withSelectionMenuTarget).hide();
      } else {
        $(this.noSelectionMenuTarget).hide();
        $(this.withSelectionMenuTarget).show();
      }
    }
  }

  updateOverselection(visible) {
    if (visible) {
      if (this.overselection) {
        $(this.overselectionToggleTarget).html(`(Select Only ${this.visibleCount} Visible)`);
      } else {
        $(this.overselectionToggleTarget).html(`(Select All ${this.filteredCount} Results)`);
      }

      $(this.overselectionToggleTarget).show();
    } else {
      $(this.overselectionToggleTarget).hide();
    }
  }

  toggleOverselection(event) {
    event.preventDefault();
    this.overselection = !this.overselection;
    this.updateCount();
  }

  changedCallback() {
    if (this.element.selectionChangedCallback) {
      this.element.selectionChangedCallback(this.selectedRowIds());
    }
  }

  applySearch(term) {
    const query = term.replace(/\s*/g, '').toLowerCase();

    const [a, b] = partition(this.rowTargets, row => {
      const search = row.dataset.search.replace(/\s*/g, '').toLowerCase();
      return search.includes(query);
    });

    $(a).show();
    $(b).hide();
  }

  selectedRows() {
    return filter(this.rowCheckBoxTargets, t => $(t).checkbox('is checked'));
  }

  selectedRowIds() {
    return map(this.selectedRows(), t => t.dataset.id);
  }

  selectedRowData() {
    return map(this.selectedRows(), ({ dataset: d }) => ({
        id: parseInt(d.id),
        lock_version: parseInt(d.lockVersion),
      })
    );
  }

  isOverselection() {
    return this.overselection;
  }
}
