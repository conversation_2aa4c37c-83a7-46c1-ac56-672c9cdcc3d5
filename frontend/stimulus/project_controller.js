import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = ['panel', 'panelButton'];

  connect() {
    this.storageKey = `projectPanel-${this.element.dataset.projectId}`;
    console.log('strage key', this.storageKey);
    this.activePanel = window.localStorage.getItem(this.storageKey) || 'tasks';
    this.activatePanel();
  }

  disconnect() {
    window.localStorage.setItem(this.storageKey, this.activePanel);
  }

  activatePanel() {
    this.panelTargets.forEach(target => {
      target.classList.toggle('active', target.dataset.panel === this.activePanel);
    });

    this.panelButtonTargets.forEach(target => {
      target.classList.toggle('active', target.dataset.panel === this.activePanel);
    });
  }

  showPanel = ({ currentTarget: { dataset: { panel } } }) => {
    this.activePanel = panel;
    this.activatePanel();
  }
}
