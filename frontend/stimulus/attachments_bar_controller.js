import { Controller } from 'stimulus';

import AttachmentStore from '../attachments/attachment_store';

let uploadId = 0;

export default class extends Controller {
  static targets = ['attachButton', 'fileInput'];

  connect() {
    const uploadFiles = this.uploadFiles.bind(this);

    $(this.fileInputTarget).on('change', () => {
      uploadFiles(this.fileInputTarget.files);
      $(this.fileInputTarget).val(null);
    });

    const box = $(this.element).closest('.comment-box')[0];

    this.store = new AttachmentStore('/api/v1/photos');

    box.ondragenter = function (e) {
      e.preventDefault();
      $(this).addClass('drophover');
    };

    box.ondragexit = function (e) {
      e.preventDefault();
      $(this).removeClass('drophover');
    };

    box.ondragover = function (e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'copy';
      $(this).addClass('drophover');
    };

    box.ondrop = function (e) {
      e.preventDefault();
      $(this).removeClass('drophover');
      uploadFiles(e.dataTransfer.files);
    };

    $(this.attachButtonTarget).click(e => {
      e.preventDefault();
      this.fileInputTarget.click();
    });

    this.setupRemoveExistingAttachments();
  }

  uploadFiles(files) {
    for (let i = 0; i < files.length; i += 1) {
      const file = files[i];

      $(this.attachButtonTarget).before(`
        <div class="ui attachment label" data-id="${uploadId}">
          ${file.name}
          <i class="delete notched circle loading icon"></i>
          <input type="hidden" name="attachment_ids[]" />
        </div>
      `);

      this.store
        .createResource(files[i], { onProgress: this.handleProgress.bind(this, uploadId) })
        .then(this.handleCompleted.bind(this, uploadId))
        .fail(xhr => { console.log('fail', xhr); });

      uploadId += 1;
    }
  }

  handleProgress(id, progress) {
  }

  handleCompleted(id, data) {
    console.log('completed,', data);
    const attachment = $(this.element).find(`.attachment[data-id="${id}"]`);
    attachment.find('input').val(data.id);
    attachment.find('i').attr('class', 'delete icon');
    attachment.find('i').on('click', () => attachment.remove());
  }

  setupRemoveExistingAttachments() {
    $(this.element).find(`.attachment[data-shrine-attachment-id] .delete.icon`).on('click', function () {
      const label = $(this).closest('.attachment');

      const id = label.data('shrineAttachmentId');

      label.replaceWith(`
        <input
          type="hidden"
          name="remove_shrine_attachment_ids[]"
          value="${id}"
        >
        </input>
      `);
    });
  }
}
