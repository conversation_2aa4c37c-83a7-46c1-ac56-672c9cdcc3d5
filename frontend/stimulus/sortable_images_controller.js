import { Controller } from 'stimulus';
import { map } from 'lodash';

export default class extends Controller {
  connect() {
    this.setupSortable();
  }

  setupSortable() {
    const sortable = new Sortable(this.element, {
      draggable: 'a',
      delay: {
        mouse: 0,
        drag: 0,
        touch: 100,
      },
      mirror: {
        constrainDimensions: true,
        appendTo: `#${this.element.id}`,
      },
      sortAnimation: {
        duration: 150,
        easingFunction: 'ease-in-out',
      },
      distance: 15,
      plugins: [SortAnimation],
    });

    sortable.on('sortable:stop', () => {
      setTimeout(() => {
        if (this.xhr) {
          this.xhr.abort();
        }

        const ids = map(this.element.querySelectorAll('a'), a => a.dataset.id);

        const data = { [this.paramName]: ids };

        this.xhr = $.ajax({
          url: this.url,
          data,
          type: 'PATCH',
          dataType: 'json',
        });
      }, 20);
    });
  }

  get paramName() {
    return 'ordered_attachment_ids';
  }

  get url() {
    return `${window.location.pathname}/reorder`;
  }
}
