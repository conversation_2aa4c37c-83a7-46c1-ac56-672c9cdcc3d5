import { Controller } from "stimulus"
import { debounce, union, difference } from "lodash"

export default class extends Controller {
  static targets = [
    'dropDown',
    'subSelectionExclusions'
  ]

  connect() {
    // connect the dropdown "callbacks"
    this.$dropDown = $(`#${this.element.id} .dropdown`)
      .dropdown({
        onLabelSelect: this.onLabelSelect.bind(this)
      })

    // connect the sidebar
    this.$sidebar = $(`#${this.data.get('dom-id')}`).sidebar({ transition: 'overlay' })
    this.$sidebarSelectionGroupName = this.$sidebar.find('.selection-group-name')
    this.$sidebarSelectionGroupMemberSearch = this.$sidebar.find('input.selection-group-member-search')
    this.$sidebarSelectionGroupMembers = this.$sidebar.find('.selection-group-members')

    this.sidebarContentURL = this.data.get('content-url')
    this.sidebarContentURLQueryParamName = this.data.get('content-url-query-param-name')
    this.currentPage = 1

    this.$sidebarSelectionGroupMemberSearch.on('keyup', debounce(this.onSearchKeyUp.bind(this), 300))

    // init sub-selections
    this.subSelectionExclusions = {}
  }

  onSubSelectAllToggle(event) {
    const $checkbox = $(event.target)

    if ($checkbox.is(':checked')) {
      this.$sidebarSelectionGroupMembers.find('input[type="checkbox"]:not(:disabled)').prop('checked', true)
      this.subSelectionExclusions[this.selectedGroupId] = difference(
        this.subSelectionExclusions[this.selectedGroupId],
        this.sidebarSelectableIds,
      )
    } else {
      this.$sidebarSelectionGroupMembers.find('input[type="checkbox"]:not(:disabled)').prop('checked', false)
      this.subSelectionExclusions[this.selectedGroupId] = union(
        this.subSelectionExclusions[this.selectedGroupId],
        this.sidebarDeselectableIds
      )
      $checkbox.prop('checked', false)
    }
  }

  onLabelSelect($selectedLabel) {
    this.selectedGroupId = $selectedLabel.dataset.value
    this.selectedGroupName = $selectedLabel.innerText
    this.$sidebarSelectionGroupMemberSearch.val('')
    this.currentPage = 1
    this.searchTerm = null

    this.refreshSubSelections({}, () => {
      this.$sidebar.sidebar('show')
      this.$sidebarSelectionGroupMemberSearch.trigger($.Event('keydown', { which: 13 }))
    })
  }

  onSearchKeyUp(event) {
    this.searchTerm = event.target.value

    this.refreshSubSelections({ searchTerm: this.searchTerm, page: this.currentPage })
  }

  onSubSelectionChange(event) {
    const $checkbox = $(event.target)
    const subSelectionId = parseInt($checkbox.data('id'))

    if (!this.subSelectionExclusions[this.selectedGroupId]) {
      this.subSelectionExclusions[this.selectedGroupId] =
        JSON.parse(this.subSelectionExclusionsTarget.value)[this.selectedGroupId] ?? []
    }

    if ($checkbox.is(':checked')) {
      this.subSelectionExclusions[this.selectedGroupId] = this.subSelectionExclusions[this.selectedGroupId].filter(id => id !== subSelectionId)

      if (this.subSelectionExclusions[this.selectedGroupId].length === 0) {
        this.$sidebar.find('.subselect-all-toggle').prop('checked', true)
      }
    } else {
      if (subSelectionId && this.subSelectionExclusions[this.selectedGroupId].indexOf(subSelectionId) === -1 ) {
        this.subSelectionExclusions[this.selectedGroupId].push(subSelectionId)
      }

      this.$sidebar.find('.subselect-all-toggle').prop('checked', false)
    }

    $(this.subSelectionExclusionsTarget).val(JSON.stringify(this.subSelectionExclusions))

    const subSelectionsCount = this.$sidebar.find('.total-selected').data('subSelectionsCount')

    this.$sidebar.find('.total-selected').text(`${subSelectionsCount - this.subSelectionExclusions[this.selectedGroupId].length} selected`)

    this.$dropDown.find(`.label[data-value=${this.selectedGroupId}] i.filter`).remove()

    if (this.subSelectionExclusions[this.selectedGroupId].length > 0) {
      this.$dropDown.find(`.label[data-value=${this.selectedGroupId}]`).prepend('<i class="filter icon"></i>')
    }
  }

  onPaginationClick(event) {
    event.preventDefault()

    const $link = $(event.target)

    if ($link.hasClass('left')) {
      this.currentPage -= 1
    } else if ($link.hasClass('right')) {
      this.currentPage += 1
    } else {
      const unparsed = $link.attr('href').split('?');
      if (unparsed.length > 1) {
        const params = new URLSearchParams($link.attr('href').split('?')[1])
        this.currentPage = parseInt(params.get('page'))
      } else {
        this.currentPage = null
      }
    }

    this.refreshSubSelections({ searchTerm: this.searchTerm, page: this.currentPage })
  }

  refreshSubSelections({ searchTerm, page }, callback = () => {}) {
    const url = new URL(this.sidebarContentURL)
    const params = url.searchParams

    params.append(this.sidebarContentURLQueryParamName, this.selectedGroupId)
    
    if (searchTerm) {
      params.append('search_term', searchTerm)
    }

    if (page) {
      params.append('page', page)
    }

    const data = { sub_selection_exclusions: this.subSelectionExclusions[this.selectedGroupId] ?? [] }

    $.post(url.toString(), data, (html) => {
      this.$sidebarSelectionGroupName.text(this.selectedGroupName)
      this.$sidebarSelectionGroupMembers.html(html)
      this.$sidebar.find('.subselect-all-toggle').on('change', this.onSubSelectAllToggle.bind(this))
      this.$sidebar.find('.pagination a.item').on('click', this.onPaginationClick.bind(this))
      this.$sidebarSelectionGroupMembers.on('change', 'input[type="checkbox"]', this.onSubSelectionChange.bind(this))
      this.sidebarDeselectableIds =
        this.$sidebar.find('.subselect-all-toggle').data('deselectableIds') ?? []
      this.sidebarSelectableIds =
        this.$sidebar.find('.subselect-all-toggle').data('selectableIds') ?? []

      callback()
    })
  } 
}
