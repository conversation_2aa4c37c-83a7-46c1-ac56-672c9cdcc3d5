import { Controller } from 'stimulus';
import { camelCase, map, mapValues } from 'lodash';
import * as Plot from '@observablehq/plot';

export default class extends Controller {
  connect() {
    const options = JSON.parse(this.element.dataset.options);
    const jsonData = JSON.parse(this.element.dataset.data);
    const data = mapValues(jsonData, array => (
      map(array, item => {
        if ('date' in item) {
          return { ...item, date: Date.parse(item.date) };
        }

        return item;
      })
    ));

    options.marks = map(options.marks, settings => {
      const mark = camelCase(settings.mark);
      const dataName = settings.data;
      let markOptions = settings.options;
      const dataset = data[dataName];
      const fn = Plot[mark];

      // Apply Transform
      if (markOptions.transform) {
        const transform = Plot[camelCase(markOptions.transform)];

        delete markOptions.transform;

        markOptions = transform(markOptions);
      }

      // Apply Text Format
      if (markOptions.format) {
        const key = markOptions.text;
        const formatter = d3.format(markOptions.format);
        markOptions.text = d => formatter(d[key]);
      }

      return fn(dataset, markOptions);
    });

    const drawPlot = () => {
      options.height = this.element.clientHeight;
      options.width = this.element.clientWidth;

      const plot = Plot.plot(options);

      this.element.replaceChildren(plot);
    };

    this.resizeObserver = new ResizeObserver(drawPlot);

    this.resizeObserver.observe(this.element);

    drawPlot();
  }

  disconnect() {
    this.resizeObserver.disconnect();
  }
}
