import { Controller } from 'stimulus';
import { truncate } from 'lodash';

export default class extends Controller {
  static values = {
    url: String,
    initialized: <PERSON><PERSON><PERSON>
  };

  connect() {
    // Load the dropdown values when the dropdown is first opened
    $(this.element).dropdown('setting', 'onShow', () => this.load());

    if (this.filterValue) {
      // Make sure we are loaded regardless if opened,
      // because a filter is set in the URL and we have to display a value
      this.load();
    }
  }

  load() {
    if (this.initializedValue) {
      // Already initialized
      return;
    }

    this.initializedValue = true;

    this.element.classList.add('loading');

    $.get(this.urlValue, (values) => {
      let items = values.map(value => {
        return this.createItem({
          name: truncate(value[0], { length: 30 }), // 30 matches Rails' behavior
          value: value[1]
        });
      });

      $(this.element).find('.scrolling.menu').append(items.join(''));
      this.element.classList.remove('loading');

      if (this.filterValue) {
        setTimeout(() => { $(this.element).dropdown('set selected', this.filterValue); }, 0);
      }
    });
  }

  createItem({ name, value }) {
    return `<a class="item" data-value="${value}">${name}</a>`;
  }

  get filterName() {
    return this.element.getAttribute('data-name');
  }

  get filterValue() {
    return qs.parse(window.location.search, { ignoreQueryPrefix: true })?.filters?.[this.filterName];
  }
}
