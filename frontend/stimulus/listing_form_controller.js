import { Controller } from 'stimulus';
import { renderUjsErrors } from '../util/render_ujs_errors';

export default class extends Controller {
  static targets =  ['editLink', 'addLink'];
  static values = {
    listingId : String,
    initialClaimOptions: Array,
  };

  async connect () {
    if (!isNaN(parseInt(this.listingIdValue))) {
      this.setupClaimDropdown();
      this.updateDropdownButton(this.selectedZillowClaimId);
      this.setupZillowFeedCheckbox();
    }
  }

  setupZillowFeedCheckbox() {
    if ($('#zillow_feed_checkbox').length > 0) {
      this.zillowFeedCheckbox$ = $('#zillow_feed_checkbox').checkbox({
        onChecked: function () {
          $('#zillow_fields').show();
        },
        onUnchecked: function () {
          $('#zillow_fields').hide();
        }
      });
    }
  }

  get selectedZillowClaimId() {
    return $('#listing_property_zillow_claim_id').val() ?? ' ';
  }

  submit (e) {
    if(this.validateRequired()) {
      return e
    } else {
      e.preventDefault();
      return e;
    }
  }

  validateRequired () {
    if (isNaN(parseInt(this.selectedZillowClaimId))) {
      $('.claim-dropdown-field .ui.dropdown').addClass('error');
      return false;
    } else {
      $('.claim-dropdown-field .ui.dropdown').removeClass('error');
      return true;
    }
  }


  get searchUrl() {
    return [
      '/zillow/claims/search?line_one={query}',
      `listing_id=${this.listingIdValue}`
    ].join('&');
  }

  setupClaimDropdown () {
    this.claimDropdown$ = $(this.element)
      .find('.claim-dropdown-field .ui.dropdown')
      .dropdown({
        apiSettings: {
          url: this.searchUrl,
          saveRemoteData: false,
          cache: false,
          beforeXHR: (xhr) => {
            xhr.responseUrl += `?bust=${new Date().getTime()}`
          }
        },
        allowReselection: true,
        action: 'activate',
        onChange: (value, text, _elt) => {
          this.updateClaimDropdown(value, text);
          this.updateDropdownButton(value)
        },
        values: this.initialClaimOptionsValue
      });
  }

  updateDropdownButton (value) {
    if(!isNaN(parseInt(value))) {
      $(this.editLinkTarget).show();
      $(this.editLinkTarget).attr('href', `/zillow/claims/${value}/edit`);
      $(this.addLinkTarget).hide();
    } else {
      $(this.editLinkTarget).hide();
      $(this.addLinkTarget).show();
    }
  }

  updateClaimDropdown(id, name) {
    $('#listing_property_zillow_claim_id').val(id);
    $('#listing_property_zillow_claim_name').val(name);
    this.claimDropdown$.dropdown('set text', name);
  }

  addClaim(claimCreatedEvent) {
    const dropdownOption = claimCreatedEvent.detail.dropdown_option;
    this.claimDropdown$.dropdown('clear cache');
    $.getJSON(this.searchUrl, (json) => {
      const new_options = json.results;
      new_options.find((option) => {
        return option.value.toString() === dropdownOption.value.toString()
      }).selected = true;
      this.claimDropdown$.dropdown('change values', new_options);
      this.updateDropdownButton(dropdownOption.value);
      this.claimDropdown$.dropdown('refresh');
    })
  }
}
