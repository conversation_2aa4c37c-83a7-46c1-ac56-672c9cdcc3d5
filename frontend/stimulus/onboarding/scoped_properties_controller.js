import { Controller } from "stimulus"

export default class extends Controller {
  static targets = ["portfolio", "propertyDropdownWrapper"]
  static values = {
    selectablePropertiesPath: String
  }

  updateProperties() {
    const portfolioId = this.portfolioTarget.value;
    const url = `${this.selectablePropertiesPathValue}?portfolio_id=${portfolioId}`

    if (this.xhr) {
      this.xhr.abort();
    }

    this.xhr = $.get(url, html => {
      const content = $(html).find(".content").get(0)
      this.propertyDropdownWrapperTarget.innerHTML = content.innerHTML;
      initializeSemanticFields();
      $('.info.circle.icon').popup();
    });
  }
}
