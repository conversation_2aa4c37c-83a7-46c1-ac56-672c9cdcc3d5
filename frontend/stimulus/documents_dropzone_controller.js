import DropzoneController from './dropzone_controller';

export default class extends DropzoneController {
  bindEvents() {
    super.bindEvents();

    this.dropzone.on('success', file => { this.handleSuccessfulFile(file); });

    this.dropzone.on('queuecomplete', () => { this.restoreMessage(); });
  }

  handleSuccessfulFile(file) {
    this.removePreview(file);
    this.associateUpload(file);
  }

  removePreview(file) {
    setTimeout(() => { $(file.previewElement).hide(200); }, 500);
  }

  associateUpload(file) {
    const signedBlobId = file.signed_blob_id;

    const signedParentId = this.element.dataset.parentSgid;

    const url = `/contacts/${signedParentId}/documents?signed_blob_id=${signedBlobId}`;

    $.post(url);
  }

  restoreMessage() {
    setTimeout(() => {
      this.element.classList.remove('dz-started');
    }, 600);
  }
}
