import { Controller } from 'stimulus';

export default class extends Controller {
  static targets = [
    'durationFields', 'explicitFields', 'rangeFields',
    'installmentsFields', 'addInstallmentButton'
  ];

  connect() {
    $(this.element).find('#payment_plan_preset_type').dropdown('setting', 'onChange', this.typeChanged.bind(this));

    $(this.installmentsFieldsTarget).on('cocoon:after-insert', this.installmentAdded.bind(this));

    $(this.installmentsFieldsTarget).on('cocoon:after-remove', this.installmentRemoved.bind(this));
  }

  typeChanged(type) {
    if (type === 'PaymentPlan::Preset::Duration') {
      this.disableFieldset(this.explicitFieldsTarget);
      this.disableFieldset(this.rangeFieldsTarget);
      this.enableFieldset(this.durationFieldsTarget);
    } else if (type === 'PaymentPlan::Preset::Explicit') {
      this.disableFieldset(this.durationFieldsTarget);
      this.disableFieldset(this.rangeFieldsTarget);
      this.enableFieldset(this.explicitFieldsTarget);
    } else if (type === 'PaymentPlan::Preset::Range') {
      this.disableFieldset(this.durationFieldsTarget);
      this.disableFieldset(this.explicitFieldsTarget);
      this.enableFieldset(this.rangeFieldsTarget);
    }
  }

  disableFieldset(target) {
    $(target).attr('disabled', true);
    $(target).hide();
  }

  enableFieldset(target) {
    $(target).attr('disabled', false);
    $(target).show();
  }

  installmentAdded() {
    window.initializeSemanticFields();
    this.limitInstallmentCount();
  }

  installmentRemoved() {
    this.limitInstallmentCount();
  }

  limitInstallmentCount() {
    const count = this.installmentsFieldsTarget.children.length;

    const classList = this.addInstallmentButtonTarget.classList;

    if (count >= 12) {
      classList.add('disabled');
    } else {
      classList.remove('disabled');
    }
  }
}
