import { Controller } from 'stimulus';

export default class TrayWindowController extends Controller {
  static targets = ['focusable'];

  connect() {
    this.element[this.identifier] = this;
  }

  open() {
    $(this.element).show(200);

    $(this.element)
      .removeClass('minimized')
      .find('.minimize-message-popup')
      .removeClass('up')
      .addClass('down');

    this.focus();
  }

  minimize(event) {
    $(this.element).closest('.tray-window').toggleClass('minimized');
    $(event.target).toggleClass('down').toggleClass('up');
  }

  close() {
    $(this.element).closest('.tray-window').hide(200);
  }

  focus() {
    if (this.hasFocusableTarget) {
      const focusable = $(this.focusableTarget);
      const value = focusable.val();
      focusable.focus().val('').val(value);
    }
  }

  // TODO: Move to email window specific controller
  prepareReply(email) {
    // Set reply id
    const replyingToId = email.replyingToId;
    $(this.element).find('#email_replying_to_id').val(replyingToId);

    // Set Re: Subject
    let subject = email.subject;
    if (subject && subject.length && !subject.startsWith('Re: ')) {
      subject = `Re: ${subject}`;
    }
    $(this.focusableTarget).val(subject);
  }
}
