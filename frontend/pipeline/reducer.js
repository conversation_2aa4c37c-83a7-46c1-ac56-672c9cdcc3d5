import { unionBy } from 'lodash';

import { START_LOADING_PIPELINE, RECEIVE_PIPELINE_ENTRIES, SET_PIPELINE_FILTER } from './actions';

const defaultState = {
  leads: { items: [] },
  applicants: { items: [] },
  expiring: { items: [] },
  waitlist: { items: [] },
  filter: undefined,
};

export default (state = defaultState, action) => {
  switch (action.type) {
    case START_LOADING_PIPELINE:
      return {
        ...state,
        [action.source]: {
          ...state[action.source],
          loading: true,
        },
      };
    case RECEIVE_PIPELINE_ENTRIES:
      return {
        ...state,
        [action.source]: {
          ...state[action.source],
          loading: false,
          items: unionBy(state[action.source].items, action.entries, 'id'),
          lastCount: action.entries.length,
        },
      };
    case SET_PIPELINE_FILTER:
      return { ...defaultState, filter: action.filter };
    default:
      return state;
  }
};
