import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { map } from 'lodash';

const Column = ({ items, component: Component, source }) => (
  <div className={`${source} column`}>
    {map(items, item => <Component key={item.id} item={item} />)}
  </div>
);

Column.propTypes = {
  source: PropTypes.string.isRequired,
  items: PropTypes.array.isRequired,
};

Column.defaultProps = {
  items: [],
};

const mapState = (state, ownProps) => ({
  items: state.pipeline[ownProps.source].items,
});

export default connect(mapState)(Column);
