import { Component } from 'react';
import { connect } from 'react-redux';
import { map } from 'lodash';

import * as actions from './actions';

class Filter extends Component {
  state = { properties: [], };

  componentDidMount() {
    $.get('/manage/properties.json?include_floorplans=true',
      properties => this.setState({ properties })
    );

    $(this.dropdown).dropdown({
      allowCategorySelection: true,
      onChange: this.props.setPipelineFilter,
      placeholder: 'Filter',
    });
  }

  render() {
    return (
      <div
        className="ui mini floating top right pointing dropdown button"
        style={{
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          marginRight: '1em',
          position: 'absolute',
          right: 0,
          top: 0,
          zIndex: 100,
        }}
        ref={dropdown => this.dropdown = dropdown}
      >
        <i className="filter icon" />
        <span
          className="default text"
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: 72,
          }}
        >
          Filter
        </span>
        <div className="left menu">
          <div className="header" style={{
            fontSize: '1em',
            textTransform: 'none',
          }}>
            Filter Pipeline
          </div>
          <div className="divider" />
          <div className="item" data-value="">Everything</div>
          {map(this.state.properties, property => (
            <div key={property.id} className="item" data-value={`gid://revela/Property/${property.id}`}>
              <span className="text">{property.name}</span>
              <div className="left menu">
                {map(property.floorplans, floorplan => (
                  <div key={floorplan.id} className="item" data-value={`gid://revela/Floorplan/${floorplan.id}`}>
                    {floorplan.name}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
}

export default connect(null, actions)(Filter);
