import PropTypes from 'prop-types';
import { Component } from 'react';
import { connect } from 'react-redux';

import ApplicantCard from './cards/applicant';
import Column from './column';
import ColumnHeader from './column_header';
import ExpiringCard from './cards/expiring';
import LeadCard from './cards/lead';
import WaitlistCard from './cards/waitlist';
import Filter from './filter';
import { fetchPipeline } from './actions';

class Pipeline extends Component {
  componentDidMount() {
    this.container = $('.action-container');
    this.container.on('scroll', this.scroll);
  }

  componentWillUnmount() {
    this.container.off('scroll', this.scroll);
  }

  scroll = () => {
    const st = this.container.scrollTop();
    const ih = this.container.innerHeight();
    const sh = this.container[0].scrollHeight;
    if (st + ih + 300 > sh) {
      this.props.loadMore();
    }
  }

  render() {
    const { visible } = this.props;

    return (
      <div className="ui pipeline container" style={{
        display: visible ? 'flex' : 'none',
      }}>
        <div className={`ui four steps`}>
          <Filter />
          <ColumnHeader name="Leads" />
          <ColumnHeader name="Applicants" />
          <ColumnHeader name="Waitlist" />
          <ColumnHeader name="Expiring" />
        </div>
        <div className={`ui four column padded divided grid`}>
          <Column source="leads" component={LeadCard} />
          <Column source="applicants" component={ApplicantCard} />
          <Column source="waitlist" component={WaitlistCard} />
          <Column source="expiring" component={ExpiringCard} />
        </div>
      </div>
    );
  }
}

Pipeline.propTypes = {
  loadMore: PropTypes.func.isRequired,
};

const mapDispatch = dispatch => ({
  loadMore: () => dispatch(fetchPipeline),
});

export default connect(null, mapDispatch)(Pipeline);
