import PropTypes from 'prop-types';

const ColumnHeader = ({ name, newCount }) => (
  <div className="step" style={{ flexDirection: 'column' }}>
    <div className="title">
      <h2 className="ui header">
        {name}
      </h2>
    </div>
    <div className="description">
      {newCount} New
    </div>
  </div>
);

ColumnHeader.propTypes = {
  name: PropTypes.string.isRequired,
  newCount: PropTypes.number.isRequired,
};

ColumnHeader.defaultProps = {
  newCount: 0,
};

export default ColumnHeader;
