import PropTypes from 'prop-types';
import classNames from 'classnames';
import moment from 'moment';
import { renameProp } from 'recompose';
import { startCase } from 'lodash';

const StatusBar = ({ status }) => {
  if (!status) {
    return null;
  }

  const icon = {
    awaiting_results: 'yellow wait',
    awaiting_payment: 'yellow wait',
    requires_adjudication: 'teal legal',
    requires_review: 'teal search',
    approved: 'green check',
    rejected: 'red remove',
  }[status];

  return (
    <div className="extra content">
      <i className={classNames(icon, { icon })} />
      {startCase(status)}
    </div>
  );
}

const ApplicantCard = ({ applicant: { archive_url: archive, url, name, date, status } }) => (
  <a className="ui card" href={url}>
    <div className="content">
      <a
        href={archive}
        data-confirm="Archive application?"
        title="Archive"
      >
        <i className="right floated archive icon" />
      </a>
      <div className="header">
        {name}
      </div>
      <div className="meta">
        Applied {moment(date).fromNow()}
      </div>
    </div>
    {<StatusBar status={status} />}
  </a>
);

ApplicantCard.propTypes = {
  applicant: PropTypes.shape({
    archive_url: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    date: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
  }).isRequired,
};

export default renameProp('item', 'applicant')(ApplicantCard);
