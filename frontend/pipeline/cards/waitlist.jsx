import PropTypes from 'prop-types';
import moment from 'moment';
import { renameProp } from 'recompose';

const WaitlistCard = ({ entry: { url, name, date, position, expected_date, floorplan, remove_url } }) => (
  <a className="ui card" href={url}>
    <div className="content">
      <a
        href={remove_url}
        data-method="delete"
        data-confirm={`Remove ${name} from the ${floorplan} waitlist?`}
      >
        <i className="right floated remove icon" />
      </a>
      <div className="header">
        {name}
      </div>
      <div className="meta">
        Waitlisted {moment(date).fromNow()}
      </div>
      <div className="description">
        {position === 0 ? 'Ready' : `#${position}`} for {floorplan}
      </div>
      {position !== 0 &&
        <div className="meta">
          Expected to wait {moment(expected_date).fromNow(true)}
        </div>
      }
    </div>
  </a>
);

WaitlistCard.propTypes = {
  entry: PropTypes.shape({
    date: PropTypes.string.isRequired,
    expected_date: PropTypes.string,
    floorplan: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    position: PropTypes.number,
    remove_url: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
  }).isRequired,
};

export default renameProp('item', 'entry')(WaitlistCard);
