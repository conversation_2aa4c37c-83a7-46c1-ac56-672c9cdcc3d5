import PropTypes from 'prop-types';
import moment from 'moment';
import { renameProp } from 'recompose';

const temp = (t) => {
  switch (t) {
    case 'hot':
      return 'Hot';
    case 'interested':
      return 'Interested';
    case 'contacted':
      return 'Contacted';
    case 'not_interested':
      return 'Not Interested';
    default:
      return '';
  }
}

const level = (temp) => {
  switch (temp) {
    case 'hot':
      return 'full';
    case 'interested':
      return 'three quarters';
    case 'contacted':
      return 'one quarter';
    case 'not_interested':
      return 'empty';
    default:
      return '';
  }
}

const LeadCard = ({ lead: { archive_url: archive, url, name, date, floorplan, phone, temperature } }) => (
  <a className="ui card" href={url}>
    <div className="content">
      <a
        href={archive}
        data-confirm="Archive guest card?"
        title="Archive"
      >
        <i className="right floated archive icon" />
      </a>
      <div className="header">
        {name}
      </div>
      <div className="meta">
        Visited {moment(date).fromNow()}
      </div>
      <div className="description">
        {floorplan}
      </div>
    </div>
    {phone &&
      <div className="extra content">
        <i className="phone icon" />
        {phone}
        <br />
      </div>
    }
    {temperature &&
      <div className="extra content">
        <i className={`thermometer ${level(temperature)} icon`} />
        {temp(temperature)}
      </div>
    }
  </a>
);

LeadCard.propTypes = {
  lead: PropTypes.shape({
    archive_url: PropTypes.string.isRequired,
    date: PropTypes.string.isRequired,
    floorplan: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
    phone: PropTypes.string,
  }).isRequired,
};

export default renameProp('item', 'lead')(LeadCard);
