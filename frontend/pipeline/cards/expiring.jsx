import PropTypes from 'prop-types';
import moment from 'moment';
import { renameProp } from 'recompose';

const ExpiringCard = ({ lease: { url, name, date, property } }) => (
  <a className="ui card" href={url}>
    <div className="content">
      <div className="header">
        {property}
      </div>
      <div className="meta">
        {name}
      </div>
      <div className="description">
        Expires {moment(date).fromNow()}
      </div>
    </div>
  </a>
);

ExpiringCard.propTypes = {
  lease: PropTypes.shape({
    date: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    property: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
  }).isRequired,
};

export default renameProp('item', 'lease')(ExpiringCard);
