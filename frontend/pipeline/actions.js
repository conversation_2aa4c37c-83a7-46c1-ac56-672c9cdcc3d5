import { forEach } from 'lodash';

export const SET_PIPELINE_FILTER = 'SET_PIPELINE_FILTER';
export const setPipelineFilter = filter => (dispatch, getState) => {
  dispatch({ type: SET_PIPELINE_FILTER, filter });
  fetchPipeline(dispatch, getState);
};

export const RECEIVE_PIPELINE_ENTRIES = 'RECEIVE_PIPELINE_ENTRIES';
export const receiveEntries = (source, entries) =>
  ({ type: RECEIVE_PIPELINE_ENTRIES, source, entries });

export const START_LOADING_PIPELINE = 'START_LOADING_PIPELINE';
export const startLoadingPipeline = source => ({ type: START_LOADING_PIPELINE, source });

export const fetchPipeline = (dispatch, getState) => {
  const { pipeline, pipeline: { filter } } = getState();

  forEach(['leads', 'applicants', 'expiring', 'waitlist'], source => {
    const { lastCount, items, loading } = pipeline[source];

    if (!loading && lastCount !== 0) {
      dispatch(startLoadingPipeline(source));

      const offset = items.length;

      $.get(`/leasing/pipeline/${source}.json?offset=${offset}&filter=${filter}`,
        entries => dispatch(receiveEntries(source, entries)));
    }
  });
};
