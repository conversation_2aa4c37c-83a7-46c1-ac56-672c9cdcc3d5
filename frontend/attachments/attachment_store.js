class AttachmentStore {
  constructor(attachmentsUrl, callbacks = {}) {
    this.attachmentsUrl = attachmentsUrl;
    this.callbacks = callbacks;
  }

  createResource(file) {
    return this.getSignedUploadUrl(file)
      .then(({ url, content_type }) => this.uploadAttachment(file, url, content_type))
      .then(downloadUrl => this.saveAttachment(file, downloadUrl));
  }

  destroyResource(attachment) {
    const url = `${this.attachmentsUrl}/${attachment.id}`;

    return $.ajax({
      url,
      type: 'DELETE',
    });
  }

  getSignedUploadUrl(file) {
    const url = `${this.attachmentsUrl}/signed_url`;

    return $.ajax({
      url,
      type: 'GET',
      dataType: 'json',
      data: { filename: file.name },
    });
  }

  uploadAttachment(file, uploadUrl, contentType) {
    const deferred = new $.Deferred();

    const xhr = new XMLHttpRequest();
    xhr.open('PUT', uploadUrl, true);
    xhr.setRequestHeader('Content-Type', contentType);

    const { onProgress } = this.callbacks;

    xhr.onload = () => {
      if (xhr.status === 200) {
        if (onProgress) {
          onProgress(file, file.size);
        }

        deferred.resolve(uploadUrl.split('?')[0]);
      } else {
        deferred.reject(xhr);
      }
    };

    xhr.onerror = () => deferred.reject(xhr);

    xhr.upload.onprogress = e => {
      if (e.lengthComputable) {
        if (onProgress) {
          onProgress(file, e.loaded);
        }
      }
    };

    xhr.send(file);

    return deferred.promise();
  }

  saveAttachment(file, downloadUrl) {
    const url = this.attachmentsUrl;

    return $.ajax({
      url,
      type: 'POST',
      dataType: 'json',
      data: {
        attachment: {
          direct_upload_url: downloadUrl,
          upload_file_name: file.name,
          upload_content_type: file.type,
          upload_file_size: file.size,
        },
      },
    });
  }
}

export default AttachmentStore;
