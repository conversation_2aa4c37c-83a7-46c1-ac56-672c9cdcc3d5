import PropTypes from 'prop-types';

const isImage = (attachment) => attachment.content_type.startsWith('image');

const Thumbnail = ({ attachment, small }) => {
  const { filename, url } = attachment;

  const size = small ? '60px' : '120px';
  const iconSize = small ? 'large' : 'huge';
  const fontSize = small ? '0.5em' : '0.8em';

  const content = isImage(attachment) ? (
    <img
      src={url} className="ui small rounded image"
      style={{ width: size, height: size, objectFit: 'cover' }}
    />
  ) : (
    <div
      style={{
        width: size,
        height: size,
        display: 'inline-flex',
        flexDirection: 'column',
        justifyContent: 'space-around',
        alignItems: 'center',
        border: '1px solid gray',
        borderRadius: '4px',
      }}
    >
      <i
        className={`ui ${iconSize} file text outline icon`}
      />
      <div style={{ fontSize }}>
        {_.truncate(filename, { length: 15 })}
      </div>
    </div>
  );

  return content;
};

Thumbnail.propTypes = {
  attachment: PropTypes.object.isRequired,
  small: PropTypes.bool.isRequired,
};

Thumbnail.defaultProps = {
  small: false,
};

export default Thumbnail;
