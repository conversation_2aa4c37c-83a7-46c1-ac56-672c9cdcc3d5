import thunk from 'redux-thunk';
import { Component } from 'react';
import { Provider, connect } from 'react-redux';
import { applyMiddleware, combineReducers, createStore } from 'redux';

import ActionBar from '../action_bar/action_bar.jsx';

import Bookings from '../leasing/bookings/bookings';
import Pipeline from '../pipeline/pipeline.jsx';
import pipeline from '../pipeline/reducer';
import { fetchPipeline } from '../pipeline/actions';

const modeHeader = (state = { mode: 'vert' }, action) => {
  switch (action.type) {
    case 'CHANGE_MODE':
      return _.assign({}, state, { mode: action.mode });
    default:
      return state;
  }
};

const modeContainer = (state = { data: [] }, action) => {
  switch (action.type) {
    case 'SET_DATA':
      return _.assign({}, state, { data: action.data });
    default:
      return state;
  }
};

const reducer = combineReducers({
  modeHeader,
  modeContainer,
  pipeline,
});

const store = createStore(reducer, applyMiddleware(thunk));

const changeMode = (mode) => ({
  type: 'CHANGE_MODE',
  mode,
});

const ModeButtons = connect(
  (state) => ({
    mode: state.modeHeader.mode,
  }),
  { changeMode }
)(({ mode, changeMode }) => (
  <div className="ui tiny icon mode buttons">
    <button
      className={`ui ${mode === 'vert' ? 'active' : ''} icon button`}
      onClick={() => changeMode('vert')}
    >
      <i className="sidebar icon" style={{ transform: 'rotate(90deg)' }} />
      Pipeline
    </button>

    <button
      className={`ui ${mode === 'horz' ? 'active' : ''} icon button`}
      onClick={() => changeMode('horz')}
    >
      <i className="sidebar icon" />
      Availability
    </button>
  </div>
));

const ActionButtons = () => (
  <div className="action buttons">
    <a className="ui button" href="/leasing/guest_cards/new">
      New Guest Card
    </a>
    <a className="ui button" href="/leasing/lease_application_invites/new">
      New Application
    </a>
    <a className="ui button" href="/leasing/leases/new">
      New Lease
    </a>
  </div>
);

const ModeContainer = connect(
  (state) => ({
    mode: state.modeHeader.mode,
    data: state.modeContainer.data,
  }),
  null
)(({ mode, data }) => (
  <div className="action-container" style={{ overflowY: 'scroll' }}>
    <Pipeline
      visible={mode === 'vert'}
      data={data}
    />
    <Bookings visible={mode === 'horz'} />
  </div>
));

class ModeWrapper extends Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    store.dispatch(fetchPipeline);
  }

  render() {
    return (
      <Provider store={store}>
        <div className="mode-wrapper">
          <ActionBar
            title="Leasing"
            modeButtons={<ModeButtons />}
            actionButtons={<ActionButtons />}
          />

          <ModeContainer />
        </div>
      </Provider>
    );
  }
}

export default ModeWrapper;
