import PropTypes from 'prop-types';
import Recaptcha from 'react-recaptcha';
import classNames from 'classnames';
import qs from 'qs';
import { Component } from 'react';
import { chunk, map, partition, snakeCase } from 'lodash';

import CalendarField from '../forms/fields/calendar_field';
import RailsForm from '../forms/rails_form';
import Dropdown from '../selectors/dropdown';
import { formatMoney } from '../util/money';

const recaptchaSiteKey = '6LepIjsUAAAAANYFTrBEkW5orO7a5LEJjglv9s8X' // Production;
// const recaptchaSiteKey = '6LfLIDsUAAAAAAmgMeJngY1wJNs47_LvMmN7vnox'; // Development

class NewGuestCard extends Component {
  static propTypes = {
    action: PropTypes.string.isRequired,
    recaptcha: PropTypes.bool.isRequired,
  };

  static defaultProps = {
    action: '/leasing/guest_cards',
    recaptcha: false,
    options: {},
  };

  constructor(props) {
    super(props);

    const { options } = props;

    this.state = {
      floorplans: [],
      selectProperty: options.selectProperty !== false, // Should the user pick a property
      selectFloorplan: options.selectFloorplan !== false, // Should the user pick a floorplan
      selectSource: options.selectSource !== false, // Should the user pick a source
      extraFields: options.extraFields || [],
      recaptchaVerified: false,
      startDates: options.startDates || [],
      extraParams: options.extraParams || [],
    };
  }

  componentDidMount() {
    $('#new_guest_card').form({
      inline: false,
      on: 'submit',
      fields: {
        guest_card_start_date: {
          identifier: 'guest_card_start_date',
          rules: [
            {
              type: 'checked',
              prompt: 'Please select a start date',
            }
          ]
        },
        guest_card_start_gender: {
          identifier: 'guest_card_gender',
          rules: [
            {
              type: 'checked',
              prompt: 'Please select a gender',
            }
          ]
        },
        guest_card_first_name: {
          identifier: 'guest_card_first_name',
          rules: [
            {
              type: 'empty',
              prompt: 'Please enter your first name',
            }
          ]
        },
        guest_card_last_name: {
          identifier: 'guest_card_last_name',
          rules: [
            {
              type: 'empty',
              prompt: 'Please enter your last name',
            }
          ],
        },
        guest_card_banner_id: {
          identifier: 'guest_card_banner_id',
          rules: [
            {
              type: 'empty',
              prompt: 'Please enter your Banner ID#',
            },
            {
              type: 'regExp',
              value: /^(b|B)/,
              prompt: 'Banner ID should start with a B'
            }
          ],
        },
      },
    });
  }

  handlePropertyChanged = ({ floorplans }) => this.setState({ floorplans });

  recaptchaOnload = () => { };

  recaptchaVerified = () => this.setState({ recaptchaVerified: true });

  renderExtraFields = extraFields => {
    const [sections, fields] = partition(extraFields, f => f.fields || f.units);

    return [
      this.renderFields(fields),
      map(sections, this.renderSection),
    ];
  }

  renderSection = section => {
    if (section.type === 'gender') {
      return this.renderGenderField();
    }

    return (
      <div style={{ padding: '1.5em 0' }}>
        <h5 className="ui dividing header">
          {section.label}
        </h5>
        <div style={{ padding: '0 1em' }}>
          {section.fields && this.renderFields(section.fields)}
          {section.units && this.renderUnitPreferences(section.units, section.name)}
        </div>
      </div>
    );
  }

  renderFields = fields => map(chunk(fields, 2), fields => ( // Group equal width upto 2
    <div className="two fields">
      {map(fields, ({ name, label, required, validate }) => {
        const id = `extra_${snakeCase(name)}`;

        return (
          <div className={classNames('ui', { required: required === 'true' }, 'field')}>
            <label htmlFor={id}>{label}</label>
            <input
              id={id}
              type="text"
              name={`guest_card[data][${name}]`}
              data-validate={validate}
            />
          </div>
        );
      })}
    </div>
  ));

  renderGenderField = () => {
    const genderOptions = [
      {
        value: 'Male',
        label: 'Male',
      },
      {
        value: 'Female',
        label: 'Female',
      },
      {
        value: 'Non-binary',
        label: 'Non-binary',
      },
    ];

    return [
      <div className="grouped required fields">
        {map(genderOptions, (option, i) => (
          <div className="field">
            <div className="ui radio checkbox">
              <label>{option.label}</label>
              <input
                id="guest_card_gender"
                name="guest_card[data][gender]"
                type="radio"
                value={option.value}
                data-validate="guest_card_gender"
              />
            </div>
          </div>
        ))}
      </div>,
      <center>
        <i>
          Please choose Option 1, 2, or 3 below and rate the units according to your preference.
          <br />
          You may choose more than one of the three options; however, please note in the
          <br />
          “additional details” field at the end of the application which Option is your preference
        </i>
        <div className="ui list">
          <div className="item">OPTION #1 = Studio</div>
          <div className="item">OPTION #2 = 1-Bedroom</div>
          <div className="item">OPTION #3 = Shared multi-bedroom</div>
        </div>
      </center>
    ];
  };

  renderRoommateField = namespace => index => (
    <div className="four fields">
      <div className="field">
        {index === 1 && <label>Name of Roommate</label>}
        <input
          type="text"
          name={`${namespace}[${index}][name]`}
          placeholder={`Roommate ${index}`}
        />
      </div>
      <div className="field">
        {index === 1 && <label>Roomate Email Address</label>}
        <input type="email" name={`${namespace}[${index}][email]`} />
      </div>
      <div className="field">
        {index === 1 && <label>Is Roommate a 1st-year graduate?</label>}
        <input
          type="text"
          name={`${namespace}[${index}][year]`}
        />
      </div>
      <div className="field">
        {index === 1 && <label>Roommate Banner #</label>}
        <input type="text" name={`${namespace}[${index}][banner_id]`} />
      </div>
    </div>
  );

  renderUnitPreferences = (units, namespace) => (
    <div>
      <i>Please number in order of preference (1 - {units.filter(u => u.name).length}).</i>
      <table className="ui very basic fixed small compact celled table">
        <thead>
          <tr>
            <th>Preference</th>
            <th>Property</th>
            <th>Unit</th>
            <th>Price</th>
            <th>Restrictions</th>
          </tr>
        </thead>
        <tbody>
          {units.map((unit, index) => (
            <tr key={index}>
              <td>
                {unit.label !== 'APARTMENTS' && unit.label !== 'HOUSES' && (
                  <input
                    type="text"
                    name={`guest_card[data][${namespace}][${unit.name}]`}
                  />
                )}
              </td>
              <td>{unit.label}</td>
              <td>{unit.description}</td>
              <td>{unit.price}</td>
              <td>{unit.restriction}</td>
            </tr>
          ))}
        </tbody>
      </table>
      {namespace === 'option_3][preferences' && (
        <div>
          <div className="field">
            <div className="ui radio checkbox">
              <label>Please assign my roommate/roommates</label>
              <input
                name="guest_card[data][option_3][roommate_selection]"
                type="radio"
                value="Please assign my roommates"
              />
            </div>
          </div>
          <div className="field">
            <div className="ui radio checkbox">
              <label>I have pre-selected my roommates</label>
              <input
                name="guest_card[data][option_3][roommate_selection]"
                type="radio"
                value="I have pre-selected roommates"
              />
            </div>
          </div>
          {map([1, 2, 3], this.renderRoommateField('guest_card[data][option_3][roommates]'))}
        </div>
      )}
    </div>
  );

  render() {
    const { action, recaptcha } = this.props;

    const {
      extraFields,
      extraParams,
      recaptchaVerified,
      selectFloorplan,
      selectProperty,
      selectSource,
      startDates,
    } = this.state;

    const mapFloorplans = floorplans => (
      map(floorplans, floorplan => {
        const { name, price_cents } = floorplan;
        return { ...floorplan, name: `${name} - ${formatMoney(price_cents, true)}` };
      })
    );

    const recaptchaDisabled = recaptcha && !recaptchaVerified;

    return (
      <div className="ui clearing guest-card segment">
        <RailsForm
          id="new_guest_card"
          action={action}
        >
          {map(extraParams, ({ name, value }) => (
            <input type="hidden" name={name} value={value} />
          ))}

          <div className="ui two fields">
            <div className="ui field">
              <h1 className="ui welcome header">
                Welcome!
              </h1>
            </div>
          </div>

          {window.location.host.startsWith('brown.') &&
            <div className="ui field">
              <div className="ui checkbox">
                <input
                  type="checkbox"
                  id="guest_card_first_year"
                  name="guest_card[data][first_year_graduate_student]"
                />
                <label htmlFor="guest_card_first_year">
                  I am a first-year graduate student
                </label>
              </div>
            </div>
          }

          <div className="ui two fields">
            {selectProperty &&
              <div className="ui required field">
                <label htmlFor="guest_card_property">Property</label>
                <Dropdown
                  fluid
                  defaultText="Select Property"
                  id="guest_card_property"
                  name="guest_card[property_id]"
                  onChange={this.handlePropertyChanged}
                  url="/manage/properties.json?include_floorplans=true"
                />
              </div>
            }
            {selectFloorplan &&
              <div className="ui required field">
                <label htmlFor="guest_card_floorplan">Floorplan</label>
                <Dropdown
                  fluid
                  defaultText="Select Floorplan"
                  id="guest_card_floorplan"
                  name="guest_card[floorplan_id]"
                  items={mapFloorplans(this.state.floorplans)}
                />
              </div>
            }
          </div>

          {startDates.length === 0 &&
            <div className="ui two fields">
              <CalendarField
                className="ui required calendar field"
                id="guest_card_date"
                label="Approximate Move-In Date"
                name="guest_card[move_in_date]"
                placeholder="Move-In"
              />
              <CalendarField
                className="ui calendar field"
                id="guest_card_date"
                label="Approximate Move-Out Date"
                name="guest_card[data][move_out_date]"
                placeholder="Move-Out"
              />
            </div>
          }

          {startDates.length > 0 &&
            <div className="grouped required fields">
              <label htmlFor="guest_card_date">Start Date</label>
              {map(startDates, (startDate, i) => (
                <div className="field">
                  <div className="ui radio checkbox">
                    <input
                      id="guest_card_date"
                      name="guest_card[move_in_date]"
                      type="radio"
                      value={startDate}
                      data-validate="guest_card_start_date"
                    />
                    <label>{startDate}</label>
                  </div>
                </div>
              ))}
            </div>
          }

          <div className="ui two fields">
            <div className="ui required field">
              <label htmlFor="guest_card_first_name">First Name</label>
              <input
                name="guest_card[data][first_name]"
                id="guest_card_first_name"
              />
            </div>
            <div className="ui required field">
              <label htmlFor="guest_card_last_name">Last Name</label>
              <input
                name="guest_card[data][last_name]"
                id="guest_card_last_name"
              />
            </div>
          </div>
          <div className="ui two fields">
            <div className="ui required field">
              <label htmlFor="guest_card_email">Email</label>
              <input
                name="guest_card[data][email]"
                id="guest_card_email"
              />
            </div>
            <div className="ui required field">
              <label htmlFor="guest_card_phone">Phone</label>
              <input
                name="guest_card[data][phone]"
                id="guest_card_phone"
              />
            </div>
          </div>

          {selectSource &&
            <div className="ui required field">
              <label htmlFor="guest_card_source">Source</label>
              <Dropdown
                allowAdditions
                defaultText="Source"
                fluid
                id="guest_card_source"
                multiple={false}
                name="source"
                textField="source"
                url="/guests/sources"
                valueField="source"
              />
            </div>
          }

          {this.renderExtraFields(extraFields)}

          <div className="ui field">
            <label htmlFor="guest_card_notes">
              Any additional details that you would like to provide?
            </label>
            <textarea
              name="guest_card[data][notes]"
              id="guest_card_notes"
              rows="2"
            />
          </div>

          {recaptcha &&
            <Recaptcha
              render="explicit"
              sitekey={recaptchaSiteKey}
              verifyCallback={this.recaptchaVerified}
              onloadCallback={this.recaptchaOnload}
            />
          }

          <div className="ui error message" />

          <button
            className={classNames('ui primary', { disabled: recaptchaDisabled }, 'submit button')}
            type="submit"
            data-disable-with="Submitting"
          >
            Submit
          </button>
        </RailsForm>
      </div>
    );
  }
}

export default NewGuestCard;
