import { Component } from 'react';
import { pickBy } from 'lodash';
import PropTypes from 'prop-types';

import Dropdown from '../../../selectors/dropdown';
import RailsForm from '../../../forms/rails_form';
import ActionBar from '../../../action_bar/action_bar';

const classNames = require('classnames');

class LeaseApplicationInvitation extends Component {
  constructor(props) {
    super(props);
    this.state = { ...props.lead };
  }

  setDefaultProperty() {
    if (this.state.property) {
      return this.state.property.id;
    }
    return undefined;
  }

  setDefaultFloorplan() {
    if (this.state.property) {
      return this.state.property.floorplan_id;
    }
    return undefined;
  }

  changeProperty = property => this.setState({ property });
  changeFloorplan = floorplan => this.setState({ floorplan });

  render() {
    const generatePropertyUrl = property => (
      property ? `/leasing/leases/available_floorplans.json?property_id=${property.id}` : null
    );

    const fillOutDisabled = !this.state.floorplan;
    const sendDisabled = !this.state.floorplan || !this.state.email;

    const link = new URI('/apply');
    if (this.state.floorplan) {
      const filters = {
        email: this.state.email,
        floorplan_id: this.state.floorplan.id,
        lead_id: this.state.id,
      };
      const params = pickBy(filters);
      link.addSearch(params);
    }

    return (
      <div className="lease-application-invitation">
        <ActionBar
          title="Lease Application Invitation"
        />
        <div className="action-container">
          <div className="ui container">
            <div style={{ paddingTop: '2em' }}>
              <h3 className="ui dividing header">
                Property Information
              </h3>
              <RailsForm
                action="/leasing/lease_application_invites"
                id="lease_application_invite"
              >
                <input
                  type="hidden"
                  value={this.state.id}
                  name="lease_application[lead_id]"
                />
                <div className="ui grid" style={{ paddingTop: '1.2em' }}>
                  <div className="six wide column">
                    <div className="ui segment" style={{ textAlign: 'justify' }}>
                      Select a property and a floorplan to
                      invite the applicant to apply to. The
                      lease application invitation sent will
                      be for the selected property and floorplan.
                    </div>
                  </div>
                  <div className="ten wide column">
                    <div className="ui form">
                      <div className="two fields">
                        <div className="field">
                          <label htmlFor="property">Property</label>
                          <Dropdown
                            id="property"
                            name="lease_application[property_id]"
                            onChange={this.changeProperty}
                            url="/manage/properties.json"
                            defaultSelection={this.setDefaultProperty()}
                          />
                        </div>
                        <div className="field">
                          <label htmlFor="floorplan">Floorplan</label>
                          <Dropdown
                            id="floorplan"
                            name="lease_application[floorplan_id]"
                            url={generatePropertyUrl(this.state.property)}
                            defaultSelection={this.setDefaultFloorplan()}
                            selectFirst="single"
                            onChange={this.changeFloorplan}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <h3 className="ui dividing header">
                  Applicant Email
                </h3>
                <div className="ui grid" style={{ paddingTop: '1.2em' }}>
                  <div className="eight wide column">
                    <div className="ui segment" style={{ textAlign: 'justify' }}>

                      Enter the applicant&apos;s email address. This is
                      the address that the lease application
                      invitation will be sent to. In this email will
                      be a link to the lease application.
                    </div>
                  </div>
                  <div className="eight wide column">
                    <div className="ui form">
                      <div className="field">
                        <label htmlFor="email">Email Address</label>
                        <input
                          defaultValue={this.state.email}
                          type="email"
                          id="email"
                          name="lease_application[email]"
                          value={this.state.email}
                          onChange={event => {
                            this.setState({ email: event.target.value });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div style={{ paddingTop: '1.2em' }} >
                  <div className="ui right floated buttons">
                    <button
                      className={classNames('ui', { disabled: sendDisabled }, 'button')}
                      onSubmit={this.handleSubmit}
                    >
                      Send Application
                    </button>
                    <div className="or" />
                    <a
                      className={classNames('ui', { disabled: fillOutDisabled }, 'button')}
                      href={link}
                      target="_blank"
                    >
                      Fill out Now
                    </a>
                  </div>
                </div>
              </RailsForm>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

LeaseApplicationInvitation.propTypes = {
  lead: PropTypes.object,
};


export default LeaseApplicationInvitation;
