import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import classNames from 'classnames';
import uuid from 'uuid';

class DateField extends Component {
  static propTypes = {
    className: PropTypes.string,
    id: PropTypes.string,
    input: PropTypes.object.isRequired,
    label: PropTypes.string.isRequired,
    meta: PropTypes.object,
    required: PropTypes.bool.isRequired,
  }

  static defaultProps = {
    required: false,
    id: uuid.v4(),
    input: {},
    meta: {},
  }

  componentDidMount() {
    const node = findDOMNode(this);
    // const { input: { onChange } } = this.props;
    // const options = { type: 'date', onChange };
    const options = { type: 'date' };
    $(node).calendar(options);
  }

  render() {
    const { className, input, id, label, required, meta: { error, touched } } = this.props;

    return (
      <div className={className || classNames({ required, error: error && touched }, 'ui calendar field')}>
        <label htmlFor={id}>
          {label}
        </label>
        <div className="ui fluid left icon input">
          <input
            id={id}
            type="text"
            {...input}
          />
          <i className="calendar alternate outline icon" />
        </div>
      </div>
    );
  }
}

export default DateField;
