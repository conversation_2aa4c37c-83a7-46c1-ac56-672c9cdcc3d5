import PropTypes from 'prop-types';
import { map } from 'lodash';
import moment from 'moment';

const formLayout = {
  pages: [
    {
      title: 'About Me',
      fields: [
        { key: 'first_name', label: 'First Name' },
        { key: 'middle_name', label: 'Middle Name' },
        { key: 'last_name', label: 'Last Name' },
        { key: 'birthdate', label: 'Birth Date' },
        { key: 'email', label: 'Email' },
        { key: 'phone', label: 'Home Phone' },
        { key: 'cellphone', label: 'Cell Phone' },
        { key: 'dl', label: 'Drivers License Number' },
        { key: 'license_state', label: 'State' },
        { key: 'ssn', label: 'Social Security Number' },
      ],
    },
    {
      title: 'Rental History',
      fields: [
        { key: 'rentals', type: 'array', label: 'Residence', fields: [
          { key: 'street_address', label: 'Street Address' },
          { key: 'city', label: 'City' },
          { key: 'state', label: 'State' },
          { key: 'zip', label: 'Zip' },
          { key: 'rent', label: 'Monthly Rent' },
          { key: 'dates', label: 'Dates of Residency' },
          { key: 'owner', label: 'Owner or Manager Name' },
          { key: 'owner_phone', label: 'Manager Phone' },
          { key: 'reason', label: 'Reason for Moving' },
        ] },
      ],
    },
    {
      title: 'Employment History',
      fields: [
        { key: 'employments', type: 'array', label: 'Employer', fields: [
          { key: 'name', label: 'Employer Name' },
          { key: 'occupation', label: 'Occupation' },
          { key: 'address', label: 'Address' },
          { key: 'phone', label: 'Phone' },
          { key: 'supervisor', label: 'Supervisor' },
          { key: 'dates', label: 'Dates' },
          { key: 'wage', label: 'Monthly Pay' },
        ] },
      ],
    },
    {
      title: 'General Information',
      description: `
      If you answer yes to any of the following questions, please provide a brief explanation.
      `,
      fields: [
        { key: 'move_in_date', label: 'Estimated Move-in Date' },
        { key: 'pets', label: 'Do you have any pets?' },
        { key: 'vehicles', label: 'Do you have any vehicles?' },
        { key: 'other_residents', label: 'Will there be anybody else living with you?' },
        { key: 'smoke', label: 'Do any of the people who would be living in the apartment smoke?' },
        { key: 'late_rent', label: 'Have you ever been served a late rent notice?' },
        { key: 'eviction', label: 'Have you ever been served an eviction notice?' },
        { key: 'conviction', label: 'Have you ever been convicted of a felony or misdemeanor?' },
        {
          key: 'signature', type: 'image', label: 'Signature', description: `
          By signing this application, I verify that the statements in this
          application are true and correct. I authorize the use of the information
          and contacts provided to complete a credit, reference, and/or background
          check. I understand that false or lack of information may result in the
          rejection of this application.
          `,
        },
      ],
    },
  ],
};


const LeaseApplication = ({ application }) => {
  let renderField;

  const renderFieldArray = (field, context, i) => {
    const array = context[field.key];
    return (
      <div key={i}>
        {map(array, (entry, index) => (
          <div className="section" key={index}>
            <h3 className="ui header">
              {index === 0 ? 'Current' : 'Previous'} {field.label}
            </h3>
            {map(field.fields, (f, j) => renderField(f, array[index], j))}
          </div>
        ))}
      </div>
    );
  };

  renderField = (field, context, index) => {
    const value = context[field.key];

    if (field.type === 'array') return renderFieldArray(field, context, index);

    const element = field.type === 'image' ? (
      <img src={value} width={320} alt="signature" />
    ) : value;

    return (
      <div className="field" key={index}>
        <h5 className="ui header">
          {field.label}
        </h5>
        {field.description && <i className="description">{field.description}</i>}
        {element}
      </div>
    );
  };

  const renderPage = (page, index) => (
    <div className="page" key={index}>
      <h2 className="ui dividing header">
        {page.title}
      </h2>
      {page.description && <i className="description">{page.description}</i>}
      {map(page.fields, (field, index) => renderField(field, application.form, index))}
    </div>
  );

  const name = `${application.form.first_name} ${application.form.last_name}`;

  return (
    <div className="printable paper lease application">
      <h1 className="ui title header">
        {name}
        <div className="sub header">
          Lease Application from {moment(application.submitted_at).format('l')}
        </div>
      </h1>

      {map(formLayout.pages, renderPage)}
    </div>
  );
};

LeaseApplication.propTypes = {
  application: PropTypes.object.isRequired,
};

export default LeaseApplication;
