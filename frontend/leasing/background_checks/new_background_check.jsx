import PropTypes from 'prop-types';
import { Component } from 'react';

import Dropdown from '../../selectors/dropdown';
import RailsForm from '../../forms/rails_form';
import { formatMoney } from '../../util/money';

const packageType = PropTypes.shape({
  id: PropTypes.number.isRequired,
  cost: PropTypes.number.isRequired,
  name: PropTypes.string.isRequired,
});

const PackageDescription = ({ selectedPackage }) => (
  <div>
    {formatMoney(selectedPackage.cost, true)}
  </div>
);

PackageDescription.propTypes = {
  selectedPackage: packageType.isRequired,
};

class NewBackgroundCheck extends Component {
  static propTypes = {
    packages: PropTypes.arrayOf(packageType).isRequired,
    leadId: PropTypes.string.isRequired,
  };

  constructor(props) {
    super(props);
    this.onPackageChanged = this.handlePackageChanged.bind(this);
    this.state = { selectedPackage: null };
  }

  handlePackageChanged(selectedPackage) {
    this.setState({ selectedPackage });
  }

  render() {
    const { packages, leadId } = this.props;

    const { selectedPackage } = this.state;

    const packageDescription = selectedPackage ?
      <PackageDescription selectedPackage={selectedPackage} /> :
      null;

    return (
      <RailsForm
        action="/leasing/background_checks"
        id="new_background_check"
      >
        <div className="required field">
          <label htmlFor="package_id">
            Package
          </label>
          <Dropdown
            id="package_id"
            items={packages}
            name="background_check[package_id]"
            onChange={this.onPackageChanged}
          />
        </div>
        {packageDescription}
        <input type="hidden" name="lead_id" value={leadId} />
        <button className="ui right floated button">
          Submit
        </button>
      </RailsForm>
    );
  }
}

export default NewBackgroundCheck;
