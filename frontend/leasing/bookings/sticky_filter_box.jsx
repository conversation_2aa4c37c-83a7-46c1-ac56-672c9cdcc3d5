import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';

class StickyFilterBox extends Component {
  static propTypes = {
    children: PropTypes.node.isRequired,
  };

  componentDidMount() {
    this.node = findDOMNode(this);
    // $(this.node).sticky();
  }

  render() {
    return (
      <div className="ui bottom attached segment" style={{
        top: 'unset',
        bottom: 'unset',
        margin: '-96px 0 0',
        width: 'inherit',
        position: 'fixed',
        zIndex: 1000,
        boxShadow: '0 5px 5px 0 rgba(0, 0, 0, 0.05)',
        borderTop: 'none',
      }}
      >
        {this.props.children}
      </div>
    );
  }
}

export default StickyFilterBox;
