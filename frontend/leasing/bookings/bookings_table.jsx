import { extent, select, axisTop, axisLeft, scaleTime, scaleBand } from 'd3';
import { flatMap, map } from 'lodash';
import moment from 'moment';
import URI from 'urijs';

import AutoWidthChart from '../../charts/auto_width_chart.jsx';
import D3Wrapper from '../../charts/d3_wrapper.jsx';
import { colors } from '../../util/color.js';

const dateDomain = data => {
  const dates = map(
    flatMap(data.units,
      unit => flatMap(unit.bookings,
        booking => [booking.start_date, booking.end_date])),
    date => moment(date));

  const [dataMin, dataMax] = extent(dates);

  const start = data.startDate ? moment(data.startDate) : dataMin;
  const end = data.endDate ? moment(data.endDate) : dataMax;

  return [start, end];
};

class BookingsTableD3 {
  constructor(element, props) {
    const { width, height, margin } = props;

    this.svg = select(element)
      .append('svg')
      .attr('width', width)
      .attr('height', height);

    this.graph = this.svg
      .append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);

    /*
     * Clip Path
     */
    this.clipRect = this.graph.append('clipPath')
      .attr('id', 'clip-path')
      .append('rect');

    /*
     * X Axis
     */
    this.x = scaleTime();

    this.xaxis = axisTop().scale(this.x).ticks(7);

    this.graph.append('g').attr('class', 'x axis');

    /*
     * Y Axis
     */
    this.y = scaleBand().padding(0);

    this.yaxis = axisLeft().scale(this.y);

    this.graph.append('g').attr('class', 'y axis');

    this.resize(element, props);
    this.update(element, props);
  }

  resize(element, props) {
    const { width, height, margin } = props;

    this.gwidth = width - margin.left - margin.right;
    this.gheight = 30 * props.data.units.length;

    this.svg.attr('height', this.gheight + margin.top + margin.bottom);

    this.clipRect
      .attr('width', this.gwidth)
      .attr('height', this.gheight);

    this.xaxis
      .tickSizeInner(-(this.gheight));

    this.yaxis
      .tickSizeOuter(-(this.gwidth));
  }

  update(element, props) {
    this.resize(element, props);

    const { data } = props;

    const unitData = data.units;

    const animationDuration = 300;

    /*
     * X Axis
     */
    this.x
      .domain(dateDomain(data))
      .range([0, this.gwidth]);

    this.graph.selectAll('.x.axis')
      .transition()
      .duration(animationDuration)
      .call(this.xaxis);

    /*
     * Y Axis
     */
    this.y
      .domain(map(unitData, unit => unit.name))
      .range([0, this.gheight]);

    this.graph.selectAll('.y.axis').call(this.yaxis);

    // Unit Links
    const clickUnit = unit => Turbolinks.visit(`/manage/units/${unit.id}`);

    this.graph.selectAll('.y.axis text')
      .style('cursor', 'pointer')
      .on('click', (d, i) => clickUnit(unitData[i]));

    /*
     * Unit Rows
     */
    const units = this.graph.selectAll('.unit')
      .data(unitData);

    const enterUnits = units
      .enter()
      .append('g')
      .attr('class', 'unit');

    // Divider Line
    enterUnits.filter((d, i) => i > 0)
      .append('line')
      .attr('y1', 0)
      .attr('y2', 0)
      .attr('x1', 0)
      .attr('x2', this.gwidth)
      .attr('stroke-width', 1)
      .attr('stroke', 'black');

    const mergeUnits = enterUnits
      .merge(units)
        .attr('transform', (d, i) => `translate(0, ${i * this.y.step()})`);

    units.exit()
      .remove();

    /*
     * Booking Rectangles
     */
    const bookings = mergeUnits.selectAll('g').data(u => u.bookings);

    const clickBooking = booking => {
      if (booking.type === 'availability') {
        const url = new URI('/leasing/leases/new');
        const startDate = booking.start_date || dateDomain(data)[0].format('l');
        const endDate = booking.end_date || dateDomain(data)[1].format('l');

        url.addSearch({ unit_id: booking.unit_id, start_date: startDate, end_date: endDate });

        Turbolinks.visit(url.resource());
      } else {
        Turbolinks.visit(`/leasing/leases/${booking.id}`);
      }

      Turbolinks.visit(url);
    };

    const fillColor = booking => (booking.type === 'availability' ? colors.green : colors.red);
    const opacity = booking => (booking.type === 'availability' ? 0.2 : 0.5);
    const hoverOpacity = booking => (booking.type === 'availability' ? 0.5 : 0.75);
    const width = booking => {
      const b = booking.end_date ? this.x(moment(booking.end_date)) : this.gwidth;
      const a = booking.start_date ? this.x(moment(booking.start_date)) : 0;
      // undefined start date, real end date off left of time scale causes - width
      const w = Math.max(b - a, 0);
      return w;
    };

    const bookingX = booking => (booking.start_date ? this.x(moment(booking.start_date)) : 0);

    const gs = bookings
      .enter()
      .append('g');

    gs.append('rect')
      .style('transition', 'opacity .2s')
      .style('cursor', 'pointer')
      .attr('rx', 6)
      .attr('ry', 6)
      .attr('clip-path', 'url(#clip-path)')
      .style('fill', fillColor)
      .style('opacity', opacity);

    gs.append('text').style('opacity', opacity);

    const rects = mergeUnits.selectAll('rect').data(u => u.bookings)
      .on('click', clickBooking)
      .on('mouseover', function (d) { d3.select(this).style('opacity', hoverOpacity(d)); })
      .on('mouseout', function (d) { d3.select(this).style('opacity', opacity(d)); })
      .attr('height', this.y.bandwidth() * 0.8)
      .attr('y', this.y.bandwidth() * 0.1);

    rects
      .append('title');

    mergeUnits.selectAll('title').data(u => u.bookings)
      .text(d => {
        const format = date => moment(date).format('MM/DD/YYYY (ddd)');

        if (d.tenants) {
          return `${d.tenants} - ${format(d.start_date)} - ${format(d.end_date)}`;
        } else if (d.end_date) {
          return `Available from ${format(d.start_date)} - ${format(d.end_date)}`;
        }

        return `Available from ${format(d.start_date)}`;
      });

    rects
      .transition()
      .duration(animationDuration)
      .attr('x', bookingX)
      .attr('width', width)
      .style('fill', fillColor)
      .style('opacity', opacity);

    mergeUnits.selectAll('text').data(u => u.bookings)
      .on('click', clickBooking)
      .attr('x', d => (bookingX(d) + 3))
      .attr('y', this.y.bandwidth() * 0.5)
      .attr('dy', '.35em')
      .style('fill', 'white')
      .style('opacity', 1)
      .style('pointer-events', 'none')
      .text(d => d.tenants);

    bookings.exit()
      .remove();
  }

  destroy() {

  }
}

class BookingsTable extends D3Wrapper {
  createChart(node, state) {
    return new BookingsTableD3(node, state);
  }
}

export default new AutoWidthChart(BookingsTable);
