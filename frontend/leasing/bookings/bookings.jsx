import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import moment from 'moment';
import { assign, concat, filter, first, last, map, some } from 'lodash';

import StickyFilterBox from './sticky_filter_box';
import Filters from './filters';
import BookingsTable from './bookings_table';

class Bookings extends Component {
  constructor(props) {
    super(props);
    this.state = {
      units: [],
      mode: undefined,
      rangeStart: undefined,
      rangeEnd: undefined,
      madeSelection: false,
    };
    this.onPortfolioChanged = this.handlePortfolioChanged.bind(this);
    this.onPropertyChanged = this.handlePropertyChanged.bind(this);
    this.changeStartRange = this.handleRangeChanged.bind(this, 'rangeStart');
    this.changeEndRange = this.handleRangeChanged.bind(this, 'rangeEnd');
    this.clickClear = this.handleClickClear.bind(this);
    this.changeAmenities = this.handleChangeAmenities.bind(this);
  }

  componentDidMount() {
    // this.fetchBookings();

    const all = this.showAll.bind(this);

    $(findDOMNode(this)).find('.message .close').on('click', function () {
      $(this).closest('.message').transition('fade');
      all();
    });
  }

  setFuture(span) {
    const startDate = moment();
    const endDate = startDate.clone().add(1, span);

    if (!this.state.madeSelection) {
      this.fetchBookings();
    }

    this.setState({ startDate, endDate, mode: span, madeSelection: true });
    this.clearRange();
  }

  showAll() {
    if (!this.state.madeSelection) {
      this.fetchBookings();
    }

    this.setState({ startDate: undefined, endDate: undefined, mode: 'all', madeSelection: true });
    this.clearRange();
  }

  clearRange() {
    this.setState({ rangeStart: undefined, rangeEnd: undefined });
    this.refs.filters.clearRange();
  }

  clearAmenities() {
    this.refs.filters.clearAmenities();
  }

  fetchBookings(portfolio) {
    const params = portfolio ? { portfolio } : {};
    $.get('/leasing/bookings.json', params, units =>
      this.setState({ units })
    );
  }

  handlePortfolioChanged(portfolio) {
    this.setState({ madeSelection: true });
    this.fetchBookings(`portfolio-${portfolio.id}`);
  }

  handlePropertyChanged(property) {
    this.setState({ madeSelection: true });
    this.fetchBookings(`property-${property.id}`);
  }

  handleRangeChanged(key, date) {
    this.setState({ [key]: date }, () => {
      const { rangeStart, rangeEnd } = this.state;
      if (rangeStart && rangeEnd) {
        if (!this.state.madeSelection) {
          this.fetchBookings();
        }

        this.setState({ startDate: rangeStart, endDate: rangeEnd, mode: 'custom', madeSelection: true });
      }
    });
  }

  handleClickClear() {
    this.clearRange();
    this.clearAmenities();
    this.fetchBookings();
  }

  handleChangeAmenities(amenities) {
    this.setState({ madeSelection: true });
    $.get('/leasing/bookings.json', { amenities }, units =>
      this.setState({ units })
    );
  }

  /*
   * Filter by availability in range
   */
  withinRange(units) {
    const { rangeStart, rangeEnd } = this.state;

    if (rangeStart && rangeEnd) {
      return filter(units, unit => (
        some(unit.bookings, booking => {
          if (booking.type !== 'availability') {
            return false;
          }

          const before = booking.start_date === undefined ||
            moment(booking.start_date) <= rangeStart;

          const after = booking.end_date === undefined
            || moment(booking.end_date) >= rangeEnd;

          return before && after;
        })
      ));
    }

    return units;
  }

  /*
   * Add 'available' bookings in empty spaces
   */
  withAvailabilities(units) {
    return map(units, unit => {
      const bookings = unit.bookings;
      const availabilities = [];

      const availability = (start, end) =>
        ({ type: 'availability', start_date: start, end_date: end, unit_id: unit.id });

      if (bookings.length === 0) {
        // Entire availability
        availabilities.push(availability(undefined, undefined));
      } else {
        // Before first booking
        const firstBooking = first(bookings);
        availabilities.push(availability(undefined, firstBooking.start_date));

        // After last booking
        const lastBooking = last(bookings);
        availabilities.push(availability(lastBooking.end_date, undefined));

        // TODO between bookings
      }

      return assign({}, unit, { bookings: concat(bookings, availabilities) });
    });
  }

  render() {
    const modeButton = (text, onClick, active) => (
      <button
        className={`ui ${active ? 'active' : ''} button`}
        onClick={onClick}
      >
        {text}
      </button>
    );

    const dateModes = (
      <div className="ui tiny mode buttons">
        {modeButton('Week', () => this.setFuture('week'), this.state.mode === 'week')}
        {modeButton('Month', () => this.setFuture('month'), this.state.mode === 'month')}
        {modeButton('Year', () => this.setFuture('year'), this.state.mode === 'year')}
        {modeButton('All', () => this.showAll(), this.state.mode === 'all')}
      </div>
    );

    const data = {
      startDate: this.state.startDate,
      endDate: this.state.endDate,
      units: this.withinRange(this.withAvailabilities(this.state.units)),
    };

    const content = this.state.madeSelection ? (
      <BookingsTable
        data={data}
        width={720}
        height={650}
        margin={{
          top: 40,
          left: 175,
          bottom: 1,
          right: 40,
        }}
      />
    ) : (
      <div className="ui text container" style={{ marginTop: '2em' }}>
        <div className="ui info message" style={{ position: 'relative' }}>
          Make a selection above to see relevant bookings, or close this to see all units.
          <i className="close icon" style={{ position: 'absolute', right: '1em', top: '1em' }} />
        </div>
      </div>
    );

    const  { visible } = this.props;

    return (
      <div
        className="ui container"
        style={{
          paddingTop: '96px',
          display: visible ? 'block' : 'none',
        }}
      >
        <StickyFilterBox>
          <Filters
            ref="filters"
            changePortfolio={this.onPortfolioChanged}
            changeProperty={this.onPropertyChanged}
            changeBedSize={this.changeBedSize}
            changeStartRange={this.changeStartRange}
            changeEndRange={this.changeEndRange}
                changeAmenities={this.changeAmenities}
            clearFilters={this.clickClear}
          />
        </StickyFilterBox>
        {content}
      </div>
    );
  }
}

export default Bookings;
