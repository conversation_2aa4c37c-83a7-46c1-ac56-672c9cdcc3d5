import PropTypes from 'prop-types';
import { Component } from 'react';

import Dropdown from '../../selectors/dropdown';
import CalendarField from '../../forms/fields/calendar_field';

import AmenitiesSelector from './amenities_selector';

class Filters extends Component {

  static propTypes = {
    changePortfolio: PropTypes.func.isRequired,
    changeProperty: PropTypes.func.isRequired,
    changeStartRange: PropTypes.func.isRequired,
    changeEndRange: PropTypes.func.isRequired,
    changeAmenities: PropTypes.func.isRequired,
  };

  constructor(params) {
    super(params);
    this.state = {};
  }

  clearRange() {
    this.refs.rangeStart.clear();
    this.refs.rangeEnd.clear();
  }

  clearAmenities() {
    this.refs.amenities.clear();
  }

  render() {
    const {
      changeProperty,
      changeStartRange,
      changeEndRange,
      changeAmenities,
      clearFilters,
    } = this.props;

    const { portfolio } = this.state;

    const changePortfolio = portfolio => {
      this.setState({ portfolio });
      this.props.changePortfolio(portfolio);
    };

    const clickClear = () => {
      this.clearRange();
      this.setState({ portfolio: undefined });
      this.refs.portfolioDropdown.clear();
      this.refs.propertyDropdown.clear();
      clearFilters();
    };

    const portfoliosUrl = '/manage/portfolios.json';
    const basePropertiesUrl = '/manage/properties.json';
    const amenitiesUrl = '/leasing/bookings/amenities.json';

    const propertiesUrl = portfolio
      ? `${basePropertiesUrl}?portfolio_id=${portfolio.id}`
      : basePropertiesUrl;

    return (
      <div className="ui form">
        <div className="five fields">
          <div className="field">
            <label>Portfolio</label>
            <Dropdown
              ref="portfolioDropdown"
              url={portfoliosUrl}
              defaultText="All Portfolios"
              onChange={changePortfolio}
            />
          </div>

          <div className="field">
            <label>Property</label>
            <Dropdown
              ref="propertyDropdown"
              url={propertiesUrl}
              defaultText="All Properties"
              onChange={changeProperty}
            />
          </div>

          <CalendarField
            ref="rangeStart"
            id="start_range"
            label="Available From"
            placeholder="Start"
            endCalendar="#end_range"
            onChange={changeStartRange}
          />

          <CalendarField
            ref="rangeEnd"
            id="end_range"
            label="Available To"
            placeholder="End"
            startCalendar="#start_range"
            onChange={changeEndRange}
          />

          <div className="field">
            <label>Amenities</label>
            <AmenitiesSelector ref="amenities" onChange={changeAmenities} />
          </div>

          <button
            onClick={clickClear}
            className="ui button"
            style={{ alignSelf: 'flex-end' }}
          >
            Clear
          </button>
        </div>
      </div>
    );
  }
}

export default Filters;
