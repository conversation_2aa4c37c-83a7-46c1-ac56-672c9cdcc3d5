import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import { filter, includes, map } from 'lodash';

import Checkbox from '../../forms/fields/redux/checkbox';

class AmenitiesSelector extends Component {
  state = { amenities: [], selected: [] };

  componentDidMount() {
    $.get('/leasing/bookings/amenities.json', amenities => this.setState({ amenities }));

    const $node = $(findDOMNode(this));
    $node.find('.amenities.button').popup({
      on: 'click',
      popup: '.amenities.popup',
      position: 'bottom center',
    });
  }

  notifyAmenitiesChanged = () => {
    const { selected } = this.state;
    this.props.onChange(selected);
  }

  handleToggle = amenity => value => {
    const { selected } = this.state;

    let newSelected;

    if (value) {
      newSelected = [...selected, amenity];
    } else {
      newSelected = filter(this.state.selected, a => a !== amenity);
    }

    this.setState({ selected: newSelected }, this.notifyAmenitiesChanged);
  }

  clear() {
    this.setState({ selected: [] });
  }

  render() {
    const { amenities, selected } = this.state;

    const count = selected.length || 'Any';
    const pluralized = selected.length === 1 ? 'Amenity' : 'Amenities';

    return (
      <div>
        <button className="ui basic fluid amenities button">
          {count} {pluralized}
        </button>
        <div className="ui amenities popup">
          <div className="ui list">
            {amenities.length === 0 && <i>None available</i>}
            {map(amenities, (amenity, i) =>
              <Checkbox
                key={i}
                label={amenity}
                className="ui checkbox item"
                input={{
                  onChange: this.handleToggle(amenity),
                  value: includes(selected, amenity),
                }}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
}

AmenitiesSelector.propTypes = {
  onChange: PropTypes.func.isRequired,
};

export default AmenitiesSelector;
