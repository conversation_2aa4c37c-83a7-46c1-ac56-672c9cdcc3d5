import moment from 'moment';

export const RENDER_LEASES_WARNING = 'RENDER_LEASES_WARNING';
export const checkAvailability = (dispatch, getState) => {
  const { lease: { id: leaseId, unit_id: unitId, start_date: startDate, end_date: endDate } } = getState();

  if (unitId && startDate && endDate) {
    const params = {
      id: leaseId,
      unit_id: unitId,
      start_date: moment(startDate).format(),
      end_date: moment(endDate).format(),
    };

    $.get('/leasing/leases/check_availability.json', params, data => {
      const { available, leases } = data;

      if (!available) {
        dispatch({ type: RENDER_LEASES_WARNING, leaseConflicts: leases });
      }
    });
  }
};
