import { find } from 'lodash';

import { changeChargeAmount } from './charges';
import { fetchChargePresets } from './charge_presets';

export const SET_UNIT = 'SET_UNIT';
export const setUnit = (unit, force = false) => (dispatch, getState) => {
  const { unit: existingUnit, charges } = getState();

  // Only dispatch if unit has changed
  if (force || (!existingUnit || existingUnit.id !== unit.id)) {
    dispatch({ type: SET_UNIT, unit });

    if (unit) {
      // Update monthly rent
      const charge = find(charges, c => c.name === 'Monthly Rent');
      if (charge) {
        const amount = ((unit.floorplan.price_cents || 0) / 100).toString();
        dispatch(changeChargeAmount(charge, amount));
      }
    }

    dispatch(fetchChargePresets(unit));
  }
};
