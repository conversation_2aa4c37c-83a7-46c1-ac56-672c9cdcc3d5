import { differenceBy } from 'lodash';

export const CHARGE_ADDED = 'CHARGE_ADDED';
export const chargeAdded = (charge, tenants) => ({ type: CHARGE_ADDED, id: charge.id, amount: charge.amount, tenants });

export const ADD_CHARGE = 'ADD_CHARGE';
export const addCharge = preset => (dispatch, getState) => {
  const charges = getState().charges;
  dispatch({ type: ADD_CHARGE, preset });
  const charge = differenceBy(getState().charges, charges, 'id')[0];
  dispatch(chargeAdded(charge, getState().tenants));
};

export const CHARGE_REMOVED = 'CHARGE_REMOVED';
export const chargeRemoved = charge => ({ type: CHARGE_REMOVED, id: charge.id });

export const REMOVE_CHARGE = 'REMOVE_CHARGE';
export const removeCharge = charge => dispatch => {
  dispatch({ type: REMOVE_CHARGE, id: charge.id });
  dispatch(chargeRemoved(charge));
};

export const CHANGE_CHARGE_CODE = 'CHANGE_CHARGE_CODE';
export const changeChargeCode = (charge, account_id) => ({
  type: CHANGE_CHARGE_CODE, id: charge.id, account_id,
});

export const CHANGE_CHARGE_NAME = 'CHANGE_CHARGE_NAME';
export const changeChargeName = (charge, name) => ({
  type: CHANGE_CHARGE_NAME, id: charge.id, name,
});

export const CHANGE_CHARGE_AMOUNT = 'CHANGE_CHARGE_AMOUNT';
export const changeChargeAmount = (charge, amount) => ({
  type: CHANGE_CHARGE_AMOUNT, id: charge.id, amount,
});

export const CHANGE_CHARGE_FREQUENCY = 'CHANGE_CHARGE_FREQUENCY';
export const changeChargeFrequency = (charge, recurring) => ({
  type: CHANGE_CHARGE_FREQUENCY, id: charge.id, recurring,
});

export const CHANGE_CHARGE_BILL_SEPARATELY = 'CHANGE_CHARGE_BILL_SEPARATELY';
export const changeChargeBillSeparately = (charge, value) => ({
  type: CHANGE_CHARGE_BILL_SEPARATELY, id: charge.id, value,
});

export const CHANGE_CHARGE_INHERIT_TERM = 'CHANGE_CHARGE_INHERIT_TERM';
export const changeChargeInheritTerm = (charge, value) => ({
  type: CHANGE_CHARGE_INHERIT_TERM, id: charge.id, value,
});

export const CHANGE_CHARGE_START_DATE = 'CHANGE_CHARGE_START_DATE';
export const changeChargeStartDate = (charge, start_date) => ({
  type: CHANGE_CHARGE_START_DATE, id: charge.id, start_date,
});

export const CHANGE_CHARGE_END_DATE = 'CHANGE_CHARGE_END_DATE';
export const changeChargeEndDate = (charge, end_date) => ({
  type: CHANGE_CHARGE_END_DATE, id: charge.id, end_date,
});

export const SPLIT_CHARGE = 'SPLIT_CHARGE';
export const splitCharge = charge => ({ type: SPLIT_CHARGE, charge });
