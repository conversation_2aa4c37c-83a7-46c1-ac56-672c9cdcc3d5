import { checkAvailability } from './check_availability';

export const SET_START_DATE = 'SET_START_DATE';
export const setStartDate = date => (dispatch, getState) => {
  dispatch({ type: SET_START_DATE, date });
  checkAvailability(dispatch, getState);
};

export const SET_END_DATE = 'SET_END_DATE';
export const setEndDate = date => (dispatch, getState) => {
  dispatch({ type: SET_END_DATE, date });
  checkAvailability(dispatch, getState);
};

export const SET_ORIGINAL_END_DATE = 'SET_ORIGINAL_END_DATE';
export const setOriginalEndDate = date => (dispatch, getState) => {
  dispatch({ type: SET_ORIGINAL_END_DATE, date });
};
