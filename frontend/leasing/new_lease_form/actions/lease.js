export const SET_PRORATION = 'SET_PRORATION';
export const setProration = proration => ({ type: SET_PRORATION, proration });

export const SET_KIND = 'SET_KIND';
export const setKind = kind => ({ type: SET_KIND, kind });

export const SET_GRACE_PERIOD_DAYS = 'SET_GRACE_PERIOD_DAYS';
export const setGracePeriodDays = grace_period_days => ({ type: SET_GRACE_PERIOD_DAYS, grace_period_days });

export const SET_ELIGIBLE_FOR_PLACEMENT_FEE = 'SET_ELIGIBLE_FOR_PLACEMENT_FEE';
export const setEligibleForPlacementFee = eligible_for_placement_fee => ({ type: SET_ELIGIBLE_FOR_PLACEMENT_FEE, eligible_for_placement_fee });
