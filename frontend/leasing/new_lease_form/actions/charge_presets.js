export const RECEIVE_CHARGE_PRESETS = 'RECEIVE_CHARGE_PRESETS';
export const receiveChargePresets = charge_presets =>
  ({ type: RECEIVE_CHARGE_PRESETS, charge_presets });

export const fetchChargePresets = unit => dispatch => {
  if (unit) {
    $.get(
      `/leasing/leases/charge_presets.json?unit_id=${unit.id}`,
      results => dispatch(receiveChargePresets(results)),
    );
  } else {
    dispatch(receiveChargePresets([]));
  }
};
