import { differenceBy, reject, forEach } from 'lodash';

export const TENANT_ADDED = 'TENANT_ADDED';
export const tenantAdded = (tenant, charges) => ({ type: TENANT_ADDED, id: tenant.id, charges });

export const ADD_ADDITIONAL_TENANT = 'ADD_ADDITIONAL_TENANT';
export const addAdditionalTenant = (values = {}) => (dispatch, getState) => {
  const tenants = getState().tenants;
  dispatch({ type: ADD_ADDITIONAL_TENANT, values });
  const tenant = differenceBy(getState().tenants, tenants, 'id')[0];
  dispatch(tenantAdded(tenant, getState().charges));
};

export const CHANGE_ADDITIONAL_TENANT_FIELD = 'CHANGE_ADDITIONAL_TENANT_FIELD';
export const changeAdditionalTenantField = (tenant, field, value) => ({
  type: CHANGE_ADDITIONAL_TENANT_FIELD, id: tenant.id, field, value,
});

export const TENANT_REMOVED = 'TENANT_REMOVED';
export const tenantRemoved = tenant => ({ type: TENANT_REMOVED, id: tenant.id });

export const REMOVE_ADDITIONAL_TENANT = 'REMOVE_ADDITIONAL_TENANT';
export const removeAdditionalTenant = tenant => dispatch => {
  dispatch({ type: REMOVE_ADDITIONAL_TENANT, id: tenant.id });
  dispatch(tenantRemoved(tenant));
};

export const SET_SELECTED_TENANTS = 'SET_SELECTED_TENANTS';
export const setSelectedTenants = tenants => (dispatch, getState) => {
  const before = getState().tenants;
  dispatch({ type: SET_SELECTED_TENANTS, tenants });
  const after = getState().tenants;

  if (before.length > after.length) {
    const oldTenants = differenceBy(before, after, 'id');

    forEach(oldTenants, tenant => dispatch(tenantRemoved(tenant)));
  } else if (before.length < after.length) {
    const newTenants = differenceBy(after, before, 'id');
    forEach(newTenants, tenant => dispatch(tenantAdded(tenant, getState().charges)));
  }
};

export const addApplicant = applicant => (dispatch, getState) => {
  const current = getState().tenants;

  if (current.length === 0) {
    applicant.role = 'primary_tenant';
    applicant.role_text = 'Primary Tenant';
  } else {
    applicant.role = 'co_tenant';
    applicant.role_text = 'Co-Tenant';
  }

  const tenants = [...reject(current, 'additional'), applicant];
  dispatch(setSelectedTenants(tenants));
};
