export const RECEIVE_CHARGE_CODES = 'RECEIVE_CHARGE_CODES';
export const receiveChargeCodes = (charge_codes, rent_account_id) =>
  ({ type: RECEIVE_CHARGE_CODES, charge_codes, rent_account_id });

export const fetchChargeCodes = property => dispatch => $.get(
  `/organization/charge_codes.json?property_id=${property.id}`,
  results => dispatch(receiveChargeCodes(results.accounts, results.rent_account_id)),
);
