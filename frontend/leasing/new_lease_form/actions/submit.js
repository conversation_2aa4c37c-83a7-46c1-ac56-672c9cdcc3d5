export const SUBMIT_FORM = 'SUBMIT_FORM';
export const submitForm = () => (dispatch, getState) => {
  dispatch({
    type: SUBMIT_FORM,
    response: {
      submitting: true,
      errors: [],
    },
  });

  const state = getState();

  const chargeAmounts = state.tenantAmounts;

  const charges = state.charges;

  const data = {
    lease: {
      charges,
      charge_amounts: chargeAmounts,
      tenants: state.tenants,
      ...state.lease,
    },
  };

  const { lease: { id, renewal } } = state;

  const url = (id && !renewal) ? `/leasing/leases/${id}` : '/leasing/leases';

  const type = (id && !renewal) ? 'PUT' : 'POST';

  $.ajax({
    url,
    type,
    dataType: 'json',
    data,
    success: lease => {
      Turbolinks.visit(`/leasing/leases/${lease.id}`);
    },
    error: error => {
      let errors = [];

      try {
        errors = JSON.parse(error.responseText);
      } catch (e) {
        errors = ['An Unkown Error Has Occurred'];
      }

      dispatch({
        type: SUBMIT_FORM,
        response: {
          submitting: false,
          errors,
        },
      });

      $('#lease-form').scrollParent().scrollTop(0);
    },
  });
};
