import { Component } from 'react';
import { find, forOwn, map } from 'lodash';
import { connect } from 'react-redux';

import { changeAdditionalTenantField, removeAdditionalTenant } from '../actions/tenants';

class TenantsTable extends Component {
  constructor(props) {
    super(props);
    this.roleDropdowns = [];
  }

  componentDidMount() {
    this.updateDropdowns();
  }

  componentDidUpdate() {
    this.updateDropdowns();
  }

  updateDropdowns() {
    const { changeRole, tenants } = this.props;

    forOwn(this.roleDropdowns, (dropdown, tenantId) => {
      $(dropdown)
        .dropdown({
          onChange(value) {
            const tenant = find(tenants, t => t.id == tenantId);
            changeRole(tenant, value);
          },
        });
    });
  }

  render() {
    const { tenants, removeTenant } = this.props;

    return (
      <table className="ui very basic celled fixed single line small table">
        <thead>
          <tr>
            <th className="four wide">Name</th>
            <th className="three wide">Role</th>
            <th>Email</th>
            <th className="three wide">Phone</th>
            <th className="two wide" />
          </tr>
        </thead>
        <tbody>
          {map(tenants, tenant => (
            <tr key={tenant.id}>
              <td>{tenant.name}</td>
              <td style={{ overflow: 'visible' }} >
                <div
                  ref={dropdown => { this.roleDropdowns[tenant.id] = dropdown; }}
                  className="ui floating dropdown tenant-role-dropdown"
                >
                  <span className="text">{tenant.role_text}</span>
                  <i className="dropdown icon" />
                  <div className="menu">
                    <div className="item" data-value="primary_tenant">Primary Tenant</div>
                    <div className="item" data-value="co_tenant">Co-Tenant</div>
                    <div className="item" data-value="guarantor">Guarantor</div>
                    <div className="item" data-value="occupant">Occupant</div>
                    <div className="item" data-value="minor">Minor</div>
                  </div>
                </div>
              </td>
              <td>{tenant.email}</td>
              <td>{tenant.phone}</td>
              <td className="center aligned">
                <a href="#" onClick={event => { event.preventDefault(); removeTenant(tenant); }}>
                  (Remove)
                </a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  }
}

const mapState = state => ({
  tenants: state.tenants,
});

const mapDispatch = dispatch => ({
  removeTenant: tenant => {
    dispatch(removeAdditionalTenant(tenant));
  },
  changeRole: (tenant, role) => {
    dispatch(changeAdditionalTenantField(tenant, 'role', role));
  },
});

export default connect(mapState, mapDispatch)(TenantsTable);
