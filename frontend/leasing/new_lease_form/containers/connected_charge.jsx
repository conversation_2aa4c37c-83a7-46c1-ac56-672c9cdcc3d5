import { connect } from 'react-redux';

import Charge from '../components/charge';

import {
  removeCharge,
  changeChargeCode,
  changeChargeName,
  changeChargeAmount,
  changeChargeBillSeparately,
  changeChargeInheritTerm,
  changeChargeStartDate,
  changeChargeEndDate,
} from '../actions/charges';

const mapState = (state, props) => ({
  chargeCodes: state.chargeCodes,
  index: props.index,
  multiTenant: state.tenants.length > 1,
});

const mapDispatch = (dispatch, props) => ({
  clickRemove: () => dispatch(removeCharge(props.charge)),
  changeChargeCode: account => dispatch(changeChargeCode(props.charge, account)),
  changeName: name => dispatch(changeChargeName(props.charge, name)),
  changeAmount: amount => dispatch(changeChargeAmount(props.charge, amount)),
  changeBillSeparately: value => dispatch(changeChargeBillSeparately(props.charge, value)),
  changeInheritTerm: value => dispatch(changeChargeInheritTerm(props.charge, value)),
  changeStartDate: start_date => dispatch(changeChargeStartDate(props.charge, start_date)),
  changeEndDate: end_date => dispatch(changeChargeEndDate(props.charge, end_date)),
});

export default connect(mapState, mapDispatch)(Charge);
