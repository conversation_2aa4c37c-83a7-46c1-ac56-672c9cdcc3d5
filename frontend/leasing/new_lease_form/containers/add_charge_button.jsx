import { Component } from 'react';
import { connect } from 'react-redux';
import { array, func } from 'prop-types';
import { find, map } from 'lodash';

import { addCharge } from '../actions/charges';

/*
 * Button
 */
const AddChargeButton = ({ handleAdd }) => (
  <button
    className="small ui button"
    onClick={e => { e.preventDefault(); handleAdd(); }}
  >
    <i className="plus icon" />
    Add Charge
  </button>
);

AddChargeButton.propTypes = {
  clickAdd: func.isRequired,
};

/*
 * Dropdown
 */
class AddChargeDropdown extends Component {
  componentDidMount() {
    const onChange = (id, b, c) => {
      if (id) {
        const preset = find(this.props.chargePresets, p => p.id.toString() === id.toString());
        this.props.handleAdd(preset);
      }
    }

    $(this.dropdown).dropdown({ action: 'select', forceSelection: false, onChange });
  }

  render() {
    const { chargePresets } = this.props;

    return (
      <div
        ref={c => { this.dropdown = c; }}
        className="ui floating dropdown labeled search icon button"
      >
        <i className="plus icon" />
        <span className="text">Add Charge </span>
        <div className="menu">
          <div className="item">Blank</div>
          {map(chargePresets, preset => (
            <div key={preset.id} className="item" data-value={preset.id}>{preset.name}</div>
          ))}
        </div>
      </div>
    );
  }
}

AddChargeDropdown.propTypes = {
  chargePresets: array.isRequired,
  handleAdd: func.isRequired,
};

/*
 * Container
 */
const AddCharge = ({ chargePresets, handleAdd }) => {
  if (chargePresets.length) {
    return (
      <AddChargeDropdown chargePresets={chargePresets} handleAdd={handleAdd} />
    );
  }

  return (
    <AddChargeButton handleAdd={handleAdd} />
  );
};

AddCharge.propTypes = {
  chargePresets: array.isRequired,
  handleAdd: func.isRequired,
};

const mapState = state => ({
  chargePresets: state.chargePresets,
});

const mapDispatch = dispatch => ({
  handleAdd: preset => dispatch(addCharge(preset)),
});

export default connect(mapState, mapDispatch)(AddCharge);
