import { connect } from 'react-redux';

import Duration from '../components/duration';
import { setKind, setProration } from '../actions/lease';
import { setStartDate, setEndDate, setOriginalEndDate } from '../actions/dates';

const proration = state => {
  const value = state.lease.proration;

  if (value) {
    return value;
  }

  if (window.location.hostname.startsWith('pmi')) {
    return 'prorated_per_diem_30';
  }

  return undefined;
};

const kind = state => {
  const value = state.lease.kind;

  if (value) {
    return value;
  }

  if (window.location.hostname.startsWith('pmi')) {
    return 'rollover';
  }

  return undefined;
};

const mapState = state => ({
  startDate: state.lease.start_date,
  endDate: state.lease.end_date,
  originalEndDate: state.lease.end_date_before_rollover,
  kind: kind(state),
  proration: proration(state),
  leaseConflicts: state.leaseConflicts,
  showProration: !(state.lease.renewal && window.location.hostname.startsWith('pmi')),
  showOriginalEndDate: state.lease.id && window.location.hostname.startsWith('marketplacehomes'),
});

const mapDispatch = dispatch => ({
  changeStartDate: date => dispatch(setStartDate(date)),
  changeEndDate: date => dispatch(setEndDate(date)),
  changeOriginalEndDate: date => dispatch(setOriginalEndDate(date)),
  changeRenewal: renewal => dispatch(setKind(renewal)),
  changeProration: proration => dispatch(setProration(proration)),
});

export default connect(mapState, mapDispatch)(Duration);
