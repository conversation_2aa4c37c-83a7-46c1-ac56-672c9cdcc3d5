import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import classNames from 'classnames';

import SubmissionErrors from '../components/submission_errors';
import { submitForm } from '../actions/submit';

const Submit = ({ edit, disabled, onClick }) => (
  <div className="ui very basic padded vertical segment">
    <SubmissionErrors />
    <button
      className={classNames('ui primary submit button', { disabled })}
      onClick={onClick}
    >
      {edit ? 'Update' : 'Create'}
    </button>
  </div>
);

Submit.propTypes = {
  disabled: PropTypes.bool.isRequired,
  edit: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

const valid = state => (
  (state.tenants.length > 0) &&
    (state.unit !== null) &&
    (state.lease.start_date && state.lease.end_date)
);

const mapState = state => ({
  disabled: !valid(state) || state.submit.submitting,
  edit: state.lease.id && !state.lease.renewal,
});

const mapDispatch = dispatch => ({
  onClick: () => dispatch(submitForm()),
});

export default connect(mapState, mapDispatch)(Submit);
