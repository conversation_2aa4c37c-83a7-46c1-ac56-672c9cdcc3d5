import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { filter, map } from 'lodash';

import TenantAmount from './tenant_amount';
import Discrepency from './discrepency';
import { splitCharge } from '../actions/charges';

const TenantAmounts = ({ charge, tenantAmounts, split }) => (
  <div>
    <h5 className="ui horizontal divider header">
      Tenant Amounts
    </h5>
    <div className="fields">
      {map(
        tenantAmounts, tenantAmount =>
          <TenantAmount key={tenantAmount.id} tenantAmount={tenantAmount} />
        )
      }
    </div>
    <button
      className="ui inline tiny compact button"
      onClick={e => {
        e.preventDefault();
        split();
      }}
    >
      Split
    </button>
    <Discrepency charge={charge} />
  </div>
);

TenantAmounts.propTypes = {
  charge: PropTypes.object.isRequired,
  tenantAmounts: PropTypes.array.isRequired,
  split: PropTypes.func.isRequired,
};

const mapState = (state, props) => ({
  tenantAmounts: filter(state.tenantAmounts, { chargeId: props.charge.id }),
});

const mapDispatch = (dispatch, props) => ({
  split: () => dispatch(splitCharge(props.charge)),
});

export default connect(mapState, mapDispatch)(TenantAmounts);
