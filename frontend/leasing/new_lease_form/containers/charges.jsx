import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { map } from 'lodash';

import AddChargeButton from './add_charge_button';
import ConnectedCharge from './connected_charge';
import GracePeriodDays from '../components/grace_period_days';

const Charges = ({ charges }) => (
  <div style={{ marginTop: '3em' }}>
    <h3 className="ui dividing header">
      Charges
    </h3>
    <GracePeriodDays />
    {map(charges, (charge, index) => <ConnectedCharge key={charge.id} charge={charge} index={index} />)}
    <AddChargeButton />
  </div>
);

Charges.propTypes = {
  charges: PropTypes.array.isRequired,
};

const mapState = state => ({
  charges: state.charges,
});

export default connect(mapState)(Charges);
