import { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { differenceBy, find, map } from 'lodash';

import { addApplicant } from '../actions/tenants';

const generateUrl = leaseId => {
  const url = '/leasing/leases/applicants.json';
  return leaseId ? `${url}?lease_id=${leaseId}` : url;
};

class ApplicantSelector extends Component {
  state = { applicants: [] };

  componentDidMount() {
    const url = generateUrl(this.props.leaseId);

    $.get(url, applicants => {
      this.setState({ applicants });
    });

    const onChange = id => {
      if (id) {
        const applicant = find(this.state.applicants, t => t.id.toString() === id.toString());
        this.props.handleAdd(applicant);
      }
    };

    $(this.dropdown).dropdown({ action: 'select', forceSelection: false, onChange });
  }

  render() {
    const applicants = differenceBy(this.state.applicants, this.props.tenants, record => record.id.toString());

    return (
      <div
        ref={c => { this.dropdown = c; }}
        className="ui floating dropdown labeled search small icon button"
      >
        <i className="plus icon" />
        <span className="text">Add Applicant</span>
        <div className="menu">
          {map(applicants, applicant => (
            <div key={applicant.id} className="item" data-value={applicant.id}>{applicant.name}</div>
          ))}
        </div>
      </div>
    );
  }
}

ApplicantSelector.propTypes = {
  leaseId: PropTypes.number,
  handleAdd: PropTypes.func.isRequired,
  tenants: PropTypes.array.isRequired,
};

const mapState = state => ({
  leaseId: state.lease.id,
  tenants: state.tenants,
});

const mapDispatch = dispatch => ({
  handleAdd: applicant => {
    dispatch(addApplicant(applicant));
  },
});

export default connect(mapState, mapDispatch)(ApplicantSelector);
