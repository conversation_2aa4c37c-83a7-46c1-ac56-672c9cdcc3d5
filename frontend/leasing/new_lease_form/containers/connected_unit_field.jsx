import { connect } from 'react-redux';

import UnitField from '../components/unit_field.jsx';
import { setUnit } from '../actions/unit.js';

const mapState = state => ({
  property: state.property,
  defaultValue: state.unit ? state.unit.id : undefined,
});

const mapDispatch = dispatch => ({
  changeUnit: unit => dispatch(setUnit(unit)),
});

export default connect(mapState, mapDispatch)(UnitField);
