import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { changeChargeFrequency } from '../actions/charges';

const button = (text, value, active, onChange) => (
  <button
    className={`ui ${active ? 'active' : ''} button`}
    onClick={event => {
      event.preventDefault();
      onChange(value);
    }}
  >
    {text}
  </button>
);


const Frequency = ({ charge: { recurring }, onChange }) => (
  <div className="field">
    <label>Frequency</label>
    <div className="ui mini compact buttons">
      {button('Monthly', true, recurring, onChange)}
      {button('One Time', false, !recurring, onChange)}
    </div>
  </div>
);

Frequency.propTypes = {
  charge: PropTypes.shape({
    recurring: PropTypes.bool.isRequired,
  }).isRequired,
  onChange: PropTypes.func.isRequired,
};

const mapDispatch = (dispatch, props) => ({
  onChange: recurring => dispatch(changeChargeFrequency(props.charge, recurring)),
});

export default connect(null, mapDispatch)(Frequency);
