import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { filter, sumBy } from 'lodash';

import { formatMoney } from '../../../util/money';

const Discrepency = ({ difference }) => {
  if (difference === 0) {
    return null;
  }

  const direction = difference > 0 ? 'under' : 'over';
  const amount = Math.abs(difference);

  return <i style={{ color: '#DB2828' }}>{formatMoney(amount, false)} {direction}</i>;
};

Discrepency.propTypes = {
  difference: PropTypes.number.isRequired,
};

const difference = (charge, tenantAmounts) => {
  const total = charge.amount;
  const relatedAmounts = filter(tenantAmounts, { chargeId: charge.id });
  const applied = sumBy(relatedAmounts, tenantAmount => parseFloat(tenantAmount.amount));
  return total - applied;
};

const mapState = (state, props) => ({
  difference: difference(props.charge, state.tenantAmounts),
});

export default connect(mapState)(Discrepency);
