import { connect } from 'react-redux';

import { addAdditionalTenant } from '../actions/tenants';

const Modal = () => (
  <div className="ui tiny modal" id="additional-tenant-modal">
    <i className="ui black close icon" />
    <div className="header">
      Add Additional Tenant
    </div>
    <div className="content">
      <form className="ui form" id="additional-tenant-form">
        <div className="two fields">
          <div className="required field">
            <label htmlFor="first_name">First Name</label>
            <input type="text" name="first_name" id="first_name" />
          </div>
          <div className="required field">
            <label htmlFor="last_name">Last Name</label>
            <input type="text" name="last_name" id="last_name" />
          </div>
        </div>
        <div className="two fields">
          <div className="field">
            <label htmlFor="email">Email</label>
            <input type="text" name="email" id="email" />
          </div>
          <div className="field">
            <label htmlFor="phone">Phone</label>
            <input type="text" name="phone" id="phone" />
          </div>
        </div>
      </form>
    </div>
    <div className="actions">
      <button className="ui cancel button">
        Cancel
      </button>
      <button className="ui blue positive button">
        Save
      </button>
    </div>
  </div>
);

const AdditionalTenantsModal = ({ showModal }) => (
  <div style={{ display: 'inline' }}>
    <button className="ui small button" onClick={showModal}>
      <i className="plus icon" />
      Add Additional Tenant
    </button>
    <Modal />
  </div>
);

const mapDispatch = dispatch => ({
  showModal: function () {
    $('#additional-tenant-modal')
      .modal(window.modalDefaults)
      .modal('setting', 'onShow', function () {
        $('#additional-tenant-form')[0].reset();
        window.initializeSemanticFields();
      })
      .modal('setting', 'onApprove', function () {
        const form = $('#additional-tenant-form');

        const values = {};

        $.map(form.serializeArray(), function (n, i) {
          values[n.name] = n.value;
        });

        dispatch(addAdditionalTenant(values));
      })
      .modal('show');
  },
});

export default connect(null, mapDispatch)(AdditionalTenantsModal);
