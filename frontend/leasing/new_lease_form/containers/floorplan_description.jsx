import PropTypes from 'prop-types';
import { connect } from 'react-redux';

const FloorplanDescription = ({ floorplan }) => {
  if (floorplan) {
    const { name, bedrooms, bathrooms, square_feet: squareFeet } = floorplan;

    return (
      <div>
        <b>Floorplan</b>
        <br />
        {name}
        <br />
        {bedrooms} bedroom, {bathrooms} bathroom, {squareFeet} sqft.
      </div>
    );
  }

  return null;
};

FloorplanDescription.propTypes = {
  floorplan: PropTypes.shape({
    bathrooms: PropTypes.number.isRequired,
    bedrooms: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    square_feet: PropTypes.number.isRequired,
  }),
};

const mapState = state => ({
  floorplan: state.unit ? state.unit.floorplan : undefined,
});

export default connect(mapState)(FloorplanDescription);
