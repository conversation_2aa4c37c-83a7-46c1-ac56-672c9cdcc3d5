import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { find } from 'lodash';

import MoneyField from '../../../forms/fields/money_field';

import { changeTenantAmount } from '../actions/tenant_amount';

const TenantAmount = ({ tenantAmount, tenantName, amount, changeAmount }) => (
  <div style={{ paddingLeft: '0.5em' }}>
    <MoneyField
      id={`tenant_amount_${tenantAmount.id}`}
      label={tenantName}
      className="required field"
      amount={amount}
      onChange={changeAmount}
    />
  </div>
);

const tenantName = tenant => {
  if (!tenant) {
    return '';
  }

  if (tenant.name) {
    return tenant.name;
  }

  return `${tenant.first_name} ${tenant.last_name}`;
};

const mapState = (state, props) => ({
  amount: props.tenantAmount.amount,
  tenantName: tenantName(find(state.tenants, { id: props.tenantAmount.tenantId })),
});

const mapDispatch = (dispatch, props) => ({
  changeAmount: amount => dispatch(changeTenantAmount(props.tenantAmount, amount)),
});

TenantAmount.propTypes = {
  tenantAmount: PropTypes.object.isRequired,
  tenantName: PropTypes.string.isRequired,
  amount: PropTypes.string.isRequired,
  changeAmount: PropTypes.func.isRequired,
};

export default connect(mapState, mapDispatch)(TenantAmount);
