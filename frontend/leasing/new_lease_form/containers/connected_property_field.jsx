import { connect } from 'react-redux';

import PropertyField from '../components/property_field.jsx';
import { setProperty } from '../actions/property.js';

const mapState = state => ({
  defaultValue: state.property ? state.property.id : undefined,
});

const mapDispatch = dispatch => ({
  changeProperty: property => dispatch(setProperty(property)),
});

export default connect(mapState, mapDispatch)(PropertyField);
