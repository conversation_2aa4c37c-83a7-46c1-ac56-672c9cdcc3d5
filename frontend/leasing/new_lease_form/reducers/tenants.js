import uuid from 'uuid';
import { assign, filter, map, reject } from 'lodash';

import {
  SET_SELECTED_TENANTS,
  ADD_ADDITIONAL_TENANT,
  CHANGE_ADDITIONAL_TENANT_FIELD,
  REMOVE_ADDITIONAL_TENANT,
} from '../actions/tenants';

const newTenant = values => ({
  id: uuid.v4(),
  first_name: values.first_name || '',
  last_name: values.last_name || '',
  email: values.email || '',
  phone: values.phone || '',
  name: `${values.first_name} ${values.last_name}`,
  additional: true,
  role: values.role || 'primary_tenant',
  role_text: values.role_text || 'Primary Tenant',
});

export default (state = [], action) => {
  switch (action.type) {
    case SET_SELECTED_TENANTS:
      // Keep additional tenants, replace normal tenants with new set
      return [...filter(state, 'additional'), ...action.tenants];

    case ADD_ADDITIONAL_TENANT: {
      const values = action.values;

      if (state.length === 0) {
        values.role = 'primary_tenant';
        values.role_text = 'Primary Tenant';
      } else {
        values.role = 'co_tenant';
        values.role_text = 'Co-Tenant';
      }

      return [...state, newTenant(values)];
    }

    case CHANGE_ADDITIONAL_TENANT_FIELD:
      return map(state, tenant => {
        if (tenant.id === action.id) {
          return assign({}, tenant, { [action.field]: action.value });
        }
        return tenant;
      });

    case REMOVE_ADDITIONAL_TENANT:
      return reject(state, { id: action.id });

    default:
      return state;
  }
};
