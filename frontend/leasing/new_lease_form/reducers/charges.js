import uuid from 'uuid';
import { map, reject } from 'lodash';

import {
  ADD_CHARGE,
  CHANGE_CHARGE_AMOUNT,
  CHANGE_CHARGE_FREQUENCY,
  CHANGE_CHARGE_NAME,
  CH<PERSON>GE_CHARGE_CODE,
  CHANGE_CHARGE_BILL_SEPARATELY,
  CHANGE_CHARGE_INHERIT_TERM,
  CHANGE_CHARGE_START_DATE,
  CHANGE_CHARGE_END_DATE,
  REMOVE_CHARGE,
} from '../actions/charges';

import {
  RECEIVE_CHARGE_CODES,
} from '../actions/charge_codes';

import { SET_UNIT } from '../actions/unit';

import chargeReducer from './charge';

const newCharge = preset => {
  let name = 'New Charge';
  let amount = '';
  let account_id = undefined;
  let recurring = false;
  let charge_preset_id = undefined;

  if (preset) {
    name = preset.name;
    amount = (preset.amount_cents / 100).toString();
    account_id = preset.account_id;
    recurring = preset.recurring;
    charge_preset_id = preset.id;
  }

  return {
    id: uuid.v4(),
    charge_preset_id,
    account_id,
    name,
    amount,
    recurring,
    additional: true,
    billed_separately: false, // TODO: from preset?
    inherit_term: true,
  };
};

const defaultState = [{ ...newCharge(), name: 'Monthly Rent', recurring: true }];

export default (state = defaultState, action) => {
  switch (action.type) {
    case ADD_CHARGE:
      return [...state, newCharge(action.preset)];

    case REMOVE_CHARGE:
      return reject(state, { id: action.id });

    case CHANGE_CHARGE_NAME:
    case CHANGE_CHARGE_AMOUNT:
    case CHANGE_CHARGE_FREQUENCY:
    case CHANGE_CHARGE_CODE:
    case CHANGE_CHARGE_BILL_SEPARATELY:
    case CHANGE_CHARGE_INHERIT_TERM:
    case CHANGE_CHARGE_START_DATE:
    case CHANGE_CHARGE_END_DATE:
    case SET_UNIT:
    case RECEIVE_CHARGE_CODES:
      return map(state, c => chargeReducer(c, action));

    default:
      return state;
  }
};
