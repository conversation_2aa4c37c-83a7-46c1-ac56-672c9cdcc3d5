import uuid from 'uuid';

import { find, filter, includes, map, reject } from 'lodash';

import { CHANGE_TENANT_AMOUNT } from '../actions/tenant_amount';
import { TENANT_ADDED, TENANT_REMOVED } from '../actions/tenants';
import { CHARGE_ADDED, CHARGE_REMOVED, SPLIT_CHARGE, CHANGE_CHARGE_AMOUNT } from '../actions/charges';

export default (state = [], action) => {
  switch (action.type) {
    case CHANGE_TENANT_AMOUNT:
      return map(state, tenantAmount => {
        if (tenantAmount.id === action.id) {
          return { ...tenantAmount, amount: action.amount };
        }

        return tenantAmount;
      });

    case TENANT_ADDED: {
      const newTenantAmounts = map(action.charges, charge => {
        const chargeAmount = find(charge.charge_amounts, { tenant_id: action.id });
        let amount;

        if (chargeAmount) {
          amount = chargeAmount.amount || '0';
        } else if (state.length === 0) { // Existing charge, one tenant
          amount = charge.amount;
        } else {
          amount = '0';
        }

        return { id: uuid.v4(), tenantId: action.id, chargeId: charge.id, amount };
      });

      return [...state, ...newTenantAmounts];
    }

    case TENANT_REMOVED:
      return reject(state, { tenantId: action.id });

    case CHARGE_ADDED: {
      const newTenantAmounts = map(action.tenants, (tenant, index) => ({
        id: uuid.v4(),
        tenantId: tenant.id,
        chargeId: action.id,
        amount: (index === 0 ? (action.amount || '0') : '0'),
      }));

      return [...state, ...newTenantAmounts];
    }

    case CHARGE_REMOVED:
      return reject(state, { chargeId: action.id });

    case SPLIT_CHARGE: {
      const charge = action.charge;
      const tenantAmounts = filter(state, { chargeId: charge.id });
      let remaining = charge.amount;
      const count = tenantAmounts.length;
      const each = Math.floor(remaining * 100 / count) / 100;

      let i = 0;
      return map(state, tenantAmount => {
        if (!includes(tenantAmounts, tenantAmount)) {
          return tenantAmount;
        }

        let cents = 0;
        if (i === count - 1) {
          cents = remaining;
        } else {
          cents = each;
          remaining -= each;
        }

        i = i + 1;

        if (tenantAmount.chargeId === charge.id) {
          return { ...tenantAmount, amount: cents };
        }

        return tenantAmount;
      });
    }

    case CHANGE_CHARGE_AMOUNT: {
      // Sync charge amount with tenant amount if only one tenant
      if (filter(state, { chargeId: action.id }).length === 1) {
        return map(state, tenantAmount => {
          if (tenantAmount.chargeId === action.id) {
            return { ...tenantAmount, amount: action.amount };
          }

          return tenantAmount;
        });
      }

      return state;
    }

    default:
      return state;
  }
};
