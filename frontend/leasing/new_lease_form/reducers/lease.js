import { get } from 'lodash';

import { SET_PROPERTY } from '../actions/property';
import { SET_UNIT } from '../actions/unit';
import { SET_START_DATE, SET_END_DATE, SET_ORIGINAL_END_DATE } from '../actions/dates';
import { SET_PRORATION, SET_KIND, SET_GRACE_PERIOD_DAYS, SET_ELIGIBLE_FOR_PLACEMENT_FEE } from '../actions/lease';

export default (state = {}, action) => {
  switch (action.type) {
    case SET_PROPERTY:
      return { ...state, property_id: get(action, 'property.id') };
    case SET_UNIT:
      return { ...state, unit_id: get(action, 'unit.id') };
    case SET_START_DATE:
      return { ...state, start_date: action.date };
    case SET_END_DATE:
      return { ...state, end_date: action.date };
    case SET_ORIGINAL_END_DATE:
      return { ...state, end_date_before_rollover: action.date };
    case SET_PRORATION:
      return { ...state, proration: action.proration.id };
    case SET_KIND:
      return { ...state, kind: action.kind.id };
    case SET_GRACE_PERIOD_DAYS:
      return { ...state, grace_period_days: action.grace_period_days };
    case SET_ELIGIBLE_FOR_PLACEMENT_FEE:
      return { ...state, eligible_for_placement_fee: action.eligible_for_placement_fee };
    default:
      return state;
  }
};
