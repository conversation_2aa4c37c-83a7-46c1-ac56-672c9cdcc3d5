import { combineReducers } from 'redux';

import charges from './charges';
import chargeCodes from './charge_codes';
import chargePresets from './charge_presets';
import property from './property';
import tenants from './tenants';
import tenantAmounts from './tenant_amounts';
import unit from './unit';
import submit from './submit';
import leaseConflicts from './check_availability';

import lease from './lease';

export default combineReducers({
  charges,
  chargeCodes,
  chargePresets,
  property,
  tenants,
  tenantAmounts,
  unit,
  submit,
  leaseConflicts,
  lease,
});
