import { find } from 'lodash';

import {
  CHANGE_CHARGE_AMOUNT,
  CHANGE_CHARGE_FREQUENCY,
  CHANGE_CHARGE_NAME,
  CHANGE_CHARGE_CODE,
  CHANGE_CHARGE_BILL_SEPARATELY,
  CHANGE_CHARGE_INHERIT_TERM,
  CHANGE_CHARGE_START_DATE,
  CHANGE_CHARGE_END_DATE,
} from '../actions/charges';

import { RECEIVE_CHARGE_CODES } from '../actions/charge_codes';

import { SET_UNIT } from '../actions/unit';

export default (state, action) => {
  if (action.type === RECEIVE_CHARGE_CODES) {
    if (state.name === 'Monthly Rent') {
      const rent_account = find(action.charge_codes, { id: action.rent_account_id });

      if (rent_account) {
        return { ...state, account_id: rent_account.id };
      }
    }
  }

  if (state.id !== action.id) {
    return state;
  }

  switch (action.type) {
    case CHANGE_CHARGE_CODE:
      return { ...state, account_id: action.account_id };

    case <PERSON>AN<PERSON>_CHARGE_NAME:
      return { ...state, name: action.name };

    case <PERSON>ANGE_CHARGE_AMOUNT:
      return { ...state, amount: action.amount };

    case CHANGE_CHARGE_FREQUENCY:
      return { ...state, recurring: action.recurring };

    case CHANGE_CHARGE_BILL_SEPARATELY:
      return { ...state, billed_separately: action.value };

    case CHANGE_CHARGE_INHERIT_TERM:
      return { ...state, inherit_term: action.value };

    case CHANGE_CHARGE_START_DATE:
      return { ...state, start_date: action.start_date };

    case CHANGE_CHARGE_END_DATE:
      return { ...state, end_date: action.end_date };

    default:
      return state;
  }
};
