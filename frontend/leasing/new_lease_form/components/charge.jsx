import PropTypes from 'prop-types';

import CalendarField from '../../../forms/fields/calendar_field';
import Checkbox from '../../../forms/fields/redux/checkbox';
import MoneyField from '../../../forms/fields/money_field';

import Frequency from '../containers/frequency';
import TenantAmounts from '../containers/tenant_amounts';

const Charge = ({ index, charge, chargeCodes, changeChargeCode, changeName, changeAmount, changeBillSeparately, changeInheritTerm, changeStartDate, changeEndDate, clickRemove, multiTenant }) => (
  <div className="ui charge segment">
    <div className="ui two column grid">
      <div className="column">
        <h4 className="ui header">
          {charge.name}
        </h4>
      </div>
      <div className="column">
        <button
          className="ui right floated compact icon button"
          onClick={e => { e.preventDefault(); clickRemove(); }}
        >
          <i className="remove icon" />
        </button>
      </div>
    </div>
    <div className="four fields">
      <div className="required field">
        <label htmlFor={`charge_${charge.id}_code`}>Charge Code</label>
        <Dropdown
          fluid
          id={`charge_${charge.id}_code`}
          items={chargeCodes}
          onChange={account => { if (account) changeChargeCode(account.id); } }
          defaultSelection={charge.account_id}
        />
      </div>
      <div className="required field">
        <label htmlFor={`charge_${charge.id}_name`}>Name</label>
        <input
          id={`charge_${charge.id}_name`}
          type="text"
          value={charge.name}
          onChange={e => changeName(e.target.value)}
        />
      </div>
      <MoneyField
        id={`charge_amount_${charge.id}`}
        label="Amount"
        amount={charge.amount}
        onChange={changeAmount}
      />
      <Frequency charge={charge} />
    </div>
    <div className="ui fields">
      {charge.recurring === true && (
        <div className="ui field">
          <Checkbox
            id={`charge_${charge.id}_inherit_term`}
            label="Entire Term"
            input={{
              value: charge.inherit_term,
              onChange: changeInheritTerm,
            }}
          />
        </div>
      )}
      {index !== 0 && charge.recurring === true && (
        <div className="ui field">
          <Checkbox
            label="Bill Separately"
            input={{
              value: charge.billed_separately,
              onChange: changeBillSeparately,
            }}
          />
        </div>
      )}
    </div>
    {!charge.inherit_term && charge.recurring === true && (
      <div className="two fields">
        <CalendarField
          id={`charge_${charge.id}_start_date`}
          className="ui calendar field"
          label="Start Date"
          placeholder="Start"
          defaultValue={charge.start_date}
          onChange={changeStartDate}
        />
        <CalendarField
          id={`charge_${charge.id}_end_date`}
          className="ui calendar field"
          label="End Date"
          placeholder="End"
          defaultValue={charge.end_date}
          onChange={changeEndDate}
        />
      </div>
    )}
    {multiTenant && <TenantAmounts charge={charge} />}
  </div>
);

Charge.propTypes = {
  index: PropTypes.number.isRequired,
  clickRemove: PropTypes.func.isRequired,
  changeName: PropTypes.func.isRequired,
  changeAmount: PropTypes.func.isRequired,
  changeBillSeparately: PropTypes.func.isRequired,
  changeInheritTerm: PropTypes.func.isRequired,
  changeStartDate: PropTypes.func.isRequired,
  changeEndDate: PropTypes.func.isRequired,
  charge: PropTypes.shape({
    amount: PropTypes.string.isRequired,
    billed_separately: PropTypes.bool.isRequired,
    inherit_term: PropTypes.bool.isRequired,
    name: PropTypes.string.isRequired,
  }).isRequired,
  multiTenant: PropTypes.bool.isRequired,
};

export default Charge;
