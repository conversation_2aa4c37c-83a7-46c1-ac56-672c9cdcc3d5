import PropTypes from 'prop-types';

import Dropdown from '../../../selectors/dropdown.jsx';

const UnitField = ({ property, defaultValue, changeUnit }) => {
  const dropdown = property ? (
    <Dropdown
      defaultText="Select"
      id="unit_id"
      name="lease[unit_id]"
      url={`/leasing/leases/available_units.json?property_id=${property.id}`}
      onChange={changeUnit}
      defaultSelection={defaultValue}
    />
  ) : (
    <Dropdown
      defaultText="Select"
      id="unit_id"
      name="lease[unit_id]"
      items={[]}
      defaultSelection={defaultValue}
    />
  );

  return (
    <div className="required field">
      <label htmlFor="unit_id">Unit</label>
      {dropdown}
    </div>
  );
};

UnitField.propTypes = {
  changeUnit: PropTypes.func.isRequired,
  property: PropTypes.shape({
    id: PropTypes.number.isRequired,
  }),
  defaultValue: PropTypes.number,
};

export default UnitField;
