import { connect } from 'react-redux';

import Checkbox from '../../../forms/fields/redux/checkbox';
import { setEligibleForPlacementFee } from '../actions/lease';

const EligibleForPlacementFee = ({ enabled, renewal, eligibleForPlacementFee, changeEligibleForPlacementFee }) => {
  if (!enabled) {
    return null;
  }

  return (
    <div className="field">
      <Checkbox
        label={`This ${renewal ? 'renewal' : 'lease'} was placed by the management company and is eligible for placement fees`}
        input={{
          value: eligibleForPlacementFee,
          onChange: changeEligibleForPlacementFee,
        }}
      />
    </div>
  );
};

const mapState = state => ({
  eligibleForPlacementFee: state.lease.eligible_for_placement_fee,
  enabled: state.lease.show_eligible_for_placement_fee,
  renewal: state.lease.renewal,
});

const mapDispatch = dispatch => ({
  changeEligibleForPlacementFee: eligibleForPlacementFee => {
    dispatch(setEligibleForPlacementFee(eligibleForPlacementFee));
  },
});

export default connect(mapState, mapDispatch)(EligibleForPlacementFee);
