import PropTypes from 'prop-types';
import { map } from 'lodash';

const LeaseConflictWarning = ({ leases }) => (
  <div className="ui negative message">
    <div className="header">
      There is a conflict with your selected unit and date range:
    </div>
      {map(leases, lease => (
        <a
          href={`/leasing/leases/${lease.id}`}
          target="_blank"
          key={lease.id}
        >
          {`Lease from ${lease.start_date} to ${lease.end_date}`}
        </a>
      ))}
  </div>
);

LeaseConflictWarning.propTypes = {
  leases: PropTypes.array.isRequired,
};

export default LeaseConflictWarning;
