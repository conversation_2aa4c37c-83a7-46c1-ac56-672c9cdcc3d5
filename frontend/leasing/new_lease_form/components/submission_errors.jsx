import { connect } from 'react-redux';

const SubmissionErrors = ({ errors }) => {
  if (errors.length) {
    return (
      <div className="ui visible error message">
        <ul className="ui bulleted list">
          {errors.map((error) => <li className="item" key={error}>{error}</li>)}
        </ul>
      </div>
    )
  }

  return null;
}

const mapState = state => ({
  errors: state.submit.errors,
});

export default connect(mapState)(SubmissionErrors);
