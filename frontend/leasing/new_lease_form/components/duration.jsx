import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';

import LeaseConflictWarning from './lease_conflict_warning';
import CalendarField from '../../../forms/fields/calendar_field';
import Dropdown from '../../../selectors/dropdown';

const renewalOptions = [
  { name: 'Fixed', id: 'fixed' },
  { name: 'Rollover', id: 'rollover' },
  { name: 'Month to Month', id: 'month_to_month' },
];

const prorationOptions = [
  { name: 'Per Diem', id: 'prorated_per_diem' },
  { name: 'Per Diem 30', id: 'prorated_per_diem_30' },
  { name: 'Nearest Month', id: 'prorated_nearest_month' },
  { name: 'Half Month', id: 'prorated_half_month' },
  { name: 'None', id: 'prorated_none' },
  { name: 'Whole', id: 'prorated_whole' },
];

const Duration = ({ startDate, endDate, originalEndDate, changeStartDate, changeEndDate, showOriginalEndDate, changeOriginalEndDate, kind, proration, changeRenewal, changeProration, showProration, leaseConflicts }) => (
  <div style={{ marginTop: '3em' }}>
    <h3 className="ui dividing header">
      Lease Duration
    </h3>
    <div className="two fields">
      <CalendarField
        id="lease_start_date"
        label="Start Date"
        name="lease[start_date]"
        placeholder="Start"
        className="ui required calendar field"
        options={{ type: 'date' }}
        onChange={changeStartDate}
        defaultValue={startDate}
      />
      <CalendarField
        id="lease_end_date"
        label="End Date"
        name="lease[end_date]"
        placeholder="End"
        className="ui required calendar field"
        options={{ type: 'date' }}
        onChange={changeEndDate}
        defaultValue={endDate}
      />
    </div>
    {!isEmpty(leaseConflicts) && <LeaseConflictWarning leases={leaseConflicts} />}

    <div className="two fields">
      <div className="field">
        <label htmlFor="lease_kind">
          Renewal
        </label>
        <Dropdown
          id="lease_kind"
          items={renewalOptions}
          onChange={changeRenewal}
          defaultSelection={kind}
        />
      </div>

      <div className="field">
        {showProration && [
          <label htmlFor="lease_proration">
            Proration
          </label>,
          <Dropdown
            id="lease_proration"
            items={prorationOptions}
            onChange={changeProration}
            defaultSelection={proration}
          />,
        ]}
      </div>
    </div>

    {showOriginalEndDate &&
      <CalendarField
        id="lease_end_date_before_rollover"
        label="Original End Date"
        name="lease[end_date_before_rollover]"
        placeholder="Default"
        className="ui calendar field"
        options={{ type: 'date' }}
        onChange={changeOriginalEndDate}
        defaultValue={originalEndDate}
      />
    }
  </div>
);

Duration.propTypes = {
  changeStartDate: PropTypes.func.isRequired,
  changeEndDate: PropTypes.func.isRequired,
  changeOriginalEndDate: PropTypes.func.isRequired,
  changeRenewal: PropTypes.func.isRequired,
  changeProration: PropTypes.func.isRequired,
  startDate: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  endDate: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  originalEndDate: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  leaseConflicts: PropTypes.array,
  kind: PropTypes.string.isRequired,
  proration: PropTypes.string.isRequired,
  showProration: PropTypes.bool.isRequired,
};

export default Duration;
