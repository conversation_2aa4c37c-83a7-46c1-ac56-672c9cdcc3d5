import { connect } from 'react-redux';

import { setGracePeriodDays } from '../actions/lease';

const GracePeriodDays = ({ enabled, gracePeriodDays, changeGracePeriodDays }) => {
  if (!enabled) {
    return null;
  }

  return (
    <div className="field">
      <label htmlFor="grace_period_days">Grace Period Days</label>
      <input
        type="text"
        placeholder="Configuration Default"
        value={gracePeriodDays}
        onChange={changeGracePeriodDays}
      />
    </div>
  );
};

const mapState = state => ({
  gracePeriodDays: state.lease.grace_period_days,
  enabled: state.lease.show_grace_period_days,
});

const mapDispatch = dispatch => ({
  changeGracePeriodDays: event => {
    const days = event.target.value;
    dispatch(setGracePeriodDays(days));
  },
});

export default connect(mapState, mapDispatch)(GracePeriodDays);
