import PropTypes from 'prop-types';

import Dropdown from '../../../selectors/dropdown.jsx';

const PropertyField = ({ defaultValue, changeProperty }) => (
  <div className="required field">
    <label htmlFor="property_id">
      Property
    </label>
    <Dropdown
      defaultText="Select"
      id="property_id"
      name="lease[property_id]"
      onChange={changeProperty}
      url="/manage/properties.json"
      defaultSelection={defaultValue}
    />
  </div>
);

PropertyField.propTypes = {
  changeProperty: PropTypes.func.isRequired,
  defaultValue: PropTypes.number,
};

export default PropertyField;
