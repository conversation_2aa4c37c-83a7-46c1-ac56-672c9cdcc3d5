import { Component } from 'react';
import { createStore, applyMiddleware } from 'redux';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';
import { map } from 'lodash';

import { formatMoney } from '../../util/money';

import { fetchChargeCodes } from './actions/charge_codes';
import { fetchChargePresets } from './actions/charge_presets';
import { setSelectedTenants, tenantAdded } from './actions/tenants';
import { setUnit } from './actions/unit';

import App from './components/app';
import reducer from './reducers';

const amountedCharges = charges => map(charges, charge => ({
  ...charge,
  amount: (charge.amount_cents / 100).toString(),
  charge_amounts: map(charge.allocations, charge_amount => ({
    ...charge_amount,
    amount: (charge_amount.amount_cents / 100).toString(),
  })),
}));


// For editing an existing Lease
const hydrateLease = (props, store) => {
  const lease = props.lease;
  const state = store.getState();
  state.lease.show_grace_period_days = props.show_grace_period_days;
  state.lease.renewal = lease.renewal;
  state.lease.id = lease.id;
  state.lease.start_date = lease.start_date;
  state.lease.end_date = lease.end_date;
  state.lease.end_date_before_rollover = lease.end_date_before_rollover;
  state.lease.unit_id = lease.unit.id;
  state.lease.proration = lease.proration;
  state.lease.kind = lease.kind;
  state.lease.grace_period_days = lease.grace_period_days;
  state.lease.eligible_for_placement_fee = lease.eligible_for_placement_fee;
  state.property = lease.property;
  state.unit = lease.unit;
  state.charges = amountedCharges(lease.charge_schedule_entries);
  store.dispatch(setSelectedTenants(lease.tenants));
};

const hydrate = (props, store) => {
  const state = store.getState();
  // Hydrate from url
  const params = new URI(window.location.href).search(true);
  if (params.start_date) state.lease.start_date = params.start_date;
  if (params.end_date) state.lease.end_date = params.end_date;

  if (props.property) state.property = props.property;
  if (props.unit) state.unit = props.unit;
  if (props.unit) state.lease.unit_id = props.unit.id;
  if (props.start_date) state.lease.start_date = props.start_date;
  if (props.end_date) state.lease.end_date = props.end_date;
  if (props.lead) {
    const tenants = [props.lead];
    store.dispatch(setSelectedTenants(tenants));
  }

  if (props.lease) {
    hydrateLease(props, store);
  } else {
    state.lease.eligible_for_placement_fee = true;
  }

  state.lease.show_eligible_for_placement_fee = props.show_eligible_for_placement_fee;

  if (state.property) {
    store.dispatch(fetchChargeCodes(state.property));
  }

  if (state.unit) {
    store.dispatch(fetchChargePresets(state.unit));
  }

  if (state.unit && !props.lease) {
    store.dispatch(setUnit(state.unit, true));
  }
};

class NewLeaseForm extends Component {
  constructor(props) {
    super(props);

    this.store = createStore(reducer, {}, applyMiddleware(thunk));

    hydrate(props, this.store);
  }

  render() {
    return (
      <Provider store={this.store}>
        <App />
      </Provider>
    );
  }
}

export default NewLeaseForm;
