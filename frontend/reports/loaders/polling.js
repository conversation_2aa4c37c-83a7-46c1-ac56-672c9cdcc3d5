import Base from './base';

export default class extends Base {
  requestUpdated(reportRequest) {
    if (this.pendingRequest && this.pendingRequest.uuid == reportRequest.uuid) {
      // Pending request still relevant

      if (reportRequest.status == 'completed') {
        // Pending request now completed

        if (reportRequest.payload) {
          // Render already available
          this.resultsAvailable(reportRequest, reportRequest.payload);
        } else {
          // Fetch render
          this.#fetchReportPayload(reportRequest);
        }
      } else if (reportRequest.status == 'failed') {
        this.pendingRequest.fail(reportRequest.exception);
      }
    }
  }

  _cancelPendingRequest() {
    if (this.pendingRequest) {
      if (this.pendingRequest.status === 'pending') {
        $.ajax({
          url: `${this.#reportsPrefix()}/requests/${this.pendingRequest.uuid}`,
          type: 'DELETE',
        });
      } else {
        // Not cancelling, already cancelled or completed
      }
    }

    super._cancelPendingRequest();
  }

  _fetch(reportRequest) {
    const url = this.#url(reportRequest);

    $.post(url).done(json => {
      this.requestUpdated(json.request);

      var poll;

      poll = (duration) => {
        // console.log('polling, then ', duration);

        duration = duration * 2;

        if (duration > 15000) {
          duration = 15000;
        }

        if (!this.pendingRequest) {
          return; // Already completed somewhere
        }

        if (reportRequest.uuid !== this.pendingRequest.uuid) {
          return; // No longer active
        }

        if (reportRequest.status === 'canceled') {
          return; // Canceled
        }

        if (reportRequest.status === 'completed') {
          return; // Already completed, possibly from websocket
        }

        $.get(`${this.#reportsPrefix()}/requests/${json.request.uuid}`, json => {
          if (json.request.status === 'pending') {
            setTimeout(poll, duration, duration);
          } else {
            this.requestUpdated(json.request);
          }
        }).fail((e) => {
          console.log('Failed to poll', e);
          setTimeout(poll, duration, duration);
        });
      }

      poll(250); // Begin polling with 250 ms wait
    });
  }

  #url(reportRequest) {
    const query = {
      filters: reportRequest.filters,
      slug: reportRequest.slug,
      sort: reportRequest.sort,
      uuid: reportRequest.uuid,
    };

    const queryString = qs.stringify(query);

    return `${this.#reportsPrefix()}/requests?${queryString}`;
  }

  #fetchReportPayload(reportRequest) {
    $.get(`${this.#reportsPrefix()}/requests/${reportRequest.uuid}/rendered`).done(html => {
      this.resultsAvailable(reportRequest, html);
    });
  }

  #reportsPrefix() {
    const parts = window.location.pathname.split('/');

    parts.pop();

    const prefix = parts.join('/');

    return prefix;
  }
}
