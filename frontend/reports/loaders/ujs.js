import Base from './base';

export default class extends Base {
  _cancelPendingRequest() {
    if (this.xhr) {
      this.xhr.abort();
    }

    super._cancelPendingRequest();
  }

  _fetch(reportRequest) {
    const url = this.#url(reportRequest);

    const doneCallback = _jsResponse => {
      // Response is a JS that is already evaluated,
      // supply null as html in the callback
      this.resultsAvailable(reportRequest, null);
    };

    const errorCallback = (xhr, status, error) => {
      if (status === 'error') {
        reportRequest.fail();
      }
    };

    this.xhr = $.get(url).done(doneCallback).error(errorCallback);
  }

  #url(reportRequest) {
    const query = {
      filters: reportRequest.filters,
      sort: reportRequest.sort,
    };

    const queryString = qs.stringify(query);

    const slug = reportRequest.slug;

    const parts = window.location.pathname.split('/');

    parts.pop();

    const prefix = parts.join('/');

    return `${prefix}/${slug}.js?${queryString}`;
  }
}
