export default class {
  constructor() {
    this.pendingRequest = null;
  }

  load(reportRequest) {
    this._cancelPendingRequest();
    this._makeCurrent(reportRequest);
    this._fetch(reportRequest);
  }

  requestUpdated(reportRequest) {
  }

  resultsAvailable(reportRequest, html) {
    if (this.pendingRequest && this.pendingRequest.uuid === reportRequest.uuid) {
      this.pendingRequest.complete(html);
      this.pendingRequest = null;
    }
  }

  _cancelPendingRequest() {
    if (this.pendingRequest) {
      this.pendingRequest.cancel();
    }
  }

  _makeCurrent(reportRequest) {
    this.pendingRequest = reportRequest;
  }

  _fetch(reportRequest) {
  }
}
