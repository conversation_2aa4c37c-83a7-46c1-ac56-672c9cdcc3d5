import classNames from 'classnames';
import { Component } from 'react';
import { bool } from 'prop-types';
import { map } from 'lodash';

import Dropdown from '../selectors/dropdown';

// TODO: we can eagerly load contexts and store them here,
// passing arrays to Dropdown can save one ajax trip
class AccountingContextSelector extends Component {
  static propTypes = {
    required: bool.isRequired,
    allowPortfolios: bool.isRequired,
  };

  static defaultProps = {
    required: false,
    allowPortfolios: false,
  };

  state = {
    contexts: [null],
    topLevel: null,
  };

  componentDidMount() {
    const { allowPortfolios } = this.props;

    $.get(
      `/reports/accounting_contexts.json?allow_portfolios=${allowPortfolios}`,
      topLevel => this.setState({ topLevel }),
    );
  }

  setContext = (context, index) => {
    const { contexts } = this.state;
    contexts.splice(index);
    contexts[index] = context;
    if (context && context.child) {
      contexts.push(null);
    }
    this.setState({ contexts });

    const change = this.props.onChange || ((context) => {
      window.reportSetFilter('context_id', context && context.id || null);
    });

    // Notifiy listener of new context, or parent context if deselected
    if (context) {
      change(context);
    } else {
      change(contexts[index - 1]);
    }
  }

  render() {
    const { contexts, topLevel } = this.state;
    const { allowPortfolios } = this.props;

    return (
      <div className="dropdowns">
        {map(contexts, (context, i) => {
          const props = {};

          let label = allowPortfolios ? 'Portfolio' : 'Entity';

          if (context) {
            label = context.type;
          } else if (topLevel && topLevel.length) {
            label = topLevel[0].type;
          }

          if (i === 0) {
            props.url = `/reports/accounting_contexts.json?allow_portfolios=${allowPortfolios}`;
          } else {
            const parent = contexts[i - 1];
            label = parent.child;
            if (parent) {
              props.url = `/reports/accounting_contexts.json?parent_sgid=${parent.id}`;
            } else {
              props.items = [];
            }
          }

          const required = (i === 0 && this.props.required);

          const id = `accounting_context_${i}`;

          return (
            <div key={i} className={classNames('ui', { required }, 'field')}>
              <label htmlFor={id}>{label}</label>
              <Dropdown
                id={id}
                onChange={c => this.setContext(c, i)}
                selectFirst="single"
                defaultText="Any"
                addExplicitDefaultTextOption
                options={{ placeholder: 'Any' }}
                {...props}
              />
            </div>
          );
        })}
      </div>
    );
  }
}

export default AccountingContextSelector;
