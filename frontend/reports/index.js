const reports = {};

import Request from './request';

reports.Request = Request;

import UjsLoader from './loaders/ujs';
reports.ujsLoader = new UjsLoader();

import PollingLoader from './loaders/polling';
reports.pollingLoader = new PollingLoader();

const subdomain = window.location.hostname.split('.')[0];

const disabledSubdomains = [];

const usePollingLoader = !disabledSubdomains.includes(subdomain);

if (usePollingLoader) {
  console.log('Using pollingLoader');
  reports.loader = reports.pollingLoader;
} else {
  reports.loader = reports.ujsLoader;
}

export default reports;
