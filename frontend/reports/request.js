import uuid from 'uuid';

export default class {
  constructor({
    slug,
    filters = {},
    sort = {},
    format = 'html',
    completed = undefined,
    failed = undefined,
  } = {}) {
    this.uuid = uuid.v4();
    this.status = 'pending';
    this.slug = slug;
    this.filters = filters;
    this.sort = sort;
    this.format = format;
    this.completed = completed;
    this.failed = failed;
  }

  complete(html) {
    if (this.status === 'canceled') {
      return;
    }

    this.status = 'completed';

    if (this.completed) {
      this.completed(html);
    }
  }

  fail(exception) {
    if (this.status === 'canceled') {
      return;
    }

    this.status = 'failed';

    if (this.failed) {
      this.failed(exception);
    }
  }

  cancel() {
    this.status = 'canceled';
  }
}
