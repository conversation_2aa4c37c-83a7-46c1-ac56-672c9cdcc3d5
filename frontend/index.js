import 'jquery-tablesort';

// Magnific popup
require('imports-loader?define=>false&exports=>false!../node_modules/magnific-popup/dist/jquery.magnific-popup.js');
require('../node_modules/magnific-popup/dist/magnific-popup.css');

// Shopify Draggable
import Sortable from '@shopify/draggable/lib/es5/sortable';
import SwapAnimation from '@shopify/draggable/lib/es5/plugins/swap-animation';
import SortAnimation from '@shopify/draggable/lib/es5/plugins/sort-animation';
window.Sortable = Sortable;
window.SwapAnimation = SwapAnimation;
window.SortAnimation = SortAnimation;

// ActiveStorage
import * as ActiveStorage from "@rails/activestorage"
ActiveStorage.start()

// ActionText
require("trix")
require("@rails/actiontext")

// Stimulus
import 'element-closest';

import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';

const stimulus = StimulusApplication.start();
const stimulusControllers = require.context('./stimulus/', true, /\.js$/);
stimulus.load(stimulusDefinitions(stimulusControllers));

require("./forms/fields/no_or_explain");

// PDFJS
import * as pdfjsLib from 'pdfjs-dist/webpack';
window.pdfjsLib = pdfjsLib;

// React
import React from 'react';
window.React = React;

import PropTypes from 'prop-types';
window.PropTypes = PropTypes;

import ReactDOM from 'react-dom';
window.ReactDOM = ReactDOM;

import qs from 'qs';
window.qs = qs;

import OnlineUsers from './notifications/online_users';
window.OnlineUsers = new OnlineUsers();

import DataTable from './table/data_table.jsx';
window.DataTable = DataTable;

import LineChart from './charts/line_chart/line_chart.jsx';
window.LineChart = LineChart;

import Dropdown from './selectors/dropdown.jsx';
window.Dropdown = Dropdown;

import TenantSearchField from './forms/fields/tenant_search_field';
window.TenantSearchField = TenantSearchField;

import LinkModal from './modal_search/link_modal';
window.LinkModal = LinkModal;

import Pie from './charts/pie/pie.jsx';
window.Pie = Pie;

import StatisticPie from './maintenance/pulse/statistic_pie.jsx';
window.StatisticPie = StatisticPie;

import ModeWrapper from './mode_wrapper/mode_wrapper.jsx';
window.ModeWrapper = ModeWrapper;

import NewLeaseForm from './leasing/new_lease_form';
window.NewLeaseForm = NewLeaseForm;

import CommentsSidebar from './messaging/chats/comments_sidebar';
window.CommentsSidebar = CommentsSidebar;

import MessagingChats from './messaging/chats';
window.MessagingChats = MessagingChats;

import NewIncomeForm from './accounting/receivables/income/new';
window.NewIncomeForm = NewIncomeForm;

import PaymentForm from './accounting/payment_form/payment_form.jsx';
window.PaymentForm = PaymentForm;

import LineItemsArea from './accounting/line_items/line_items_area.jsx';
window.LineItemsArea = LineItemsArea;

import FilterBox from './filter/filter_box.jsx';
window.FilterBox = FilterBox;

import StackedArea from './charts/stacked_area/stacked_area.jsx';
window.StackedArea = StackedArea;

import YearlyProgressChart from './accounting/yearly_progress_chart.jsx';
window.YearlyProgressChart = YearlyProgressChart;

import NewGuestCard from './leasing/new_guest_card.jsx';
window.NewGuestCard = NewGuestCard;

import ProjectMembersDropdown from './projects/project_members_dropdown.jsx';
window.ProjectMembersDropdown = ProjectMembersDropdown;

import ProjectBudgetForm from './projects/budget_form';
window.ProjectBudgetForm = ProjectBudgetForm;

import MemberAssignment from './projects/member_assignment.jsx';
window.MemberAssignment = MemberAssignment;

import NewTicketForm from './maintenance/form/new_ticket_form.jsx';
window.NewTicketForm = NewTicketForm;

import AttachmentsInput from './maintenance/form/attachments.jsx';
window.AttachmentsInput = AttachmentsInput;

import AccountingCardExecutiveSummary from './cards/executive_summary';
window.AccountingCardExecutiveSummary = AccountingCardExecutiveSummary;

import AccountingCardExpensesOverview from './cards/expenses_overview/index';
window.AccountingCardExpensesOverview = AccountingCardExpensesOverview;

import AccountingCardIncomeAndExpenses from './cards/income_and_expenses';
window.AccountingCardIncomeAndExpenses = AccountingCardIncomeAndExpenses;

import AccountingCardPayablesAndReceivables from './cards/payables_and_receivables';
window.AccountingCardPayablesAndReceivables = AccountingCardPayablesAndReceivables;

import {
  PayablesSummary as AccountingCardPayablesSummary,
  ReceivablesSummary as AccountingCardReceivablesSummary
} from './cards/invoice_summary/index.jsx';
window.AccountingCardPayablesSummary = AccountingCardPayablesSummary;
window.AccountingCardReceivablesSummary = AccountingCardReceivablesSummary;

import AccountingCardProfitAndLoss from './cards/profit_and_loss';
window.AccountingCardProfitAndLoss = AccountingCardProfitAndLoss;

import Transactions from './accounting/transactions/transactions.jsx';
window.Transactions = Transactions;

import PhotoAttachmentsArea from './forms/fields/photo_attachments_area.jsx';
window.PhotoAttachmentsArea = PhotoAttachmentsArea;

import AmenitiesInput from './forms/fields/amenities_input.jsx';
window.AmenitiesInput = AmenitiesInput;

import Bookings from './leasing/bookings/bookings.jsx';
window.Bookings = Bookings;

import AttachedTickets from './projects/attached_tickets';
window.AttachedTickets = AttachedTickets;

import TurbolinksPortfolioSelector from './filter/turbolinks_portfolio_selector';
window.TurbolinksPortfolioSelector = TurbolinksPortfolioSelector;

import TurbolinksDateSelector from './filter/turbolinks_date_selector';
window.TurbolinksDateSelector = TurbolinksDateSelector;

import LeaseApplication from './leasing/application/show/lease_application';
window.LeaseApplication = LeaseApplication;

import NewBackgroundCheck from './leasing/background_checks/new_background_check.jsx';
window.NewBackgroundCheck = NewBackgroundCheck;

import TemperatureDropdown from './leasing/temperature_dropdown';
window.TemperatureDropdown = TemperatureDropdown;

import DocumentsArea from './portfolio/documents/documents_area';
window.DocumentsArea = DocumentsArea;

import MoneyField from './forms/fields/money_field';
window.MoneyField = MoneyField;

import App from './channels/app';
window.App = App;

import LeaseApplicationInvitation from './leasing/application/invitation/index';
window.LeaseApplicationInvitation = LeaseApplicationInvitation;

import OperationsSidebar from './operations/pulse/sidebar/index.jsx';
window.OperationsSidebar = OperationsSidebar;

import Documents from './organization/documents/index';
window.Documents = Documents;

import CalendarPopup from './calendar/popup';
window.CalendarPopup = CalendarPopup;

import ScheduleMaintenanceTicket from './maintenance/schedule_modal';
window.ScheduleMaintenanceTicket = ScheduleMaintenanceTicket;

import AccountingContextSelector from './reports/accounting_context_selector';
window.AccountingContextSelector = AccountingContextSelector;

import Reports from './reports';
window.reports = Reports;
