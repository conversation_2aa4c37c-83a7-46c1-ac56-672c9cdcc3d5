const renderUjsErrors = (formSelector, errors) => {
  $(formSelector).addClass('error');

  if (errors.length < 2) {
    $(formSelector).find('.ui.error.message')
      .html(errors[0]);
  } else {
    const wrapped_errors = errors.map( error => `<li>${error}</li>` ).join('')
    $(formSelector).find('.ui.error.message')
      .html(`<ul class='list'>${wrapped_errors}</ul>`);
  }
};

export { renderUjsErrors };
