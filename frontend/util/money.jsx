import { format } from 'd3-format';

// Limit number to two digits after a decimal place, for controlling input
const forceTwoDecimal = value => value.replace(/([^.]*\.\d\d).*/g, '$1');

// Remove anything except digits, negative, and decimal place, for sending to
// change callback
const sanitizeMoney = value => value.toString().replace(/[^0-9.-]/g, '').replace(/[^0-9-]*$/, '');

// Use d3 to format money with dollar sign and commas
const formatMoney = (amount, cents = false, dashes = false, parenthesis = true) => {
  if (dashes && amount == 0) {
    return '-';
  }

  let str = null;

  const display = parenthesis ? Math.abs(amount) : amount;

  if (cents) {
    str = format('$,.2f')(display / 100);
  } else {
    str = format('$,.2f')(display);
  }

  if (parenthesis && amount < 0) {
    str = `(${str})`;
  }

  return str;
};

const inputDisplay = value => formatMoney(sanitizeMoney(value), false, false, false);
const storeAndDisplay = input => {
  const current = input.val();
  input.data('userInput', current);
  const allowEmpty = input.data('allow-empty');

  if (allowEmpty && current === '') {
    input.val(current);
  } else {
    input.val(inputDisplay(current));
  }
};

// To be installed on jquery, to be used on a $('.money.field input')
const makeMoney = function () {
  this.each(function () {
    const input = $(this);

    storeAndDisplay(input);

    input.off('blur').blur(() => storeAndDisplay(input));

    input.off('focus').focus(() => {
      const value = input.data('userInput');

      if (value === '$0.00') {
        input.val('');
      } else {
        input.val(value);
      }
    });
  });

  return this;
};

// TODO: move to common frontend index file
$.fn.makeMoney = makeMoney;

window.formatMoney = formatMoney;
window.sanitizeMoney = sanitizeMoney;

export { formatMoney, sanitizeMoney, forceTwoDecimal, makeMoney };
