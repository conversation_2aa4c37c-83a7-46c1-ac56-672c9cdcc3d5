import PropTypes from 'prop-types';
import { Component } from 'react';
import { filter, map } from 'lodash';

import ChatItem from './chat_item';
import SearchInput from './search_input';
import { sortChatsByDate } from './util';

class ChatList extends Component {
  constructor(props) {
    super(props);
    this.state = { filter: '' };
  }

  filteredChats= chats => {
    const searchFilter = this.state.filter.toLowerCase().replace(/[^a-z]/g, '');
    return filter(chats, chat => chat.title.toLowerCase().replace(/[^a-z]/g, '').includes(searchFilter));
  }

  render() {
    const chats = sortChatsByDate(this.filteredChats(this.props.chats));

    return (
      <div>
        <h1 className="ui header">
          Chats
        </h1>
        <SearchInput
          value={this.state.filter}
          onChange={event => this.setState({ filter: event.target.value })}
        />
        <div className="ui selection list">
          {map(chats, (chat, i) => <ChatItem key={i} chat={chat} onClick={() => this.props.onClickChat(chat)} />)}
        </div>
      </div>
    );
  }
}

ChatList.propTypes = {
  chats: PropTypes.array.isRequired,
  onClickChat: PropTypes.func.isRequired,
};

export default ChatList;
