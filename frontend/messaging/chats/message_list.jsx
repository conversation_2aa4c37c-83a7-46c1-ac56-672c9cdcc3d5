import PropTypes from 'prop-types';

import Message from './message';
import moment from 'moment';
import { map, sortBy } from 'lodash';

// A message gets an avatar header if it is a new day, different author, or it
// has been more than 5 minutes.
const shouldBeAvatar = (lastMessage, message) => {
  if (!lastMessage) {
    return true;
  }

  var timeOut = moment(message.created_at).subtract(5, 'minutes').isAfter(moment(lastMessage.created_at));
  var differentAuthors = message.author.id !== lastMessage.author.id;
  return timeOut || differentAuthors;
};

const MessageList = ({ messages }) => {
  let lastMessage = null;

  const sorted = sortBy(messages, message => moment(message.created_at));

  const messageItems = map(sorted, message => {
    const avatar = shouldBeAvatar(lastMessage, message);
    lastMessage = message;
    return (
      <Message key={message.id}
        avatar={avatar}
        author={message.author}
        body={message.body}
        created_at={message.created_at}
      />
    );
  });

  const noneMessage = messageItems.length === 0 ? (
    <div className="meta">
      No recent activity
    </div>
  ) : null;

  return (
    <div className="ui comments">
      {noneMessage}
      {messageItems}
    </div>
  );
};

MessageList.propTypes = {
  messages: PropTypes.array.isRequired,
};

export default MessageList;
