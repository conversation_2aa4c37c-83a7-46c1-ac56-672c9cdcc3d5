import PropTypes from 'prop-types';
import className from 'classnames';
import moment from 'moment';
import { truncate } from 'lodash';

import { lastMessage } from './util';

const ChatItem = ({ onClick, chat }) => {
  const message = lastMessage(chat);

  const hint = message ?
    `${message.you ? 'You: ' : ''} ${truncate(message.body, { length: 20 })}`
      : null;

  const date = message ?
    moment(message.created_at).fromNow()
      : null;

  const online = chat.online;

  return (
    <a
      key={chat.id}
      className={className('item', { online })}
      onClick={onClick}
    >
      <div className="ui avatar image">
        <img src={chat.avatar_url} style={{ borderRadius: '.25rem', maxWidth: 'unset' }} />
      </div>
      <div className="content">
        <div className="header" style={{ display: 'inline-block' }}>
          {chat.title}
        </div>
        <div className="metadata" style={{ display: 'inline-block', marginLeft: '.5em', fontSize: '.875em' }}>
          <span className="date">
            {date}
          </span>
        </div>
        <div className="meta" style={{ fontSize: '.875em' }}>
          {hint}
        </div>
      </div>
    </a>
  );
};

ChatItem.propTypes = {
  onClick: PropTypes.func.isRequired,
  chat: PropTypes.object.isRequired,
}

export default ChatItem;
