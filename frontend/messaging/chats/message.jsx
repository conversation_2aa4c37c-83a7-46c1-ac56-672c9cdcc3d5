import PropTypes from 'prop-types';
import Linkify from 'react-linkify';
import moment from 'moment';

const AvatarMessage = ({ author, body, created_at }) => (
  <div className="avatar comment">
    <a className="avatar">
      <img src={author.avatar_url} role="presentation" />
    </a>
    <div className="content">
      <a className="author">{author.name}</a>
      <div className="metadata">
        <span className="date">
          {moment(created_at).fromNow()}
        </span>
      </div>
      <div className="text">
        <Linkify properties={{ target: '_blank' }}>
          {body}
        </Linkify>
      </div>
    </div>
  </div>
);

const SimpleMessage = ({ body, created_at }) => (
  <div className="comment">
    <div className="content">
      <div className="metadata">
        <span className="date">
          {moment(created_at).format('h:mm')}
        </span>
      </div>
      <div className="text">
        <Linkify properties={{ target: '_blank' }}>
          {body}
        </Linkify>
      </div>
    </div>
  </div>
);

const Message = props => {
  if (props.avatar) {
    return <AvatarMessage {...props} />;
  }

  return <SimpleMessage {...props} />;
};

Message.propTypes = {
  avatar: PropTypes.bool.isRequired,
  author: PropTypes.object.isRequired,
  body: PropTypes.string.isRequired,
  created_at: PropTypes.string.isRequired,
};

Message.defaultProps = {
  avatar: true,
};

export default Message;
