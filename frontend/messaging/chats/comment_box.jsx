import PropTypes from 'prop-types';
import { Component } from 'react';

class CommentBox extends Component {
  constructor(props) {
    super(props);
    this.state = { comment: '' };
    this.onKeyPress = this.handleKeyPress.bind(this);
    this.onChange = this.handleChange.bind(this);
  }

  handleKeyPress(e) {
    // Enter without shift
    if (e.which === 13 && !e.shiftKey) {
      e.preventDefault();
      this.props.onComment(e.target.value);
      this.setState({ comment: '' });
    }
  }

  handleChange(event) {
    const text = $(event.target).val();

    const max = 1000;

    if (text.length > max) {
      this.setState({ comment: text.substring(0, max) });
    } else {
      this.setState({ comment: text });
    }
  }

  render() {
    return (
      <textarea
        className="transparent"
        rows="2"
        placeholder="Type a message..."
        value={this.state.comment}
        onKeyPress={this.onKeyPress}
        onChange={this.onChange}
      />
    );
  }
}

CommentBox.propTypes = {
  onComment: PropTypes.func.isRequired,
};

export default CommentBox;
