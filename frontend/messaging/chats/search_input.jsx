import PropTypes from 'prop-types';

const SearchInput = ({ onChange, value }) => (
  <div className="ui small fluid icon input">
    <i className="search icon" />
    <input
      type="text"
      placeholder="Search..."
      value={value}
      onChange={onChange}
    />
  </div>
);

SearchInput.propTypes = {
  onChange: PropTypes.func.isRequired,
  value: PropTypes.string.isRequired,
};

export default SearchInput;
