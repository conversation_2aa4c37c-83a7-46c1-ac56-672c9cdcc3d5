import PropTypes from 'prop-types';
import { Component } from 'react';
import { uniqBy } from 'lodash';

import CommentBox from './comment_box';
import MessageList from './message_list';

class ChatRoom extends Component {
  constructor(props) {
    super(props);
    this.state = { messages: props.recentMessages };
  }

  componentDidMount() {
    this.scroll();

    const { id, type } = this.props;

    this.follow(id, type);
  }

  componentWillReceiveProps(props) {
    this.unfollow(this.props.id, this.props.type);
    this.follow(props.id, props.type);
    this.setState({ messages: props.recentMessages });
  }

  componentDidUpdate() {
    this.scroll();
  }

  componentWillUnmount() {
    const { id, type } = this.props;
    App.chat.unfollow(type, id);
  }

  follow(id, type) {
    App.chat.follow(type, id, message => {
      const { messages } = this.state;

      this.setState({
        messages: uniqBy([...messages, message], 'id'),
      });
    });
  }

  unfollow(id, type) {
    App.chat.unfollow(type, id);
  }

  speak(message) {
    const { id, type } = this.props;
    App.chat.speak(message, type, id);
  }

  scroll() {
    const collection = $('.ui.comments');
    const height = collection[0].scrollHeight;
    collection.scrollTop(height);
  }

  render() {
    const comment = this.speak.bind(this);

    return (
      <div className="chat-room">
        <MessageList messages={this.state.messages} />
        <form className="ui reply form">
          <div className="field">
            <CommentBox onComment={comment} />
          </div>
        </form>
      </div>
    );
  }
}

ChatRoom.propTypes = {
  id: PropTypes.number.isRequired,
  type: PropTypes.string.isRequired,
  recentMessages: PropTypes.array.isRequired,
};

export default ChatRoom;
