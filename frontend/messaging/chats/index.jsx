import PropTypes from 'prop-types';
import moment from 'moment';
import uuid from 'uuid';
import { Component } from 'react';
import { find, forEach, includes, map, sortBy, unionBy } from 'lodash';

import { register<PERSON>and<PERSON>, unregister<PERSON>andler } from '../../notifications/notify';

import ChatList from './chat_list';
import ChatRoom from './chat_room';
import { sortChatsByDate } from './util';

class Chats extends Component {
  constructor(props) {
    super(props);

    this.state = { chats: props.chats };

    this.handler = {
      id: uuid.v4(),
      kind: 'message',
      handle: this.handleMessageEvent,
    };
  }

  componentDidMount() {
    registerHandler(this.handler);
    OnlineUsers.subscribe(this);
  }

  componentWillUnmount() {
    unregisterHandler(this.handler);
    OnlineUsers.unsubscribe(this);
  }

  onlineUsersChanged = onlineUserIds => {
    const chats = map(this.state.chats, chat => {
      if (includes(onlineUserIds, chat.user_id)) {
        return { ...chat, online: true };
      }

      return { ...chat, online: false };
    });

    this.setState({ chats });
  }

  handleMessageEvent = event => {
    const chats = map(this.state.chats, chat => {
      if (chat.id === event.chat_room_id) {
        return {
          ...chat,
          recent_messages: sortBy(
            unionBy([...chat.recent_messages, event], 'id'),
            message => moment(message.created_at)
          ),
        };
      }

      return chat;
    });

    this.setState({ chats });
    return true;
  }

  handleClickChat = chat => {
    if (chat.id) {
      this.setState({ selectedChatId: chat.id });
    } else {
      this.loadChat(chat.username);
    }
  }

  loadChat = username => {
    $.get({
      url: `/messaging/chats/@${username}`,
      success: chat => {
        this.setState({ chats: unionBy([chat], this.state.chats, 'username') }, () => this.setState({ selectedChatId: chat.id }));
      },
    });
  }

  render() {
    const { chats, selectedChatId } = this.state;

    let chat;
    if (selectedChatId) {
      chat = find(chats, { id: selectedChatId });
    } else {
      chat = find(sortChatsByDate(chats), chat => chat.id);
    }

    let chatRoom;
    if (chat) {
      chatRoom = <ChatRoom
        id={chat.id}
        type="chat"
        recentMessages={chat.recent_messages}
      />;
    } else if (selectedChatId) {
      chatRoom = <div className="ui active loader" />;
    } else {
      chatRoom = null;
    };

    return (
      <div style={{ flex: '1 1 auto', overflowY: 'hidden', display: 'flex' }}>
        <div
          className="chats list"
          style={{
            overflowY: 'auto',
            maxWidth: '300px',
            flex: '1 1 auto',
          }}
        >
          <ChatList
            chats={this.state.chats}
            onClickChat={this.handleClickChat}
          />
        </div>
        <div
          style={{
            padding: '0',
            display: 'flex',
            flexDirection: 'column',
            flex: '1 1 auto',
          }}
        >
          <h1 className="ui chat header">
            {chat && chat.title}
          </h1>
          {chatRoom}
        </div>
      </div>
    );
  }
}

Chats.propTypes = {
  chats: PropTypes.array.isRequired,
};

export default Chats;
