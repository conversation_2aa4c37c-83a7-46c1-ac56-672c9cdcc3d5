import { last, sortBy } from 'lodash';
import moment from 'moment';

// The most recent message in a chat
export const lastMessage = chat => (chat ? last(chat.recent_messages) : null);

// The date of the most recent message in a chat
export const lastMessageDate = chat => {
  if (chat) {
    const message = lastMessage(chat);
    if (message) {
      return moment(message.created_at);
    }
  }
  return null;
};

// Sort chats in ascending order by their chat's last message date
export const sortChatsByDate = chats => sortBy(sortBy(chats, 'title'), chat => -lastMessageDate(chat));
