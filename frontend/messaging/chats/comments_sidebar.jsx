import PropTypes from 'prop-types';

import ChatRoom from './chat_room';

const CommentsSidebar = ({ type, id, messages }) => (
  <div style={{ display: 'flex', flexDirection: 'column', flex: '1 1 auto', overflowY: 'hidden' }}>
    <h2 className="ui chat header">
      Comments
    </h2>
    <ChatRoom
      type={type}
      id={id}
      recentMessages={messages}
      title="Comments"
    />
  </div>
);

CommentsSidebar.propTypes = {
  id: PropTypes.number.isRequired,
  messages: PropTypes.array.isRequired,
  type: PropTypes.string.isRequired,
};

export default CommentsSidebar;
