import { map, truncate } from 'lodash';
import PropTypes from 'prop-types';

const EventsList = ({ events, onRemoveEvent }) => {
  if (events.length === 0) {
    return <i>No events scheduled</i>;
  }

  const item = (event, i) => {
    const Tag = event.link ? 'a' : 'div';

    const click = {};
    if (Tag === 'a') {
      click.onClick = () => {
        $('#calendar-button').popup('hide');
      };
    }

    return (
      <Tag
        key={i}
        className="item"
        href={event.link}
        {...click}
      >
        {event.type === 'calendar_event' &&
          <a onClick={() => onRemoveEvent(event)}>
            <i className="grey right floated remove icon" />
          </a>
        }
        <div className="content">
          <div className="header">
            {event.title}
          </div>
          <div className="meta" style={{ color: 'gray' }}>
            <i>{truncate(event.description || 'No Description', { length: 240 })}</i>
          </div>
        </div>
      </Tag>
    );
  };

  return (
    <div className="ui divided list">
      {map(events, (event, i) => item(event, i))}
    </div>
  );
};

EventsList.propTypes = {
  events: PropTypes.array.isRequired,
  onRemoveEvent: PropTypes.func.isRequired,
};

export default EventsList;
