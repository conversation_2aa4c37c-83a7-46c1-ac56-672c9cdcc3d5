import PropTypes from 'prop-types';
import classNames from 'classnames';
import moment from 'moment';
import { Component } from 'react';

const combine_date_time = (date, time) => {
  const tmp = moment(time, 'HH:mm')
  return moment(date)
    .hour(tmp.hour())
    .minute(tmp.minute())
    .second(0)
    .millisecond(0)
}

class NewEvent extends Component {
  state = { event_start: '', event_end: '', submitting: false };

  handleSubmit = event => {
    event.preventDefault();

    this.setState({ submitting: true });

    const { target: form } = event;
    const { date } = this.props;
    const { event_start, event_end } = this.state;

    const data = $(form).serializeArray();

    if (event_start && event_end) {
      const start = combine_date_time(date, event_start);
      const end = combine_date_time(date, event_end);

      data.push(
        { name: 'calendar_event[start_date]', value: start.format() }
      )
      data.push(
        { name: 'calendar_event[end_date]', value: end.format() }
      )
    } else {
      data.push({ name: 'calendar_event[date]', value: date })
    }

    $.post({
      url: '/manage/calendar_events.json',
      data,
      dataType: 'json',
      success: calendarEvent => {
        this.setState({ submitting: false });
        this.props.onEventAdded(calendarEvent);
      },
    });
  }

  render() {
    const { submitting } = this.state;

    return (
      <form
        className="ui new event tiny form"
        onSubmit={this.handleSubmit}
        style={{ marginTop: '1em' }}
      >
        <div className="required field">
          <label htmlFor="event_title">Title</label>
          <input id="event_title" name="calendar_event[title]" type="text" />
        </div>

        <div className="fields">
          <div className="field">
            <label>Start</label>
            <input
              type="time"
              onChange={(ev) => {
                this.setState({ event_start: ev.target.value })
              }}
              value={this.state.event_start}
            />
          </div>

          <div className="field">
            <label>End</label>
            <input
              type="time"
              onChange={(ev) => {
                this.setState({ event_end: ev.target.value })
              }}
              value={this.state.event_end}
            />
          </div>
        </div>

        <div className="field">
          <label htmlFor="event_description">Description</label>
          <textarea
            rows={2}
            id="event_description"
            name="calendar_event[description]"
          />
        </div>

        <div className="ui two small compact buttons">
          <button
            type="button"
            className="ui button"
            onClick={this.props.onClickCancel}
          >
            <i className="ui remove icon" />
            Cancel
          </button>
          <button
            type="submit"
            className={classNames('ui', { loading: submitting }, 'button')}
          >
            <i className="ui check icon" />
            Save
          </button>
        </div>
      </form>
    );
  }
}

NewEvent.propTypes = {
  date: PropTypes.instanceOf(Date).isRequired,
  onClickCancel: PropTypes.func.isRequired,
  onEventAdded: PropTypes.func.isRequired,
};

NewEvent.defaultProps = {
  date: moment().toDate(),
};

export default NewEvent;
