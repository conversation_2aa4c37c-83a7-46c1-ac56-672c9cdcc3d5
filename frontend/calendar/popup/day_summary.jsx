import PropTypes from 'prop-types';
import moment from 'moment';
import { Component } from 'react';
import { capitalize } from 'lodash';

import NewEvent from './new_event';
import EventsList from './events_list';

// Sunday, February 14th 2010
const dateFormat = 'dddd, MMMM Do YYYY';

class DaySummary extends Component {
  state = { addingEvent: false };

  handleClickAdd = () => this.setState({ addingEvent: true });

  handleClickCancel = () => this.setState({ addingEvent: false });

  handleEventAdded = event => {
    this.setState({ addingEvent: false });
    this.props.onEventAdded(event);
  }

  render() {
    const { date, events, onRemoveEvent } = this.props;

    const header = moment(date).format(dateFormat);

    let subheader;
    if (moment().isSame(date, 'day')) {
      subheader = 'today';
    } else {
      subheader = moment(date).fromNow();
    }

    const content = this.state.addingEvent ?
      <NewEvent
        date={date}
        onClickCancel={this.handleClickCancel}
        onEventAdded={this.handleEventAdded}
      /> : <EventsList events={events} onRemoveEvent={onRemoveEvent} />;

    const addButton = this.state.addingEvent ?
      null :
      <button
        className="ui small compact fluid button"
        onClick={this.handleClickAdd}
        style={{ marginTop: '1em' }}
      >
        <i className="ui plus icon" />
        Add Event
      </button>;

    return (
      <div className="day summary">
        <h4 className="ui header">
          {header}
          <div className="sub header">
            {capitalize(subheader)}
          </div>
        </h4>
        {content}
        {addButton}
      </div>
    );
  }
}

DaySummary.propTypes = {
  date: PropTypes.instanceOf(Date).isRequired,
  events: PropTypes.array.isRequired,
  onEventAdded: PropTypes.func.isRequired,
  onRemoveEvent: PropTypes.func.isRequired,
};

DaySummary.defaultProps = {
  date: moment().toDate(),
};

export default DaySummary;
