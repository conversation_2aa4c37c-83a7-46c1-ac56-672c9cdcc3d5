import moment from 'moment';
import { Component } from 'react';
import { find, filter, reject } from 'lodash';

import DaySummary from './day_summary';

class CalendarPopup extends Component {
  state = { events: [] };

  componentDidMount() {
    // Calendar Settings
    this.calendar({
      inline: true,
      type: 'date',
      onChange: this.handleDateChanged,
      formatter: {
        cell: this.cellFormatter,
      },
    });

    $(document).on('turbolinks:load', this.hookupButton);

    this.hookupButton();

    this.poll();

    // Prevent rerenders
    $('div[data-react-class="CalendarPopup"]').removeAttr('data-react-class');
  }

  hookupButton = () => $('#calendar-button').popup({
    on: 'click',
    onShow: this.handleShowPopup,
    popup: '#calendar-popup',
    position: 'bottom center',
    transition: 'slide down',
    movePopup: false,
  });

  calendar = (...args) => $(this.calendarRef).calendar(...args);

  cellFormatter = (cell, date) => {
    const comp = moment(date);

    if (find(this.state.events, event => comp.isSame(event.start_date, 'day'))) {
      cell.addClass('events');
    } else {
      cell.removeClass('events');
    }
  }

  poll = () => {
    const interval = 1000 * 60 * 60; // 1 hour
    this.fetchEvents();
    setTimeout(this.poll, interval);
  };

  fetchEvents = () => $.get('/manage/calendar_events.json', events => this.setState({ events }));

  // Reset the date to today when the popup is opened
  handleShowPopup = () => this.calendar('set date', moment().toDate());

  handleDateChanged = date => this.setState({ date });

  handleEventAdded = event => this.setState({ events: [...this.state.events, event] });

  handleRemoveEvent = ({ id }) => {
    $.ajax({
      url: `/manage/calendar_events/${id}`,
      method: 'delete',
      success: () => this.setState({
        events: reject(this.state.events, { id }),
      }),
    });
  }

  render() {
    const { date, events } = this.state;

    const daysEvents = filter(events, event => moment(date).isSame(moment(event.start_date), 'day'));

    return (
      <div id="calendar-popup" className="ui popup">
        <div className="ui calendar" ref={c => this.calendarRef = c} />
        <DaySummary
          date={date}
          events={daysEvents}
          onEventAdded={this.handleEventAdded}
          onRemoveEvent={this.handleRemoveEvent}
        />
      </div>
    );
  }
}

export default CalendarPopup;
