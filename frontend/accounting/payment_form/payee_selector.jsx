import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { pickPayee } from './actions';

import Dropdown from '../../selectors/dropdown';

const PayeeSelector = ({ defaultSelection, onChange }) => (
  <div className="ui required field">
    <label htmlFor="payee_gid">
      Recipient
    </label>
    <Dropdown
      id="payee_gid"
      defaultText="Recipient"
      url="/accounting/payables/payments/payees.json"
      name="payee_gid"
      onChange={onChange}
      defaultSelection={defaultSelection}
    />
  </div>
);

PayeeSelector.propTypes = {
  defaultSelection: PropTypes.number,
  onChange: PropTypes.func.isRequired,
};

const mapState = state => ({
  defaultSelection: state.defaultPayeeId,
});

const mapDispatch = dispatch => ({
  onChange: payee => dispatch(pickPayee(payee)),
});

export default connect(mapState, mapDispatch)(PayeeSelector);
