import PropTypes from 'prop-types';
import { connect } from 'react-redux';

const Header = ({ paymentMode }) => {
  const text = paymentMode === 'ach' ?
    'Make Electronic Payment' : 'Record Check Payment';

  return (
    <div className="ui breadcrumb">
      <a className="segment" href="/accounting/payables">Payables</a>
      <i className="right angle icon divider" />
      <h1 className="ui header">{text}</h1>
    </div>
  );
};

Header.propTypes = {
  paymentMode: PropTypes.oneOf(['ach', 'check']),
};

const mapState = state => ({ paymentMode: state.paymentMode });

export default connect(mapState)(Header);
