import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

class MemoInput extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      memo: props.memo || ''
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.memo !== this.props.memo) {
      this.setState({ memo: this.props.memo || '' });
    }
  }

  render() {
    const { paymentMode } = this.props;

    if (paymentMode !== 'ach') return null;

    return (
      <div className="field">
        <label htmlFor="payment_description">Memo</label>
        <input
          type="text"
          name="payment[description]"
          id="payment_description"
          value={this.state.memo}
          maxLength="140"
          placeholder="Enter payment memo (will appear on recipient's bank statement)"
          onChange={e => this.setState({ memo: e.target.value })}
        />
      </div>
    );
  }
}

MemoInput.propTypes = {
  memo: PropTypes.string,
  paymentMode: PropTypes.string.isRequired
};

const mapStateToProps = state => {
  const selectedInvoice = state.invoices?.find(invoice => invoice.selected);

  return {
    memo: selectedInvoice?.description || state.memo || '',
    paymentMode: state.paymentMode
  };
};

export default connect(mapStateToProps)(MemoInput);
