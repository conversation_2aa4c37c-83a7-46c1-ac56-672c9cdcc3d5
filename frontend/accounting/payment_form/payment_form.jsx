import { Component } from 'react';
import { applyMiddleware, createStore } from 'redux';
import { Provider } from 'react-redux';
import thunk from 'redux-thunk';

import RailsForm from '../../forms/rails_form';

import reducer from './reducer';

import BankAccountSelector from './bank_account_selector';
import CheckInput from './check_input';
import Header from './header';
import Invoices from './invoices';
import MemoInput from './memo_input';
import OverdraftWarning from './overdraft_warning';
import PayeeSelector from './payee_selector';
import PaymentControls from './payment_controls';
import PaymentModeSelector from './payment_mode_selector';

class PaymentForm extends Component {
  constructor(props) {
    super(props);

    const defaultState = { paymentMode: 'ach' };

    const params = (new URI(window.location)).search(true);
    const { invoice_id: invoiceId, payee_id: payeeId } = params;

    defaultState.defaultPayeeId = payeeId;
    defaultState.defaultInvoiceId = invoiceId ? parseInt(invoiceId, 10) : undefined;
    defaultState.selectAll = (payeeId && !invoiceId) ? true : undefined;

    this.store = createStore(reducer, defaultState, applyMiddleware(thunk));
    this.state = { submitting: false };
  }

  componentDidMount() {
    const $newPayment = $('#new_payment');

    $newPayment.form({
      fields: {
        bank_account_id: 'empty',
      },
    });

    $newPayment.on('submit', async (e) => {
      e.preventDefault();
      if (!$newPayment.form('is valid')) {
        $('.port').animate({ scrollTop: 0 });

        return;
      };

      let data = new FormData($newPayment[0]);

      this.setState({ data, submitting: true, form: e.target });

      let { safe_to_create } = await $.ajax({
        method: "POST",
        url: "/accounting/payables/payments/safe_to_create",
        processData: false,
        contentType: false,
        data
      });

      if (safe_to_create) return this.createPayment();

      this.setState({ submitting: false });
      $('#overdraft-modal').modal('show');
    });
  }

  createPayment = () => {
    this.setState({ submitting: true });
    $('#new_payment').off('submit');
    this.state.form.submit();
  };

  render() {
    return (
      <Provider store={this.store}>
        <RailsForm
          id="new_payment"
          action="/accounting/payables/payments"
        >
          <Header />
          <PayeeSelector />
          <PaymentModeSelector />
          <BankAccountSelector />
          <CheckInput />
          <MemoInput />
          <Invoices maximumCount={this.props.maximum_count} />
          <PaymentControls submitting={this.state.submitting} />
          <OverdraftWarning createPayment={this.createPayment} />
        </RailsForm>
      </Provider>
    );
  }
}

export default PaymentForm;
