import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _, { includes, sumBy } from 'lodash';

import InvoiceSelectionTable from '../invoice_selection_table';
import { togglePayment } from './actions';

const Invoices = ({ payee, invoices, onChangePay, selectedIds, maximumCount }) => {
  if (!payee) {
    return (
      <div className="ui info message">
        Please select a recipient.
      </div>
    );
  }

  if (!invoices) {
    return (
      <div className="ui very basic active loading segment" />
    );
  }

  const amount = sumBy(invoices, invoice => {
    if (includes(selectedIds, invoice.id)) {
      return parseFloat(invoice.balance);
    }

    return 0;
  });

  return (
    <div>
      {
        maximumCount && (invoices.length === maximumCount) && (
          <div className="ui info message">
            The maximum number of invoices are displayed, some invoices may not be shown.
          </div>
        )
      }
      <InvoiceSelectionTable
        invoices={invoices}
        amount={amount}
        ids={selectedIds}
        onChangePay={onChangePay}
      />
      <input
        type="hidden"
        name="invoice_ids"
        value={selectedIds}
      />
    </div>
  );
};

Invoices.propTypes = {
  payee: PropTypes.object,
  invoices: PropTypes.array,
  selectedIds: PropTypes.array.isRequired,
  onChangePay: PropTypes.func.isRequired
};

const mapState = state => ({
  payee: state.payee,
  invoices: state.invoices,
  selectedIds: _(state.invoices)
    .filter('selected')
    .map('id')
    .value(),
});

const mapDispatch = dispatch => ({
  onChangePay: invoiceId => dispatch(togglePayment(invoiceId)),
});

export default connect(mapState, mapDispatch)(Invoices);
