import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import Calendar<PERSON><PERSON> from '../../forms/fields/calendar_field';

const CheckInput = ({ visible }) => {
  if (!visible) {
    return null;
  }

  return (
    <div className="fields">
      <div className="eight wide required field">
        <label htmlFor="check_memo">Memo</label>
        <input id="check_memo" type="text" name="payment[description]" />
      </div>
      <CalendarField
        className="four wide required field"
        defaultValue={new Date().toString()}
        id="check_date"
        label="Date"
        name="payment[date]"
        placeholder=""
      />
      <div className="four wide field">
        <label htmlFor="check_number">Check Number</label>
        <input
          id="check_number"
          name="payment[check_number]"
          type="number"
          min={1}
        />
      </div>
    </div>
  );
};

CheckInput.propTypes = { visible: PropTypes.bool.isRequired };

const mapState = state => ({ visible: state.paymentMode === 'check' });

export default connect(mapState)(CheckInput);
