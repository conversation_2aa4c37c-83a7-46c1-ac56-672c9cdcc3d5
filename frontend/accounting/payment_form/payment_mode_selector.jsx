import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import OrButton from '../../components/or_button';

import { setPaymentMode } from './actions';

const PaymentModeSelector = ({ payee, onChange }) => {
  if (!payee) {
    return null;
  }

  if (!payee.ach_available) {
    return (
      <div className="ui info message">
        This recipient does not have electronic payments setup.
        You will only be able to record check payments.
      </div>
    );
  }

  return (
    <div className="required field">
      <OrButton
        left="Electronic"
        right="Check"
        leftColor="orange"
        rightColor="orange"
        onChange={onChange}
      />
    </div>
  );
};

PaymentModeSelector.propTypes = {
  onChange: PropTypes.func.isRequired,
  payee: PropTypes.shape({
    ach_available: PropTypes.bool.isRequired,
  }),
};

const mapState = state => ({
  payee: state.payee,
});

const mapDispatch = dispatch => ({
  onChange: check => dispatch(setPaymentMode(check ? 'check' : 'ach')),
});

export default connect(mapState, mapDispatch)(PaymentModeSelector);
