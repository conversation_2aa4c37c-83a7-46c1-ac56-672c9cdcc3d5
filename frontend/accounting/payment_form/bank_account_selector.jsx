import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import Dropdown from '../../selectors/dropdown';

const updateCheckNumber = bankAccount => {
  if (bankAccount) {
    $('form#new_payment #check_number')
      .attr('placeholder', bankAccount.next_check_number);
  }
};

const BankAccountSelector = ({ achRequired }) => {
  const url = achRequired ?
    '/organization/bank_accounts.json?ach_credit_available=true'
    :
    '/organization/bank_accounts.json';

  return (
    <div className="ui required field">
      <label htmlFor="bank_account_id">
        Bank Account
      </label>
      <Dropdown
        id="bank_account_id"
        defaultText="Bank Account"
        url={url}
        name="bank_account_id"
        onChange={updateCheckNumber}
      />
    </div>
  );
};

BankAccountSelector.propTypes = {
  achRequired: PropTypes.bool.isRequired,
};

const mapState = state => ({
  achRequired: (state.payee !== undefined) && (state.paymentMode === 'ach'),
});

export default connect(mapState)(BankAccountSelector);
