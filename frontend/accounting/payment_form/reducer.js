import { map } from 'lodash';

import {
  SET_PAYEE,
  SET_INVOICES,
  SET_PAYMENT_MODE,
  TOGGLE_PAYMENT,
} from './actions';

const initialState = {
  paymentMode: 'ach',
  selectedInvoices: [],
  selectAll: false,
  payee: null,
  bankAccount: null,
  memo: '',
  invoices: undefined,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case SET_PAYEE:
      return { ...state, payee: action.payee, invoices: undefined };
    case SET_INVOICES:
      const invoices = map(action.invoices, invoice => {
        if (state.selectAll || state.defaultInvoiceId === invoice.id) {
          const selected = true;
          
          if (!state.memo) state.memo = invoice.description;

          return { ...invoice, selected };
        }

        return invoice;
      });

      return { ...state, invoices };
    case TOGGLE_PAYMENT:
      const updatedInvoices = map(state.invoices, invoice => {
        if (invoice.id === action.invoiceId) {
          const selected = !invoice.selected;
          
          if (selected && !state.memo) {
            state.memo = invoice.description;
          }

          return { ...invoice, selected };
        }
        return invoice;
      });
      
      return { ...state, invoices: updatedInvoices };
    case SET_PAYMENT_MODE:
      return { ...state, paymentMode: action.mode };
    default:
      return state;
  }
}
