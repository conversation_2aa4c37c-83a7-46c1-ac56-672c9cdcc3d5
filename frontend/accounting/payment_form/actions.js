// Updates the state with the new payee
export const SET_PAYEE = 'SET_PAYEE';
const setPayee = payee => ({ type: SET_PAYEE, payee });

// Updates the state with the new invoices
export const SET_INVOICES = 'SET_INVOICES';
const setInvoices = invoices => ({ type: SET_INVOICES, invoices });

// Fetches the invoices for the new payee and dispatches setInvoices
export const FETCH_INVOICES = 'FETCH_INVOICES';
const fetchInvoices = payee => dispatch => $.get({
  url: '/accounting/payables/invoices.json',
  data: { payee_gid: payee.id, unpaid: true },
  success: invoices => dispatch(setInvoices(invoices)),
});

// Toggles payment selection for an invoice id
export const TOGGLE_PAYMENT = 'TOGGLE_PAYMENT';
export const togglePayment = invoiceId => ({ type: TOGGLE_PAYMENT, invoiceId });

// Changes the payment mode
export const SET_PAYMENT_MODE = 'SET_PAYMENT_MODE';
export const setPaymentMode = mode => ({ type: SET_PAYMENT_MODE, mode });

// Dispatches setPayee and then fetchInvoices, updates payment mode
export const PICK_PAYEE = 'PICK_PAYEE';
export const pickPayee = payee => dispatch => {
  dispatch(setPayee(payee));
  dispatch(setPaymentMode(payee.ach_available ? 'ach' : 'check'));
  dispatch(fetchInvoices(payee));
};
