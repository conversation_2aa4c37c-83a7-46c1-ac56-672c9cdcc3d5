import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import _ from 'lodash';

import { formatMoney } from '../../util/money';

/*
 * 'Save' and 'Save and New' buttons,
 * only enabled for a positive payment amount.
 */
const PaymentControls = ({ disabled, text, amount, kind, submitting }) => (
  <div className="clearfix">
    <input
      type="hidden"
      value={formatMoney(amount, false)}
      name="payment[amount]"
    />
    <input
      type="hidden"
      value={kind}
      name="payment[kind]"
    />
    {!submitting &&
      <button className="right floated ui submit button" disabled={disabled}>
        {text} {formatMoney(amount, false)}
      </button>
    }
    {!submitting &&
      <button className="right floated basic ui submit button" name="new" disabled={disabled}>
        {text} and New
      </button>
    }
    {submitting &&
      <button className="right floated ui submit button" disabled>
        Submitting...
      </button>
    }
    {submitting &&
      <button className="right floated basic ui submit button" name="new" disabled>
        Submitting...
      </button>
    }
  </div>
);

PaymentControls.propTypes = {
  disabled: PropTypes.bool.isRequired,
  text: PropTypes.string.isRequired,
  kind: PropTypes.string.isRequired,
  amount: PropTypes.number.isRequired,
  submitting: PropTypes.bool.isRequired
};

const mapState = state => {
  const amount = _(state.invoices)
    .filter('selected')
    .map('balance')
    .sumBy(parseFloat) || 0;

  return {
    disabled: amount <= 0,
    text: state.paymentMode === 'check' ? 'Record' : 'Pay',
    kind: state.paymentMode,
    amount,
  };
};

export default connect(mapState)(PaymentControls);
