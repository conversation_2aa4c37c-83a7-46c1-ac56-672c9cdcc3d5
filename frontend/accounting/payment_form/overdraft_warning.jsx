import PropTypes from 'prop-types';

const OverdraftWarning = ({ createPayment, cancelPayment }) => {
  return (
    <div className="ui small modal" id="overdraft-modal">
      <i className="black close icon" />
      <div className="header">
        Potential Overdraft
      </div>
      <div className="content">
        According to our records, one or more of the accounts selected are at risk of being overdrafted if you go through with this batch process.
      </div>
      <div className="actions">
        <button className="ui cancel button">
          Cancel
        </button>
        <button className="button ui primary approve" onClick={createPayment}>
          Continue
        </button>
      </div>
    </div >
  );
};

OverdraftWarning.propTypes = {
  createPayment: PropTypes.func.isRequired
};

export default OverdraftWarning;
