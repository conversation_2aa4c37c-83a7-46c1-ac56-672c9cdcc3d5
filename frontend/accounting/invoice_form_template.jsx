import PropTypes from 'prop-types';
import { Component } from 'react';
import { combineReducers, createStore } from 'redux';
import moment from 'moment';

import DateField from '../leasing/application/fields/date_field';

import LineItemsArea from './line_items/line_items_area';
import lineItemsReducer from './line_items/reducer';
import RailsForm from '../forms/rails_form';

const store = createStore(combineReducers({ lineItems: lineItemsReducer }));

const baseValidations = {
  inline: true,
  on: 'submit',
  fields: {
    date: {
      identifer: 'invoice[post_date]',
      rules: [{ type: 'empty', prompt: 'Please enter a date' }],
    },
    dueDate: {
      identifer: 'invoice[due_date]',
      rules: [{ type: 'empty', prompt: 'Please enter a due date' }],
    },
    description: {
      identifier: 'invoice[description]',
      rules: [{ type: 'empty', prompt: 'Please enter a description' }],
    },
    line_item_description: {
      identifier: 'line_item_description',
      rules: [{ type: 'empty', prompt: 'Please enter a description' }],
    },
    line_item_quantity: {
      identifier: 'line_item_quantity',
      rules: [{ type: 'empty', prompt: 'Please enter a quantity' }],
    },
  },
};

class InvoiceFormTemplate extends Component {
  constructor(props) {
    super(props);
    this.state = { amount: 0, description: '', invoiceNumber: '' };
  }

  componentDidMount() {
    const { url } = this.props;
    if (url) {
      $.get(url, invoice => {
        const { line_items } = invoice;

        store.dispatch({ type: 'SET_LINE_ITEMS', lineItems: line_items });
        this.setState({
          description: invoice.description,
          date: invoice.post_date,
          invoiceNumber: invoice.invoice_number,
        });
      });
    }

    this.applyValidations();
  }

  componentWillReceiveProps(props) {
    if (props.markup !== this.props.markup) {
      store.dispatch({ type: 'SET_MARKUP', markup: props.markup });
    }
  }

  applyValidations = () => {
    $('form#new_invoice').form(baseValidations);
  }

  handleAmountChanged(amount) {
    this.setState({ amount });
  }

  amount() {
    const amount = this.state.amount.toFixed(2);
    return `$${amount}`;
  }

  render() {
    const amountChanged = this.handleAmountChanged.bind(this);

    return (
      <RailsForm
        id="new_invoice"
        action={this.props.action}
      >
        {this.props.children}

        <br />
        <br />

        <div className="three fields">
          <DateField
            input={{
              id: 'invoice_post_date',
              name: 'invoice[post_date]',
              defaultValue: moment().toString(),
              onChange: date => this.setState({ date }),
              value: this.state.date,
            }}
            label="Date"
          />

          <DateField
            input={{
              id: 'invoice_due_date',
              name: 'invoice[due_date]',
              defaultValue: moment().add(30, 'days').toString(),
              onChange: date => this.setState({ due_date: date }),
              value: this.state.date,
            }}
            label="Due"
          />

          <div className="field">
            <label htmlFor="invoice_number">Invoice Number</label>
            <input
              type="text"
              id="invoice_number"
              name="invoice[invoice_number]"
              value={this.state.invoiceNumber}
              onChange={e => this.setState({ invoiceNumber: e.target.value }) }
            />
          </div>
        </div>

        <br />
        <br />

        <div className="required field">
          <label htmlFor="invoice_description">Description</label>
          <input
            type="text"
            id="invoice_description"
            name="invoice[description]"
            value={this.state.description}
            onChange={e => this.setState({ description: e.target.value }) }
          />
        </div>

        <br />
        <br />

        <LineItemsArea name="invoice" onAmountChanged={amountChanged} store={store} />

        <br />
        <br />

        <div className="fields" >
          <div className="twelve wide field" />
          <div className="four wide field">
            <h4 className="ui header">
              Total
            </h4>
            {this.amount()}
          </div>
        </div>

        <br />
        <br />

        <div
          className="ui right floated very basic segment"
          style={{ margin: 0, padding: 0 }}
        >
          <input className="ui submit button" type="submit" value="Save" />
          <input
            className="ui submit button"
            name="new"
            type="submit"
            value="Save and New"
          />
        </div>
      </RailsForm>
    );
  }
}

InvoiceFormTemplate.propTypes = {
  action: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  markup: PropTypes.number.isRequired,
  url: PropTypes.string.isRequired,
};

InvoiceFormTemplate.defaultProps = {
  markup: 0,
};

export default InvoiceFormTemplate;
