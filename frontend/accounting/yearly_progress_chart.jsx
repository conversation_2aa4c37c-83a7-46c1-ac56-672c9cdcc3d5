import PropTypes from 'prop-types';

import MultiBarChart from '../charts/multi_bar/multi_bar_chart';
import NetworkChart from '../charts/network_chart';
import AutoWidthChart from '../charts/auto_width_chart';

const url = (source, filter) => {
  const params = { ...filter, source };
  const uri = new URI('/accounting/yearly_progress');
  uri.addSearch(params);
  return uri.toString();
};

const Chart = new NetworkChart(MultiBarChart);

const YearlyProgressChart = props => (
  <Chart
    url={url(props.source, props.filter)}
    grid={{
      horizontal: true,
      vertical: false,
    }}
    legend={props.source === 'profit_loss'}
    {...props}
  />
);

YearlyProgressChart.propTypes = {
  filter: PropTypes.object.isRequired,
  source: PropTypes.string.isRequired,
};

YearlyProgressChart.defaultProps = {
  filter: {},
};

export default new AutoWidthChart(YearlyProgressChart);
