import PropTypes from 'prop-types';

import { formatMoney } from '../../util/money.jsx';

const Amount = ({ amountCents, balanceCents }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      flex: '1 1 auto',
      alignItems: 'flex-end',
      justifyContent: 'space-between',
    }}
  >
    <span>{formatMoney(amountCents, true)}</span>
    <i style={{ fontSize: '0.75em' }}>{formatMoney(balanceCents, true)} Balance</i>
  </div>
);

Amount.propTypes = {
  amountCents: PropTypes.number.isRequired,
  balanceCents: PropTypes.number.isRequired,
};

export default Amount;
