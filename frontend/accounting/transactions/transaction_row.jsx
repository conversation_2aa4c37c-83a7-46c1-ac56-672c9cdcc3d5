import PropTypes from 'prop-types';

import Amount from './amount.jsx';
import DateBox from './date_box.jsx';
import Description from './description.jsx';

const TransactionRow = ({ transaction, amountCents, balanceCents }) => (
  <a
    style={{
      display: 'flex',
      height: '48px',
    }}
    href={transaction.url}
    className="item"
  >
    <DateBox
      date={transaction.date}
    />
    <Description
      description={transaction.description}
      regarding={transaction.regarding}
      status={transaction.status}
      type={transaction.type}
    />
    <Amount
      amountCents={amountCents}
      balanceCents={balanceCents}
    />
  </a>
);

TransactionRow.propTypes = {
  transaction: PropTypes.shape({
    type: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
  }).isRequired,
  amountCents: PropTypes.number.isRequired,
  balanceCents: PropTypes.number.isRequired,
};

export default TransactionRow;
