import PropTypes from 'prop-types';

import StatusLabel from './status_label.jsx';

const direction = type => ((type === 'Invoice') ? 'for' : 'from');

const Description = ({ description, regarding, type, status }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
    }}
  >
    <div>{description}</div>
    <span style={{ fontSize: '0.75em' }}>
      <StatusLabel status={status} />
      {type} {direction(type)} {regarding}
    </span>
  </div>
);

Description.propTypes = {
  description: PropTypes.string.isRequired,
  regarding: PropTypes.string.isRequired,
  status: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
};

export default Description;
