import PropTypes from 'prop-types';
import { reverse } from 'lodash';

import Paginate from '../../pagination/paginate.jsx';
import TransactionRow from './transaction_row.jsx';

const Transactions = ({ transactions }) => {
  let balance = 0;

  const transactionRows = _.map(transactions, transaction => {
    const amount = transaction.amount_cents;

    balance += amount;

    return (
      <TransactionRow
        key={transaction.type + transaction.id}
        transaction={transaction}
        amountCents={amount}
        balanceCents={balance}
      />
    );
  });

  return (
    <Paginate
      blankHeight="48px"
      className="ui selection list"
      perPage={8}
    >
      {reverse(transactionRows)}
    </Paginate>
  );
};

Transactions.propTypes = {
  transactions: PropTypes.array.isRequired,
};

export default Transactions;
