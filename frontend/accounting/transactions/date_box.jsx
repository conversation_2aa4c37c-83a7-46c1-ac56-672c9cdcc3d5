import PropTypes from 'prop-types';
import moment from 'moment';

const DateBox = ({ date }) => {
  const momented = moment(date);
  const month = momented.format('MMM');
  const day = momented.format('DD');

  return (
    <div
      style={{
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'column',
        marginRight: '1em',
        width: '24px',
      }}
    >
      <div
        style={{
          textTransform: 'uppercase',
          fontSize: '0.6em',
        }}
      >
        {month}
      </div>
      <div>
        {day}
      </div>
    </div>
  );
};

DateBox.propTypes = {
  date: PropTypes.string.isRequired,
};

export default DateBox;
