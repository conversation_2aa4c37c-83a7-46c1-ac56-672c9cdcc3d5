import PropTypes from 'prop-types';

const color = status => {
  switch (status) {
    case 'paid':
      return 'green';
    default:
      return 'red';
  }
};

const StatusLabel = ({ status }) => (
  <div
    className={`ui empty ${color(status)} circular label`}
    style={{ fontSize: '0.75em', marginRight: '0.5em' }}
  />
);

StatusLabel.propTypes = {
  status: PropTypes.string.isRequired,
};

export default StatusLabel;
