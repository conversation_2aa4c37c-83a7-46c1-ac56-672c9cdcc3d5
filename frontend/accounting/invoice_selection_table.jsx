import PropTypes from 'prop-types';
import { forEach, includes, min, sumBy } from 'lodash';

import { formatMoney } from '../util/money';

const InvoiceSelectionTable = ({ invoices, amount, ids, onChangePay }) => {
  const rows = [];

  if (invoices.length === 0) {
    rows.push(
      <tr key={0}>
        <td colSpan={5}>(No Approved Unpaid Invoices)</td>
      </tr>
    );
  }

  let totalPayment = 0.0;
  let applied = 0.0;
  let remain = parseFloat(amount);

  forEach(invoices, invoice => {
    const { id } = invoice;

    const checked = includes(ids, id);

    const payment = checked ? min([remain, parseFloat(invoice.balance)]) : 0;

    const remaining = invoice.balance - payment;

    totalPayment += payment;
    remain -= payment;

    rows.push(
      <tr key={id}>
        <td>
          <div className="ui checkbox">
            <input
              id={`pay_${invoice.id}`}
              type="checkbox"
              checked={checked}
              onChange={() => onChangePay(id)}
            />
            <label htmlFor={`pay_${invoice.id}`}>
              {invoice.invoice_number}
            </label>
          </div>
        </td>
        <td>
          <a href={invoice.url} target="_blank">
            {invoice.description} <i className="external alternate icon" />
          </a>
        </td>
        <td>{invoice.physical_date}</td>
        <td className="right aligned">{formatMoney(invoice.balance)}</td>
        <td className="right aligned">{formatMoney(payment)}</td>
        <td className="right aligned">{formatMoney(remaining)}</td>
      </tr>,
    );
  });

  const totalBalance = sumBy(invoices, i => parseFloat(i.balance));
  const totalRemaining = totalBalance - totalPayment;

  rows.push(
    <tr key={rows.length}>
      <td colSpan={2}><b>Total</b></td>
      <td />
      <td className="right aligned"><b>{formatMoney(totalBalance)}</b></td>
      <td className="right aligned"><b>{formatMoney(totalPayment)}</b></td>
      <td className="right aligned"><b>{formatMoney(totalRemaining)}</b></td>
    </tr>,
  );

  return (
    <table className="ui fixed table">
      <thead>
        <tr>
          <th>Pay</th>
          <th>Description</th>
          <th>Date</th>
          <th className="right aligned">Balance</th>
          <th className="right aligned">Payment</th>
          <th className="right aligned">New Balance</th>
        </tr>
      </thead>
      <tbody>
        {rows}
      </tbody>
    </table>
  );
};

InvoiceSelectionTable.propTypes = {
  amount: PropTypes.number.isRequired,
  ids: PropTypes.array.isRequired,
  invoices: PropTypes.array.isRequired,
  onChangePay: PropTypes.func.isRequired,
};

export default InvoiceSelectionTable;
