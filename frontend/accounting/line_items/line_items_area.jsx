import PropTypes from 'prop-types';
import { Component } from 'react';
import { connect } from 'react-redux';
import { map, sumBy } from 'lodash';

import LineItemField from './line_item_field.jsx';

class LineItemsArea extends Component {
  componentDidMount() {
    const { store } = this.props;

    store.dispatch({ type: 'INITIALIZE' });
    store.subscribe(() => {
      const { lineItems } = store.getState();
      const total = sumBy(lineItems, 'amount');
      this.props.onAmountChanged(total);
    });
  }

  render() {
    const { clickAdd, lineItems, name, store } = this.props;
    const items = map(lineItems, (item, i) => (
      <LineItemField key={item.id} index={i} store={store} name={name} />
    ));

    return (
      <div id="line-items">
        <h4 className="ui dividing header">
          Line Items
        </h4>

        {items}

        <button
          className="ui right floated small basic button"
          onClick={e => { e.preventDefault(); clickAdd(); }}
        >
          <i className="plus icon" />
          Add
        </button>
      </div>
    );
  }
}

LineItemsArea.propTypes = {
  clickAdd: PropTypes.func.isRequired,
  lineItems: PropTypes.array.isRequired,
  name: PropTypes.string.isRequired,
  onAmountChanged: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  lineItems: state.lineItems,
});

const mapDispatchToProps = (dispatch) => ({
  clickAdd: () => dispatch({ type: 'ADD_LINE_ITEM' }),
});

const connected = connect(mapStateToProps, mapDispatchToProps)(LineItemsArea);

export default connected;
