import PropTypes from 'prop-types';
import { connect } from 'react-redux';

const RemoveButton = ({ disabled, onClick }) => {
  const className = `ui ${disabled ? 'disabled' : ''} small basic icon button`;

  return (
    <button
      className={className}
      style={{ marginTop: '20px' }}
      onClick={e => { e.preventDefault(); onClick(); }}
    >
      <i className="remove icon" />
    </button>
  );
};

RemoveButton.propTypes = ({
  disabled: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
});

const mapStateToProps = state => ({
  disabled: state.lineItems.length < 2,
});

const mapDispatchToProps = (dispatch, props) => ({
  onClick: () => dispatch({ type: 'REMOVE_LINE_ITEM', index: props.index }),
});

export default connect(mapStateToProps, mapDispatchToProps)(RemoveButton);
