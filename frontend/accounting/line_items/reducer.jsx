import update from 'immutability-helper';
import uuid from 'uuid';
import { assign, map, merge } from 'lodash';

const newItem = () => ({ id: uuid.v4(), description: '', unitPrice: 0, quantity: 1, amount: 0 });

const convertItem = item => ({
  id: uuid.v4(),
  description: item.description,
  unitPrice: (item.unit_price_cents / 100).toString(),
  quantity: item.quantity,
  amount: (item.unit_price_cents * item.quantity) / 100,
});

const initialState = [newItem()];

export default (state = initialState, action) => {
  switch (action.type) {
    case 'INITIALIZE':
      return initialState;
    case 'SET_LINE_ITEMS':
      return map(action.lineItems, convertItem);
    case 'ADD_LINE_ITEM':
      return update(state, { $push: [newItem()] });
    case 'REMOVE_LINE_ITEM':
      return update(state, { $splice: [[action.index, 1]] });
    case 'CHANGE_LINE_ITEM': {
      const { lineItem } = action;
      const amount = lineItem.unitPrice * lineItem.quantity;
      const originalPrice = lineItem.unitPrice;
      return update(state, { [action.index]: { $set: merge(lineItem, { originalPrice, amount }) } }); }
    case 'SET_MARKUP': {
      const markup = action.markup;

      const lineItems = map(state, item => {
        const originalPrice = item.originalPrice || item.unitPrice;
        const unitPrice = parseInt(originalPrice) + (parseInt(originalPrice) * markup / 100);
        const amount = unitPrice * parseInt(item.quantity);
        return assign({}, item, { originalPrice, unitPrice, amount });
      });

      return lineItems;
    }
    default:
      return state;
  }
};
