import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import Money<PERSON><PERSON> from '../../forms/fields/money_field';
import RemoveButton from './remove_button';

const LineItemField = ({ changeItem, item, name, store, index }) => (
  <div className="fields">
    <div className="six wide required field">
      <label htmlFor="item_description">Description</label>
      <input
        data-validate="line_item_description"
        id="item_description"
        name={`${name}[line_items_attributes][][description]`}
        onChange={e => changeItem(_.assign({}, item, { description: e.target.value }))}
        type="text"
        value={item.description}
      />
    </div>

    <MoneyField
      amount={item.unitPrice}
      className="three wide required field"
      id="item_unit_price"
      label="Unit Price"
      name={`${name}[line_items_attributes][][unit_price]`}
      onChange={unitPrice => changeItem(_.assign({}, item, { unitPrice }))}
    />

    <div className="three wide required field">
      <label htmlFor="item_quantity">Quantity</label>
      <input
        data-validate="line_item_quantity"
        id="item_quantity"
        min={0}
        name={`${name}[line_items_attributes][][quantity]`}
        onChange={e => changeItem(_.assign({}, item, { quantity: e.target.value }))}
        type="number"
        value={item.quantity}
      />
    </div>

    <div className="three wide field">
      <label>Amount</label>
      <div style={{ paddingTop: '8px' }}>
        ${item.amount.toFixed(2)}
      </div>
    </div>

    <div className="one wide field">
      <RemoveButton index={index} store={store} />
    </div>
  </div>
);

LineItemField.propTypes = {
  changeItem: PropTypes.func.isRequired,
  item: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
};

const mapStateToProps = (state, props) => ({
  item: state.lineItems[props.index],
});

const mapDispatchToProps = (dispatch, props) => ({
  changeItem: lineItem => dispatch({ type: 'CHANGE_LINE_ITEM', index: props.index, lineItem }),
});

export default connect(mapStateToProps, mapDispatchToProps)(LineItemField);
