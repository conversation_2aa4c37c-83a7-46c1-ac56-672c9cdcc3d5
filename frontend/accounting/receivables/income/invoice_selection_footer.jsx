import { formatMoney } from '../../../util/money';

import { sumBy } from 'lodash';

const sumColumn = (invoices, field) =>
  <th className="right aligned">{formatMoney(sumBy(invoices, field), true)}</th>;

const InvoiceSelectionFooter = ({ invoices }) => {
  if (invoices.length === 0) {
    return null;
  }

  const amount = sumBy(invoices, 'amount_cents');

  return (
    <tfoot>
      <tr>
        <th colSpan={3}>Total</th>
        {sumColumn(invoices, 'amount_cents')}
        {sumColumn(invoices, 'balance_cents')}
        {sumColumn(invoices, 'remaining_cents')}
        {sumColumn(invoices, 'payment_cents')}
      </tr>
    </tfoot>
  );
}

export default InvoiceSelectionFooter;
