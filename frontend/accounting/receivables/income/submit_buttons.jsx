import classNames from 'classnames';
import { connect } from 'react-redux';

import submit from './submit';

const makeButton = (submitting, text, mode, onSubmit, handleSubmit, paymentState) => (
  <button
    className={classNames({ disabled: submitting || !paymentState.payer }, 'right floated ui submit button')}
    onClick={handleSubmit(values => submit({ ...values, mode }, paymentState, mode))}
  >
    {text}
  </button>
);

const SubmitButtons = ({ submitting, onSubmit, handleSubmit, paymentState }) => [
  makeButton(submitting, 'Save and New', 'new', onSubmit, handleSubmit, paymentState),
  makeButton(submitting, 'Save', 'save', onSubmit, handleSubmit, paymentState),
];

const mapState = state => ({
  paymentState: state.payment,
});

export default connect(mapState)(SubmitButtons);
