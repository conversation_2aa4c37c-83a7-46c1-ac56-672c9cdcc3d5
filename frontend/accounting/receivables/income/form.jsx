import classNames from 'classnames';
import moment from 'moment';
import { arrayOf, bool, func, string } from 'prop-types';
import { connect } from 'react-redux';
import { get, map } from 'lodash';
import { reduxForm, Field } from 'redux-form';

import DateField from '../../../leasing/application/fields/date_field';
import MoneyField from '../../../forms/fields/money_field';
import Search from '../../../components/search';
import TextField from '../../../forms/fields/redux/text_field';

import * as actions from './actions';
import InvoiceSelection from './invoice_selection';
import PaymentKind from './payment_kind';
import SubmitButtons from './submit_buttons';

const Form = ({ handleSubmit, isCheck, setPayer, submitting, error }) => (
  <form className={classNames('ui', { error: error.length }, 'form')}>
    <div className="ui error message">
      <ul className="list">
        {map(error, (message, i) => <li key={i}>{message}</li>)}
      </ul>
    </div>

    <div className="ui field">
      <label>Payer</label>
      <Search
        placeholder="Tenant or Vendor Name"
        onChange={setPayer}
        icon="user icon"
        url="/accounting/receivables/income/payers.json?q={query}"
      />
    </div>

    <Field name="payment[kind]" component={PaymentKind} />

    <Field
      component={TextField}
      id="payment_description"
      label="Description"
      name="payment[description]"
      required
    />

    <div className="fields">
      <Field
        className="ui six wide required calendar field"
        component={DateField}
        id="payment_date"
        label="Date"
        name="payment[date]"
      />

      <Field
        className="ui six wide required field"
        component={MoneyField}
        id="payment_amount"
        label="Amount"
        name="payment[amount]"
      />

      {isCheck &&
        <Field
          component={TextField}
          className="four wide field"
          id="payment_check_number"
          label="Check Number"
          name="payment[check_number]"
          type="number"
        />
      }
    </div>

    <InvoiceSelection />

    <SubmitButtons handleSubmit={handleSubmit} submitting={submitting} />
  </form>
);

Form.propTypes = {
  error: arrayOf(string).isRequired,
  handleSubmit: func.isRequired,
  isCheck: bool.isRequired,
  setPayer: func.isRequired,
  submitting: bool.isRequired,
};

Form.defaultProps = {
  error: [],
};

const mapState = state => ({
  isCheck: get(state, 'form.payment.values.payment.kind') === 'check',
});

const initialValues = {
  payment: {
    kind: 'cash',
    date: new moment().format('LL'),
  },
};

export default connect(mapState, actions)(reduxForm({ form: 'payment', initialValues })(Form));
