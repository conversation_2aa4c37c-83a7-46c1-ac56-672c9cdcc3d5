export const RECEIVE_INVOICES = 'RECEIVE_INVOICES';
export const receiveInvoices = invoices => ({ type: RECEIVE_INVOICES, invoices });

export const TOGGLE_INVOICE = 'TOGGLE_INVOICE';
export const toggleInvoice = invoice => ({ type: TOGGLE_INVOICE, invoice });

export const TOGGLE_CREDIT = 'TOGGLE_CREDIT';
export const toggleCredit = () => ({ type: TOGGLE_CREDIT });

export const SET_PAYER = 'SET_PAYER';
export const setPayer = payer => dispatch => {
  dispatch({ type: SET_PAYER, payer });

  const params = { payer_sgid: payer.id };

  $.get({
    url: '/accounting/receivables/invoices.json',
    data: params,
    success: invoices => dispatch(receiveInvoices(invoices)),
  });
};
