import classNames from 'classnames';
import { connect } from 'react-redux';
import { includes } from 'lodash';

import { formatMoney } from '../../../util/money';
import * as actions from './actions';

const moneyCell = cents =>
  <td className="right aligned">{formatMoney(cents, true, true)}</td>;

const InvoiceRow = ({ invoice, toggleInvoice, apply }) => (
  <tr>
    <td>
      <div className={classNames({ checked: apply }, 'ui checkbox')}>
        <input
          type="checkbox"
          onChange={() => toggleInvoice(invoice)}
          checked={apply}
        />
        <label />
      </div>
    </td>
    <td>{invoice.physical_date}</td>
    <td><a href={invoice.url}>{invoice.description}</a></td>
    {moneyCell(invoice.amount_cents)}
    {moneyCell(invoice.balance_cents)}
    {moneyCell(invoice.remaining_cents)}
    {moneyCell(invoice.payment_cents)}
  </tr>
);

const mapState = (state, props) => ({
  apply: includes(state.payment.selectedInvoiceIds, props.invoice.id),
});

export default connect(mapState, actions)(InvoiceRow);
