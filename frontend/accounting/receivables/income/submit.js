import { SubmissionError } from 'redux-form';

export default (values, paymentState, mode) => {
  const {
    payer: { id: payer_sgid },
    selectedInvoiceIds: invoice_ids,
    applyCredit: apply_credit,
  } = paymentState;

  const data = {
    ...values,
    payer_sgid,
    invoice_ids,
    apply_credit,
  };

  return new Promise((resolve, reject) => {
    $.post({
      url: '/accounting/receivables/income.json',
      data,
      success: payment => {
        if (mode === 'save') {
          Turbolinks.visit(`/accounting/payments/${payment.id}`);
        } else {
          Turbolinks.visit(window.location);
        }
      },
      error: xhr => {
        reject(new SubmissionError({ _error: xhr.responseJSON }));
      },
    });
  });
};
