import thunk from 'redux-thunk';
import { Component } from 'react';
import { Provider } from 'react-redux';
import { applyMiddleware, combineReducers, createStore } from 'redux';
import { reducer as form } from 'redux-form';

import payment from './reducer';
import Form from './form';

class NewIncomeForm extends Component {
  constructor(props) {
    super(props);
    this.store = createStore(
      combineReducers({ form, payment }),
      applyMiddleware(thunk),
    );
  }

  render() {
    return (
      <Provider store={this.store}>
        <Form />
      </Provider>
    );
  }
}

export default NewIncomeForm;
