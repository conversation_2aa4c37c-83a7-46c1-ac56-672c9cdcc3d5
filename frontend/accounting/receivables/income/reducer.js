import { map, xor } from 'lodash';

import { SET_PAYER, RECEIVE_INVOICES, TOGGLE_CREDIT, TOGGLE_INVOICE } from './actions';

const initialState = {
  invoices: [],
  selectedInvoiceIds: [],
  payer: undefined,
  applyCredit: true,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case RECEIVE_INVOICES:
      return {
        ...state,
        invoices: action.invoices,
        selectedInvoiceIds: map(action.invoices, 'id'),
      };
    case SET_PAYER:
      return { ...state, payer: action.payer };
    case TOGGLE_INVOICE:
      return {
        ...state,
        selectedInvoiceIds: xor(state.selectedInvoiceIds, [action.invoice.id]),
      };
    case TOGGLE_CREDIT:
      return { ...state, applyCredit: !state.applyCredit };
    default:
      return state;
  }
};
