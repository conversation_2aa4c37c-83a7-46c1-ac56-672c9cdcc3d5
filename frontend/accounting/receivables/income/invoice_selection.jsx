import classNames from 'classnames';
import { Component } from 'react';
import { connect } from 'react-redux';
import { capitalize, find, get, includes, map, min, reverse, sortBy, sumBy, unionBy } from 'lodash';

import { formatMoney } from '../../../util/money';

import * as actions from './actions';
import InvoiceRow from './invoice_row';
import InvoiceSelectionFooter from './invoice_selection_footer';

class InvoiceSelection extends Component {
  state = {
    sortColumn: 'date',
    sortDirection: 'ascending',
  };

  sortBy = sortColumn => {
    const sortDirection = (
      // Already selected and ascending?
      this.state.sortColumn === sortColumn &&
      this.state.sortDirection === 'ascending'
    ) ? 'descending' : 'ascending';

    this.setState({ sortColumn, sortDirection });
  };

  sortingHeader = (name, rightAligned = false) => {
    const classes = [];

    if (rightAligned) {
      classes.push('right aligned');
    }

    if (this.state.sortColumn === name) {
      classes.push(`sorted ${this.state.sortDirection}`);
    }

    return (
      <th
        className={classNames(classes)}
        onClick={() => this.sortBy(name)}
      >
        {capitalize(name.replace('_cents', ''))}
      </th>
    );
  }

  render() {
    const { applyCredit, creditCents, invoices, paymentCents, selectedInvoiceIds, toggleCredit } = this.props;

    const selectionOrder = unionBy(
      map(selectedInvoiceIds, id => find(invoices, { id })),
      invoices,
      'id',
    );

    const maxCredit = min([creditCents, sumBy(invoices, 'balance_cents')]);

    const appliedCredit = applyCredit ? maxCredit : 0;

    let remainingCredit = appliedCredit;
    let remainingPayment = paymentCents;

    const entries = map(selectionOrder, invoice => {
      const applying = includes(selectedInvoiceIds, invoice.id);

      const thisCredit = applying ? min([invoice.balance_cents, remainingCredit]) : 0;
      remainingCredit -= thisCredit;

      const thisPayment = applying ? min([invoice.balance_cents - thisCredit, remainingPayment]) : 0;

      const thisRemaining = invoice.balance_cents - thisCredit - thisPayment;

      remainingPayment -= thisPayment;

      return {
        ...invoice,
        payment_cents: thisPayment,
        remaining_cents: thisRemaining,
      };
    });

    const sorted = sortBy(entries, this.state.sortColumn);

    const sortedEntries = this.state.sortDirection === 'descending' ?
      reverse(sorted) : sorted;

    const appliedCreditEntry = {
      amount_cents: -creditCents,
      balance_cents: -creditCents,
      remaining_cents: applyCredit ? -remainingCredit : -creditCents,
      payment_cents: applyCredit ? 0 : 0,
    };

    const newCreditEntry = {
      amount_cents: 0,
      balance_cents: 0,
      remaining_cents: -remainingPayment,
      payment_cents: remainingPayment,
    };

    return [
      <table className="ui sortable fixed single line selectable table">
        <thead>
          <tr>
            <th>Apply</th>
            {this.sortingHeader('date')}
            {this.sortingHeader('description')}
            {this.sortingHeader('amount_cents', true)}
            {this.sortingHeader('balance_cents', true)}
            {this.sortingHeader('remaining_cents', true)}
            {this.sortingHeader('payment_cents', true)}
          </tr>
        </thead>
        <tbody>
          {creditCents > 0 &&
            <tr>
              <td colSpan={1}>
                <div className={classNames({ checked: applyCredit }, 'ui checkbox')}>
                  <input checked={applyCredit} type="checkbox" onChange={toggleCredit} />
                  <label />
                </div>
              </td>
              <td />
              <td>Available Credit</td>
              <td className="right aligned">{formatMoney(appliedCreditEntry.amount_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(appliedCreditEntry.balance_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(appliedCreditEntry.remaining_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(appliedCreditEntry.payment_cents, true, true)}</td>
            </tr>
          }
          {map(sortedEntries, invoice => <InvoiceRow key={invoice.id} invoice={invoice} />)}
          {invoices.length === 0 &&
            <tr><td colSpan={7}><i>No open invoices</i></td></tr>
          }
          {remainingPayment > 0 &&
            <tr>
              <td colSpan={3}>Credit</td>
              <td className="right aligned">{formatMoney(newCreditEntry.amount_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(newCreditEntry.balance_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(newCreditEntry.remaining_cents, true, true)}</td>
              <td className="right aligned">{formatMoney(newCreditEntry.payment_cents, true, true)}</td>
            </tr>
          }
        </tbody>
        <InvoiceSelectionFooter invoices={[...sortedEntries, appliedCreditEntry, newCreditEntry]} />
      </table>,
    ];
  }
}

const mapState = state => ({
  applyCredit: state.payment.applyCredit,
  creditCents: get(state, 'payment.payer.credit_cents', 0),
  invoices: state.payment.invoices,
  selectedInvoiceIds: state.payment.selectedInvoiceIds,
  paymentCents: parseInt(parseFloat(get(state, 'form.payment.values.payment.amount', '0.0')) * 100, 10),
});

export default connect(mapState, actions)(InvoiceSelection);
