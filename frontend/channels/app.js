const App = {};
App.cable = ActionCable.createConsumer();

import NotificationsChannel from './notifications';
App.notifications = App.cable.subscriptions.create('NotificationsChannel', NotificationsChannel);

import ChatChannel from './chat';
App.chat = App.cable.subscriptions.create('ChatChannel', ChatChannel);

import DownloadsChannel from './downloads';
App.downloads = App.cable.subscriptions.create('DownloadsChannel', DownloadsChannel);

// TODO: Conditional?
import ReportsChannel from './reports';
App.reports = App.cable.subscriptions.create('ReportsChannel', ReportsChannel);

export default App;
