import { v4 as uuidv4 } from 'uuid';

let connected = false;

const toastsByDownloadId = {};

const toastOptions = {
  position: 'bottom right',
  newestOnTop: true,
};

const progressOptions = {
  displayTime: 5000,
  progressUp: true,
  showProgress: 'top',
};

const successToastOptions = {
  classProgress: 'green',
  ...progressOptions,
};

const failureToastOptions = {
  classProgress: 'red',
  ...progressOptions,
};

const loadingToastHtml = (header, message) => `
  <div class="ui toast">
    <div class="content">
      <div class="ui active inline blue elastic loader" style="float: right;" />
      <div class="ui header">${header}</div>
      ${message}
    </div>
  </div>
`;

const loadingToastHtmlNode = (...args) => $(loadingToastHtml(...args));

const generateDownloadId = () => {
  return uuidv4();
}
window.generateDownloadId = generateDownloadId;

const followingDownloadIds = () => JSON.parse(sessionStorage.downloadIds || '[]');

const followDownloadId = downloadId => {
  sessionStorage.downloadIds = JSON.stringify([...followingDownloadIds(), downloadId]);
};
window.followDownloadId = followDownloadId;

const isFollowingDownloadId = downloadId => followingDownloadIds().includes(downloadId);

const showPreparingToast = (downloadId, title, subtitle) => {
  const node = loadingToastHtmlNode(title, subtitle);

  const existing = toastsByDownloadId[downloadId];

  if (existing) {
    const content = node.find('.content');
    $(existing).find('.content').replaceWith(content);
  } else {
    const toast = node.toast({
      ...toastOptions,
      closeOnClick: false,
      displayTime: 0,
    });

    toastsByDownloadId[downloadId] = toast;
  }
};

const downloadFile = ({ url, filename }) => {
  const a = document.createElement('a');
  a.href = url;
  a.setAttribute('download', filename);
  a.click();
};

const closePreparingToast = downloadId => {
  const toast = toastsByDownloadId[downloadId];

  if (toast) {
    toast.toast('close');
  }
};

const DownloadsChannel = {
  connected() {
    console.log('Downloads channel connected.');
    connected = true;
  },

  disconnected() {
    console.log('Downloads channel disconnected.');
    connected = false;
  },

  received(data) {
    console.log('Downloads channel received', data);

    const downloadId = data.download_id;

    // Make sure this tab is interested in this download.
    if (!isFollowingDownloadId(downloadId)) {
      console.log('This tab is not interested in ' + downloadId);
      return;
    }

    if (data.type === 'download_preparing') {
      showPreparingToast(downloadId, data.title, data.subtitle);
    } else if (data.type === 'download_failed') {
      closePreparingToast(downloadId);
      $('body').toast({
        ...toastOptions,
        ...failureToastOptions,
        title: data.title,
        message: data.subtitle,
      });
    } else if (data.type === 'download_ready') {
      downloadFile(data);
      closePreparingToast(downloadId);
    } else if (data.type === 'task_completed') {
      closePreparingToast(downloadId);

      if (data.title) {
        $('body').toast({
          ...toastOptions,
          ...successToastOptions,
          title: data.title,
          message: data.subtitle,
        });
      }

      if (data.redirect) {
        Turbolinks.visit(data.redirect);
      }
    } else {
      console.log('Unknown message.');
    }
  },

  request(url) {
    if (!connected) {
      $('body').toast({
        ...toastOptions,
        ...failureToastOptions,
        title: 'Unable to Request Download',
        message: 'Please check your network connection or try later',
      });
    } else {
      const node = loadingToastHtmlNode('Requesting Download', 'Please wait');

      const downloadId = generateDownloadId();

      const toast = node.toast({
        ...toastOptions,
        closeOnClick: false,
        displayTime: 0,
      });

      followDownloadId(downloadId);

      toastsByDownloadId[downloadId] = toast;

      $.get(url, { download_id: downloadId });
    }
  },
};

/*
 * Hijack data-background-download anchors to request a background download.
 */
$(document).on('turbolinks:load', () => {
  $('a[data-background-download]').off('click').click(event => {
    event.preventDefault();

    const url = event.target.href;

    console.log('Requesting background download of', url);

    App.downloads.request(url);
  });
});

export default DownloadsChannel;
