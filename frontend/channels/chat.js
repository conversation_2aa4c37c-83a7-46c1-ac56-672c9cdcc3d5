const ChatChannel = {
  getPendingConnections() {
    if (!this.pending) {
      this.pending = [];
    }

    return this.pending;
  },

  getCallbacks() {
    if (!this.callbacks) {
      this.callbacks = {};
    }

    return this.callbacks;
  },

  connected() {
    this.connected = true;

    this.getPendingConnections().forEach(pending =>
      this.follow(pending.type, pending.id, pending.callback)
    );
  },

  disconnected() {
    this.connected = false;
  },

  follow(type, id, callback) {
    if (this.connected === true) {
      this.getCallbacks()[type + id] = callback;
      this.perform('follow', {
        resource_type: type,
        resource_id: id,
      });
    } else {
      this.getPendingConnections().push({ type, id, callback });
    }
  },

  unfollow(type, id) {
    this.perform('unfollow', {
      resource_type: type,
      resource_id: id,
    });
  },

  received(data) {
    const callback = this.getCallbacks()[_.snakeCase(data.chat_room_type) + data.chat_room_id];
    callback(data);
  },

  speak(message, type, id) {
    this.perform('speak', {
      body: message,
      resource_type: type,
      resource_id: id,
    });
  },
};

export default ChatChannel;
