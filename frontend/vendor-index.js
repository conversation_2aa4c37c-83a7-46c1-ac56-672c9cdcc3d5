// Magnific popup
require('imports-loader?define=>false&exports=>false!../node_modules/magnific-popup/dist/jquery.magnific-popup.js');
require('../node_modules/magnific-popup/dist/magnific-popup.css');

import React from 'react';
window.React = React;

import ReactDOM from 'react-dom';
window.ReactDOM = ReactDOM;

import NewInvoiceForm from './vendor/new_invoice_form';
window.NewInvoiceForm = NewInvoiceForm;

import PhotoAttachmentsArea from './forms/fields/photo_attachments_area.jsx';
window.PhotoAttachmentsArea = PhotoAttachmentsArea;

// Stimulus
import 'element-closest';

import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';

const stimulus = StimulusApplication.start();
stimulus.load(
  stimulusDefinitions(
    require.context(
      './stimulus/',
      true,
      /\.\/(vendors|timeline|attachments_bar).*\.js$/
    )
  )
);
