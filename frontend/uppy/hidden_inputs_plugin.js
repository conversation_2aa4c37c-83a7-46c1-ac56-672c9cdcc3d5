import { BasePlugin } from '@uppy/core';
import { nanoid } from 'nanoid';

/*
 * This plugin creates hidden form inputs that <PERSON> can use to create attachments from directly uploaded files.
 *
 * Options:
 *   target: A DOM node to append hidden inputs to.
 *   useS3: Whether to use S3 or to use the server provided Shrine uploader.
 *   collectionName: The name of the input field to add photos to, e.g. "post[photos_attributes]".
 *   attributeName: The name of the shrine attachment attribute to use, e.g. "upload".
 *
 * The name of the input field will be, e.g., "post[photos_attributes][abc123][upload]", where "abc123" is a NanoID.
 */
export default class extends BasePlugin {
  constructor(uppy, options) {
    super(uppy, options);
    this.type = 'custom';
    this.id = options.id || 'HiddenInputsPlugin';
    this.target = options.target;
    this.useS3 = options.useS3;
    this.useActiveStorage = options.useActiveStorage;
    this.collectionName = options.collectionName;
    this.attributeName = options.attributeName;
    this.activeStorageAttribute = options.activeStorageAttribute;
  }

  install() {
    this.uppy.log(`[${this.id}] Installed`);

    this.uppy.on('upload-success', (file, response) => {
      this.uppy.log(`[${this.id}] Creating hidden input element`);

      const hiddenField = document.createElement('input');

      hiddenField.type = 'hidden';
      hiddenField.name = `${this.collectionName}[${nanoid()}][${this.attributeName}]`;

      if (this.useActiveStorage) {
        hiddenField.name = `${this.activeStorageAttribute}`;
        hiddenField.value = response.signed_id;
        this.target.appendChild(hiddenField);
        return;
      }

      if (this.useS3) {
        const key = file.s3Multipart.key; // Indeed, even though this may not be 'multipart'.

        const id = key.match(/^cache\/(.+)/)[1]; // Remove cache prefix

        hiddenField.value = JSON.stringify(
          {
            id: id,
            storage: 'cache',
            metadata: {
              size: file.size,
              filename: file.name,
              mime_type: file.type,
            },
          },
        );
      } else {
        hiddenField.value = JSON.stringify(response.body);
      }

      this.target.appendChild(hiddenField);
    });
  }
}
