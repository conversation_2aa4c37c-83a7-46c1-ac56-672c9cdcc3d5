import { BasePlugin } from '@uppy/core';

/*
 * This plugin conditionally disables a form's submit button when there are incomplete or pending uploads.
 *
 * Options:
 *   target: A DOM node containing a form.
 */
export default class extends BasePlugin {
  constructor(uppy, options) {
    super(uppy, options);
    this.type = 'custom';
    this.id = options.id || 'DisableSubmitPlugin';
    this.target = options.target;

    const form = this.target.closest('form');
    const internalSubmitButtons = form.querySelectorAll('[type="submit"]');
    const externalSubmitButtons = document.querySelectorAll(`[form="${form.id}"]`);
    this.submitElements = [
      ...internalSubmitButtons,
      ...externalSubmitButtons
    ];
  }

  install() {
    this.uppy.log(`[${this.id}] Installed`);
  }

  update() {
    this.updateSubmitState();
  }

  updateSubmitState() {
    const files = this.uppy.getFiles();

    const inProgress = files.some(file => !file.progress.uploadComplete);

    if (inProgress) {
      this.disableSubmit();
    } else {
      this.enableSubmit();
    }
  }

  enableSubmit() {
    this.uppy.log(`[${this.id}] Enabling submit`);

    this.submitElements.forEach(element => {
      element.disabled = false;
    });
  }

  disableSubmit() {
    this.uppy.log(`[${this.id}] Disabling submit`);

    this.submitElements.forEach(element => {
      element.disabled = true;
    });
  }
}
