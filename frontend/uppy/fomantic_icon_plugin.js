import { BasePlugin } from '@uppy/core';

/*
 * This plugin replaces the Uppy icons with FomanticUI icons.
 *
 * Options:
 *   target: A DOM node to replace icons within.
 *   icons: An object mapping an Uppy acquirer name to a FomanticUI icon, e.g. { 'Webcam': 'camera' }.
 */
export default class extends BasePlugin {
  constructor(uppy, options) {
    super(uppy, options);
    this.type = 'custom';
    this.id = options.id || 'FomanticIconPlugin';
    this.icons = options.icons;
    this.target = options.target;
  }

  install() {
    this.uppy.log(`[${this.id}] Installed`);
  }

  update() {
    setTimeout(() => {
      requestAnimationFrame(() => {
        this.replaceIcons();
      });
    }, 0);
  }

  replaceIcons() {
    this.uppy.log(`[${this.id}] Replacing icons`);

    // Update source icons
    for (const [acquirer, icon] of Object.entries(this.icons)) {
      const elements = this.target.querySelectorAll(`div[data-uppy-acquirer-id="${acquirer}"] .uppy-DashboardTab-inner`);

      elements.forEach(element => {
        element.innerHTML = `<i class="ui large blue ${icon} icon"></i>`;
      });
    }

    // Update webcam icon
    this.target.querySelectorAll('.uppy-Webcam-permissonsIcon').forEach(element => {
      element.innerHTML = `<i class="ui huge blue ${this.icons.Webcam} icon"></i>`;
    });

    // Update remove icons
    this.target.querySelectorAll('.uppy-Dashboard-Item-action--remove').forEach(element => {
      element.innerHTML = '<i class="ui remove icon"></i>';
    });
  }
}
