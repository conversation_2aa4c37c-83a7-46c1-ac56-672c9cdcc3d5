import PropTypes from 'prop-types';
import { Component } from 'react';

class OrButton extends Component {
  static propTypes = {
    left: PropTypes.string.isRequired,
    right: PropTypes.string.isRequired,
    onChange: PropTypes.func.isRequired,
    leftColor: PropTypes.string,
    rightColor: PropTypes.string,
  }

  static defaultProps = {
    leftColor: '',
    rightColor: '',
  }

  state = { enabled: false };

  handleChange(enabled) {
    this.setState({ enabled: enabled });
    this.props.onChange(enabled);
  }

  render() {
    let leftClass, rightClass;

    if (this.state.enabled) {
      leftClass = 'ui button';
      rightClass = `ui ${this.props.rightColor} active button`;
    } else {
      leftClass = `ui ${this.props.leftColor} active button`;
      rightClass = 'ui button';
    }

    return (
      <div className='ui buttons'>
        <button className={leftClass}
                type='button'
                onClick={this.handleChange.bind(this, false)}>
          {this.props.left}
        </button>
        <div className='or' />
        <button className={rightClass}
                type='button'
                onClick={this.handleChange.bind(this, true)}>
          {this.props.right}
        </button>
      </div>
    );
  }
}

export default OrButton;
