import PropTypes from 'prop-types';
import { Component } from 'react';

class Search extends Component {
  componentDidMount() {
    const search = $(this.node);

    const { onChange: onSelect, url } = this.props;

    search.search({
      minCharacters: 0,
      searchOnFocus: true,
      apiSettings: { url },
      onSelect,
    });
  }

  render() {
    const { icon, placeholder } = this.props;

    return (
      <div className="ui search" ref={node => this.node = node}>
        <div className="ui left icon input">
          <input className="prompt" type="text" placeholder={placeholder} />
          <i className={icon} />
        </div>
      </div>
    );
  }
}

Search.defaultProps = {
  icon: 'search icon',
  onChange: PropTypes.func.isRequired,
  url: '/api/v1/search?q={query}',
};

Search.propTypes = {
  icon: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  url: PropTypes.string.isRequired,
};

export default Search;
