import { Component } from 'react';

import { pctWithIcon } from '../../util/percentage_change.jsx';
import { formatMoney } from '../../util/money.jsx';

class ApAr extends Component {
  constructor(props) {
    super(props);
    this.state = {
      accounts_payable: {
        age_days: 0,
        balance_cents: 0,
        compare_pct: 0,
      },
      accounts_receivable: {
        age_days: 0,
        balance_cents: 0,
        compare_pct: 0,
      },
    };
  }

  componentDidMount() {
    $.get('/accounting/summary.json', summary => this.setState(summary));
  }

  render() {
    const { accounts_payable, accounts_receivable } = this.state;

    const apBalance = formatMoney(accounts_payable.balance_cents, true);
    const arBalance = formatMoney(accounts_receivable.balance_cents, true);

    const apPct = pctWithIcon(accounts_payable.compare_pct);
    const arPct = pctWithIcon(accounts_receivable.compare_pct);

    return (
      <div className="content">
        <h3 className="ui header">
          Payables and Receivables
        </h3>
        <table className="ui small single line fixed unstackable compact celled table">
          <thead>
            <tr>
              <th>Category</th>
              <th>Month Balance</th>
              <th>Percentage Change</th>
              <th>Average Age</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><a href="/accounting/payables">Accounts Payable</a></td>
              <td className="right aligned">{apBalance}</td>
              <td>{apPct}</td>
              <td>{accounts_payable.age_days} days</td>
            </tr>
            <tr>
              <td><a href="/accounting/receivables">Accounts Receivable</a></td>
              <td className="right aligned">{arBalance}</td>
              <td>{arPct}</td>
              <td>{accounts_receivable.age_days} days</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }
}

export default ApAr;
