import PropTypes from 'prop-types';
import { map } from 'lodash';
import moment from 'moment';
import { formatMoney } from '../../util/money'

const InvoiceTable = ({ invoices }) => {

  const visitInvoice = invoice => () => {
    Turbolinks.visit(invoice.url);
  }

  return (
    <table className="ui very basic small compact selectable single line table">
      <thead>
        <tr>
          <th>Date</th>
          <th>Description</th>
          <th>Amount</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {map(invoices, (invoice, index) => (
          <tr
            key={index}
            className="invoice-row"
            onClick={visitInvoice(invoice)}
            style={{cursor: 'pointer'}}
          >
            <td>{moment(invoice.date).format("MM/DD/YY")}</td>
            <td>{invoice.description}</td>
            <td>{formatMoney(invoice.amount, true)}</td>
            <td>{invoice.status}</td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}

InvoiceTable.propTypes = {
  invoices: PropTypes.array.isRequired,
};

InvoiceTable.defaultProps = {
  invoices: [],
}

export default InvoiceTable;
