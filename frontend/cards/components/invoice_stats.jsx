import PropTypes from 'prop-types';

import { pctWithIcon } from '../../util/percentage_change.jsx';
import { formatMoney } from '../../util/money';

const InvoiceStats = ({ stats }) => {
  const { openInvoicesCount,
          openInvoicesPercentChange,
          currentBalance,
          balancePercentChange } = stats;

  return (
    <div
      className="ui two column divided stackable tiny statistics grid"
    >
      <div className="ui statistic column">
        <div className="value">{openInvoicesCount}</div>
        <div className="label">Open Invoices</div>
        <div className="percentage description">
          {pctWithIcon(openInvoicesPercentChange)} from last month
        </div>
      </div>
      <div className="ui statistic column">
        <div className="value">{formatMoney(currentBalance, true)}</div>
        <div className="label">Current Balance</div>
        <div className="percentage description">
          {pctWithIcon(balancePercentChange)} from last month
        </div>
      </div>
    </div>
    );
};

InvoiceStats.propTypes = {
  stats: PropTypes.object.isRequired,
  type: PropTypes.string.isRequired,
};

InvoiceStats.defaultProps = {
  stats: {},
};

export default InvoiceStats;
