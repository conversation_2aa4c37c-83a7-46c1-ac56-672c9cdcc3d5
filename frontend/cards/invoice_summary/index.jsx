import PropTypes from 'prop-types';
import { Component } from 'react';
import { capitalize } from 'lodash';
import { defaultProps } from 'recompose';

import InvoiceTable from '../components/invoice_table';
import InvoiceStats from '../components/invoice_stats';

class InvoicesSummary extends Component {
  constructor(props) {
    super(props);
    this.state = { entries: undefined, stats: undefined };
    this.type = this.props.type || '';
    this.url = `/accounting/cards/${this.type}_summary.json`;
  }
  componentDidMount() {
    $.get(this.url, response => {
      const { entries, stats } = response;
      this.setState({ entries, stats });
    });
  }

  render() {
    const { entries, stats } = this.state;

    return (
      <div className="content">
        <div className="ui two column stackable grid">
          <div className="column">
            <h3 className=" ui header">
              {`${capitalize(this.type)} Summary`}
              <div className="sub header">
                Recent Unpaid Invoices
              </div>
            </h3>
          </div>
          <div className="column">
            <a
              href={`/accounting/${this.type}`}
              className="ui compact right floated small inverted blue button"
            >
              {`View ${capitalize(this.type)}`}
            </a>
          </div>
        </div>
        <InvoiceStats stats={stats} type={this.type} />
        <InvoiceTable invoices={entries} />
      </div>
    );
  }
}

InvoicesSummary.propTypes = {
  type: PropTypes.string.isRequired,
};

export const PayablesSummary = defaultProps({ type: 'payables' })(InvoicesSummary);
export const ReceivablesSummary = defaultProps({ type: 'receivables' })(InvoicesSummary);
