import { pctWithIcon } from '../../util/percentage_change.jsx';

const ExecutiveSummary = () => (
  <div className="ui content">
    <h3 className="ui header">
      Executive Summary
    </h3>
    <table className="ui small single line fixed unstackable compact celled table">
      <thead>
        <tr>
          <th>Category</th>
          <th>Amount</th>
          <th>Percentage Change</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Cash Received</td>
          <td className="right aligned">$0.00</td>
          <td>{pctWithIcon(0)}</td>
        </tr>
        <tr>
          <td>Cash Spent</td>
          <td className="right aligned">$0.00</td>
          <td>{pctWithIcon(0)}</td>
        </tr>
        <tr>
          <td>Cash Surplus</td>
          <td className="right aligned">$0.00</td>
          <td>{pctWithIcon(0)}</td>
        </tr>
        <tr>
          <td>Closing Bank Balance</td>
          <td className="right aligned">$0.00</td>
          <td>{pctWithIcon(0)}</td>
        </tr>
      </tbody>
    </table>
  </div>
);

export default ExecutiveSummary;
