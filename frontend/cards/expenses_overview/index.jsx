import { Component } from 'react';
import { reverse, sortBy } from 'lodash';

import Pie from '../../charts/pie/pie';
import { formatMoney } from '../../util/money';

class ExpensesOverview extends Component {
  constructor(props) {
    super(props);
    this.state = { expenses: [], selectedItem: null };
    this.url = '/accounting/cards/expenses_overview.json';
  }

  componentDidMount() {
    $.get(this.url, response => {
      const { expenses } = response;
      this.setState({ expenses });
    });
  }

  handleSelectionChanged = item => this.setState({ selectedItem: item });

  render() {
    const { expenses, selectedItem } = this.state;

    const data = reverse(sortBy(expenses, 'value'));

    let name;
    let amount;
    if (selectedItem) {
      name = selectedItem.name;
      amount = formatMoney(selectedItem.value, true);
    }

    return (
      <div className="content">
        <h3 className="ui header">
          Expenses Overview
          <div className="sub header">
            Last 90 Days
          </div>
        </h3>
        <div className="center aligned image" style={{ backgroundColor: 'white' }}>
          <Pie
            height={300}
            width={300}
            id="expenses-overview-pie"
            data={data}
            onSelectionChanged={this.handleSelectionChanged}
          />
        </div>
        <div className="ui list">
          <div className="item">Name: {name || <i>Hover for detail</i>}</div>
          <div className="item">Amount: {amount}</div>
        </div>
      </div>
    );
  }
}

export default ExpensesOverview;
