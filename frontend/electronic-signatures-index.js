// Stimulus
import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';
const stimulus = StimulusApplication.start();
const stimulusControllers = require.context('./stimulus/', true, /inline_pdf_controller\.js$/);
stimulus.load(stimulusDefinitions(stimulusControllers));

import SignaturePad from 'signature_pad';
window.SignaturePad = SignaturePad;

import * as pdfjsLib from 'pdfjs-dist/webpack';
window.pdfjsLib = pdfjsLib;
