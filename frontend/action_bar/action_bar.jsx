import PropTypes from 'prop-types';

const ActionBar = ({ actionButtons, modeButtons, title }) => (
  <div className="action-bar">
    <div className="ui container">
      <h1 className="ui header">
        {title}
      </h1>

      {modeButtons}

      {actionButtons}
    </div>
  </div>
);

ActionBar.propTypes = {
  actionButtons: PropTypes.element,
  modeButtons: PropTypes.element,
  title: PropTypes.string.isRequired,
};

export default ActionBar;
