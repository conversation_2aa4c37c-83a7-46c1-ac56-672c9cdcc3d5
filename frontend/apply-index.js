import 'jquery-tablesort';

// Stimulus
import { Application as StimulusApplication } from 'stimulus';
import { definitionsFromContext as stimulusDefinitions } from 'stimulus/webpack-helpers';
const stimulus = StimulusApplication.start();
const stimulusControllers = require.context('./stimulus/', true, /(inline_pdf_controller|pet_fields_controller)\.js$/);
stimulus.load(stimulusDefinitions(stimulusControllers));

// Magnific popup
require('imports-loader?define=>false&exports=>false!../node_modules/magnific-popup/dist/jquery.magnific-popup.js');
require('../node_modules/magnific-popup/dist/magnific-popup.css');
require('./forms/fields/no_or_explain');

import React from 'react';
window.React = React;

import ReactDOM from 'react-dom';
window.ReactDOM = ReactDOM;

import LeaseApplication from './leasing/application/show/lease_application.jsx';
window.LeaseApplication = LeaseApplication;

import NewGuestCard from './leasing/new_guest_card';
window.NewGuestCard = NewGuestCard;

import SignaturePad from 'signature_pad';
window.SignaturePad = SignaturePad;

import * as pdfjsLib from 'pdfjs-dist/webpack';
window.pdfjsLib = pdfjsLib;

import qs from 'qs';
window.qs = qs;

document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('revela-apply');

  if (container) {
    ReactDOM.render(<ApplyPlugin />, container);
  }
});
