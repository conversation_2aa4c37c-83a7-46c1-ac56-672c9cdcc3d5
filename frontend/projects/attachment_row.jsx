import PropTypes from 'prop-types';

const AttachmentRow = ({ attachment: { maintenance_ticket: ticket }, onClick }) => (
  <div
    className="item"
  >
    <div className="right floated content">
      <button
        className="ui compact tiny icon button"
        onClick={onClick}
      >
        <i className="remove icon" />
      </button>
    </div>
    <div className="content">
      <a
        className="header"
        href={`/maintenance/tickets/${ticket.id}`}
      >
        {ticket.subject}
      </a>
      <div className="description">
        Ticket #{ticket.id}
      </div>
    </div>
  </div>
);

AttachmentRow.propTypes = {
  attachment: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
};

export default AttachmentRow;
