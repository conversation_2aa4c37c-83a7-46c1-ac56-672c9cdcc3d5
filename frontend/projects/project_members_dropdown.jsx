import PropTypes from 'prop-types';
import { Component } from 'react';
import { map } from 'lodash';

class ProjectMembersDropdown extends Component {
  constructor(props) {
    super(props);
    this.state = { members: props.employees };
  }

  componentDidMount() {
    $('#project-members-dropdown').dropdown();
  }

  availableMembers() {
    return this.state.members;
  }

  render() {

    const options = map(this.availableMembers(), (member) =>
      <div className="item" key={member.id} data-value={member.id}>{member.name}</div>
    );

    return (
      <div className="ui small fluid multiple search selection dropdown" id="project-members-dropdown">
        <input name="member_ids" type="hidden" />
        <i className='dropdown icon' />
        <div className="default text">Text</div>
        <div className="menu">
          {options}
        </div>
      </div>
    );
  }
}

ProjectMembersDropdown.propTypes = {
  employees: PropTypes.array.isRequired,
};

export default ProjectMembersDropdown;
