import PropTypes from 'prop-types';
import { Component } from 'react';
import { map } from 'lodash';

class MemberAssignment extends Component {
  constructor(props) {
    super(props);
    this.state = { members: [] };
  }

  componentDidMount() {
    $('#member-assignment-dropdown').dropdown();

    const pid = this.props.project_id;

    $.get({
      url: `/maintenance/projects/${pid}/members.json`,
      success: (members) => {
        this.setState({ members });
      },
    });
  }

  availableMembers() {
    return this.state.members;
  }

  render() {
    const options = map(this.availableMembers(), (member) =>
      <div className="item" key={member.id} data-value={member.id}>{member.name}</div>
    );

    return (
      <div
        className="ui small fluid multiple search selection dropdown"
        id="member-assignment-dropdown"
      >
        <input name="assigned_member_ids" type="hidden" />
        <i className="dropdown icon" />
        <div className="default text">Text</div>
        <div className="menu">
          {options}
        </div>
      </div>
    );
  }
}

MemberAssignment.propTypes = {
  project_id: PropTypes.number.isRequred,
};

export default MemberAssignment;
