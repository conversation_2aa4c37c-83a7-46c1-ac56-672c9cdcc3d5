import { array, func, number } from 'prop-types';

import Dropdown from '../selectors/dropdown';
import MoneyField from '../forms/fields/money_field';

const BudgetAmountRow = ({ accounts, id, onChange, onRemove }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-end',
    }}
  >
    <div className="ui two required fields" style={{ flex: '1 0 auto' }}>
      <div className="field">
        <label htmlFor={`budget_amounts_${id}_account_id`}>
          Account
        </label>
        <Dropdown
          id={`budget_amounts_${id}_account_id`}
          name={`budget[budget_amounts_attributes][${id}][account_id]`}
          items={accounts}
        />
      </div>
      <MoneyField
        label="Amount"
        id={`budget_amounts_${id}_amount`}
        name={`budget[budget_amounts_attributes][${id}][amount]`}
        onChange={amount => onChange(parseFloat(amount) * 100)}
      />
    </div>
    <button
      className="ui basic icon button"
      onClick={onRemove}
      style={{ margin: '0 0 1em 1em' }}
      type="button"
    >
      <i className="remove icon" />
    </button>
  </div>
);

BudgetAmountRow.propTypes = {
  accounts: array.isRequired,
  id: number.isRequired,
  onChange: func.isRequired,
  onRemove: func.isRequired,
};

export default BudgetAmountRow;
