import classNames from 'classnames';
import { Component } from 'react';
import { map, reject, sumBy } from 'lodash';
import { string } from 'prop-types';

import Dropdown from '../selectors/dropdown';
import RailsForm from '../forms/rails_form';
import { formatMoney } from '../util/money';

import BudgetAmountRow from './budget_amount_row';

let lastId = 0;

const makeRow = () => {
  const id = lastId;
  lastId += 1;
  return { id, amount: 0 };
};

class BudgetForm extends Component {
  static propTypes = {
    action: string.isRequired,
  };

  state = {
    accounts: [],
    rows: [makeRow()],
  };

  setChartOfAccounts = chart => {
    this.setState({ accounts: [] }); // Clear existing accounts

    $.get(
      `/organization/charts_of_accounts/${chart.id}/accounts.json`,
      accounts => this.setState({ accounts }),
    );
  }

  handleAddRow = () => {
    this.setState({ rows: [...this.state.rows, makeRow()] });
  }

  handleRemoveRow = id => {
    this.setState({ rows: reject(this.state.rows, { id }) });
  }

  handleChangeRow = (id, amount) => {
    const rows = map(this.state.rows, row => {
      if (row.id === id) {
        return { id, amount };
      }

      return row;
    });

    this.setState({ rows });
  }

  render() {
    const { props: { action }, state: { accounts, rows } } = this;

    const amount = sumBy(rows, 'amount');

    const disabled = accounts.length === 0;

    const submitDisabled = disabled || amount === 0;

    return (
      <div className="ui narrow container">
        <div className="ui attached message">
          <div className="right floated ui tiny statistic">
            <div className="value">{formatMoney(amount, true)}</div>
            <div className="label">Total Budget</div>
          </div>
          <div className="header">
            Prepare Budget
          </div>
          <p>
            This project does not have a budget yet.
          </p>
        </div>
        <RailsForm
          action={action}
          id="project_budget_form"
          className="ui bottom attached fluid clearing segment form"
        >
          <div className="ui required field">
            <label htmlFor="budget_chart_of_accounts_id">
              Chart of Accounts
            </label>
            <Dropdown
              id="budget_chart_of_accounts_id"
              name="budget[chart_of_accounts_id]"
              url="/organization/charts_of_accounts.json"
              onChange={this.setChartOfAccounts}
            />
          </div>

          <h4 className="ui dividing header">Amounts</h4>

          <div className={classNames('ui very basic', { disabled }, 'clearing segment')}>
            {map(rows, ({ id }) => (
              <BudgetAmountRow
                key={id}
                id={id}
                accounts={accounts}
                onChange={newAmount => this.handleChangeRow(id, newAmount)}
                onRemove={() => this.handleRemoveRow(id)}
              />
            ))}
            <button
              type="button"
              className="ui right floated basic small button"
              onClick={this.handleAddRow}
            >
              Add Row
            </button>
          </div>
          <button
            type="submit"
            className={classNames('right floated ui', { disabled: submitDisabled }, 'submit button')}
          >
            Create Budget
          </button>
        </RailsForm>
      </div>
    );
  }
}

export default BudgetForm;
