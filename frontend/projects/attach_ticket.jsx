import PropTypes from 'prop-types';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import { map } from 'lodash';

class AttachTicket extends Component {
  static propTypes = {
    projectId: PropTypes.number.isRequired,
    onNewAttachment: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = { items: [] };
  }

  componentDidMount() {
    this.node = findDOMNode(this);

    $(this.node).dropdown({
      action: 'hide',
      onChange: this.handleChange.bind(this),
    });

    $.get('/maintenance/feed.json', tickets =>
      this.setState({ items: tickets })
    );
  }

  handleChange(ticketId) {
    const ticket = _.find(this.state.items, item => item.id === parseInt(ticketId, 10));

    const data = { projects_ticket: { maintenance_ticket_id: ticket.id } };

    const id = this.props.projectId;

    $.ajax({
      type: 'POST',
      url: `/maintenance/projects/${id}/project_tickets`,
      data,
      success: result => { this.props.onNewAttachment(result); },
      dataType: 'json',
    });
  }

  render() {
    const items = map(this.state.items, item => (
      <div key={item.id} className="item" data-value={item.id}>
        #{item.id} - {item.subject}
      </div>
    ));

    return (
      <div className="ui floating labled icon dropdown button">
        <i className="plus icon" />
        <span className="text">Attach Ticket</span>
        <div className="menu">
          <div className="ui icon search input">
            <i className="search icon" />
            <input placeholder="Search..." type="text" />
          </div>
          <div className="scrolling menu">
            {items}
          </div>
        </div>
      </div>
    );
  }
}

export default AttachTicket;
