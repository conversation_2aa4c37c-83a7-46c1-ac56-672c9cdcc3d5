import PropTypes from 'prop-types';
import { Component } from 'react';
import { concat, map, reject } from 'lodash';

import AttachTicket from './attach_ticket';
import AttachmentRow from './attachment_row';

class AttachedTickets extends Component {
  static propTypes = {
    project: PropTypes.object.isRequired,
    attachments: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props);
    this.state = { attachments: props.attachments };
    this.onNewAttachment = this.handleNewAttachment.bind(this);
    this.removeAttachment = this.handleRemoveAttachment.bind(this);
  }

  handleNewAttachment(attachment) {
    const attachments = concat(this.state.attachments, [attachment]);
    this.setState({ attachments });
  }

  handleRemoveAttachment(attachment) {
    const id = attachment.id;
    const projectId = this.props.project.id;

    $.ajax({
      type: 'DELETE',
      url: `/maintenance/projects/${projectId}/project_tickets/${id}`,
      success: () => {
        const attachments = reject(this.state.attachments, { id: attachment.id });
        this.setState({ attachments });
      },
      dataType: 'json',
    });
  }

  render() {
    const ticketRows = map(this.state.attachments, (attachment, i) =>
      <AttachmentRow
        key={i}
        attachment={attachment}
        onClick={() => this.removeAttachment(attachment)}
      />
    );

    return (
      <div style={{ marginTop: '1em', maxWidth: '400px' }}>
        <h3 className="ui header">
          Attached Maintenance Tickets
        </h3>
        <div className="ui divided list">
          {ticketRows}
        </div>
        <AttachTicket
          projectId={this.props.project.id}
          onNewAttachment={this.onNewAttachment}
        />
      </div>
    );
  }
}

export default AttachedTickets;
