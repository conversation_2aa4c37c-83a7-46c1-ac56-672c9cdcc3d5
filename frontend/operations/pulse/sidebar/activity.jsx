import moment from 'moment';
import { capitalize, startCase } from 'lodash';
import { shape, string } from 'prop-types';

const extra = activity => {
  if (!activity.due_at && !activity.scheduled_at) {
    return null;
  }

  if (activity.scheduled_at) {
    return `Scheduled for ${moment(activity.scheduled_at).calendar()}`;
  }

  return `Due ${moment(activity.due_at).fromNow()}`;
};

const Activity = ({ activity }) => (
  <a className="item" href={activity.path}>
    <div className="right floated content">
      {moment(activity.created_at).fromNow()}
    </div>
    <div className="content">
      <div className="header">{activity.title}</div>
      <div className="meta">
        {capitalize(startCase(activity.type))} assigned to you
      </div>
      <div className="description">{activity.description}</div>
      <div className="extra">{extra(activity)}</div>
    </div>
  </a>
);

Activity.propTypes = {
  activity: shape({
    title: string.isRequired,
    description: string.isRequired,
  }).isRequired,
};

export default Activity;
