import { Component } from 'react';

import TopTabs from './top_tabs';
import Activities from './activities';
import Leads from './leads';
import WorkOrders from './work_orders';

class OperationsSidebar extends Component {
  state = {
    activeTab: 'workOrders',
  };

  changeTab = activeTab => this.setState({ activeTab });

  render() {
    const { activeTab } = this.state;

    return (
      <div>
        <TopTabs changeTab={this.changeTab} activeTab={activeTab} />
        <Activities visible={activeTab === 'activities'} />
        <WorkOrders visible={activeTab === 'workOrders'} />
        <Leads visible={activeTab === 'leads'} />
      </div>
    );
  }
}

export default OperationsSidebar;
