import PropTypes from 'prop-types';
import { camelCase } from 'lodash';
import classNames from 'classnames';

const Link = ({ text, changeTab, activeTab }) => {
  const thisTab = camelCase(text); // e.g. workOrders

  const active = thisTab === activeTab;

  return (
    <a
      className={classNames({ active }, 'item')}
      onClick={() => changeTab(thisTab)}
    >
      {text}
    </a>
  );
};

Link.propTypes = {
  changeTab: PropTypes.func.isRequired,
  activeTab: PropTypes.string.isRequired,
  text: PropTypes.string.isRequired,
};

const TopTabs = props => (
  <div className="ui three item menu tabs underlined" style={{ flex: '0 0 auto', zIndex: 10 }}>
    <Link text="Activities" {...props} />
    <Link text="Work Orders" {...props} />
    <Link text="Leads" {...props} />
  </div>
);

export default TopTabs;
