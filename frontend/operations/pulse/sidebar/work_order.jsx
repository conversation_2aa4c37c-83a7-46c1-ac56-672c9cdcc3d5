import moment from 'moment';
import { truncate } from 'lodash';

const WorkOrder = ({ ticket }) => (
  <a className="item" href={`/maintenance/tickets/${ticket.id}`}>
    <div className="right floated content">
      {moment(ticket.opened_at).fromNow()}
    </div>
    <div className="content">
      <div className="header">
        {ticket.subject}
      </div>
      <i>{truncate(ticket.description, { length: 120 })}</i>
    </div>
  </a>
);

export default WorkOrder;
