import { Component } from 'react';
import { bool } from 'prop-types';
import { map } from 'lodash';

import Activity from './activity';

class Activities extends Component {
  static propTypes = {
    visible: bool.isRequired,
  }

  state = { activities: [] };

  componentDidMount() {
    $.get('/operations/pulse/activities.json', activities => this.setState({ activities }));
  }

  render() {
    const { visible } = this.props;
    const { activities } = this.state;

    return (
      <div
        style={{
          display: visible ? 'flex' : 'none',
          flexDirection: 'column',
          flexGrow: 1,
        }}
      >
        {activities.length === 0 &&
          <div className="ui message" style={{ margin: '1em' }}>
            <i>No scheduled activities to show.</i>
          </div>
        }
        <div className="ui divided selection list">
          {map(activities, (activity, i) => <Activity key={i} activity={activity} />)}
        </div>
      </div>
    );
  }
}

export default Activities;
