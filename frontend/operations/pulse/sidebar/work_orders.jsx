import { Component } from 'react';
import { findDOMNode } from 'react-dom';
import { map } from 'lodash';

import WorkOrder from './work_order';

class WorkOrders extends Component {
  constructor(props) {
    super(props);

    $.get('/maintenance/tickets.json?filters[status]=show-open', tickets => this.setState({ open: tickets }));
    $.get('/maintenance/tickets.json?filters[status]=closed', tickets => this.setState({ closed: tickets }));
  }

  state = {
    open: [],
    closed: [],
  };

  componentDidMount() {
    const accordion = $(findDOMNode(this)).find('.ui.accordion');
    accordion.accordion({
      duration: 200,
      onClosing() {
        const index = $(this).index();
        if (index === 1) {
          accordion.accordion('open', 1);
        } else {
          accordion.accordion('open', 0);
        }
      },
    });
  }

  render() {
    const { visible } = this.props;

    return (
      <div
        style={{
          display: visible ? 'flex' : 'none',
          flexDirection: 'column',
          flexGrow: 1,
          overflowY: 'hidden',
        }}
      >
        <div className="ui two item menu tabs">
          <a
            className="item"
            href="/maintenance/tickets/new"
            style={{ textAlign: 'center', borderBottom: 0 }}
          >
            <i className="plus icon" />
            New Ticket
          </a>

          <a
            className="item"
            href="/maintenance/tickets"
            style={{ textAlign: 'center', borderBottom: 0 }}
          >
            View All
          </a>
        </div>
        <div className="ui accordion" style={{ display: 'flex', flexDirection: 'column', flexGrow: 1, overflowY: 'hidden' }}>
          <div className="active title">
            <i className="dropdown icon" />
            Open
          </div>
          <div className="content active" style={{ overflowY: 'scroll', padding: 0, flexGrow: 1 }}>
            <div className="transition visible">
              {this.state.open.length === 0 &&
                <div className="ui message" style={{ margin: '1em' }}>
                  <i>
                    No open tickets to show.&nbsp;
                    <a href="/maintenance/tickets/new">Create a Work Order</a>
                  </i>
                </div>
              }
              <div className="ui divided selection list">
                {map(this.state.open, (ticket, index) => <WorkOrder key={index} ticket={ticket} />)}
              </div>
            </div>
          </div>
          <div className="title">
            <i className="dropdown icon" />
            Closed
          </div>
          <div className="content" style={{ overflowY: 'scroll', padding: 0 }}>
            <div className="transition hidden">
              <div className="ui divided selection list">
                {map(this.state.closed, (ticket, index) => <WorkOrder key={index} ticket={ticket} />)}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default WorkOrders;
