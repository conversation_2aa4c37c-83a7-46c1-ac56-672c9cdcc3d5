export const SET_DATE = 'SET_DATE';
export const setDate = date => ({ type: SET_DATE, date });

export const SET_DURATION = 'SET_DURATION';
export const setDuration = duration => ({ type: SET_DURATION, duration });

export const RECEIVE_EVENTS = 'RECEIVE_EVENTS';
export const receiveEvents = events => ({ type: RECEIVE_EVENTS, events });

export const fetchEvents = () => dispatch => {
  $.get(
    '/manage/calendar_events.json?all_users=true',
    events => dispatch(receiveEvents(events)),
  );
};

export const RECEIVE_EMPLOYEES = 'RECEIVE_EMPLOYEES';
export const receiveEmployees = employees => ({ type: RECEIVE_EMPLOYEES, employees });

export const fetchEmployees = () => dispatch => {
  $.get(
    '/organization/employees.json',
    employees => dispatch(receiveEmployees(employees)),
  );
};
