import { connect } from 'react-redux';
import * as actions from './actions';
import classNames from 'classnames';

const mapState = state => ({ duration: state.duration });

const DurationButton = connect(mapState, actions)(
  ({ duration, setDuration, value, children }) => (
    <button
      className={classNames('ui button', { active: value === duration })}
      onClick={() => setDuration(value)}
    >
      {children}
    </button>
  ),
);

const DurationSelector = () => (
  <div className="field">
    <label>Schedule Approximately...</label>
    <div className="ui basic fluid vertical buttons">
      <DurationButton value={30}>30 Minutes</DurationButton>
      <DurationButton value={60}>1 Hour</DurationButton>
      <DurationButton value={120}>2 Hours</DurationButton>
      <DurationButton value={240}>4 Hours</DurationButton>
    </div>
  </div>
);

export default connect(mapState, actions)(DurationSelector);
