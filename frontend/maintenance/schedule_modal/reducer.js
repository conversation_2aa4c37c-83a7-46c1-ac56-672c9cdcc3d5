import { SET_DATE, SET_DURATION, RECEIVE_EVENTS, RECEIVE_EMPLOYEES } from './actions';

export default (state, action) => {
  switch (action.type) {
    case SET_DATE:
      return { ...state, date: action.date };
    case SET_DURATION:
      return { ...state, duration: action.duration };
    case RECEIVE_EVENTS:
      return { ...state, events: action.events };
    case RECEIVE_EMPLOYEES:
      return { ...state, employees: action.employees };
    default:
      return state;
  }
};
