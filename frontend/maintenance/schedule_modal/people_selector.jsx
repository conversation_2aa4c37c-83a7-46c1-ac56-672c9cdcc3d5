import { Component } from 'react';
import { connect } from 'react-redux';
import { map } from 'lodash';

class PeopleSelector extends Component {
  render() {
    const { employees } = this.props;

    return (
      <div className="field" id="schedule-employee-checkboxes">
        <label>People</label>
        <div className="grouped fields">
          {map(employees, employee => (
            <div className="field">
              <div className="ui checkbox">
                <input type="checkbox" checked data-id={employee.id} />
                <label>{employee.name}</label>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
}

const mapState = state => ({
  employees: state.employees,
});

export default connect(mapState)(PeopleSelector);
