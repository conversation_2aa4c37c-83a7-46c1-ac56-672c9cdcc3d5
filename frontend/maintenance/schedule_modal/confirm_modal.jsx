import { connect } from 'react-redux';
import { map } from 'lodash';

import RailsForm from '../../forms/rails_form';

const ConfirmModal = ({ employees }) => (
  <div id="schedule-request" className="ui tiny modal">
    <i className="black close icon" />
    <div className="header">Confirm Details</div>
    <div className="content">
      <RailsForm
        className="ui form"
        action={window.location.pathname}
        id="schedule_work_order"
        patch
      >
        <div className="required field">
          <label>Employee</label>
          <select
            id="schedule_employee_id"
            className="ui search selection dropdown"
            name="maintenance_ticket[assignment_attributes][user_id]"
          >
            <option value="">Select</option>
            {map(employees, employee => (
              <option key={employee.id} value={employee.id}>{employee.name}</option>
            ))}
          </select>
        </div>
        <div className="two required fields">
          <div className="ui calendar field" id="start-time-calendar">
            <label>Start Time</label>
            <div className="ui left icon input">
              <input
                id="schedule_start_time"
                name="maintenance_ticket[assignment_attributes][start_date]"
                type="text"
              />
              <i className="calendar alternate outline icon" />
            </div>
          </div>
          <div className="ui calendar field" id="end-time-calendar">
            <label>End Time</label>
            <div className="ui left icon input">
              <input
                id="schedule_end_time"
                name="maintenance_ticket[assignment_attributes][end_date]"
                type="text"
              />
              <i className="calendar alternate outline icon" />
            </div>
          </div>
        </div>
      </RailsForm>
    </div>
    <div className="actions">
      <button className="ui basic cancel button">Cancel</button>
      <button
        className="ui primary approve button"
        type="submit"
        form="schedule_work_order"
        data-disable-with="Scheduling"
      >
        Schedule
      </button>
    </div>
  </div>
);

const mapState = state => ({
  employees: state.employees,
});

export default connect(mapState)(ConfirmModal);
