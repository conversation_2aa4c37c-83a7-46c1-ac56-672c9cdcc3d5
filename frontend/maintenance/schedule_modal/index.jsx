import moment from 'moment';
import thunk from 'redux-thunk';
import { Component } from 'react';
import { Provider } from 'react-redux';
import { createStore, applyMiddleware } from 'redux';

import ConfirmModal from './confirm_modal';
import DurationSelector from './duration_selector';
import LargeCalendar from './large_calendar';
import PeopleSelector from './people_selector';
import SmallCalendar from './small_calendar';
import reducer from './reducer';
import { fetchEmployees, fetchEvents } from './actions';

const Options = () => (
  <div className="calendar-options">
    <SmallCalendar />
    <div className="ui form">
      <DurationSelector />
      <PeopleSelector />
    </div>
  </div>
);

export default class extends Component {
  constructor(props) {
    super(props);

    const defaultState = {
      date: moment(),
      events: [],
      duration: 60,
      employees: [],
    };

    this.store = createStore(reducer, defaultState, applyMiddleware(thunk));
    this.store.dispatch(fetchEvents());
    this.store.dispatch(fetchEmployees());

    const timeSlot = { startTime: moment(), endTime: moment() };

    this.state = { timeSlot };
  }

  selectTimeSlot = (timeSlot) => {
    this.setState({ timeSlot });

    const modal  = '.ui.modal#schedule-request';

    const form = $(`${modal} form`);

    $(modal)
      .modal(window.modalDefaults)
      .modal('setting', 'observeChanges', true)
      .modal('setting', 'autofocus', false)
      .modal('setting', 'onVisible', () => {
        $(`${modal} #start-time-calendar`)
          .calendar({ type: 'datetime' })
          .calendar('set date', timeSlot.startTime.toDate());

        $(`${modal} #end-time-calendar`)
          .calendar({ type: 'datetime' })
          .calendar('set date', timeSlot.endTime.toDate());

        $(`${modal} #schedule_employee_id`).dropdown();

        form.form({
          on: 'submit',
          inline: true,
          fields: {
            schedule_employee_id: ['empty'],
            schedule_start_time: ['empty'],
            schedule_end_time: ['empty'],
          },
        });
      })
      .modal('setting', 'onApprove', () => {
        form.form('submit');
        return false;
      })
      .modal('show');

  }

  render() {
    return (
      <Provider store={this.store}>
        <div>
          <div className="ui large modal" id="schedule-work-order">
            <i className="black close icon" />
            <div className="ui header">
              Schedule Work Order
            </div>
            <div className="scrolling content">
              <div className="ui two column divided grid">
                <div className="four wide column" style={{ height: 1228 }}>
                  <Options />
                </div>
                <div className="twelve wide column">
                  <LargeCalendar selectTimeSlot={this.selectTimeSlot} />
                </div>
              </div>
            </div>
          </div>
          <ConfirmModal />
        </div>
      </Provider>
    );
  }
}
