import { connect } from 'react-redux';
import { map } from 'lodash';

import TimeColumn from './time_column';
import DayColumn from './large_calendar/day_column';

const weekDays = startDate => {
  const days = [];

  for (let i = 0; i < 7; i += 1) {
    days.push(startDate.clone().add(i, 'days'));
  }

  return days;
};

const Header = ({ startDate }) => (
  <div className="ui seven column grid week-header">
    {map(weekDays(startDate), day => (
      <div className="column">
        <div className="wday">{day.format('ddd')}</div>
        <div className="day">{day.format('D')}</div>
      </div>
    ))}
  </div>
);

const DayColumns = ({ startDate, selectTimeSlot }) => (
  <div className="ui seven column divided days grid" style={{ height: 1152 }}>
    {map(weekDays(startDate), (day, i) => <DayColumn key={i} day={day} selectTimeSlot={selectTimeSlot} />)}
  </div>
);

const LargeCalendar = ({ date, selectTimeSlot }) => (
  <div className="large calendar">
    <Header startDate={date} />
    <div>
      <TimeColumn day={date} />
      <DayColumns startDate={date} selectTimeSlot={selectTimeSlot} />
    </div>
  </div>
);

const mapState = state => ({
  date: state.date,
});

export default connect(mapState)(LargeCalendar);
