import { map } from 'lodash';

const TimeColumn = ({ day }) => {
  const start = day.clone().startOf('day');
  const stop = day.clone().endOf('day');

  const values = [];

  while (start.isBefore(stop)) {
    values.push(start.format('h A'));
    start.add(1, 'hour');
  }

  return (
    <div className="times column">
      {map(values, time => <div>{time}</div>)}
    </div>
  );
};

export default TimeColumn;
