import moment from 'moment';

import CalendarItem from './calendar_item';

const timeFormat = 'LT';

class Event extends CalendarItem {
  render() {
    const { event } = this.props;

    return (
      <div
        className="event"
        style={this.style}
      >
        <div className="duration">
          {moment(event.start_date).format(timeFormat)}
          -
          {moment(event.end_date).format(timeFormat)}
        </div>
        <div className="employee">{event.user_name}</div>
        <div className="description">
          {event.title}
        </div>
      </div>
    );
  }

  get right() {
    return 12;
  }

  get startTime() {
    return moment(this.props.event.start_date);
  }

  get endTime() {
    return moment(this.props.event.end_date);
  }
}

export default Event;
