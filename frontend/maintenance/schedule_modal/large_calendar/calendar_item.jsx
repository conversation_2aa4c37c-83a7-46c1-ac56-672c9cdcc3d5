import { Component } from 'react';

const hourHeight = 48;
const margin = 2;

export default class extends Component {
  get style() {
    return {
      position: 'absolute',
      left: this.left,
      right: this.right,
      top: this.offset + margin,
      height: this.height - (margin * 2),
    };
  }

  get right() {
    return margin;
  }

  get left() {
    return margin;
  }

  get height() {
    const duration = this.endTime.diff(this.startTime, 'minutes') / 60;
    return hourHeight * duration;
  }

  get offset() {
    const hours = this.startTime.hours() + (this.startTime.minutes() / 60);
    return hourHeight * hours;
  }
}
