import moment from 'moment';
import { connect } from 'react-redux';
import { map, filter } from 'lodash';

import Availability from './availability';
import Event from './event';

const resolution = 30;

const computeAvailabilities = (day, duration, events) => {
  const start = moment(day).startOf('day');
  const stop = moment(day).endOf('day');

  const startTime = moment(start);
  const endTime = moment(start).add(duration, 'minutes');

  const availabilities = [];

  while (endTime.isSameOrBefore(stop)) {
    availabilities.push({
      startTime: startTime.clone(),
      endTime: endTime.clone(),
    });

    startTime.add(resolution, 'minutes');
    endTime.add(resolution, 'minutes');
  }

  return availabilities;
};

const DayColumn = ({ selectTimeSlot, availabilities, events }) => (
  <div className="column day-column">
    {map(availabilities, (availability, i) => (
      <Availability
        key={i}
        availability={availability}
        onClick={() => selectTimeSlot(availability)}
      />
    ))}
    {map(events, (event, i) => <Event key={i} event={event} />)}
  </div>
);

const mapState = (state, props) => ({
  duration: state.duration,
  events: filter(state.events, event =>
    moment(event.start_date).isSame(props.day, 'day'),
  ),
});

const mergeProps = ({ duration, events }, _, { selectTimeSlot, day }) => ({
  selectTimeSlot,
  events,
  availabilities: computeAvailabilities(day, duration, events),
});

export default connect(mapState, null, mergeProps)(DayColumn);
