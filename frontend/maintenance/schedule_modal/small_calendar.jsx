import moment from 'moment';
import { Component } from 'react';
import { connect } from 'react-redux';
import { func } from 'prop-types';

import * as actions from './actions';

class SmallCalendar extends Component {
  static propTypes = {
    setDate: func.isRequired,
  }

  componentDidMount() {
    $(this.node).calendar({
      inline: true,
      type: 'date',
      minDate: moment().toDate(),
      onChange: this.onChange,
    });
  }

  onChange = date => {
    this.props.setDate(moment(date));
  }

  render() {
    return (
      <div className="ui calendar" ref={n => { this.node = n; }} />
    );
  }
}

export default connect(null, actions)(SmallCalendar);
