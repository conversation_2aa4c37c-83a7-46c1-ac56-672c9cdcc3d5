import PropTypes from 'prop-types';
import { Component } from 'react';

import Pie from '../../charts/pie/pie';

class StatisticPie extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selection: null,
    };
    this.onSelectionChanged = this.handleSelectionChanged.bind(this);
  }

  handleSelectionChanged(selection) {
    this.setState({ selection });
  }

  render() {
    const { selection } = this.state;

    const description = selection ? `${selection.name}: ${selection.value}` : 'Hover to inspect';

    return (
      <div className="ui statistic column" style={{ alignItems: 'center' }}>
        <Pie
          height={this.props.height}
          width={this.props.width}
          data={this.props.data}
          onSelectionChanged={this.onSelectionChanged}
        />
        <div className="label">
          {this.props.label}
        </div>
        <div className="description">
          {description}
        </div>
      </div>
    );
  }
}

StatisticPie.propTypes = {
  data: PropTypes.array.isRequired,
  label: PropTypes.string.isRequired,
  height: PropTypes.number.isRequired,
  width: PropTypes.number.isRequired,
};

StatisticPie.defaultProps = {
  data: [],
  width: 140,
  height: 140,
};

export default StatisticPie;
