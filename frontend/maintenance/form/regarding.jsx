import PropTypes from 'prop-types';

import Dropdown from '../../selectors/dropdown.jsx';

const Regarding = ({ regardingId }) => {
  const params = (new URI(window.location)).search(true);
  const defaultSelection = regardingId || params.regarding_id;

  return (
    <div className="ui field">
      <label htmlFor="maintenance_ticket_regarding">
        Regarding
      </label>
      <Dropdown
        url="/maintenance/tickets/available.json"
        name="regarding"
        id="maintenance_ticket_regarding"
        defaultText="A property, unit, or tenant"
        defaultSelection={defaultSelection}
      />
    </div>
  );
};

Regarding.propTypes = {
  regardingId: PropTypes.string,
};

export default Regarding;
