import PropTypes from 'prop-types';

import Dropdown from '../../selectors/dropdown.jsx';

const Assignment = ({ userId }) => (
  <div className="ui field">
    <label htmlFor="maintenance_ticket_assignment">
      Assign Employee
    </label>
    <Dropdown
      defaultSelection={userId}
      fluid
      defaultText="Employee"
      id="maintenance_ticket_assignment"
      name="maintenance_ticket[assignment_attributes][user_id]"
      url="/organization/employees.json"
    />
  </div>
);

Assignment.propTypes = {
  userId: PropTypes.number,
};

export default Assignment;
