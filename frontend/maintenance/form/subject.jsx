import PropTypes from 'prop-types';

const Subject = ({ subject }) => (
  <div className="required field">
    <label htmlFor="maintenance_ticket_subject">
      Subject
    </label>
    <input
      id="maintenance_ticket_subject"
      name="maintenance_ticket[subject]"
      placeholder="e.g. Broken Dishwasher"
      type="text"
      defaultValue={subject}
    />
  </div>
);

Subject.propTypes = {
  subject: PropTypes.string,
};

export default Subject;
