import { Component } from 'react';

import OrButton from '../../components/or_button';

class Recurring extends Component {
  constructor(props) {
    super(props);
    this.state = { enabled: false };
  }

  componentDidMount() {
    $('.recurring-selection .ui.dropdown').dropdown({
      onChange: (value) => {
        $("input[name='schedule[unit]']").val(value);
      },
    });
    $('.recurring-selection .ui.calendar').calendar({ type: 'date' });
    $('.recurring-selection .accordion').accordion();
    $('.recurring-selection .accordion .title').hide();
  }

  handleChange(enabled) {
    this.setState({ enabled });
    const el = $('.recurring-selection .accordion');
    if (enabled) {
      el.accordion('open', 0);
    } else {
      el.accordion('close', 0);
    }
  }

  every() {
    const amountName = this.state.enabled ? 'schedule[amount]' : null;
    const unitName = this.state.enabled ? 'schedule[unit]' : null;

    return (
      <div className="inline field">
        <label htmlFor="maintenance_ticket_recurring_amount">
          Every
        </label>
        <div className="ui fluid right labeled input">
          <input type="text" name={amountName} id="maintenance_ticket_recurring_amount" placeholder="Number" defaultValue="2" />
          <input type="hidden" name={unitName} defaultValue="weeks" />
          <div className="ui dropdown label" id="schedule-unit-dropdown">
            <div className="text">Weeks</div>
            <i className="dropdown icon"></i>
            <div className="menu">
              <div className="item">Days</div>
              <div className="item">Weeks</div>
              <div className="item">Months</div>
              <div className="item">Years</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  start() {
    const name = this.state.enabled ? 'schedule[start_date]' : null;
    return (
      <div>
        <div className="ui inline calendar field">
          <label htmlFor="maintenance_ticket_recurring_start">
            Starting on
          </label>
          <div className="ui fluid left icon input">
            <input id="maintenance_ticket_recurring_start" type="text" placeholder="Start Date" name={name} />
            <i className="calendar alternate outline icon" />
          </div>
        </div>
      </div>
    );
  }

  render() {
    const change = this.handleChange.bind(this);

    const content = (
      <div>
        <div className="title" />
        <div className="content">
          <div className="ui segment" style={{ width: '350px' }}>
            <div className="ui small form">
              {this.every()}
              {this.start()}
            </div>
          </div>
        </div>
      </div>
    );

    return (
      <div className="recurring-selection">
        <h4 className="ui header">
          Frequency
        </h4>

        <OrButton left="One Time" right="Recurring" onChange={change} />

        <div className="ui accordion">
          {content}
        </div>
      </div>
    );
  }
}

export default Recurring;
