import PropTypes from 'prop-types';

import Dropdown from '../../selectors/dropdown.jsx';

const urgencies = [
  { id: 'minor', name: 'Minor' },
  { id: 'normal', name: 'Normal' },
  { id: 'critical', name: 'Critical' },
];

const Urgency = ({ urgency }) => (
  <div className="ui field">
    <label htmlFor="maintenance_ticket_urgency">
      Urgency
    </label>
    <Dropdown
      items={urgencies}
      name="maintenance_ticket[urgency]"
      id="maintenance_ticket_urgency"
      defaultText="Urgency"
      defaultSelection={urgency}
    />
  </div>
);

Urgency.propTypes = {
  urgency: PropTypes.string.isRequired,
};

Urgency.defaultProps = {
  urgency: 'normal',
};

export default Urgency;
