import PropTypes from 'prop-types';

import Dropdown from '../../selectors/dropdown.jsx';

const Tags = ({ tags }) => (
  <div className="ui field">
    <label htmlFor="maintenance_ticket_tags">
      Tags
    </label>
    <Dropdown
      allowAdditions
      defaultSelection={tags}
      defaultText="Tags"
      fluid
      id="maintenance_ticket_tags"
      multiple
      name="maintenance_ticket[tags][]"
      textField="tag"
      url="/manage/tags?type=maintenance_ticket"
      valueField="tag"
    />
  </div>
);

Tags.propTypes = {
  tags: PropTypes.array.isRequired,
};

Tags.defaultProps = {
  tags: [],
};

export default Tags;
