import PropTypes from 'prop-types';

import RailsForm from '../../forms/rails_form';

import Attachments from './attachments';
import Assignment from './assignment';
import Description from './description';
import Recurring from './recurring';
import Subject from './subject';
import Tags from './tags';
import Regarding from './regarding';
import Urgency from './urgency';

const NewTicketForm = ({ ticket }) => {
  const action = ticket.id ? `/maintenance/tickets/${ticket.id}` : '/maintenance/tickets';

  return (
    <RailsForm
      action={action}
      id="new_maintenance_ticket"
      patch={ticket.id !== undefined}
    >
      <Subject subject={ticket.subject} />
      <Description description={ticket.description} />
      <Regarding regardingId={ticket.regarding_id} />
      <Urgency urgency={ticket.urgency} />
      <Tags tags={ticket.tags} />
      <Assignment userId={ticket.assigned_user ? ticket.assigned_user.id : undefined} />
      <Recurring />
      <br />
      <Attachments photos={ticket.photos} />

      <div className="clearfix">
        <button className="ui field right floated submit button">
          {ticket.id ? 'Update Ticket' : 'Create Ticket'}
        </button>
      </div>
    </RailsForm>
  );
};

NewTicketForm.propTypes = {
  ticket: PropTypes.object.isRequired,
};

NewTicketForm.defaultProps = {
  ticket: {},
};

export default NewTicketForm;
