import PropTypes from 'prop-types';

import StackedAreaD3 from './stacked_area_d3.js';
import D3Wrapper from '../d3_wrapper.jsx';

class StackedArea extends D3Wrapper {
  createChart(node, state) {
    return new StackedAreaD3(node, state);
  }
}

StackedArea.propTypes = {
  data: PropTypes.array.isRequired,
};

StackedArea.defaultProps = {
  width: 400,
  height: 400,
  margin: {
    top: 0,
    right: 15,
    bottom: 25,
    left: 15,
  },
};

export default StackedArea;
