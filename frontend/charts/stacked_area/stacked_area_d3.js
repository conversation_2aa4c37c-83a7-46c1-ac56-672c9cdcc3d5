class StackedAreaD3 {
  constructor(element, props) {
    const { width, height, margin } = props;

    this.svg = d3.select(element)
      .append('svg')
      .attr('class', 'd3-chart')
      .attr('width', width)
      .attr('height', height)
      .append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);

    this.x = d3.time.scale();
    this.y = d3.scale.linear();

    this.xaxis = d3.svg.axis()
      .scale(this.x)
      .ticks(5)
      .orient('bottom');

    this.stack = d3.layout.stack();

    this.area = d3.svg.area()
      .interpolate('basis')
      .x(d => this.x(d.x))
      .y0(d => this.y(d.y0))
      .y1(d => this.y(d.y0 + d.y));

    this.svg.append('g')
      .attr('class', 'x axis')
      .attr('transform', `translate(0, ${height - margin.top - margin.bottom})`)
      .call(this.xaxis);

    this.update(element, props);
  }

  update(element, props) {
    const { data, width, height, margin } = props;

    this.x
      .domain(d3.extent(data.map(d => d.date)))
      .range([0, width - margin.left - margin.right]);

    this.y
      .domain([0, 1])
      .range([height - margin.top - margin.bottom, 0]);

    const layers = _.map([1, 2, 3, 4, 5], rating => {
      const values = _.map(data, d => (
        { x: d.date._d, y: d.normalized[rating] }
      ));

      return {
        rating,
        values,
      };
    });

    this.stack
      .values(d => d.values);

    const color = d3.scale.ordinal()
      .domain([1, 2, 3, 4, 5])
      .range(['#db2828', '#f2711c', '#fbbd08', '#b5cc18', '#21ba45']);

    const stacked = this.stack(layers);
    const layer = this.svg.selectAll('.layer')
      .data(stacked)
      .enter()
        .append('g')
        .attr('class', 'layer');

    layer.append('path')
      .attr('class', 'area')
      .attr('fill', d => color(d.rating))
      .style('opacity', '0.80')
      .attr('d', d => this.area(d.values));

    this.svg.selectAll('.x.axis')
      .transition()
      .duration(500)
      .call(this.xaxis);
  }

  destroy() { }
}

export default StackedAreaD3;
