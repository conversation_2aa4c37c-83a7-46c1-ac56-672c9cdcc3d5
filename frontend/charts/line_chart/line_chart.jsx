import PropTypes from 'prop-types';

import LineChartD3 from './line_chart_d3';
import D3<PERSON>rapper from '../d3_wrapper';

class <PERSON><PERSON><PERSON> extends D3Wrapper {
  createChart(node, state) {
    return new LineChartD3(node, state);
  }
}

LineChart.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    name: PropTypes.string.isRequired,
    values: PropTypes.arrayOf(PropTypes.shape({
      date: PropTypes.instanceOf(Date).isRequired,
      value: PropTypes.number.isRequired,
    })).isRequired,
  })).isRequired,
  clampY: PropTypes.bool.isRequired,
  displayArea: PropTypes.bool.isRequired,
  displayAxes: PropTypes.bool.isRequired,
  displayLegend: PropTypes.bool.isRequired,
};

LineChart.defaultProps = {
  ...D3Wrapper.defaultProps,
  clampY: false,
  displayArea: false,
  displayAxes: true,
  displayLegend: true,
};

export default LineChart;
