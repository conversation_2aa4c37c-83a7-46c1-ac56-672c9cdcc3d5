class LineChartD3 {
  constructor(element, props) {
    const { width, height, margin, displayAxes, displayArea, onHover } = props;

    this.svg = d3.select(element)
      .append('svg')
      .attr('class', 'line-chart-d3')
      .attr('width', width)
      .attr('height', height)
      .append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);

    this.clipPath = this.svg.append('clipPath')
      .attr('id', 'rect-clip')
      .append('rect')
      .attr('width', width - margin.left - margin.right)
      .attr('height', height - margin.top - margin.bottom);

    /*
     * Scales
     */
    this.x = d3.time.scale();

    this.y = d3.scale.linear();

    /*
     * Mouse Move
     */
    if (onHover) {
      this.bisectDate = d3.bisector(d => d.date).left;
      const self = this;
      this.svg.on('mousemove', function () {
        const mouseX = d3.mouse(this)[0];
        const value = self.mouseToValue(mouseX);
        onHover(value);
      });

      this.svg.on('mouseout', () => onHover(null));
    }

    /*
     * Axis
     */
    if (displayAxes) {
      this.xaxis = d3.svg.axis()
        .scale(this.x)
        .ticks(5)
        .orient('bottom');

      this.svg.append('g')
        .attr('class', 'x axis')
        .attr('transform', `translate(0, ${height - margin.top - margin.bottom})`)
        .call(this.xaxis);

      this.yaxis = d3.svg.axis()
        .scale(this.y)
        .orient('left');

      this.svg.append('g')
        .attr('class', 'y axis')
        .call(this.yaxis);
    }

    if (displayArea) {
      this.svg.append('linearGradient')
        .attr('id', 'area-gradient')
        .attr('gradientUnits', 'userSpaceOnUse')
        .attr('x1', 0).attr('y1', 0)
        .attr('x2', 0).attr('y2', 100)
        .selectAll('stop')
        .data([
          {offset: '0%', color: '#97d1f9'},
          {offset: '100%', color: '#ffffff'}
        ])
        .enter().append('stop')
        .attr('offset', function(d) { return d.offset; })
        .attr('stop-color', function(d) { return d.color; });
    }

    /*
     * Legend
     */

    this.update(element, props);
  }

  update(element, props) {
    let data = props.data;
    if (data.length == 0) { return; }
    if (data[0].values.length == 0) { return; }
    const { width, height, margin, displayArea, displayAxes, displayLegend, clampY } = props;

    data = _.sortBy(data, d => - _.sumBy(d.values, v => v.value));

    const gwidth = width - margin.left - margin.right;
    const gheight = height - margin.top - margin.bottom;

    this.clipPath.attr('width', gwidth).attr('height', gheight);

    const values = _.flatMap(data, e => e.values);

    const xval = d => d.date;

    const yval = d => d.value;

    this.x
      .domain(d3.extent(values.map(xval)))
      .range([0, gwidth]);

    this.y
      .range([gheight * 0.95, gheight * 0.05]);

    this.mouseToValue = mouseX => {
      const invx = this.x.invert(mouseX);
      const bs = this.bisectDate(data[0].values, invx);
      const val = data[0].values[bs];
      return val;
    };

    if (clampY) {
      this.y.domain(d3.extent(values.map(yval)));
    } else {
      this.y.domain([0, _.max(_.map(values, v => yval(v))) || 0]);
    }

    const color = d3.scale.category20();

    const bottomLine = d3.svg.line()
      .interpolate('monotone')
      .x(d => this.x(xval(d)))
      .y(gheight);

    const line = d3.svg.line()
      .interpolate('monotone')
      .x(d => this.x(xval(d)))
      .y(d => this.y(yval(d)));

    const area = d3.svg.area()
      .interpolate('monotone')
      .x(d => this.x(xval(d)))
      .y0(gheight)
      .y1(d => this.y(yval(d)));

    const bottomArea = d3.svg.area()
      .interpolate('monotone')
      .x(d => this.x(xval(d)))
      .y0(gheight)
      .y1(gheight);

    const entries = this.svg.selectAll('.entry')
                       .data(data);

    const enter = entries.enter()
      .append('g')
      .attr('class', 'entry');

    // Line
    enter
      .append('path')
      .attr('class', 'line')
      .attr('clip-path', 'url(#rect-clip)')
      .attr('d', d => bottomLine(d.values));

    if (displayArea) {
      enter
        .append('path')
        .attr('class', 'area')
        .attr('clip-path', 'url(#rect-clip)')
        .attr('d', d => bottomArea(d.values));
    }

    entries.exit()
      .remove();

    entries.select('path.line')
      .style('stroke', d => color(d.name))
      .transition()
      .duration(500)
      .attr('d', d => line(d.values));

    if (displayArea) {
      entries.select('path.area')
        .transition()
        .duration(500)
        .attr('d', d => area(d.values));
    }

    if (displayAxes) {
      this.svg.selectAll('.x.axis').transition().duration(500).call(this.xaxis);
      this.svg.selectAll('.y.axis').transition().duration(500).call(this.yaxis);
    }

    if (displayLegend) {
      const legend = this.svg.selectAll('.legend').data(data);

      // Measure legend texts for horizontal spacing
      const offsets = [0];
      for (let i = 1; i < data.length; ++i) {
        const text = this.svg.append('text').text(data[i - 1].name);
        const w = text.node().getBBox().width;
        offsets[i] = offsets[i - 1] + w + 10;
        text.remove();
      }

      legend
        .enter()
        .append('text')
        .attr('class', 'legend');

      legend
        .attr('transform', (d, i) => `translate(${offsets[i]}, -20)`)
        .style('fill', (d) => color(d.name))
        .text(d => d.name);

      legend
        .exit()
          .remove();
    }
  }

  destroy() { }
}

export default LineChartD3;
