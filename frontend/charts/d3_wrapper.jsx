import PropTypes from 'prop-types';
import React from 'react';
import { Component } from 'react';
import { findDOMNode } from 'react-dom';

class D3Wrapper extends Component {
  componentDidMount() {
    this.chart = this.createChart(this.getDOMNode(), this.getChartState());
  }

  componentDidUpdate() {
    this.chart.update(this.getDOMNode(), this.getChartState());
  }

  componentWillUnmount() {
    this.chart.destroy(this.getDOMNode());
  }

  getDOMNode() {
    return findDOMNode(this);
  }

  getChartState() {
    return this.props;
  }

  render() {
    return <div className="chart-container" />;
  }
}

D3Wrapper.propTypes = {
  width: PropTypes.number.isRequired,
  height: PropTypes.number.isRequired,
  margin: PropTypes.shape({
    top: PropTypes.number.isRequired,
    right: PropTypes.number.isRequired,
    bottom: PropTypes.number.isRequired,
    left: PropTypes.number.isRequired,
  }).isRequired,
};

D3Wrapper.defaultProps = {
  width: 400,
  height: 400,
  margin: {
    top: 40,
    right: 20,
    bottom: 40,
    left: 40,
  },
};

export default D3Wrapper;
