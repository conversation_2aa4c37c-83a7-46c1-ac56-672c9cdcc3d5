import PropTypes from 'prop-types';

import D3Wrapper from '../d3_wrapper';
import MultiBarChartD3 from './multi_bar_chart_d3';

class MultiBar<PERSON>hart extends D3Wrapper {
  createChart(node, state) {
    return new MultiBarChartD3(node, state);
  }
}

MultiBarChart.PropTypes = {
  ...D3Wrapper.propTypes,
  grid: PropTypes.shape({
    horizontal: PropTypes.bool.isRequired,
    vertical: PropTypes.bool.isRequired,
  }).isRequired,
  interpolate: PropTypes.string.isRequired,
};

MultiBarChart.defaultProps = {
  ...D3Wrapper.defaultProps,
  margin: {
    top: 10,
    right: 10,
    bottom: 20,
    left: 60,
  },
  grid: {
    horizontal: false,
    vertical: false,
  },
  interpolate: 'monotone',
};

export default MultiBarChart;
