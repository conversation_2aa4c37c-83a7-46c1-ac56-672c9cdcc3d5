import moment from 'moment';

import { colors } from '../../util/color';

import uuid from 'uuid';

const topRoundedRect = (x, y, w, h, r) => (
  'M' + x + ',' + y
  + 'h' + (w - r)
  + 'a' + r + ',' + r + ' 0 0 1 ' + r + ',' + r
  + 'v' + (h - 2 * r)
  + 'v' + r + 'h' + -r
  + 'h' + (2 * r - w)
  + 'h' + -r + 'v' + -r
  + 'v' + (2 * r - h)
  + 'a' + r + ',' + r + ' 0 0 1 ' + r + ',' + -r
  + 'z'
);

class MultiBarChartD3 {
  constructor(element, props) {
    const { width, height, margin, grid } = props;

    this.gWidth = width - margin.left - margin.right;
    this.gHeight = height - margin.top - margin.bottom;

    this.svg = d3.select(element)
      .append('svg')
      .attr('class', 'd3-chart')
      .attr('width', width)
      .attr('height', height)
      .append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);

    this.clip = `rect-clip-${uuid.v4()}`;

    this.svg.append('clipPath')
      .attr('id', this.clip)
      .append('rect')
      .attr('width', this.gWidth)
      .attr('height', this.gHeight);

    this.color = d3.scale.ordinal()
      .domain(['Profit', 'Revenue', 'Expense', 'PlProfit', 'PlLoss'])
      .range(['#314655', '#49B8BF', '#CF305A', colors.orange, colors.gray]);


    /*
     * Scales
     */
    this.x = d3.scale.ordinal()
      .rangeRoundBands([0, this.gWidth], 0.75);

    this.y = d3.scale.linear()
      .domain([0, 0])
      .range([this.gHeight, 0]);

    /*
     * Axis
     */
    this.xaxis = d3.svg.axis()
      .scale(this.x)
      .ticks(d3.time.months, 1)
      .tickFormat(d3.time.format('%b'))
      .orient('bottom');

    if (grid.vertical) {
      this.xaxis = this.xaxis.innerTickSize(-(this.gHeight));
    }

    this.svg.append('g')
      .attr('class', 'x axis')
      .attr('transform', `translate(0, ${height - margin.top - margin.bottom})`)
      .call(this.xaxis);

    this.yaxis = d3.svg.axis()
      .scale(this.y)
      .ticks(5)
      .orient('left');

    if (grid.horizontal) {
      this.yaxis = this.yaxis.innerTickSize(-(this.gWidth));
    }

    this.svg.append('g')
      .attr('class', 'y axis')
      .call(this.yaxis);

    /*
     * Legend
     */

    if (props.legend === true) {
      const legendSize = 10;

      const legend = this.svg.selectAll('.legend')
        .data([
          { label: 'Revenue', color: 'PlProfit' },
          { label: 'Expense', color: 'PlLoss' },
        ])
        .enter()
        .append('g')
          .attr('class', 'legend')
          .attr('transform', (d, i) => `translate(${this.gWidth - 75}, ${i * 12})`);

      legend
        .attr('opacity', 0.1)
        .transition()
        .duration(500)
        .attr('opacity', 1);

      legend.append('rect')
        .attr('x', 0)
        .attr('y', 0)
        .attr('width', legendSize)
        .attr('height', legendSize)
        .attr('fill', d => this.color(d.color));

      legend.append('text')
        .attr('x', 12)
        .attr('y', 9)
        .attr('fill', '#999')
        .text(d => d.label);
    }

    this.update(element, props);
  }

  update(element, props) {
    const { data, interpolate } = props;

    const animationLength = 500;

    const lineValues = _.flatMap(data.lines, line => line.values);
    const barValues = _.flatMap(data.bars, bar => bar.values);
    const allValues = lineValues.concat(barValues);

    const dates = _.map(allValues, value => moment(value.date).toDate());
    const values = _.map(allValues, value => value.value);

    this.x
      .domain(dates);

    this.y
      .domain([0, d3.max(values)]);

    /*
     * Bars
     */

    const barWidth = this.x.rangeBand();
    const barSpacing = 0;
    const barCount = data.bars.length;
    const totalWidth = (barCount * barWidth) + ((barCount - 1) * barSpacing);

    const barGroups = this.svg.selectAll('.bar-group').data(data.bars);

    const xShift = i => - (totalWidth / 2) + ((barWidth + barSpacing) * i) + barWidth / 2;

    barGroups.enter()
      .append('g')
      .attr('class', 'bar-group')
      .attr('transform', (d, i) => `translate(${xShift(i)}, 0)`);

    barGroups.exit()
      .remove();

    const bars = barGroups.selectAll('rect').data(d => d.values);

    bars
      .enter()
      .append('path');

    bars
      .style('fill', (d, i, j) => this.color(data.bars[j].label))
      .attr('d', d => (
        topRoundedRect(
          this.x(moment(d.date).toDate()),
          this.y(0),
          this.x.rangeBand(),
          this.gHeight - this.y(0), 4
        )
      ))
      .transition()
      .duration(animationLength)
      .attr('d', d => (
        topRoundedRect(
          this.x(moment(d.date).toDate()),
          this.y(d.value),
          this.x.rangeBand(),
          this.gHeight - this.y(d.value), 4
        )
      ));

    bars
      .exit()
      .remove();

    /*
     * Lines
     */

    const line = d3.svg.line()
      .interpolate(interpolate)
      .x(d => this.x(moment(d.date).toDate()))
      .y(d => this.y(d.value));

    const lines = this.svg.selectAll('.entry').data(data.lines);

    const enterLines = lines.enter()
      .append('g')
      .attr('transform', `translate(${barWidth / 2}, 0)`)
      .attr('class', 'entry');

    enterLines
      .attr('opacity', 0.1)
      .transition()
      .duration(animationLength)
      .attr('opacity', 1);

    enterLines
      .append('path')
      .attr('class', 'line')
      .attr('clip-path', `url(#${this.clip})`);

    lines.exit()
      .remove();

    lines.select('path.line')
      .style('stroke', d => this.color(d.label))
      .transition()
      .duration(animationLength)
      .attr('d', d => line(d.values));

    /*
     * Axis
     */

    this.svg.selectAll('.x.axis')
      .transition()
      .duration(animationLength)
      .call(this.xaxis);

    this.svg.selectAll('.y.axis')
      .transition()
      .duration(animationLength)
      .call(this.yaxis);
  }

  destroy() { }
}

export default MultiBarChartD3;
