import PropTypes from 'prop-types';
import PieD3 from './pie_d3.js';
import D3Wrapper from '../d3_wrapper.jsx';

class Pie extends D3Wrapper {
  createChart(node, state) {
    return new PieD3(node, state);
  }
}

Pie.propTypes = {
  data: PropTypes.array.isRequired,
  height: PropTypes.number.isRequired,
  onSelectionChanged: PropTypes.func.isRequired,
  width: PropTypes.number.isRequired,
};

Pie.defaultProps = {
  height: 320,
  width: 320,
};

export default Pie;
