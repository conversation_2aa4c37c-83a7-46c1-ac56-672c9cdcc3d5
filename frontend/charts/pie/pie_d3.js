import { colors } from '../../util/color.js';

const color = d3.scale.category20c();

class PieD3 {
  constructor(element, props) {
    const { width, height } = props;

    const radius = Math.min(width, height) / 2;

    this.pie = d3.layout.pie()
      .value(d => d.value)
      .sort(null);

    this.arc = d3.svg.arc()
      .outerRadius(radius * 0.9)
      .innerRadius(0);

    this.arcOver = d3.svg.arc()
      .outerRadius(radius)
      .innerRadius(0);

    this.svg = d3.select(element)
      .append('svg')
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);
    this.update(element, props);
  }

  update(element, props) {
    const data = props.data;

    const path = this.svg.selectAll('path').data(this.pie(data));

    const pieColor = d => {
      if (d.color && d.color.startsWith('#')) {
        return d.color;
      }

      return colors[d.color] || color(d.name);
    };

    path
      .enter()
      .append('path')
      .on('mouseover', function (d) {
        props.onSelectionChanged(d.data);
        d3.select(this)
          .transition()
          .duration(120)
          .style('fill', d3.rgb(pieColor(d.data)).brighter(0.45));
      })
      .on('mouseout', function (d) {
        props.onSelectionChanged(null);
        d3.select(this)
          .style('fill', d3.rgb(pieColor(d.data)).brighter(0.45))
          .transition()
          .duration(120)
          .style('fill', pieColor(d.data));
      })
      .each(function (d) {
        this._current = d;
      });

    path
      .style('fill', d => pieColor(d.data))
      .transition()
      .duration(200)
      .attrTween('d', this.arcTweenMaker(this.arc));

    path
      .exit()
      .remove();
  }

  arcTweenMaker(arc) {
    return function (a) {
      const i = d3.interpolate(this._current, a);
      this._current = i(0);
      return function (t) {
        return arc(i(t));
      };
    };
  }

  destroy() { }
}

export default PieD3;
