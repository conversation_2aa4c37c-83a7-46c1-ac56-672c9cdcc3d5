import { Component } from 'react';

const NetworkChart = (Chart) => class extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: {
        lines: [],
        bars: [],
      },
    };
  }

  componentDidMount() {
    this.fetchData(this.props.url);
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.url !== this.props.url) {
      this.fetchData(nextProps.url);
    }
  }

  fetchData = url => $.get(url, data => this.setState({ data }));

  render() {
    // Skip url
    const { url, ...chartProps } = this.props;

    return (
      <Chart
        {...chartProps}
        data={this.state.data}
      />
    );
  }
};

export default NetworkChart;
