# A sample Guardfile
# More info at https://github.com/guard/guard#readme

# Specify only the directories we want to watch
directories %w[
  app
  lib
  config
  spec
]

# Ignore certain directories to prevent file watching issues
ignore %r{^node_modules/}
ignore %r{^tmp/}
ignore %r{^log/}
ignore %r{^public/}
ignore %r{^scripts/}
ignore %r{^vendor/}
ignore %r{^coverage/}

# Note: The cmd option is now required due to the increasing number of ways
#       rspec may be run, below are examples of the most common uses.
#  * bundler: 'bundle exec rspec'
#  * bundler binstubs: 'bin/rspec'
#  * spring: 'bin/rspec' (This will use spring if running and you have
#                        installed the spring binstubs per the docs)
#  * zeus: 'zeus rspec' (requires the server to be started separately)
#  * 'just' rspec: 'rspec'

guard :rspec, cmd: 'bundle exec rspec' do
  require 'guard/rspec/dsl'
  dsl = Guard::RSpec::Dsl.new(self)

  # RSpec files
  rspec = dsl.rspec
  watch(rspec.spec_helper) { rspec.spec_dir }
  watch(rspec.spec_support) { rspec.spec_dir }
  watch(rspec.spec_files)

  # Ruby files
  ruby = dsl.ruby
  dsl.watch_spec_files_for(ruby.lib_files)

  # Rails files
  rails = dsl.rails(view_extensions: %w(erb haml slim))
  dsl.watch_spec_files_for(rails.app_files)
  dsl.watch_spec_files_for(rails.views)

  watch(rails.controllers) do |m|
    [
      rspec.spec.call("routing/#{m[1]}_routing"),
      rspec.spec.call("controllers/#{m[1]}_controller"),
      rspec.spec.call("acceptance/#{m[1]}")
    ]
  end

  # Rails config changes
  watch(rails.spec_helper)     { rspec.spec_dir }
  watch(rails.routes)          { "#{rspec.spec_dir}/routing" }
  watch(rails.app_controller)  { "#{rspec.spec_dir}/controllers" }

  # Capybara features specs
  watch(rails.view_dirs)     { |m| rspec.spec.call("features/#{m[1]}") }
  watch(rails.layouts)       { |m| rspec.spec.call("features/#{m[1]}") }

  # ActiveAdmin files
  watch(%r{^app/admin/(.+)\.rb$}) { |m| "spec/admin/#{m[1]}_spec.rb" }

  # JSONAPI Resources files
  watch(%r{^app/resources/(.+)\.rb$}) { |m| "spec/resources/#{m[1]}_spec.rb" }

  # Model files
  watch(%r{^app/models/(.+)\.rb$}) do |m|
    [
      "spec/models/#{m[1]}_spec.rb",
      "spec/factories/#{m[1].pluralize}.rb"
    ]
  end

  # Factories
  watch(%r{^spec/factories/(.+)\.rb$}) do |m|
    [
      "spec/models/#{m[1].singularize}_spec.rb",
      "spec/controllers/#{m[1]}_controller_spec.rb",
      "spec/requests/#{m[1]}_spec.rb"
    ]
  end
end
