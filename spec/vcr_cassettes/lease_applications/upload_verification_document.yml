---
http_interactions:
- request:
    method: put
    uri: https://revela-uploads.s3.amazonaws.com/development_uploads/alever/lease_application/1/6312d0188054f7d862857bdfe172d712f40a7bd7/w2.txt
    body:
      encoding: ASCII-8BIT
      string: 'W2

'
    headers:
      Content-Type:
      - text/plain
      Accept-Encoding:
      - ''
      User-Agent:
      - aws-sdk-ruby2/2.4.4 ruby/2.5.1 x86_64-linux resources
      X-Amz-Acl:
      - public-read
      X-Amz-Server-Side-Encryption:
      - AES256
      Expect:
      - 100-continue
      Content-Md5:
      - J4kW4y4m6shuaq2gruYr4g==
      X-Amz-Date:
      - 20180921T171431Z
      Host:
      - revela-uploads.s3.amazonaws.com
      X-Amz-Content-Sha256:
      - b2dbaf376a088e720078d1c245336d66fe6d1be61e1efeb8d21f1747b5bdd358
      Authorization:
      - AWS4-HMAC-SHA256 Credential=<AWS_ACCESS_KEY_ID>/20180921/us-east-1/s3/aws4_request,
        SignedHeaders=content-md5;content-type;host;x-amz-acl;x-amz-content-sha256;x-amz-date;x-amz-server-side-encryption,
        Signature=0cbba1822a46fde864d6434b014e841caa06441e7e3476b996a722eab0b632cc
      Content-Length:
      - '3'
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - 5/06R6yL1CDMPO4H9j+2jhEfbPcMasyO08rBwF+sABx67rQR+OkXeWgB3R0od87svaIUeOsCdCg=
      X-Amz-Request-Id:
      - E6EBE159713458B6
      Date:
      - Fri, 21 Sep 2018 17:14:44 GMT
      X-Amz-Server-Side-Encryption:
      - AES256
      Etag:
      - '"278916e32e26eac86e6aada0aee62be2"'
      Content-Length:
      - '0'
      Server:
      - AmazonS3
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Fri, 21 Sep 2018 17:14:31 GMT
recorded_with: VCR 3.0.3
