---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 09 May 2025 18:12:07 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1905'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"771-EKGs9fniTY49lWViK8XklYDX47g"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6789","entityType":"Corporation","contact":{"fullName":{"first":"Bobette","last":"Wyman3"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"2069136239"},"jwtSubject":"gid://<POSTGRES_USER>/Morocco::Profile/21"},"tags":{},"authorizedUsers":[{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"3372299603"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"6515401191"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"831409<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>68"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8566621716"}},{"fullName":{"first":"Natalia","last":"Lin"},"email":"nlin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"4847072279"}},{"fullName":{"first":"Rhythm","last":"Agrawal"},"email":"ragrawal@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8086301887"}},{"fullName":{"first":"Sherman","last":"Nicolas4"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"4159043977"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"18863809"},{"type":"authorizedUserResource","id":"18863810"},{"type":"authorizedUserResource","id":"18863811"},{"type":"authorizedUserResource","id":"18863812"},{"type":"authorizedUserResource","id":"18863813"},{"type":"authorizedUserResource","id":"18863814"},{"type":"authorizedUserResource","id":"18863818"}]}}}}'
  recorded_at: Fri, 09 May 2025 18:12:07 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 09 May 2025 18:12:07 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1905'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"771-EKGs9fniTY49lWViK8XklYDX47g"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6789","entityType":"Corporation","contact":{"fullName":{"first":"Bobette","last":"Wyman3"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"2069136239"},"jwtSubject":"gid://<POSTGRES_USER>/Morocco::Profile/21"},"tags":{},"authorizedUsers":[{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"3372299603"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"6515401191"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"831409<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>68"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8566621716"}},{"fullName":{"first":"Natalia","last":"Lin"},"email":"nlin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"4847072279"}},{"fullName":{"first":"Rhythm","last":"Agrawal"},"email":"ragrawal@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8086301887"}},{"fullName":{"first":"Sherman","last":"Nicolas4"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"4159043977"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"18863809"},{"type":"authorizedUserResource","id":"18863810"},{"type":"authorizedUserResource","id":"18863811"},{"type":"authorizedUserResource","id":"18863812"},{"type":"authorizedUserResource","id":"18863813"},{"type":"authorizedUserResource","id":"18863814"},{"type":"authorizedUserResource","id":"18863818"}]}}}}'
  recorded_at: Fri, 09 May 2025 18:12:07 GMT
- request:
    method: patch
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","attributes":{"authorizedUsers":[{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"3372299603"},"jwtSubject":null},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"6515401191"},"jwtSubject":null},{"fullName":{"first":"Tom","last":"Qin"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"831409<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>68"},"jwtSubject":null},{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8566621716"},"jwtSubject":null},{"fullName":{"first":"Natalia","last":"Lin"},"email":"nlin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"4847072279"},"jwtSubject":null},{"fullName":{"first":"Rhythm","last":"Agrawal"},"email":"ragrawal@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8086301887"},"jwtSubject":null},{"fullName":{"first":"Sherman","last":"Nicolas4"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"4159043977"},"jwtSubject":null}],"contact":{"fullName":{"first":"Bobette","last":"Wyman3"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2069136239"},"jwtSubject":"gid://<POSTGRES_USER>/Morocco::Profile/21"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 09 May 2025 18:12:08 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1895'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"767-JODOf428kLyhWR6nH0Ac1RLYat0"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6789","entityType":"Corporation","contact":{"fullName":{"first":"Bobette","last":"Wyman3"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2069136239"},"jwtSubject":"gid://<POSTGRES_USER>/Morocco::Profile/21"},"tags":{},"authorizedUsers":[{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"3372299603"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"6515401191"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"831409<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>68"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8566621716"}},{"fullName":{"first":"Natalia","last":"Lin"},"email":"nlin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"4847072279"}},{"fullName":{"first":"Rhythm","last":"Agrawal"},"email":"ragrawal@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"8086301887"}},{"fullName":{"first":"Sherman","last":"Nicolas4"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"4159043977"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"18863809"},{"type":"authorizedUserResource","id":"18863810"},{"type":"authorizedUserResource","id":"18863811"},{"type":"authorizedUserResource","id":"18863812"},{"type":"authorizedUserResource","id":"18863813"},{"type":"authorizedUserResource","id":"18863814"},{"type":"authorizedUserResource","id":"18863818"}]}}}}'
  recorded_at: Fri, 09 May 2025 18:12:08 GMT
recorded_with: VCR 6.3.1
