---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
            <Username>PAYLEASE_USER_ID</Username>
            <Password>PAYLEASE_PASSWORD</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>TransactionRefund</TransactionAction>
              <TransactionId>29246599</TransactionId>
              <RefundAmount>97.38</RefundAmount>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.17.0
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 25 Nov 2019 13:24:58 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=eh4s0kp3g19nu0i3f0439vt6i6; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '526'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
           <Username>PAYLEASE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>TransactionRefund</TransactionAction>
            <TransactionId>29246599</TransactionId>
            <Code>15</Code>
            <Status>Pending</Status>
            <Message>This refund transaction is pending.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 25 Nov 2019 13:24:59 GMT
recorded_with: VCR 4.0.0
