---
http_interactions:
- request:
    method: post
    uri: "<INSPECTIFY_API_URL>/orders"
    body:
      encoding: UTF-8
      string: '{"property_address":"6001 Cass Ave, Detroit, MI, US","street_2":"Flr
        5","inspection_types":["home_inspection"],"team_external_id":"alever/1","callback_url":"http://alever.lvh.me:3001/inspectify/webhooks","order_stakeholders":[],"access_informations":[],"scheduling_instructions":"message"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - Bearer 123abc
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 09 Jan 2025 23:14:10 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1736464449&sid=************************************&s=T0g3dl%2FoK7obOvzrdb5xTwIBwF4n82HV2yJpGHhuO4E%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1736464449&sid=************************************&s=T0g3dl%2FoK7obOvzrdb5xTwIBwF4n82HV2yJpGHhuO4E%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - '0'
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Vary:
      - Accept, Origin
      Etag:
      - W/"cf02ae56f9682e26b9d7b66af6f78198"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - ''
      Set-Cookie:
      - _inspectify_session=pJV3bJvbR7lQx%2B%2B4qUwGBAuH98JBCSuYTCmeBZdpnIbKzx15W90Z1gdy1ltsM0QAQnCtYSyi9MVFkrJAxZBMHtCq8at94Y3Adljvm%2F%2Fj3rSGbh1PVCpC2YJumj4dBqRbPruwEqx%2Fw6jnOitmjKr9a6e0di2gzkUxwfNXUFIsrfTV4Qg3RAABBuxupg%3D%3D--kiVJ%2BHOQp8h6hjGq--DgwT7krvHcNRYmCMkpRYXg%3D%3D;
        path=/; secure; HttpOnly; SameSite=Lax
      X-Request-Id:
      - 17eccf4c-e0d2-492b-812b-11009f2de1f8
      X-Runtime:
      - '1.018218'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Via:
      - 1.1 vegur
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 8ff81f376a61eafd-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
      Server-Timing:
      - cfL4;desc="?proto=TCP&rtt=12590&min_rtt=11557&rtt_var=5072&sent=6&recv=7&lost=0&retrans=0&sent_bytes=2801&recv_bytes=1194&delivery_rate=349917&cwnd=252&unsent_bytes=0&cid=54a35b598a58afd6&ts=1160&x=0"
    body:
      encoding: ASCII-8BIT
      string: '{"id":"123","team_external_id":"alever/1","community_name":null,"property_address":"6001
        Cass Ave, Detroit, MI 48202 US","property_street_2":"Flr 5","edit_order_url":"https://app2-staging.inspectify.com/orders/zcQdozqjPYM7CDLBBZSsstYK/edit","reports_url":"https://app2-staging.inspectify.com/orders/zcQdozqjPYM7CDLBBZSsstYK/result","order_tags":["Pending
        Access","Pending Availability"],"floor_plan_name":null,"collect_availability_url":"https://app2-staging.inspectify.com/order_flows/zcQdozqjPYM7CDLBBZSsstYK/simple_instant_booking_availabilities","external_metadata":null,"inspections":[{"id":"261897","status":"requested","inspection_type":"home_inspection","external_id":null}]}'
  recorded_at: Thu, 09 Jan 2025 23:14:10 GMT
recorded_with: VCR 6.3.1
