---
http_interactions:
- request:
    method: post
    uri: "<INSPECTIFY_API_URL>/orders"
    body:
      encoding: UTF-8
      string: '{"property_address":"6001 Cass Ave, Detroit, MI, US","street_2":"Flr
        5","inspection_types":["home_inspection"],"team_external_id":"alever/1","callback_url":"http://alever.lvh.me:3001/inspectify/webhooks","order_stakeholders":[],"access_informations":[],"scheduling_instructions":"message"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - Bearer 123abc
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 09 Jan 2025 23:14:11 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>0&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=6cYXPX4r7SP%2FMlHr3fVA8DivrH6jccxpRwP%2FpLe4NB4%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>0&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=6cYXPX4r7SP%2FMlHr3fVA8DivrH6jccxpRwP%2FpLe4NB4%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - '0'
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Vary:
      - Accept, Origin
      Etag:
      - W/"37c1c5d8f6d08af50384a0084d805515"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - ''
      Set-Cookie:
      - _inspectify_session=5c%2Fuu8HbLLdUqtkLy11vzpD3vvk9Sdi5CKCu9Vo5zjGTLQ%2FcFbMK%2F2USdMXjUupvNPHQN3JAF683QHq96ts1y2PXOKh7QzQrebKLl8Fi7PbEEKvbGRN9J4ku%2BEPDzZYHcj%2FvU9BtEOYdNRmdQaWcpuy<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>srgSG%2FTuAPsqICEZ6Vx78H8z73GKhWxQg%3D%3D--5yRqpGpxcivJCblq--Hq9xQ5%2FMSx8FDI7FrgXnEA%3D%3D;
        path=/; secure; HttpOnly; SameSite=Lax
      X-Request-Id:
      - 804be546-49cc-<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>4d-9ff1-ea8db6f606f0
      X-Runtime:
      - '0.780957'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Via:
      - 1.1 vegur
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 8ff81f3fb98de994-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
      Server-Timing:
      - cfL4;desc="?proto=TCP&rtt=12430&min_rtt=11509&rtt_var=4974&sent=5&recv=7&lost=0&retrans=0&sent_bytes=2801&recv_bytes=1194&delivery_rate=351377&cwnd=252&unsent_bytes=0&cid=2d413cd108c75fdb&ts=911&x=0"
    body:
      encoding: ASCII-8BIT
      string: '{"id":"123","team_external_id":"alever/1","community_name":null,"property_address":"6001
        Cass Ave, Detroit, MI 48202 US","property_street_2":"Flr 5","edit_order_url":"https://app2-staging.inspectify.com/orders/WUKEucx2KA5pn9DmGBnXYpXn/edit","reports_url":"https://app2-staging.inspectify.com/orders/WUKEucx2KA5pn9DmGBnXYpXn/result","order_tags":["Pending
        Access","Pending Availability"],"floor_plan_name":null,"collect_availability_url":"https://app2-staging.inspectify.com/order_flows/WUKEucx2KA5pn9DmGBnXYpXn/simple_instant_booking_availabilities","external_metadata":null,"inspections":[{"id":"261898","status":"requested","inspection_type":"home_inspection","external_id":null}]}'
  recorded_at: Thu, 09 Jan 2025 23:14:11 GMT
- request:
    method: post
    uri: "<INSPECTIFY_API_URL>/orders"
    body:
      encoding: UTF-8
      string: '{"property_address":"6001 Cass Ave, Detroit, MI, US","street_2":"Flr
        5","inspection_types":["home_inspection"],"team_external_id":"alever/1","callback_url":"http://alever.lvh.me:3001/inspectify/webhooks","order_stakeholders":[],"access_informations":[],"scheduling_instructions":"message"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - Bearer 123abc
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 09 Jan 2025 23:14:11 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>1&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=%2F7f%2F299RjMOZ%2BFHHWJcrPTepz7K1Dj9RgRVu9eVXTWM%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>1&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=%2F7f%2F299RjMOZ%2BFHHWJcrPTepz7K1Dj9RgRVu9eVXTWM%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - '0'
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Vary:
      - Accept, Origin
      Etag:
      - W/"37c1c5d8f6d08af50384a0084d805515"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - ''
      Set-Cookie:
      - _inspectify_session=DZpB7MvHVG4tiefCEvIaQP9CjZu7xmBlj19Wrh4egVfcNnIYWsG0WuzjXrTLWPcaaeOFtqJPVcpId%2BZRB5iNBNZUoj48k8cLGH66hESQFPE6RHONJtfjugCsMf40QXKn4Y9olL23xYo1NcucLdNR4BVURJaG3RUTXe1PE9%2BVM9wSDCW22Xpg3xYy2Q%3D%3D--AWLzxZINz86XeSRx--O2UuhfKYXTSToOH2qbhDtg%3D%3D;
        path=/; secure; HttpOnly; SameSite=Lax
      X-Request-Id:
      - 93f3b046-dd27-4933-8777-4c05d984438e
      X-Runtime:
      - '0.202708'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Via:
      - 1.1 vegur
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 8ff81f<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>cd0361d2-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
      Server-Timing:
      - cfL4;desc="?proto=TCP&rtt=10756&min_rtt=10704&rtt_var=4119&sent=6&recv=7&lost=0&retrans=0&sent_bytes=2800&recv_bytes=1194&delivery_rate=363472&cwnd=252&unsent_bytes=0&cid=a3b6a47721066550&ts=318&x=0"
    body:
      encoding: ASCII-8BIT
      string: '{"id":"123","team_external_id":"alever/1","community_name":null,"property_address":"6001
        Cass Ave, Detroit, MI 48202 US","property_street_2":"Flr 5","edit_order_url":"https://app2-staging.inspectify.com/orders/WUKEucx2KA5pn9DmGBnXYpXn/edit","reports_url":"https://app2-staging.inspectify.com/orders/WUKEucx2KA5pn9DmGBnXYpXn/result","order_tags":["Pending
        Access","Pending Availability"],"floor_plan_name":null,"collect_availability_url":"https://app2-staging.inspectify.com/order_flows/WUKEucx2KA5pn9DmGBnXYpXn/simple_instant_booking_availabilities","external_metadata":null,"inspections":[{"id":"261898","status":"requested","inspection_type":"home_inspection","external_id":null}]}'
  recorded_at: Thu, 09 Jan 2025 23:14:11 GMT
recorded_with: VCR 6.3.1
