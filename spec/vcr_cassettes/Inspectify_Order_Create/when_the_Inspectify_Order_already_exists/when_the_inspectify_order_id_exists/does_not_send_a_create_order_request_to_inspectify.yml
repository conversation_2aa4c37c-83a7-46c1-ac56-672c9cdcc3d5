---
http_interactions:
- request:
    method: post
    uri: "<INSPECTIFY_API_URL>/orders"
    body:
      encoding: UTF-8
      string: '{"property_address":"6001 Cass Ave, Detroit, MI, US","street_2":"Flr
        5","inspection_types":["home_inspection"],"team_external_id":"alever/1","callback_url":"http://alever.lvh.me:3001/inspectify/webhooks","order_stakeholders":[],"access_informations":[],"scheduling_instructions":"message"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - Bearer 123abc
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 09 Jan 2025 23:14:12 GMT
      Content-Type:
      - application/json; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>1&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=%2F7f%2F299RjMOZ%2BFHHWJcrPTepz7K1Dj9RgRVu9eVXTWM%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1736464<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>1&sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&s=%2F7f%2F299RjMOZ%2BFHHWJcrPTepz7K1Dj9RgRVu9eVXTWM%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - '0'
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Vary:
      - Accept, Origin
      Etag:
      - W/"78ef9bbdfd20a3c19f09cee03d1ec217"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - ''
      Set-Cookie:
      - _inspectify_session=w0nzwXsWmPVauk7yY1jztJYe6yp9R%2Bdpf2%2F%2FnCWUalNvpsU93B14iWAjnnSGCIX8mjXPK1xDJlliFLslH8D%2FAaiVGfjdC2sObJjwGQvMNdT2NUFOzXRpCP7LHREOz4kNHMdJ2llIx%2FGy1lW4kB6yOMFLgWz6wOHAjewWDFQ0A7qitB93NWJkQOIwrg%3D%3D--mNZ9kt1gtM1jzQTk--p116qk%2F%2BiH%2B20%2BZN%2FdGthQ%3D%3D;
        path=/; secure; HttpOnly; SameSite=Lax
      X-Request-Id:
      - 478e73aa-32a6-4286-9adb-44b299948ae5
      X-Runtime:
      - '0.766058'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Via:
      - 1.1 vegur
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 8ff81f489d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>eaf0-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
      Server-Timing:
      - cfL4;desc="?proto=TCP&rtt=119<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>&min_rtt=11460&rtt_var=4644&sent=6&recv=7&lost=0&retrans=0&sent_bytes=2802&recv_bytes=1194&delivery_rate=352879&cwnd=252&unsent_bytes=0&cid=382910648d19c8c7&ts=867&x=0"
    body:
      encoding: ASCII-8BIT
      string: '{"id":"123","team_external_id":"alever/1","community_name":null,"property_address":"6001
        Cass Ave, Detroit, MI 48202 US","property_street_2":"Flr 5","edit_order_url":"https://app2-staging.inspectify.com/orders/eGdz5G1EwVFcav7wMLECCdUL/edit","reports_url":"https://app2-staging.inspectify.com/orders/eGdz5G1EwVFcav7wMLECCdUL/result","order_tags":["Pending
        Access","Pending Availability"],"floor_plan_name":null,"collect_availability_url":"https://app2-staging.inspectify.com/order_flows/eGdz5G1EwVFcav7wMLECCdUL/simple_instant_booking_availabilities","external_metadata":null,"inspections":[{"id":"261899","status":"requested","inspection_type":"home_inspection","external_id":null}]}'
  recorded_at: Thu, 09 Jan 2025 23:14:12 GMT
recorded_with: VCR 6.3.1
