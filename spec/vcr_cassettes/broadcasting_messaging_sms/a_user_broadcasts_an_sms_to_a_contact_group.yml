---
http_interactions:
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages.json
    body:
      encoding: UTF-8
      string: Body=This+message+is+sent+by+<PERSON><PERSON>+on+behalf+of+Alever.+Reply+STOP+to+opt+out.&From=%2B***********&To=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/7.1.1 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 17 Jun 2024 20:32:23 GMT
      Content-Type:
      - application/json;charset=utf-8
      Content-Length:
      - '838'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ5791996284c088c82bd2b87d8fdd3e90
      Twilio-Request-Duration:
      - '0.057'
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=31536000
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key, X-Pre-Auth-Context, X-Target-Region
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, PUT, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag, Twilio-Request-Id
      Access-Control-Allow-Credentials:
      - 'true'
      X-Shenanigans:
      - none
      X-Powered-By:
      - AT-5000
      X-Home-Region:
      - us1
    body:
      encoding: UTF-8
      string: '{"account_sid": "<TWILIO_SID>", "api_version": "2010-04-01", "body":
        "This message is sent by Revela on behalf of Alever. Reply STOP to opt out.",
        "date_created": "Mon, 17 Jun 2024 20:32:23 +0000", "date_sent": null, "date_updated":
        "Mon, 17 Jun 2024 20:32:23 +0000", "direction": "outbound-api", "error_code":
        null, "error_message": null, "from": "+***********", "messaging_service_sid":
        null, "num_media": "0", "num_segments": "1", "price": null, "price_unit":
        "USD", "sid": "SM5791996284c088c82bd2b87d8fdd3e90", "status": "queued", "subresource_uris":
        {"media": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM5791996284c088c82bd2b87d8fdd3e90/Media.json"},
        "to": "+***********", "uri": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM5791996284c088c82bd2b87d8fdd3e90.json"}'
  recorded_at: Mon, 17 Jun 2024 20:32:23 GMT
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages.json
    body:
      encoding: UTF-8
      string: Body=The+Body&From=%2B***********&To=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/7.1.1 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 17 Jun 2024 20:32:23 GMT
      Content-Type:
      - application/json;charset=utf-8
      Content-Length:
      - '772'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQe5d32cd36d812c44577546961b2e3a99
      Twilio-Request-Duration:
      - '0.081'
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=31536000
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key, X-Pre-Auth-Context, X-Target-Region
      Access-Control-Allow-Methods:
      - GET, POST, PATCH, PUT, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag, Twilio-Request-Id
      Access-Control-Allow-Credentials:
      - 'true'
      X-Shenanigans:
      - none
      X-Powered-By:
      - AT-5000
      X-Home-Region:
      - us1
    body:
      encoding: UTF-8
      string: '{"account_sid": "<TWILIO_SID>", "api_version": "2010-04-01", "body":
        "The Body", "date_created": "Mon, 17 Jun 2024 20:32:23 +0000", "date_sent":
        null, "date_updated": "Mon, 17 Jun 2024 20:32:23 +0000", "direction": "outbound-api",
        "error_code": null, "error_message": null, "from": "+***********", "messaging_service_sid":
        null, "num_media": "0", "num_segments": "1", "price": null, "price_unit":
        "USD", "sid": "SMe5d32cd36d812c44577546961b2e3a99", "status": "queued", "subresource_uris":
        {"media": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SMe5d32cd36d812c44577546961b2e3a99/Media.json"},
        "to": "+***********", "uri": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SMe5d32cd36d812c44577546961b2e3a99.json"}'
  recorded_at: Mon, 17 Jun 2024 20:32:23 GMT
recorded_with: VCR 6.2.0
