---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
            <Username>PAYLEASE_USER_ID</Username>
            <Password>PAYLEASE_PASSWORD</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>TransactionDetail</TransactionAction>
              <TransactionId>29246599</TransactionId>
              <AlwaysShowCurrency>Y</AlwaysShowCurrency>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.17.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 02 Nov 2020 20:43:23 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Set-Cookie:
      - PHPSESSID=ft6vjhjmdmsp9u3mcl3f13nrn0; path=/; secure; HttpOnly; SameSite=None
      - PHPSESSID_LEGACY=ft6vjhjmdmsp9u3mcl3f13nrn0; path=/; secure; HttpOnly
      Content-Length:
      - '767'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
           <Username>PAYLEASE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>TransactionDetail</TransactionAction>
            <TransactionId>29246599</TransactionId>
            <PaymentReferenceId>1</PaymentReferenceId>
            <PayeeId>PAYLEASE_PAYEE_ID</PayeeId>
            <FeeAmount>0.00</FeeAmount>
            <TotalAmount>12.34</TotalAmount>
            <TransactionDate>01/01/2020</TransactionDate>
            <PayerReferenceId>1</PayerReferenceId>
            <PayerSecondaryReferenceId />
            <Code>5</Code>
            <Status>Approved</Status>
            <Message>This transaction has been cancelled.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

  recorded_at: Mon, 02 Nov 2020 20:43:24 GMT
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
            <Username>PAYLEASE_USER_ID</Username>
            <Password>PAYLEASE_PASSWORD</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>TransactionDetail</TransactionAction>
              <TransactionId>29246599</TransactionId>
              <AlwaysShowCurrency>Y</AlwaysShowCurrency>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.17.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 02 Nov 2020 20:43:23 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Set-Cookie:
      - PHPSESSID=ft6vjhjmdmsp9u3mcl3f13nrn0; path=/; secure; HttpOnly; SameSite=None
      - PHPSESSID_LEGACY=ft6vjhjmdmsp9u3mcl3f13nrn0; path=/; secure; HttpOnly
      Content-Length:
      - '767'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
           <Username>PAYLEASE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>TransactionDetail</TransactionAction>
            <TransactionId>29246599</TransactionId>
            <PaymentReferenceId>1</PaymentReferenceId>
            <PayeeId>PAYLEASE_PAYEE_ID</PayeeId>
            <FeeAmount>0.00</FeeAmount>
            <TotalAmount>12.34</TotalAmount>
            <TransactionDate>01/01/2020</TransactionDate>
            <PayerReferenceId>1</PayerReferenceId>
            <PayerSecondaryReferenceId />
            <Code>5</Code>
            <Status>Approved</Status>
            <Message>This transaction has been cancelled.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

  recorded_at: Mon, 02 Nov 2020 20:43:24 GMT
recorded_with: VCR 6.0.0
