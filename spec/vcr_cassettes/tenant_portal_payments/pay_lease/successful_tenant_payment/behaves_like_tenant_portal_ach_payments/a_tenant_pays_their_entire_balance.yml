---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
            <Username>|PAYLEASE_USER_ID|</Username>
            <Password>|PAYLEASE_PASSWORD|</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>AccountPayment</TransactionAction>
              <PaymentReferenceId>2</PaymentReferenceId>
              <PaymentTraceId>30 random chars</PaymentTraceId>
              <PayeeId>|PAYLEASE_PAYEE_ID|</PayeeId>
              <PayerReferenceId>4</PayerReferenceId>
              <GatewayPayerId>1608416</GatewayPayerId>
              <CurrencyCode>USD</CurrencyCode>
              <TotalAmount>323.9</TotalAmount>
              <IncurFee>Yes</IncurFee>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 29 Jul 2019 15:34:12 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=********; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=j17kriqpgt8f91fj8a8ahtodfo; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '716'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
           <Username>|PAYLEASE_USER_ID|</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>AccountPayment</TransactionAction>
            <PaymentReferenceId>2</PaymentReferenceId>
            <PaymentTraceId>30 random chars</PaymentTraceId>
            <CurrencyCode>USD</CurrencyCode>
            <TransactionId>********</TransactionId>
            <Code>2</Code>
            <Status>Approved</Status>
            <Message>Request Received.</Message>
            <UnitAmount>323.90</UnitAmount>
            <FeeAmount>0.00</FeeAmount>
            <TotalAmount>323.90</TotalAmount>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 29 Jul 2019 15:34:14 GMT
recorded_with: VCR 4.0.0
