---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/authorized-users"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"addAuthorizedUsers","attributes":{"authorizedUsers":[{"fullName":{"first":"<PERSON>
        ","last":" <PERSON>ip<PERSON> "},"email":"elle<PERSON>@lv42.com","phone":{"countryCode":"1","number":"2813308004"}}]}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 29 Jan 2024 01:13:50 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1210'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"4ba-4h7+28tA8o40WsCgmOG6Q1aXekA"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},{"fullName":{"first":"Tom","last":"Qin"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7348812543"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2345678901"}},{"fullName":{"first":"Erlich","last":"Backman"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"1234567890"}},{"fullName":{"first":"Ellen
        ","last":" Ripley "},"email":"<EMAIL>","phone":{"countryCode":"1","number":"2813308004"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Mon, 29 Jan 2024 01:13:50 GMT
recorded_with: VCR 6.2.0
