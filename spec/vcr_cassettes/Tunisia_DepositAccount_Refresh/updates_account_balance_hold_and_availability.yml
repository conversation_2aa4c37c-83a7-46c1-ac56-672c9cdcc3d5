---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/accounts/2413772"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 22 Jan 2024 21:07:24 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '520'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"208-PKuoX5HDlcuQTHGAG5E3zPLwtHQ"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"depositAccount","id":"2413772","attributes":{"name":"<PERSON>","createdAt":"2024-01-03T18:09:57.326Z","routingNumber":"*********","accountNumber":"**********","depositProduct":"checking","balance":1,"hold":2,"available":3,"tags":{"entity_id":"10","subdomain":"alever"},"currency":"USD","status":"Open","updatedAt":"2024-01-03T18:09:57.326Z"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}},"org":{"data":{"type":"org","id":"4381"}},"bank":{"data":{"type":"bank","id":"1"}}}}}'
  recorded_at: Mon, 22 Jan 2024 21:07:24 GMT
recorded_with: VCR 6.2.0
