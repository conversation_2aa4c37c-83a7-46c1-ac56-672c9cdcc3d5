---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/statements?filter%5BaccountId%5D=2385052&filter%5Bperiod%5D=2023-12&page%5Blimit%5D=25"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 23 Jan 2024 22:43:45 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '217'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"d9-ow+KmVuV7PuP7tt6M5rKjIsjDQ8"
    body:
      encoding: UTF-8
      string: '{"data":[{"type":"accountStatementDTO","id":"********","attributes":{"period":"2023-12"},"relationships":{"customer":{"data":{"type":"customer","id":"<UNIT_API_CUSTOMER_ID>"}},"account":{"data":{"type":"account","id":"2385052"}}}}]}'
  recorded_at: Tue, 23 Jan 2024 22:43:45 GMT
recorded_with: VCR 6.2.0
