---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/check-payments/111/return"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"checkPaymentReturn","attributes":{"reason":"StopPayment"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 01 May 2025 19:39:56 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '703'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"2bf-S7fGTyww0fC3j0O3pKbtYb63pcY"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"checkPayment","id":"111","attributes":{"createdAt":"2025-05-01T19:38:05.953Z","updatedAt":"2025-05-01T19:39:55.992Z","amount":2600000,"description":"Check
        Payment | 123456","status":"MarkedForReturn","returnReason":"Stop
        Payment","returnStatusReason":"StopPayment","additionalVerificationStatus":"NotRequired","checkNumber":"123456","onUsAuxiliary":"123456","onUs":"**********/","counterpartyRoutingNumber":"*********","returnCutoffTime":"2025-05-02T15:50:00.000Z","originated":false,"isAccountClosureCheck":false},"relationships":{"account":{"data":{"type":"account","id":"5302494"}},"customer":{"data":{"type":"customer","id":"2014236"}},"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 01 May 2025 19:39:56 GMT
recorded_with: VCR 6.3.1
