---
http_interactions:
- request:
    method: put
    uri: https://revela-uploads.s3.amazonaws.com/test_uploads/alever/property/1/08450b0321b4312d10cb4d7ce6b945f636e798ed/alta_survey.txt
    body:
      encoding: ASCII-8BIT
      string: 'Survey Content

'
    headers:
      Content-Type:
      - text/plain
      Accept-Encoding:
      - ''
      User-Agent:
      - aws-sdk-ruby2/2.4.4 ruby/2.4.1 x86_64-linux resources
      X-Amz-Acl:
      - public-read
      X-Amz-Server-Side-Encryption:
      - AES256
      Expect:
      - 100-continue
      Content-Md5:
      - Ybg7aQq7sQsO/CHL6uPDBg==
      X-Amz-Date:
      - 20180426T162055Z
      Host:
      - revela-uploads.s3.amazonaws.com
      X-Amz-Content-Sha256:
      - 4f12f854c7abe2bf6db8e4f106961048d973b7a9338d6218a1b67380ebb4fda1
      Authorization:
      - AWS4-HMAC-SHA256 Credential=<AWS_ACCESS_KEY_ID>/20180426/us-east-1/s3/aws4_request,
        SignedHeaders=content-md5;content-type;host;x-amz-acl;x-amz-content-sha256;x-amz-date;x-amz-server-side-encryption,
        Signature=828bd0893a959cf14d92e30eee9b9d107298bde01b9a2309f47405cdddd3aa6e
      Content-Length:
      - '15'
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - ON2LmBZ1PNMoGEGXtQcF0tg0Zm4AZlwIULSBVYxf0fTOUJcEQ1R3CBI5sresxUC/6Z6DVgKucC0=
      X-Amz-Request-Id:
      - BE720745F95DD9DD
      Date:
      - Thu, 26 Apr 2018 16:20:58 GMT
      X-Amz-Server-Side-Encryption:
      - AES256
      Etag:
      - '"61b83b690abbb10b0efc21cbeae3c306"'
      Content-Length:
      - '0'
      Server:
      - AmazonS3
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Thu, 26 Apr 2018 16:20:55 GMT
recorded_with: VCR 3.0.3
