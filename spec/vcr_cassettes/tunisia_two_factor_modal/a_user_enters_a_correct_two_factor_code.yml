---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jan 2024 17:58:48 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '338'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"152-K08ch4E33x4cNXcvf24yCAIUis8"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"q-S24siCoAzgtfYb_lknfG_wTJ_OHBp7mPSmJ4NVd16tP3iYoV70rdcapvmPCXgTc_ik5agfXmzdiocuYjOGkC2-aCcFp7QJLLIX4Eudx5JyBEXL0rTQp1b3ZoQCA25KzBJGK1nzSgMNDatjkSSsorC1rAHgeYbDy48Ci667ULtJas2pNPiojC4HClLG-32yCOR675KFqndC79ypfVXBRKBeLqs3c7OztrKrl9g5QHkc3Lo_bqEgsq5b-ovXGVo"}}}'
  recorded_at: Wed, 10 Jan 2024 17:58:48 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write","verificationToken":"q-S24siCoAzgtfYb_lknfG_wTJ_OHBp7mPSmJ4NVd16tP3iYoV70rdcapvmPCXgTc_ik5agfXmzdiocuYjOGkC2-aCcFp7QJLLIX4Eudx5JyBEXL0rTQp1b3ZoQCA25KzBJGK1nzSgMNDatjkSSsorC1rAHgeYbDy48Ci667ULtJas2pNPiojC4HClLG-32yCOR675KFqndC79ypfVXBRKBeLqs3c7OztrKrl9g5QHkc3Lo_bqEgsq5b-ovXGVo","verificationCode":"<NELCO_CLIENT_ID>1"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jan 2024 17:58:49 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '448'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1c0-GP+TvwAek28/xX5mQ4L9XDG5eHM"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvam9obkByZXZlbGEuY28iLCJleHAiOiIyMDI0LTAxLTExVDE3OjU4OjQ5LjQ1MloiLCJqdGkiOm51bGwsIm9yZ0lkIjoiNDM4MSIsInNjb3BlIjoiYWNjb3VudHMtd3JpdGUiLCJjdXN0b21lcklkIjoiMTQ5Nzc1NiIsInVzZXJUeXBlIjoiY3VzdG9tZXIifR1-2T6Gs7_ldWvWEl0vikw465pQaIDOKf9dJ4PUKAOBV_juluiis-wJRHcqUuf74a5PX4i7A-1MmupSAifvrQ0","expiresIn":86400}}}'
  recorded_at: Wed, 10 Jan 2024 17:58:49 GMT
recorded_with: VCR 6.2.0
