---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jan 2024 17:58:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '338'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"152-yDvz2ArV84CFkGOgSW+tU6IEtio"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"fpg4SJJPU0XUMTu85yHsc6e7pUnSNVu7Slg8Xy-AzjiOZkk0IEm0udV8vM6DA5mMDeNjtOJ-f2JmqWhyPKplongluBqXFi2C3iqFMzaAwqXH7mg5jSULNaKI3Nj7oHPX-os50PdIroUWHq7rwu06pDx9zlzpviPz7D8vFO3P3WfK3YzvX9bGrjuzINA4aU1JBgmafn-aiSNL03av8AChmnEueAHvnKOhsme8BsmpEHMbehmvHo0vNLvItaeqWd8"}}}'
  recorded_at: Wed, 10 Jan 2024 17:58:51 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT

- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jan 2024 17:58:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '338'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"152-YM2zK47N20Tpgu33xq0WfxfqRik"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"r8PFL-wKU641XPVpSu1e0wUnoR2MpJeLO1Bli2cMtQCi8pthvZDr20s5rYa-xjzyyz-ZN6gJaMs8dNKaVo3zmDU4nPuBW9bU2BsLlcAD7FTjhILD-XKz9cc79fAIlPv8ctHHT0vkXbyoiLNF2DDHrK9lWBzObPk_1L1N5ZH0bKEnq06XUD87f59VJXyTxTTWdzCAkSErZq4EwXIa0LYS0Hbhq-SW_hH9dYXBSRlCftu9vxW1hSrbVQ9yzheZ_M0"}}}'
  recorded_at: Wed, 10 Jan 2024 17:58:51 GMT
recorded_with: VCR 6.2.0
