---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/transactions
    body:
      encoding: UTF-8
      string: '{"transaction":{"account_vault_id":"11e84cb9b010c52aae905968","contact_id":"11e84cb95f943bf48d9b0a63","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","action":"debit","transaction_amount":3.5,"subtotal_amount":3.5,"surcharge_amount":0.0}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.4
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Sat, 26 Apr 2025 14:36:10 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Content-Length:
      - '2263'
      Connection:
      - keep-alive
      Server:
      - Apache/2.4.62 (Debian)
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=63Dq19NBUUYhxM1eSGS68gYU4aOqoecC9Vz6wkOsYFfzS~SZL1BDpEw76vlHl2M337EAXiYaHMVeubI8exwhEGdWYv7qXp0fl86~~2DsTOO9PVDWcEX2V3FSkq0hPXnd;
        Max-Age=31536000; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Vary:
      - Accept,Accept-Encoding
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/transactions/11f022abcbb09d1ca921729c
      Cache-Control:
      - no-cache
    body:
      encoding: UTF-8
      string: '{"transaction":{"id":"11f022abcbb09d1ca921729c","payment_method":"ach","account_vault_id":"11e84cb9b010c52aae905968","recurring_id":null,"first_six":null,"last_four":"7090","account_holder_name":"Monte
        Paucek","transaction_amount":"3.50","description":null,"transaction_code":null,"avs":null,"batch":null,"order_num":"************","verbiage":null,"transaction_settlement_status":null,"effective_date":"2025-04-26T14:36:10+0000","routing":"*********","return_date":null,"created_ts":17<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678170,"modified_ts":17<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678170,"transaction_api_id":null,"terms_agree":null,"notification_email_address":null,"notification_email_sent":true,"notification_phone":null,"response_message":"","auth_amount":"3.50","auth_code":null,"status_id":131,"type_id":50,"location_id":"11e816510e9c03a6bcbc685e","reason_code_id":1000,"contact_id":"11e84cb95f943bf48d9b0a63","billing_zip":null,"billing_street":null,"product_transaction_id":"11e816510ee9ec4285c8448a","tax":"0.00","customer_ip":"***************","customer_id":null,"po_number":null,"avs_enhanced":null,"cvv_response":null,"cavv_result":null,"billing_phone":null,"billing_city":null,"billing_state":null,"billing_country":null,"clerk_number":null,"tip_amount":"0.00","created_user_id":"11e816510eb4209487387f58","modified_user_id":"11e816510eb4209487387f58","ach_identifier":null,"check_number":null,"recurring_flag":"no","installment_counter":null,"installment_total":null,"settle_date":null,"charge_back_date":null,"void_date":null,"account_type":"checking","is_recurring":<SCHEDULED_JOBS>,"is_accountvault":true,"transaction_c1":null,"transaction_c2":null,"transaction_c3":null,"additional_amounts":[],"terminal_serial_number":null,"entry_mode_id":"K","terminal_id":null,"quick_invoice_id":null,"ach_sec_code":"PPD","custom_data":null,"ebt_type":null,"voucher_number":null,"hosted_payment_page_id":null,"trx_source_id":12,"transaction_batch_id":null,"currency_code":840,"par":null,"stan":null,"currency":"USD","secondary_amount":"0.00","card_bin":null,"wallet_id":null,"paylink_id":null,"checkin_date":null,"checkout_date":null,"room_num":null,"room_rate":"0.00","advance_deposit":<SCHEDULED_JOBS>,"no_show":<SCHEDULED_JOBS>,"emv_receipt_data":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/transactions/11f022abcbb09d1ca921729c"}}}}'
  recorded_at: Sat, 26 Apr 2025 14:36:10 GMT
recorded_with: VCR 6.3.1
