---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>FAKE_GTWY_ID</GatewayId>
            <Username>FAKE_USER_ID</Username>
            <Password>FAKE_USER_API_KEY</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>1</PayerReferenceId>
              <PayerFirstName>Lorina</PayerFirstName>
              <PayerLastName>O'Kon2</PayerLastName>
              <CreditCardType>Visa</CreditCardType>
              <CreditCardNumber>****************</CreditCardNumber>
              <CreditCardExpMonth>09</CreditCardExpMonth>
              <CreditCardExpYear>19</CreditCardExpYear>
              <CreditCardCcv2>123</CreditCardCcv2>
              <BillingFirstName>Lorina</BillingFirstName>
              <BillingLastName>O'Kon2</BillingLastName>
              <BillingStreetAddress>7131 Dorris Knolls, 8405 Bogisich Station Suite 694</BillingStreetAddress>
              <BillingCity>East Tisafort</BillingCity>
              <BillingState>MN</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>84425</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 22 Jul 2019 22:54:56 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=nbhchc7njsh7atdn1r7mvgkeni; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '559'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>FAKE_GTWY_ID</GatewayId>
           <Username>FAKE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
            <PayerReferenceId>1</PayerReferenceId>
            <GatewayPayerId>1608450</GatewayPayerId>
            <Code>10</Code>
            <Status>Approved</Status>
            <Message>Payer Account has been created successfully.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 22 Jul 2019 22:54:58 GMT
recorded_with: VCR 4.0.0
