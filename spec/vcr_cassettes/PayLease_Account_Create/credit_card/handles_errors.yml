---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>FAKE_GTWY_ID</GatewayId>
            <Username>FAKE_USER_ID</Username>
            <Password>FAKE_USER_API_KEY</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>2</PayerReferenceId>
              <PayerFirstName>Stacey</PayerFirstName>
              <PayerLastName>Deckow6</PayerLastName>
              <CreditCardType>Visa</CreditCardType>
              <CreditCardNumber>****************</CreditCardNumber>
              <CreditCardExpMonth>09</CreditCardExpMonth>
              <CreditCardExpYear>19</CreditCardExpYear>
              <CreditCardCcv2>123</CreditCardCcv2>
              <BillingFirstName>Stacey</BillingFirstName>
              <BillingLastName>Deckow6</BillingLastName>
              <BillingStreetAddress>3369 Mohr Manor, 3776 Mercedez Crossroad Suite 172</BillingStreetAddress>
              <BillingCity>Schummmouth</BillingCity>
              <BillingState>WI</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>25548</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 22 Jul 2019 23:11:03 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=41pkv9mgn4n0j2g81cjkdrbbbl; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '831'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>FAKE_GTWY_ID</GatewayId>
           <Username>FAKE_USER_ID</Username>
          </Credentials>
          <Mode />
          <Errors>
           <Error>
            <Code>129</Code>
            <Status>Error</Status>
            <Message>&lt;GatewayId&gt; value is in invalid format. It can only contain digits.</Message>
           </Error>
           <Error>
            <Code>146</Code>
            <Status>Error</Status>
            <Message>GatewayId not found.</Message>
           </Error>
           <Error>
            <Code>155</Code>
            <Status>Error</Status>
            <Message>User failed to be authenticated.  GatewayId and username don't match.</Message>
           </Error>
           <Error>
            <Code>156</Code>
            <Status>Error</Status>
            <Message>User failed to be authenticated.  Username and password don't match.</Message>
           </Error>
          </Errors>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 22 Jul 2019 23:11:04 GMT
recorded_with: VCR 4.0.0
