---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>FAKE_GTWY_ID</GatewayId>
            <Username>FAKE_USER_ID</Username>
            <Password>FAKE_USER_API_KEY</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>1</PayerReferenceId>
              <PayerFirstName>Amalia</PayerFirstName>
              <PayerLastName>Rutherford2</PayerLastName>
              <CreditCardType>Visa</CreditCardType>
              <CreditCardNumber>*************</CreditCardNumber>
              <CreditCardExpMonth>09</CreditCardExpMonth>
              <CreditCardExpYear>19</CreditCardExpYear>
              <CreditCardCcv2>22</CreditCardCcv2>
              <BillingFirstName>Amalia</BillingFirstName>
              <BillingLastName>Rutherford2</BillingLastName>
              <BillingStreetAddress>9744 Zieme Curve, 6975 Pat Turnpike Suite 776</BillingStreetAddress>
              <BillingCity>Hegmannview</BillingCity>
              <BillingState>ID</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>96166</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 22 Jul 2019 23:30:14 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=gdp237kr4hr97a51en4kef9b4t; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '556'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>FAKE_GTWY_ID</GatewayId>
           <Username>FAKE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
            <PayerReferenceId>1</PayerReferenceId>
            <Code>116</Code>
            <Status>Error</Status>
            <Message>&lt;CreditCardNumber&gt; value exceeds maximum length, 15 digits for Amex; 16 otherwise.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 22 Jul 2019 23:30:15 GMT
recorded_with: VCR 4.0.0
