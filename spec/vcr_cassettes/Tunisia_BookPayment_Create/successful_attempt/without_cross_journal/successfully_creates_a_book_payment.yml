---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":100,"description":"Transfering
        money"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2471787"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"2385052"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 01 Feb 2024 22:40:52 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '603'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"25b-bpjE93wfCuJ6USw/qLFUaohjUqA"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","id":"3595446","attributes":{"createdAt":"2024-02-01T22:40:52.676Z","amount":100,"direction":"Credit","description":"Transfering
        money","status":"Sent"},"relationships":{"account":{"data":{"type":"account","id":"2471787"}},"customer":{"data":{"type":"customer","id":"1584794"}},"customers":{"data":[{"type":"customer","id":"1584794"}]},"counterpartyAccount":{"data":{"type":"account","id":"2385052"}},"counterpartyCustomer":{"data":{"type":"customer","id":"1497757"}},"transaction":{"data":{"type":"transaction","id":"6465134"}},"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 01 Feb 2024 22:40:52 GMT
recorded_with: VCR 6.2.0
