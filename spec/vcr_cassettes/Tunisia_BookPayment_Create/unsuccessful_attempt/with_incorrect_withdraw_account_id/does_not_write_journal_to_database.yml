---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":100,"description":"Transfering
        money"},"relationships":{"account":{"data":{"type":"depositAccount","id":"********"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"2385052"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Fri, 02 Feb 2024 14:21:09 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '260'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"104-hkenyrS4iuXcoo9/DR3IzK1ulxw"
    body:
      encoding: UTF-8
      string: '{"errors":[{"title":"The requested account (ID: ********) could not
        be found.","status":"404","code":"account_not_found","details":"The requested
        account (ID: ********) could not be found.","detail":"The requested account
        (ID: ********) could not be found."}]}'
  recorded_at: Fri, 02 Feb 2024 14:21:09 GMT
recorded_with: VCR 6.2.0
