---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":100,"description":"Transfering
        money"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2413772"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"********"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Fri, 02 Feb 2024 14:21:09 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '204'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"cc-JdXCU9f7GLDQx7EznBRXPivb07A"
    body:
      encoding: UTF-8
      string: '{"errors":[{"title":"Not Found","status":"404","details":"The specified
        counterparty account with id ******** was not found","detail":"The specified
        counterparty account with id ******** was not found"}]}'
  recorded_at: Fri, 02 Feb 2024 14:21:09 GMT
recorded_with: VCR 6.2.0
