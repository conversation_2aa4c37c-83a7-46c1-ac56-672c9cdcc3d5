---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":100,"description":"Transfering
        money","idempotencyKey":"key"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2472829"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"2385052"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Fri, 02 Feb 2024 14:16:47 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '575'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"23f-Z1KdnFawTVztKbq/X5t20WRoD+c"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","id":"3593139","attributes":{"createdAt":"2024-02-01T20:16:20.270Z","amount":100,"direction":"Credit","description":"Transfering
        money","status":"Rejected","reason":"InsufficientFunds"},"relationships":{"account":{"data":{"type":"account","id":"2413772"}},"customer":{"data":{"type":"customer","id":"1497756"}},"customers":{"data":[{"type":"customer","id":"1497756"}]},"counterpartyAccount":{"data":{"type":"account","id":"2385052"}},"counterpartyCustomer":{"data":{"type":"customer","id":"1497757"}},"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Fri, 02 Feb 2024 14:16:47 GMT
recorded_with: VCR 6.2.0
