---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/check-payments/111?include=customer"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 01 May 2025 23:08:21 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1931'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"78b-ijtSqG5imgMLGPCfGrSwt4KY+ng"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"checkPayment","id":"111","attributes":{"createdAt":"2025-05-01T19:38:05.953Z","updatedAt":"2025-05-01T22:00:17.075Z","amount":2600000,"description":"Check
        Payment | 123456","status":"Returned","returnReason":"Stop
        Payment","returnStatusReason":"StopPayment","additionalVerificationStatus":"NotRequired","checkNumber":"123456","onUsAuxiliary":"123456","onUs":"**********/","counterpartyRoutingNumber":"*********","returnCutoffTime":"2025-05-02T15:50:00.000Z","originated":false,"isAccountClosureCheck":false},"relationships":{"account":{"data":{"type":"account","id":"5302494"}},"customer":{"data":{"type":"customer","id":"2014236"}},"org":{"data":{"type":"org","id":"4381"}}}},"included":[{"type":"businessCustomer","id":"2014236","attributes":{"createdAt":"2024-06-06T14:07:48.682Z","name":"Natalia
        Testing Company","dba":"","address":{"street":"6001 Cass","street2":"","city":"Detroit","state":"MI","postalCode":"48188","country":"US"},"phone":{"countryCode":"1","number":"**********"},"stateOfIncorporation":"MI","ein":"<NELCO_CLIENT_ID>0001","entityType":"LLC","contact":{"fullName":{"first":"Natalia","last":"Lin"},"email":"nlin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"**********"}},"tags":{"entity_id":"9","subdomain":"staging","redirectUrl":"https://staging.<POSTGRES_USER>-staging.co/organization/bank_accounts","created_by_gid":"gid://<POSTGRES_USER>/PropertyManager/82"},"authorizedUsers":[{"fullName":{"first":"unlikely","last":"toexist"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"**********"}},{"fullName":{"first":"Johnnie","last":"Depp!"},"email":"jevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"**********"}}],"status":"Active"},"relationships":{"application":{"data":{"type":"application","id":"2331264"}},"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"********"},{"type":"authorizedUserResource","id":"********"}]}}}]}'
  recorded_at: Thu, 01 May 2025 23:08:21 GMT
recorded_with: VCR 6.3.1
