---
http_interactions:
- request:
    method: post
    uri: https://api.postgrid.com/print-mail/v1/bank_accounts
    body:
      encoding: US-ASCII
      string: bankName=Operating+Account&accountNumber=44274&bankCountryCode=US&signatureImage=https%3A%2F%2Fas1.ftcdn.net%2Fjpg%2F02%2F36%2F99%2F22%2F1000_F_236992283_sNOxCVQeFLd5pdqaKGh8DRGMZy7P4XKm.jpg&bankPrimaryLine=35696+Conn+Dale&bankSecondaryLine=Suite+632&routingNumber=*********
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      X-Api-Key:
      - "<POSTGRID_API_KEY>"
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 09 Jul 2025 15:07:11 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '593'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; preload
      X-Content-Type-Options:
      - nosniff
      Content-Security-Policy:
      - default-src 'none'
      Vary:
      - Origin
      Access-Control-Allow-Credentials:
      - 'true'
      Etag:
      - W/"251-B1Ff0zL6JWwnTd5DR4Q6m/ZiQ9I"
      Cf-Cache-Status:
      - DYNAMIC
      Nel:
      - '{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'
      Report-To:
      - '{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=pEzMBbvUB64HwrzV%2FztftL36cpEjRJwDjI8fJ7Siq3UWTs%2FAVNyIsWZdCz2k6OxxHqWU%2B5HSC3arfaewsaE78T%2FiAvZ60YnvRzDLMX7Nfe8%3D"}]}'
      Server:
      - cloudflare
      Cf-Ray:
      - 95c8bac06a1ac0b2-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
    body:
      encoding: UTF-8
      string: '{"id":"bank_oPsx1n8p9cYUXZBEfvHV7E","object":"bank_account","live":<SCHEDULED_JOBS>,"accountNumberAndIDSHA256":"/pxAJpqTRsE4PhgtIjgIxUrR88yPfNInVoUDS+klvWw=","accountNumberLast4":"4274","bankCountryCode":"US","bankName":"Operating
        Account","bankPrimaryLine":"35696 Conn Dale","bankSecondaryLine":"Suite 632","routingNumber":"*********","signatureImage":"https://pg-prod-bucket-1.s3.amazonaws.com/test/sign_ujNShuwNLX2BeDtveqXmpc?AWSAccessKeyId=AKIA5GFUILSULWTWCR64&Expires=175207<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>31&Signature=HRDf0JaHYQTHkqp1s3xfTCEe5f0%3D","createdAt":"2025-07-09T15:07:11.139Z","updatedAt":"2025-07-09T15:07:11.139Z"}'
  recorded_at: Wed, 09 Jul 2025 15:07:11 GMT
recorded_with: VCR 6.3.1
