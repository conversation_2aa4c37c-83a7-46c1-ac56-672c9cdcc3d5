---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/cards/1620240"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 03 Sep 2024 21:14:02 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '573'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"23d-LNXzg6Ugon1W3DaDJqE+PW/n9zo"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessVirtualDebitCard","id":"1620240","attributes":{"createdAt":"2024-01-30T19:38:42.821Z","last4Digits":"<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>56","expirationDate":"2028-01","address":{"street":"line
        one","city":"Novi","state":"MI","postalCode":"48375","country":"US"},"fullName":{"first":"first","last":"last"},"phone":{"countryCode":"1","number":"**********"},"email":"<EMAIL>","dateOfBirth":"2000-01-01","status":"Active","tags":{},"bin":"424242<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>9"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}},"account":{"data":{"type":"account","id":"2413772"}}}}}'
  recorded_at: Tue, 03 Sep 2024 21:14:02 GMT
recorded_with: VCR 6.2.0
