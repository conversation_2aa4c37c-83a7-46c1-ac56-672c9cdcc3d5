---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/transactions
    body:
      encoding: UTF-8
      string: '{"transaction":{"account_vault_id":"11e84cb9b010c52aae905968","contact_id":"11e84cb95f943bf48d9b0a63","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","action":"credit","transaction_amount":250.0,"subtotal_amount":250.0,"surcharge_amount":0.0}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.4.2
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 16 Jun 2021 21:19:15 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.14.0 (Ubuntu)
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/transactions/11ebcee880867a20805f548f
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"transaction":{"id":"11ebcee880867a20805f548f","payment_method":"ach","account_vault_id":"11e84cb9b010c52aae905968","recurring_id":null,"first_six":null,"last_four":"7090","account_holder_name":"Monte
        Paucek","transaction_amount":"250.00","description":null,"transaction_code":null,"avs":null,"batch":null,"order_num":"************","verbiage":null,"transaction_settlement_status":null,"effective_date":"2021-06-16T21:19:15+0000","routing":"*********","return_date":null,"created_ts":**********,"modified_ts":**********,"transaction_api_id":null,"terms_agree":null,"notification_email_address":null,"notification_email_sent":true,"response_message":"","auth_amount":"250.00","auth_code":null,"status_id":131,"type_id":40,"location_id":"11e816510e9c03a6bcbc685e","reason_code_id":1000,"contact_id":"11e84cb95f943bf48d9b0a63","billing_zip":null,"billing_street":null,"product_transaction_id":"11e816510ee9ec4285c8448a","tax":"0.00","customer_ip":null,"customer_id":null,"po_number":null,"avs_enhanced":null,"cvv_response":null,"billing_phone":null,"billing_city":null,"billing_state":null,"clerk_number":null,"tip_amount":"0.00","created_user_id":"11e816510eb4209487387f58","modified_user_id":"11e816510eb4209487387f58","ach_identifier":null,"check_number":null,"settle_date":null,"charge_back_date":null,"void_date":null,"account_type":"checking","is_recurring":<SSL_ENABLED>,"is_accountvault":true,"transaction_c1":null,"transaction_c2":null,"transaction_c3":null,"additional_amounts":[],"terminal_serial_number":null,"entry_mode_id":"K","terminal_id":null,"quick_invoice_id":null,"ach_sec_code":"PPD","custom_data":null,"hosted_payment_page_id":null,"trx_source_id":12,"transaction_batch_id":null,"currency_code":840,"par":null,"checkin_date":null,"checkout_date":null,"room_num":null,"room_rate":"0.00","advance_deposit":<SSL_ENABLED>,"no_show":<SSL_ENABLED>,"emv_receipt_data":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/transactions/11ebcee880867a20805f548f"}}}}'
  recorded_at: Sun, 05 Jul 2020 04:00:00 GMT
recorded_with: VCR 6.0.0
