---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON>","last_name":"<PERSON><PERSON><PERSON>","email":"<PERSON><PERSON><PERSON>@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","state":"MI","zip":"48223"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:12:01 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=1KHDszjE3XYN_4usEi1BQ3zARJoqKiYS_HbfKdfNJEDzx_j6bwMn31CKcVjqNePx93h1PWif44Hw~8bLVEI53diwu60GpQVL9lT6U_Tv3AhjbYoy1FrGCOT_PMnTdhQQ;
        Max-Age=31536000; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11ef0348753e20aea30be6d1
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11ef0348753e20aea30be6d1","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"John","last_name":"Stemler","email":"jstemler@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","country":"USA","state":"MI","zip":"48223","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11ef0348753e20aea30be6d1"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:12:01 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"John Stemler","is_company":<SSL_ENABLED>,"contact_id":"11ef0348753e20aea30be6d1","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"****************","exp_date":"0225","cvv":"111"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 422
      message: Data Validation Failed.
    headers:
      Date:
      - Thu, 25 Apr 2024 21:12:01 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=2riXIduR~reno1bHTVd7LnBVWx~A60OZzaoO~UOyOOF~QmSaIR3Qb6j6qKp4kiAvWUmpDxfcuLIS15IgyrmEfN1f5oi1GUVdgb201Xo~UPaTjfQ1Isy83diKqESwvDRN;
        Max-Age=31536000; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"errors":{"account_number":["Account Number is not valid"]}}'
  recorded_at: Thu, 25 Apr 2024 21:12:01 GMT
recorded_with: VCR 6.2.0
