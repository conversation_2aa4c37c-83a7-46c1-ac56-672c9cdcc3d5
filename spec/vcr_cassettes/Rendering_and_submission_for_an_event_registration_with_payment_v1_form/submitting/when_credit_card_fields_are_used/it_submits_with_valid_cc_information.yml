---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON>","last_name":"<PERSON><PERSON><PERSON>","email":"<PERSON><PERSON><PERSON>@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","state":"MI","zip":"48223"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:11:56 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=usayxcudiLjqLwdoCFdeoMVssfyt_nHnD6HBsnRNOoCk20zMJK13jnlUqjgtYmbYfdMXwYtavJX56X1JLm5wGprEC3APT8weuPE26oviHepT9gDtz_CJupKqL1sk2u9r;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11ef0348728dece0b50efbfc
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11ef0348728dece0b50efbfc","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"John","last_name":"Stemler","email":"jstemler@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","country":"USA","state":"MI","zip":"48223","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11ef0348728dece0b50efbfc"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:11:56 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"John Stemler","is_company":<SSL_ENABLED>,"contact_id":"11ef0348728dece0b50efbfc","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"****************","exp_date":"1225","cvv":"999"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:11:57 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=Qe4_gtdNfqETmgohe70LT2O9JFyeZZ9SdJeOcRVS8QtNi~AFNmiVfyRiOvdG6vTMTJadPfUcUKQAVPuzYXQcTKWSiPzUJvr8TDM3YKH_z9s9dWvh~MgySZtj4aB6UG6g;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef03487343bd4089236523
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11ef03487343bd4089236523","payment_method":"cc","title":null,"account_holder_name":"John
        Stemler","first_six":"411111","last_four":"1111","billing_address":null,"billing_zip":null,"exp_date":"1225","routing":null,"account_type":"visa","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11ef0348728dece0b50efbfc","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":20,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":null,"dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"billing_country":null,"customer_id":null,"cau_summary_status_id":0,"cau_last_updated_ts":null,"card_bin":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef03487343bd4089236523"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:11:58 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/transactions
    body:
      encoding: UTF-8
      string: '{"transaction":{"account_vault_id":"11ef03487343bd4089236523","contact_id":"11ef0348728dece0b50efbfc","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","action":"sale","transaction_amount":53.0,"subtotal_amount":50.0,"surcharge_amount":3.0,"transaction_type":"sale"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:12:00 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=UQ~3KNUCYRGo4r00SHwAHDiC8q3SG2PeSAlqvVFEAX_lid2HmfVl2BbrxtbGsvy7TnzXBxRHzk84KYcndfNBMoYdpEOrpUh9Pu4Mudquh3IUANQIRgETngYMvJXxqMPJ;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/transactions/11ef034873cc709a813cb520
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"transaction":{"id":"11ef034873cc709a813cb520","payment_method":"cc","account_vault_id":"11ef03487343bd4089236523","recurring_id":null,"first_six":"411111","last_four":"1111","account_holder_name":"John
        Stemler","transaction_amount":"53.00","description":null,"transaction_code":null,"avs":null,"batch":"1","order_num":"************","verbiage":"Test
        7719","transaction_settlement_status":null,"effective_date":null,"routing":null,"return_date":null,"created_ts":**********,"modified_ts":**********,"transaction_api_id":null,"terms_agree":null,"notification_email_address":null,"notification_email_sent":true,"notification_phone":null,"response_message":null,"auth_amount":"53.00","auth_code":"034874","status_id":101,"type_id":20,"location_id":"11e816510e9c03a6bcbc685e","reason_code_id":1000,"contact_id":"11ef0348728dece0b50efbfc","billing_zip":null,"billing_street":null,"product_transaction_id":"11e82c4f9075d924937b65bd","tax":"0.00","customer_ip":"**************","customer_id":null,"po_number":null,"avs_enhanced":"V","cvv_response":"N","cavv_result":null,"billing_phone":null,"billing_city":null,"billing_state":null,"billing_country":null,"clerk_number":null,"tip_amount":"0.00","created_user_id":"11e816510eb4209487387f58","modified_user_id":"11e816510eb4209487387f58","ach_identifier":null,"check_number":null,"recurring_flag":"no","installment_counter":null,"installment_total":null,"settle_date":null,"charge_back_date":null,"void_date":null,"account_type":"visa","is_recurring":<SSL_ENABLED>,"is_accountvault":true,"transaction_c1":null,"transaction_c2":null,"transaction_c3":null,"additional_amounts":[],"terminal_serial_number":null,"entry_mode_id":"K","terminal_id":null,"quick_invoice_id":null,"ach_sec_code":null,"custom_data":null,"hosted_payment_page_id":null,"trx_source_id":12,"transaction_batch_id":"11e82c5086e3eb20add5a23d","currency_code":840,"par":"ZZZZZZZZZZZZZZZZZZZZ111111111","stan":null,"currency":"USD","secondary_amount":"0.00","card_bin":"411111","wallet_id":null,"paylink_id":null,"checkin_date":null,"checkout_date":null,"room_num":null,"room_rate":"0.00","advance_deposit":<SSL_ENABLED>,"no_show":<SSL_ENABLED>,"emv_receipt_data":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/transactions/11ef034873cc709a813cb520"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:12:00 GMT
recorded_with: VCR 6.2.0
