---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON>","last_name":"<PERSON><PERSON><PERSON>","email":"<PERSON><PERSON><PERSON>@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","state":"MI","zip":"48223"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:42:06 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=x73psyxM85Uo8SVnHYttUNf03zudijLyhGFPLcbeS489K4NWN2H2w~t65MsYsRMhGOMQnwwQZB0p~BfmF3YIPlVVm_3U76wIzy0yrepUYnVeorEP9jVSR8hHGDpUST~D;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11ef034ca985eb2c8b756dc0
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11ef034ca985eb2c8b756dc0","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"John","last_name":"Stemler","email":"jstemler@<POSTGRES_USER>.co","address":"123
        Street","city":"Detroit","country":"USA","state":"MI","zip":"48223","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11ef034ca985eb2c8b756dc0"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:42:06 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"John Stemler","is_company":<SSL_ENABLED>,"contact_id":"11ef034ca985eb2c8b756dc0","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","account_type":"savings","account_number":"*********","routing":"*********"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:42:09 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=YaydnX9dUr29ct6rd12LH1cQQhI0Omc3mhG9VI_pG2F5UevfScacL2H~ud8_lPtZSVX0kef6tYX3V~0chTsJfsbnJ0qTirqu~nNlXNIsHYSxwzGUoY4XfkO~ef_HqYtQ;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef034cab1950509277744f
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11ef034cab1950509277744f","payment_method":"ach","title":null,"account_holder_name":"John
        Stemler","first_six":"111111","last_four":"1111","billing_address":null,"billing_zip":null,"exp_date":null,"routing":"*********","account_type":"savings","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11ef034ca985eb2c8b756dc0","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":0,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":"PPD","dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"billing_country":null,"customer_id":null,"cau_summary_status_id":null,"cau_last_updated_ts":null,"card_bin":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef034cab1950509277744f"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:42:09 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/transactions
    body:
      encoding: UTF-8
      string: '{"transaction":{"account_vault_id":"11ef034cab1950509277744f","contact_id":"11ef034ca985eb2c8b756dc0","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","action":"debit","transaction_amount":53.0,"subtotal_amount":50.0,"surcharge_amount":3.0}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 25 Apr 2024 21:42:11 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Server:
      - nginx/1.19.6
      X-Datadome:
      - protected
      Accept-Ch:
      - Sec-CH-UA,Sec-CH-UA-Mobile,Sec-CH-UA-Platform,Sec-CH-UA-Arch,Sec-CH-UA-Full-Version-List,Sec-CH-UA-Model,Sec-CH-Device-Memory
      Set-Cookie:
      - datadome=y9TfkoZBjeNe5bxJA7V0nRlD0ksLq84kuAn_35Clww4Shs_TIt0_et6wZ04lKSOigz3yJECS6knnAkVRKrZsKz4a0qXcKFBw9QbMBOo~nzWfxXh71X1LU9jr6XqWScot;
        Max-Age=********; Domain=.zeamster.com; Path=/; Secure; SameSite=Lax
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/transactions/11ef034cabf3424cad347902
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"transaction":{"id":"11ef034cabf3424cad347902","payment_method":"ach","account_vault_id":"11ef034cab1950509277744f","recurring_id":null,"first_six":null,"last_four":"1111","account_holder_name":"John
        Stemler","transaction_amount":"53.00","description":null,"transaction_code":null,"avs":null,"batch":null,"order_num":"************","verbiage":null,"transaction_settlement_status":null,"effective_date":"2024-04-25T21:42:11+0000","routing":"*********","return_date":null,"created_ts":**********,"modified_ts":**********,"transaction_api_id":null,"terms_agree":null,"notification_email_address":null,"notification_email_sent":true,"notification_phone":null,"response_message":"","auth_amount":"53.00","auth_code":null,"status_id":131,"type_id":50,"location_id":"11e816510e9c03a6bcbc685e","reason_code_id":1000,"contact_id":"11ef034ca985eb2c8b756dc0","billing_zip":null,"billing_street":null,"product_transaction_id":"11e816510ee9ec4285c8448a","tax":"0.00","customer_ip":"**************","customer_id":null,"po_number":null,"avs_enhanced":null,"cvv_response":null,"cavv_result":null,"billing_phone":null,"billing_city":null,"billing_state":null,"billing_country":null,"clerk_number":null,"tip_amount":"0.00","created_user_id":"11e816510eb4209487387f58","modified_user_id":"11e816510eb4209487387f58","ach_identifier":null,"check_number":null,"recurring_flag":"no","installment_counter":null,"installment_total":null,"settle_date":null,"charge_back_date":null,"void_date":null,"account_type":"savings","is_recurring":<SSL_ENABLED>,"is_accountvault":true,"transaction_c1":null,"transaction_c2":null,"transaction_c3":null,"additional_amounts":[],"terminal_serial_number":null,"entry_mode_id":"K","terminal_id":null,"quick_invoice_id":null,"ach_sec_code":"PPD","custom_data":null,"hosted_payment_page_id":null,"trx_source_id":12,"transaction_batch_id":null,"currency_code":840,"par":null,"stan":null,"currency":"USD","secondary_amount":"0.00","card_bin":null,"wallet_id":null,"paylink_id":null,"checkin_date":null,"checkout_date":null,"room_num":null,"room_rate":"0.00","advance_deposit":<SSL_ENABLED>,"no_show":<SSL_ENABLED>,"emv_receipt_data":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/transactions/11ef034cabf3424cad347902"}}}}'
  recorded_at: Thu, 25 Apr 2024 21:42:11 GMT
recorded_with: VCR 6.2.0
