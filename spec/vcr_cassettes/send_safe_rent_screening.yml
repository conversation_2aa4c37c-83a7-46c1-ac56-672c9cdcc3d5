---
http_interactions:
- request:
    method: post
    uri: https://vendors.residentscreening.net/b2b/demits.aspx
    body:
      encoding: UTF-8
      string: |-
        <?xml version="1.0"?>
        <ApplicantScreening xmlns:MITS="http://my-company.com/namespace">
          <Request>
            <PropertyID>
              <MITS:Identification Type="other">
                <MITS:PrimaryID/>
                <MITS:MarketingName/>
                <MITS:WebSite/>
              </MITS:Identification>
            </PropertyID>
            <RequestType>New</RequestType>
            <Agencies/>
            <ReportOptions>
              <ReportName>01</ReportName>
            </ReportOptions>
            <ReportID/>
            <OriginatorID>100005</OriginatorID>
            <MarketingSource/>
            <UserAccount></UserAccount>
            <UserName></UserName>
            <UserPassword></UserPassword>
          </Request>
          <LeaseTerms>
            <MonthlyRent>535.36</MonthlyRent>
            <LeaseMonths>12</LeaseMonths>
            <ApplicationDate>2024-04-26</ApplicationDate>
            <NumberBedrooms>5</NumberBedrooms>
            <AffordableLease>0</AffordableLease>
            <SecurityDeposit>0</SecurityDeposit>
          </LeaseTerms>
          <Applicant>
            <Other>
              <CurrentRent/>
              <ConsentObtained><REFERENCE_SERVICES_TEST_MODE></ConsentObtained>
            </Other>
            <Income>
              <EmploymentStatus>Current</EmploymentStatus>
              <EmploymentType/>
              <EmploymentGrossIncome>0.00</EmploymentGrossIncome>
              <OtherIncomeAmount>0.0</OtherIncomeAmount>
              <OtherIncomeType/>
              <OtherIncomePeriod>Month</OtherIncomePeriod>
              <Assets>0.0</Assets>
              <TotalLiabilities>0.0</TotalLiabilities>
            </Income>
            <AS_Information>
              <ApplicantIdentifier>t001</ApplicantIdentifier>
              <ApplicantType>primary_applicant</ApplicantType>
              <GuarantorFor/>
              <Spouse/>
              <Birthdate>1992-01-07</Birthdate>
              <SocSecNumber>123467890</SocSecNumber>
              <DriversLicenseID/>
              <DriversLicenseState/>
            </AS_Information>
            <Customers>
              <MITS:Customer Type="applicant">
                <MITS:CustomerID>t001</MITS:CustomerID>
                <MITS:Name>
                  <MITS:NamePrefix/>
                  <MITS:FirstName>Ellen</MITS:FirstName>
                  <MITS:NoMiddleName><SSL_ENABLED></MITS:NoMiddleName>
                  <MITS:MiddleName>L</MITS:MiddleName>
                  <MITS:LastName>Ripley</MITS:LastName>
                  <MITS:MaidenName/>
                  <MITS:NameSuffix/>
                </MITS:Name>
                <MITS:Address Type="current">
                  <MITS:Address1>1420 Washington Blvd.</MITS:Address1>
                  <MITS:Address2>Suite 301</MITS:Address2>
                  <MITS:City>Detroit</MITS:City>
                  <MITS:State>MI</MITS:State>
                  <MITS:PostalCode>48226</MITS:PostalCode>
                  <MITS:Country>United States</MITS:Country>
                  <MITS:UnparsedAddress>1420 Washington Blvd.</MITS:UnparsedAddress>
                  <MITS:Email><EMAIL></MITS:Email>
                </MITS:Address>
                <MITS:Phone Type="home">
                  <MITS:PhoneNumber/>
                </MITS:Phone>
                <MITS:Phone Type="cell">
                  <MITS:PhoneNumber>2489212775</MITS:PhoneNumber>
                </MITS:Phone>
                <MITS:Phone Type="office">
                  <MITS:PhoneNumber/>
                </MITS:Phone>
              </MITS:Customer>
            </Customers>
          </Applicant>
          <Applicant>
            <Other>
              <CurrentRent/>
              <ConsentObtained><REFERENCE_SERVICES_TEST_MODE></ConsentObtained>
            </Other>
            <Income>
              <EmploymentStatus>Current</EmploymentStatus>
              <EmploymentType/>
              <EmploymentGrossIncome>0.00</EmploymentGrossIncome>
              <OtherIncomeAmount>0.0</OtherIncomeAmount>
              <OtherIncomeType/>
              <OtherIncomePeriod>Month</OtherIncomePeriod>
              <Assets>0.0</Assets>
              <TotalLiabilities>0.0</TotalLiabilities>
            </Income>
            <AS_Information>
              <ApplicantIdentifier>t002</ApplicantIdentifier>
              <ApplicantType>occupant</ApplicantType>
              <GuarantorFor/>
              <Spouse/>
              <Birthdate>1992-02-26</Birthdate>
              <SocSecNumber>123467890</SocSecNumber>
              <DriversLicenseID/>
              <DriversLicenseState/>
            </AS_Information>
            <Customers>
              <MITS:Customer Type="applicant">
                <MITS:CustomerID>t002</MITS:CustomerID>
                <MITS:Name>
                  <MITS:NamePrefix/>
                  <MITS:FirstName>John</MITS:FirstName>
                  <MITS:NoMiddleName><SSL_ENABLED></MITS:NoMiddleName>
                  <MITS:MiddleName>J</MITS:MiddleName>
                  <MITS:LastName>Smith</MITS:LastName>
                  <MITS:MaidenName/>
                  <MITS:NameSuffix/>
                </MITS:Name>
                <MITS:Address Type="current">
                  <MITS:Address1>1420 Washington Blvd.</MITS:Address1>
                  <MITS:Address2>Suite 301</MITS:Address2>
                  <MITS:City>Detroit</MITS:City>
                  <MITS:State>MI</MITS:State>
                  <MITS:PostalCode>48226</MITS:PostalCode>
                  <MITS:Country>United States</MITS:Country>
                  <MITS:UnparsedAddress>1420 Washington Blvd.</MITS:UnparsedAddress>
                  <MITS:Email><EMAIL></MITS:Email>
                </MITS:Address>
                <MITS:Phone Type="home">
                  <MITS:PhoneNumber/>
                </MITS:Phone>
                <MITS:Phone Type="cell">
                  <MITS:PhoneNumber>1234567890</MITS:PhoneNumber>
                </MITS:Phone>
                <MITS:Phone Type="office">
                  <MITS:PhoneNumber/>
                </MITS:Phone>
              </MITS:Customer>
            </Customers>
          </Applicant>
        </ApplicantScreening>
    headers:
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
        - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 28 Feb 2024 17:25:57 GMT
      Content-Type:
      - text/xml; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - close
      Cache-Control:
      - private
      Pragma:
      - no-cache
      Set-Cookie:
      - ASP.NET_SessionId=2pz31emozahtvpibmljsfvvp; path=/; HttpOnly; SameSite=Lax
      - ASP.NET_SessionId=2pz31emozahtvpibmljsfvvp; path=/; HttpOnly; SameSite=Lax
      - __AntiXsrfToken=0895fcdfc8944b84bf14fe4a6978b01f; path=/; HttpOnly
      - __cf_bm=J5EwbXz8.C079EOqbiCuotTnu9stquvcxc6mdOq8LCs-1709141157-1.0-AYJqpyllWOl66r/Wufj8FNet+9KXkg7oR+3gIzjmDYZi93ZL08s9drH2UIe2BNws35gWvnCbS49yTyOh+6dfrBM=;
        path=/; expires=Wed, 28-Feb-24 17:55:57 GMT; domain=.residentscreening.net;
        HttpOnly; Secure; SameSite=None
      Cachecontrol:
      - no-cache
      X-Powered-By:
      - ASP.NET
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 85ca5e571e7a60a7-ORD
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<ApplicantScreening xmlns:MITS=\"http://my-company.com/namespace\">\r\n
        \ <Request>\r\n    <PropertyID>\r\n      <MITS:Identification Type=\"other\">\r\n
        \       <MITS:PrimaryID />\r\n        <MITS:MarketingName />\r\n        <MITS:WebSite
        />\r\n      </MITS:Identification>\r\n    </PropertyID>\r\n    <RequestType>New</RequestType>\r\n
        \   <Agencies />\r\n    <ReportOptions>\r\n      <ReportName>01</ReportName>\r\n
        \   </ReportOptions>\r\n    <ReportID />\r\n    <OriginatorID>100005</OriginatorID>\r\n
        \   <MarketingSource />\r\n    <UserAccount></UserAccount>\r\n    <UserName></UserName>\r\n
        \   <UserPassword></UserPassword>\r\n  </Request>\r\n  <LeaseTerms>\r\n
        \   <MonthlyRent>535.36</MonthlyRent>\r\n    <LeaseMonths>12</LeaseMonths>\r\n
        \   <ApplicationDate>2024-04-26</ApplicationDate>\r\n    <NumberBedrooms>5</NumberBedrooms>\r\n
        \   <AffordableLease>0</AffordableLease>\r\n    <SecurityDeposit>0</SecurityDeposit>\r\n
        \ </LeaseTerms>\r\n  <Applicant>\r\n    <Other>\r\n      <CurrentRent />\r\n
        \     <ConsentObtained><REFERENCE_SERVICES_TEST_MODE></ConsentObtained>\r\n
        \   </Other>\r\n    <Income>\r\n      <EmploymentStatus>Current</EmploymentStatus>\r\n
        \     <EmploymentType />\r\n      <EmploymentGrossIncome>0.00</EmploymentGrossIncome>\r\n
        \     <OtherIncomeAmount>0.0</OtherIncomeAmount>\r\n      <OtherIncomeType
        />\r\n      <OtherIncomePeriod>Month</OtherIncomePeriod>\r\n      <Assets>0.0</Assets>\r\n
        \     <TotalLiabilities>0.0</TotalLiabilities>\r\n    </Income>\r\n    <AS_Information>\r\n
        \     <ApplicantIdentifier>t001</ApplicantIdentifier>\r\n      <ApplicantType>primary_applicant</ApplicantType>\r\n
        \     <GuarantorFor />\r\n      <Spouse />\r\n      <Birthdate>1992-01-07</Birthdate>\r\n
        \     <SocSecNumber>123467890</SocSecNumber>\r\n      <DriversLicenseID />\r\n
        \     <DriversLicenseState />\r\n    </AS_Information>\r\n    <Customers>\r\n
        \     <MITS:Customer Type=\"applicant\">\r\n        <MITS:CustomerID>t001</MITS:CustomerID>\r\n
        \       <MITS:Name>\r\n          <MITS:NamePrefix />\r\n          <MITS:FirstName>Ellen</MITS:FirstName>\r\n
        \         <MITS:NoMiddleName><SSL_ENABLED></MITS:NoMiddleName>\r\n          <MITS:MiddleName>L</MITS:MiddleName>\r\n
        \         <MITS:LastName>Ripley</MITS:LastName>\r\n          <MITS:MaidenName
        />\r\n          <MITS:NameSuffix />\r\n        </MITS:Name>\r\n        <MITS:Address
        Type=\"current\">\r\n          <MITS:Address1>1420 Washington Blvd.</MITS:Address1>\r\n
        \         <MITS:Address2>Suite 301</MITS:Address2>\r\n          <MITS:City>Detroit</MITS:City>\r\n
        \         <MITS:State>MI</MITS:State>\r\n          <MITS:PostalCode>48226</MITS:PostalCode>\r\n
        \         <MITS:Country>United States</MITS:Country>\r\n          <MITS:UnparsedAddress>1420
        Washington Blvd.</MITS:UnparsedAddress>\r\n          <MITS:Email><EMAIL></MITS:Email>\r\n
        \       </MITS:Address>\r\n        <MITS:Phone Type=\"home\">\r\n          <MITS:PhoneNumber
        />\r\n        </MITS:Phone>\r\n        <MITS:Phone Type=\"cell\">\r\n          <MITS:PhoneNumber>2489212775</MITS:PhoneNumber>\r\n
        \       </MITS:Phone>\r\n        <MITS:Phone Type=\"office\">\r\n          <MITS:PhoneNumber
        />\r\n        </MITS:Phone>\r\n      </MITS:Customer>\r\n    </Customers>\r\n
        \ </Applicant>\r\n  <Applicant>\r\n    <Other>\r\n      <CurrentRent />\r\n
        \     <ConsentObtained><REFERENCE_SERVICES_TEST_MODE></ConsentObtained>\r\n
        \   </Other>\r\n    <Income>\r\n      <EmploymentStatus>Current</EmploymentStatus>\r\n
        \     <EmploymentType />\r\n      <EmploymentGrossIncome>0.00</EmploymentGrossIncome>\r\n
        \     <OtherIncomeAmount>0.0</OtherIncomeAmount>\r\n      <OtherIncomeType
        />\r\n      <OtherIncomePeriod>Month</OtherIncomePeriod>\r\n      <Assets>0.0</Assets>\r\n
        \     <TotalLiabilities>0.0</TotalLiabilities>\r\n    </Income>\r\n    <AS_Information>\r\n
        \     <ApplicantIdentifier>t002</ApplicantIdentifier>\r\n      <ApplicantType>occupant</ApplicantType>\r\n
        \     <GuarantorFor />\r\n      <Spouse />\r\n      <Birthdate>1992-02-26</Birthdate>\r\n
        \     <SocSecNumber>123467890</SocSecNumber>\r\n      <DriversLicenseID />\r\n
        \     <DriversLicenseState />\r\n    </AS_Information>\r\n    <Customers>\r\n
        \     <MITS:Customer Type=\"applicant\">\r\n        <MITS:CustomerID>t002</MITS:CustomerID>\r\n
        \       <MITS:Name>\r\n          <MITS:NamePrefix />\r\n          <MITS:FirstName>John</MITS:FirstName>\r\n
        \         <MITS:NoMiddleName><SSL_ENABLED></MITS:NoMiddleName>\r\n          <MITS:MiddleName>J</MITS:MiddleName>\r\n
        \         <MITS:LastName>Smith</MITS:LastName>\r\n          <MITS:MaidenName
        />\r\n          <MITS:NameSuffix />\r\n        </MITS:Name>\r\n        <MITS:Address
        Type=\"current\">\r\n          <MITS:Address1>1420 Washington Blvd.</MITS:Address1>\r\n
        \         <MITS:Address2>Suite 301</MITS:Address2>\r\n          <MITS:City>Detroit</MITS:City>\r\n
        \         <MITS:State>MI</MITS:State>\r\n          <MITS:PostalCode>48226</MITS:PostalCode>\r\n
        \         <MITS:Country>United States</MITS:Country>\r\n          <MITS:UnparsedAddress>1420
        Washington Blvd.</MITS:UnparsedAddress>\r\n          <MITS:Email><EMAIL></MITS:Email>\r\n
        \       </MITS:Address>\r\n        <MITS:Phone Type=\"home\">\r\n          <MITS:PhoneNumber
        />\r\n        </MITS:Phone>\r\n        <MITS:Phone Type=\"cell\">\r\n          <MITS:PhoneNumber>1234567890</MITS:PhoneNumber>\r\n
        \       </MITS:Phone>\r\n        <MITS:Phone Type=\"office\">\r\n          <MITS:PhoneNumber
        />\r\n        </MITS:Phone>\r\n      </MITS:Customer>\r\n    </Customers>\r\n
        \ </Applicant>\r\n  <Response>\r\n    <TransactionNumber>68716793</TransactionNumber>\r\n
        \   <ReportDate>2024-02-28</ReportDate>\r\n    <ApplicantDecision></ApplicantDecision>\r\n
        \   <ApplicationDecision></ApplicationDecision>\r\n    <BackgroundReport><![CDATA[\r\n
        \                       <html>\r\n                          <head>\r\n                      \r\n\t\t\r\n\t\t\r\n\t\t\r\n\r\n\t\t\r\n\t\t\r\n\r\n
        \   <script type=\"text/javascript\" src=\"jquery/jquery-1.7.1.min.js\" />\r\n
        \   <script type=\"text/javascript\" src=\"jquery/ui_controls/ui/jquery-ui-1.8.17.custom.js\"
        />\r\n\r\n    <link type=\"text/css\" href=\"jquery/ui_controls/themes/corelogic/jquery-ui-1.8.17.custom.css\"
        rel=\"stylesheet\" />\r\n    <script type=\"text/javascript\" src=\"jquery/plugins/jquery.combobox.js\"
        />\r\n    <script type=\"text/javascript\" src=\"jquery/plugins/jquery.blockUI.js\"
        />\r\n    <script type=\"text/javascript\" src=\"jquery/plugins/jquery.jqPrint.js\"
        />\r\n    <script type=\"text/javascript\" src=\"jquery/plugins/jquery.tooltip.js\"
        />\r\n\t<script type=\"text/javascript\" src=\"js/common.js?v=2\" />\r\n    \r\n
        \   <style>\r\n      .b_class {\r\n        background: #e23d28 url(jquery/ui_controls/themes/corelogic/images/button.png)
        repeat-x 50% 50% !important;\r\n        border:1px solid #e6e6e6 !important;\r\n
        \     }\r\n      .b_class_hover {\r\n        background: #e23d28 url(jquery/ui_controls/themes/corelogic/images/button_hover.png)
        repeat-x 50% 50% !important;\r\n        border:1px solid #e6e6e6 !important;\r\n
        \     }\r\n      .b_removeExtraSpaceForIE {\r\n        width:0\r\n      }\r\n
        \   </style>\r\n      \r\n    <script language=\"javascript\">\r\n      function
        IECompatibility() {\r\n        var agentStr = navigator.userAgent;\r\n        this.IsIE
        = <SSL_ENABLED>;\r\n        this.IsOn = undefined;  //defined only if IE\r\n
        \       this.Version = undefined;\r\n\r\n        if (agentStr.indexOf(\"MSIE
        7.0\") &gt; -1) {\r\n          this.IsIE = true;\r\n          this.IsOn =
        true;\r\n          if (agentStr.indexOf(\"Trident/6.0\") &gt; -1) {\r\n            this.Version
        = 'IE10';\r\n          } else if (agentStr.indexOf(\"Trident/5.0\") &gt; -1)
        {\r\n            this.Version = 'IE9';\r\n          } else if (agentStr.indexOf(\"Trident/4.0\")
        &gt; -1) {\r\n            this.Version = 'IE8';\r\n          } else {\r\n
        \           this.IsOn = <SSL_ENABLED>; // compatability mimics 7, thus not
        on\r\n            this.Version = 'IE7';\r\n          }\r\n        } //IE 7\r\n
        \     }\r\n      \r\n      $(document).ready(function() {\r\n\r\n        $(\"button\").button();\r\n
        \       $(\"button\").addClass(\"b_class\");\r\n        $(\"button\").hover(\r\n
        \         function () {\r\n            $(this).removeClass(\"b_class\").addClass(\"b_class_hover\");\r\n
        \         }, \r\n          function () {\r\n            $(this).removeClass(\"b_class_hover\").addClass(\"b_class\");\r\n
        \         }\r\n        );\r\n          \r\n        $(\"#b_print, #b_printresults,
        #b_printall, #b_printreports, #b_printletters, #b_printpage\").button({ icons:
        { primary: \"ui-icon-print\" } });\r\n        $(\"#b_close\").button({ icons:
        { primary: \"ui-icon-close\" } });\r\n        $(\"#b_save\").button({ icons:
        { primary: \"ui-icon-disk\" } });\r\n        $(\"#b_delete, #b_deleteuser,
        [id^=delete_]\").button({ icons: { primary: \"ui-icon-trash\" } });\r\n        $(\"#b_restore\").button({
        icons: { primary: \"ui-icon-arrowreturnthick-1-w\" } });\r\n        $(\"#b_associatedusers\").button({
        icons: { primary: \"ui-icon-link\" } });\r\n        $(\"#b_back\").button({
        icons: { primary: \"ui-icon-arrowthick-1-w\" } });\r\n        $(\"#b_search\").button({
        icons: { primary: \"ui-icon-search\" } });\r\n        $(\"#b_continue, #b_goscreeningappliaction\").button({
        icons: { primary: \"ui-icon-arrowthick-1-e\" } });\r\n        $(\"#b_createnewuser,
        #b_addapplicant, #b_addguestcard\").button({ icons: { primary: \"ui-icon-plus\"
        } });\r\n            \r\n        $(\"#includeAll, [id^=includeAll_]\").button({
        icons: { primary: \"ui-icon-seek-next\" }, text: <SSL_ENABLED> });\r\n        $(\"#include,
        [id^=include_]\").button({ icons: { primary: \"ui-icon-triangle-1-e\" }, text:
        <SSL_ENABLED> });\r\n\t\t\t\t$(\"#exclude, [id^=exclude_]\").button({ icons:
        { primary: \"ui-icon-triangle-1-w\" }, text: <SSL_ENABLED> });\r\n\t\t\t\t$(\"#excludeAll,
        [id^=excludeAll_]\").button({ icons: { primary: \"ui-icon-seek-prev\" }, text:
        <SSL_ENABLED> });\r\n        \r\n        if($.browser.msie &amp;&amp; jQuery.browser.version
        &lt; 10 ) {\r\n          $(\"button\").addClass(\"b_removeExtraSpaceForIE\");\r\n
        \       }\r\n      });\r\n      </script>\r\n    \r\n\t\t\r\n\r\n\t\t<link
        href=\"css/CoreLogic.css?v=2\" rel=\"stylesheet\" type=\"text/css\" />\r\n\r\n\t\t\r\n\t\t\r\n\t\t<script
        type=\"text/javascript\">\r\n\t\t\tvar sessionId = 'e51c0f81-1cf0-4c2a-8d3c-0f64f0c46d30';\r\n\t\t</script>\r\n\t\r\n
        \                           <link rel=\"stylesheet\" href=\"css/fadv_application.css\"/>\r\n
        \                           <base href=\"https://vendors.residentscreening.net/b2b/\"/>\r\n
        \                         </head>\r\n                          <body topmargin=\"0\"><html>\r\n
        \ <head>\r\n    <META http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\r\n
        \   <title>SafeRent Solutions</title>\r\n    <link rel=\"shortcut icon\" href=\"favicon.ico\">\r\n
        \   <link href=\"css/corelogic.css\" rel=\"stylesheet\" type=\"text/css\">\r\n
        \   <style>\r\n          .subtextlogo {\r\n          font-size: 24px;\r\n
        \         color: #6e6259;\r\n          margin-top: 12px;\r\n          }\r\n\r\n
        \         .subtextlogo > sup {\r\n          font-size: 12px;\r\n          }\r\n\r\n
        \         .userDataBar {\r\n          background-color: #003346;\r\n          padding:
        10px 0;\r\n          color: #fff;\r\n          }\r\n\r\n          .customTableStyle
        > thead > tr > th, .customTableStyleWithoutField > thead > tr > th {\r\n          border-bottom:
        2px solid #333;\r\n          color: #000;\r\n          border-left: 0px solid
        #FFFFFF;\r\n          text-align: left;\r\n          font-size: 12px;\r\n
        \         font-weight: normal;\r\n          text-transform: uppercase;\r\n
        \         }\r\n\r\n          .customTableStyle > tbody > tr > td, .customTableStyleWithoutField
        > tbody > tr > td {\r\n          border-left: 0px solid #FFFFFF;\r\n          /*
        text-align: left;*/\r\n          font-size: 12px;\r\n          vertical-align:
        middle;\r\n          padding: 10px 8px;\r\n          }\r\n          h1{font-size:
        26px; margin: 23px 0 18px; font-weight: 400}\r\n          h2, .accordianHeading{font-size:
        16px}\r\n          .semibold{font-weight: 600 !important;}\r\n          .text-nowrap
        {\r\n          white-space: nowrap\r\n          }\r\n          .body_b {\r\n
        \         font-family: Verdana, Arial, Helvetica, sans-serif;\r\n          font-size:
        11px;\r\n          font-style: normal;\r\n          line-height: 18px;\r\n
        \         font-weight: normal;\r\n          font-variant: normal;\r\n          text-transform:
        none;\r\n          color: #666666;\r\n          }\r\n          .body_bold
        {\r\n          font-family: Verdana, Arial, Helvetica, sans-serif;\r\n          font-size:
        11px;\r\n          line-height: 18px;\r\n          font-weight: bold;\r\n
        \         color: #666666;\r\n\r\n          }\r\n          .tabledata {\r\n
        \         font-family: Verdana, Arial, Helvetica, sans-serif;\r\n          font-size:
        8.5pt;\r\n          font-weight: normal;\r\n          color: #323532;\r\n
        \         text-decoration: none;\r\n          background-color: #ffffff;\r\n\r\n
        \         }\r\n          .tabledata a:link {\r\n          font-family: Verdana,
        Arial, Helvetica, sans-serif;\r\n          font-size: 11px;\r\n          font-weight:
        normal;\r\n          color: #F8862D;\r\n          text-decoration: none;\r\n
        \         line-height: 16px;\r\n\r\n          }\r\n          .tabledata a:active
        {\r\n          font-family: Verdana, Arial, Helvetica, sans-serif;\r\n          font-size:
        11px;\r\n          font-weight: normal;\r\n          color: #336699;\r\n          text-decoration:
        underline;\r\n          line-height: 16px;\r\n\r\n          }\r\n          .tabledata
        a:visited {\r\n          font-family: Verdana, Arial, Helvetica, sans-serif;\r\n
        \         font-size: 11px;\r\n          font-weight: normal;\r\n          color:
        #F8862D;\r\n          text-decoration: none;\r\n          line-height: 16px;\r\n\r\n
        \         }\r\n          .tabledata a:hover {\r\n          font-family: Verdana,
        Arial, Helvetica, sans-serif;\r\n          font-size: 11px;\r\n          font-weight:
        normal;\r\n          color: #336699;\r\n          text-decoration: underline;\r\n
        \         line-height: 16px;\r\n\r\n          }\r\n          .tableHeading
        {\r\n\r\n\r\n          font-family: Verdana, Arial, Helvetica, sans-serif;\r\n
        \         font-size: 12px;\r\n          font-weight: bold;\r\n          color:
        #ffffff;\r\n          text-decoration: none;\r\n\r\n          }\r\n\r\n\r\n
        \         .tr {\r\n          border: #669acc 1px double;\r\n          }\r\n
        \         .tr_left_nav {\r\n          border: #669acc  1px double;\r\n          }\r\n
        \         .tr_all {\r\n          border: #669acc 1px double;\r\n          }\r\n
        \         .td_b_dar {\r\n          border-bottom: #669acc 1px double;\r\n
        \         }\r\n\r\n\r\n          .td_l {\r\n          border-left: #dbdafb
        1px double;\r\n          }\r\n          .td_t {\r\n          border-top: #dbdafb
        1px double;\r\n          }\r\n          .td_r {\r\n          border-right:
        #dbdafb 1px double;\r\n          }\r\n          .td_b {\r\n          border-bottom:
        #dbdafb 1px double;\r\n          }\r\n\r\n          .padded {\r\n          padding:
        2px;\r\n          }\r\n          .padded2 {\r\n          padding: 4px;\r\n
        \         }\r\n          .error {\r\n          font-family: Verdana, Arial,
        Helvetica, sans-serif;\r\n          font-size: 11px;\r\n          font-weight:
        bold;\r\n          color: #C4262E;\r\n          text-decoration: none;\r\n
        \         }\r\n\r\n          .error_b {\r\n          font-family: Verdana,
        Arial, Helvetica, sans-serif;\r\n          font-size: 14px;\r\n          font-weight:
        bold;\r\n          color: #C4262E;\r\n          text-decoration: none;\r\n
        \         }\r\n\r\n          .subheadOrange2 {\r\n          font-family: Verdana,
        Arial, Helvetica, sans-serif;\r\n          font-size: 13px;\r\n          font-style:
        normal;\r\n          line-height: 18px;\r\n          font-weight: bold;\r\n
        \         font-variant: normal;\r\n          text-transform: uppercase;\r\n
        \         color: #f8862d;\r\n          }\r\n          .footer {\r\n          font-family:
        Verdana, Arial, Helvetica, sans-serif;\r\n          font-size: 9px;\r\n          font-weight:
        normal;\r\n          color: #666666;\r\n          text-decoration: none;\r\n
        \         }\r\n        </style><script>\r\n          if(window.addEventListener)\r\n
        \         window.addEventListener(\"load\", init);\r\n          else\r\n          window.attachEvent(\"onload\",
        init);\r\n\r\n          window.onload = init;\r\n\r\n          function init()
        {\r\n          setTimeout(\"submitRequest()\", 5000);\r\n          }\r\n\r\n
        \         function submitRequest() {\r\n          document.frmMain[\"anid\"].value
        = 'leasedecision.main';\r\n          document.frmMain[\"reports\"].value =
        '68716793*68716794';\r\n\r\n          document.frmMain.submit();\r\n          }\r\n
        \       </script></head>\r\n  <body topmargin=\"25\" bgcolor=\"#d0d0d0\" marginheight=\"25\">\r\n
        \   <table width=\"780\" align=\"center\" bgcolor=\"#ffffff\" border=\"0\"
        cellpadding=\"0\" cellspacing=\"0\">\r\n      <form name=\"frmMain\" method=\"POST\"
        action=\"https://vendors.residentscreening.net/b2b/default.aspx?WEBACCESSSESSIONID=e51c0f81-1cf0-4c2a-8d3c-0f64f0c46d30\">\r\n
        \       <tr>\r\n          <td style=\"border-left: 1px solid black;width:
        100%;\" colspan=\"4\" valign=\"top\">\r\n            <table border=\"0\" cellpadding=\"0\"
        cellspacing=\"0\" width=\"100%\">\r\n              <tr height=\"37\">\r\n
        \               <td colspan=\"3\" class=\"row userDataBar\" style=\"border-left:
        1px solid black; width:100%;\"></td>\r\n              </tr>\r\n            </table>\r\n
        \         </td>\r\n          <td valign=\"top\" width=\"20\"></td>\r\n        </tr>\r\n
        \       <tr valign=\"top\" height=\"151\">\r\n          <td style=\"border-left:
        1px solid black\" width=\"200\" height=\"151\">\r\n            <table border=\"0\"
        cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\r\n              <tr>\r\n
        \               <td align=\"center\"><img src=\"images/SafeRentLogo.png\"
        alt=\"SafeRent Logo\" style=\"margin-top: 40px;\"></td>\r\n              </tr>\r\n
        \             <td>\r\n                <div class=\"subtextlogo\" style=\"margin-left:
        25px;\">\r\n                      SafeRent Solutions<sup>®</sup></div>\r\n
        \             </td>\r\n            </table>\r\n          </td>\r\n          <td
        width=\"36\" align=\"left\" height=\"151\"></td>\r\n          <td width=\"530\"
        align=\"center\" height=\"151\"><br><table width=\"98%\" border=\"0\" cellpadding=\"0\"
        cellspacing=\"0\">\r\n              <tr>\r\n                <td valign=\"top\"
        align=\"left\">\r\n                  <h1>Property Management Lease Recommendation</h1>\r\n
        \                 <table class=\"table pull-left table-striped mg0px customTableStyle\"
        border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\r\n                    <thead
        style=\"font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px;
        font-weight: bold; color: #685745; text-decoration: none; background-color:
        #e8e1d5; line-height: 18px;\">\r\n                      <tr>\r\n                        <th
        class=\"text-nowrap\" width=\"68%\" height=\"35px\"><span> SCORE Recommendation
        </span></th>\r\n                        <th class=\"text-nowrap\" width=\"32%\"><span>
        CRIMSAFE Recommendation </span></th>\r\n                      </tr>\r\n                    </thead>\r\n
        \                 </table>\r\n                  <table class=\"table pull-left
        table-striped mg0px customTableStyle\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"
        width=\"100%\">\r\n                    <tr style=\"font-family: Verdana, Arial,
        Helvetica, sans-serif;\">\r\n                      <td class=\"text-nowrap
        \" width=\"68%\"></td>\r\n                      <td class=\"text-nowrap\"
        width=\"32%\"></td>\r\n                    </tr>\r\n                  </table>\r\n
        \                 <table class=\"table pull-left table-striped mg0px customTableStyle\"
        border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\r\n                    <tr
        style=\"font-family: Verdana, Arial, Helvetica, sans-serif;\">\r\n                      <td
        class=\"text-nowrap \" width=\"68%\">\r\n                      </td>\r\n                      <td
        class=\"text-nowrap\" width=\"32%\"></td>\r\n                    </tr>\r\n
        \                 </table>\r\n                  <hr>\r\n                  <h2
        class=\"semibold\">APPLICANT(S)</h2>\r\n                  <table class=\"table
        pull-left table-striped mg0px customTableStyle\" border=\"0\" cellpadding=\"0\"
        cellspacing=\"0\" width=\"100%\">\r\n                    <thead style=\"font-family:
        Verdana, Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold;
        color: #685745; text-decoration: none; background-color: #e8e1d5; line-height:
        18px;\">\r\n                      <th width=\"63%\" height=\"35px\">ELLEN
        L RIPLEY\r\n                               (***-**-7890)\r\n                            </th>\r\n
        \                     <th width=\"37%\"> </th>\r\n                    </thead>\r\n
        \                 </table><br><table class=\"table pull-left table-striped
        mg0px customTableStyle\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"
        width=\"100%\">\r\n                    <thead style=\"font-family: Verdana,
        Arial, Helvetica, sans-serif; font-size: 11px; font-weight: bold; color: #685745;
        text-decoration: none; background-color: #e8e1d5; line-height: 18px;\">\r\n
        \                     <th width=\"63%\" height=\"35px\">JOHN J SMITH\r\n                               (***-**-7890)\r\n
        \                           </th>\r\n                      <th width=\"37%\"> </th>\r\n
        \                   </thead>\r\n                  </table><br></td>\r\n              </tr>\r\n
        \           </table>\r\n          </td>\r\n          <td width=\"20\" height=\"151\"> </td>\r\n
        \         <td rowspan=\"4\" width=\"20\"> </td>\r\n        </tr>\r\n        <tr>\r\n
        \         <td style=\"border-left: 1px solid black\" valign=\"top\" width=\"200\"> </td>\r\n
        \         <td valign=\"top\" width=\"36\" align=\"left\" height=\"80\"></td>\r\n
        \         <td valign=\"top\" width=\"530\" align=\"center\"> </td>\r\n          <td
        valign=\"top\" width=\"20\"> </td>\r\n        </tr>\r\n        <tr>\r\n          <td
        style=\"border-left: 1px solid black\" valign=\"top\" width=\"200\" height=\"10\"> </td>\r\n
        \         <td valign=\"top\" width=\"36\" align=\"left\" height=\"10\"><img
        src=\"images/mits/spacer.gif\" width=\"1\" height=\"1\"></td>\r\n          <td
        valign=\"top\" width=\"530\" height=\"10\"> </td>\r\n          <td valign=\"top\"
        width=\"20\" height=\"10\"> </td>\r\n        </tr>\r\n        <tr>\r\n          <td
        style=\"border-left: 1px solid black\" valign=\"top\" width=\"200\"> </td>\r\n
        \         <td valign=\"top\" width=\"36\" align=\"left\" height=\"80\"><img
        src=\"images/mits/spacer.gif\" width=\"1\" height=\"1\"></td>\r\n          <td
        valign=\"top\" width=\"530\" class=\"body\">\r\n                If you don't
        automatically get redirected in 15 seconds, click <a href=\"#\" onclick=\"submitRequest();
        return <SSL_ENABLED>;\">here</a></td>\r\n          <td valign=\"top\" width=\"20\"></td>\r\n
        \       </tr>\r\n        <tr>\r\n          <td class=\"footer\" valign=\"middle\"
        colspan=\"5\" style=\"text-align:center; border-left: 1px solid black\">&amp;copy;\r\n
        \                 © <script>document.write(new Date().getFullYear())</script>
        SafeRent Solutions LLC. All rights reserved.\r\n              </td>\r\n        </tr>\r\n
        \       <tr>\r\n          <td class=\"footer\" valign=\"middle\" colspan=\"5\"
        style=\"text-align:center; border-left: 1px solid black\">\r\n                The
        contents of this website are protected by the copyright laws of the United
        States.<br>\r\n                No portion may be reproduced in any form, or
        by any means, without the prior written consent of SafeRent Solutions LLC.\r\n
        \             </td>\r\n        </tr>\r\n        <tr>\r\n          <td colspan=\"5\"
        align=\"left\" height=\"10\" style=\"border-left: 1px solid black\"></td>\r\n
        \       </tr>\r\n        <tr>\r\n          <td colspan=\"5\" valign=\"top\"
        align=\"left\" bgcolor=\"#d0d0d0\" height=\"15\"></td>\r\n        </tr><input
        type=\"hidden\" name=\"anid\" value=\"\"><input type=\"hidden\" name=\"reports\"
        value=\"\"></form>\r\n    </table>\r\n  <script defer src=\"https://static.cloudflareinsights.com/beacon.min.js/v84a3a4012de94ce1a686ba8c167c359c1696973893317\"
        integrity=\"sha512-euoFGowhlaLqXsPWQ48qSkBSCFs3DPRyiwVu3FjR96cMPx+Fr+gpWRhIafcHwqwCqWS42RZhIudOvEI+Ckf6MA==\"
        data-cf-beacon='{\"rayId\":\"85ca5ea7eb4e485e\",\"b\":1,\"version\":\"2024.2.1\",\"token\":\"735146ab001d496fb5824326f0bed929\"}'
        crossorigin=\"anonymous\"></script>\r\n</body>\r\n</html>]]></BackgroundReport>\r\n
        \   <Status>READY</Status>\r\n  </Response>\r\n</ApplicantScreening>"
  recorded_at: Wed, 28 Feb 2024 17:25:57 GMT
recorded_with: VCR 6.2.0
