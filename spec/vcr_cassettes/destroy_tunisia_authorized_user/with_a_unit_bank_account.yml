---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 30 May 2024 17:58:36 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1781'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"6f5-yMCSdkQW5xY+cjBt47XyxFgOQq8"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 30 May 2024 17:58:36 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 30 May 2024 17:58:37 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '459'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1cb-eqEJ7nSrmKEVymqH2N08/XN+nto"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"vKXeMMjJNEpNuSQmqYwKWc7sLvb3WaDAbZEq17Y0AMG2qrlSg+g0yi2hlN4P+JTnRuN7GJkXDq+JCF2Ov2aFEeHXfzBm+98Mlh08lHij4uPOqm/5YW3dUte7hPeLDEXp5FE6ZbrTZsuHwJFpwiOk6JAnk+IWDSG2HFnl8dgIKv9xx3Wm/2Rz5QzNrqILnBFugqoNsla4hOwiGlaD0zdDHtbPEluJmpvyidl1YIdpa8Bk9fqXIwgSTz/QAPmEiifpNUwq0WFOunxGt8u7413fsrGA59v3UZpLVxV3PxyQD0sLpldsAYQptkxJgz3+BSyGb8ywNlyKcKa5HVUdu8Fb6E/3fl9mlw","phoneLast4Digits":"2775"}}}'
  recorded_at: Thu, 30 May 2024 17:58:36 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write
        cards-write customers cards cards-sensitive cards-sensitive-write transactions
        authorizations accounts statements customers-write","verificationToken":"vKXeMMjJNEpNuSQmqYwKWc7sLvb3WaDAbZEq17Y0AMG2qrlSg+g0yi2hlN4P+JTnRuN7GJkXDq+JCF2Ov2aFEeHXfzBm+98Mlh08lHij4uPOqm/5YW3dUte7hPeLDEXp5FE6ZbrTZsuHwJFpwiOk6JAnk+IWDSG2HFnl8dgIKv9xx3Wm/2Rz5QzNrqILnBFugqoNsla4hOwiGlaD0zdDHtbPEluJmpvyidl1YIdpa8Bk9fqXIwgSTz/QAPmEiifpNUwq0WFOunxGt8u7413fsrGA59v3UZpLVxV3PxyQD0sLpldsAYQptkxJgz3+BSyGb8ywNlyKcKa5HVUdu8Fb6E/3fl9mlw","verificationCode":"<NELCO_CLIENT_ID>1"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 30 May 2024 17:58:37 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '619'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"26b-xFmIcFs45XmMKhOCIrb8V5mMVoE"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvKzEyNDg5MjEyNzc1IiwiZXhwIjoiMjAyNC0wNS0zMVQxNzo1ODozNy43NDVaIiwianRpIjpudWxsLCJvcmdJZCI6IjQzODEiLCJzY29wZSI6ImFjY291bnRzLXdyaXRlIGNhcmRzLXdyaXRlIGN1c3RvbWVycyBjYXJkcyBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyBhdXRob3JpemF0aW9ucyBhY2NvdW50cyBzdGF0ZW1lbnRzIGN1c3RvbWVycy13cml0ZSIsImN1c3RvbWVySWQiOiIxNDk3NzU2IiwidXNlclR5cGUiOiJjdXN0b21lciJ9E8jQLVE91VyCJ1Dj8sswiG0nk50j6kIjMkP6WLFOT13OIQyxs9hheacfww0qQPfI3C-YcyCK-qmUdePrkMK1AQ","expiresIn":86400}}}'
  recorded_at: Thu, 30 May 2024 17:58:37 GMT
- request:
    method: delete
    uri: "<UNIT_API_URL>/customers/1497756/authorized-users"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"removeAuthorizedUsers","attributes":{"authorizedUsersEmails":["john@<POSTGRES_USER>.co"]}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvKzEyNDg5MjEyNzc1IiwiZXhwIjoiMjAyNC0wNS0zMVQxNzo1ODozNy43NDVaIiwianRpIjpudWxsLCJvcmdJZCI6IjQzODEiLCJzY29wZSI6ImFjY291bnRzLXdyaXRlIGNhcmRzLXdyaXRlIGN1c3RvbWVycyBjYXJkcyBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyBhdXRob3JpemF0aW9ucyBhY2NvdW50cyBzdGF0ZW1lbnRzIGN1c3RvbWVycy13cml0ZSIsImN1c3RvbWVySWQiOiIxNDk3NzU2IiwidXNlclR5cGUiOiJjdXN0b21lciJ9E8jQLVE91VyCJ1Dj8sswiG0nk50j6kIjMkP6WLFOT13OIQyxs9hheacfww0qQPfI3C-YcyCK-qmUdePrkMK1AQ
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 30 May 2024 17:58:38 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1664'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"680-eX6GxwF6ROdEEWvYPl9SzlhEIpk"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 30 May 2024 17:58:38 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 30 May 2024 17:58:38 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1664'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"680-eX6GxwF6ROdEEWvYPl9SzlhEIpk"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 30 May 2024 17:58:38 GMT
recorded_with: VCR 6.2.0
