---
http_interactions:
- request:
    method: get
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?NearLatLong=42.3314,-83.0<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>8&PageSize=20
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/7.4.0 (darwin23 arm64) Ruby/3.2.7
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '7267'
      Connection:
      - keep-alive
      Date:
      - Mon, 24 Mar 2025 19:37:12 GMT
      X-Api-Domain:
      - api.twilio.com
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key
      Access-Control-Allow-Credentials:
      - 'true'
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Twilio-Request-Id:
      - RQ58712575356a09233b40392db1684f90
      Twilio-Request-Duration:
      - '0.404'
      Twilio-Concurrent-Requests:
      - '1'
      X-Verification-Disclosure:
      - 'Message Accepted. Pending content verification for US/Canada destination
        numbers:'
      X-Shenanigans:
      - none
      X-Powered-By:
      - AT-5000
      X-Home-Region:
      - us1
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 aa7679f2d01b23d9a66bfa6e92991b04.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - EWR53-C2
      X-Amz-Cf-Id:
      - ZNrZCQQXOubAz-Y2urjXpkrLN9xe3L4qA-RHIvutGN_ZR-y9CINytw==
    body:
      encoding: UTF-8
      string: '{"available_phone_numbers": [{"phone_number": "+***********", "rate_center":
        "ROCKWOOD", "locality": "Rockwood", "region": "MI", "address_requirements":
        "none", "friendly_name": "(*************", "longitude": "-83.226700", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.074600", "postal_code": "48173"},
        {"phone_number": "+17344158548", "rate_center": "NEW BOSTON", "locality":
        "New Boston", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-83.391300", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.126000", "postal_code": "48164"}, {"phone_number":
        "+15863278772", "rate_center": "ROSEVILLE", "locality": "Roseville", "region":
        "MI", "address_requirements": "none", "friendly_name": "(*************", "longitude":
        "-82.936100", "iso_country": "US", "beta": <SCHEDULED_JOBS>, "lata": "340",
        "capabilities": {"voice": true, "SMS": true, "MMS": true}, "latitude": "42.509600",
        "postal_code": "48066"}, {"phone_number": "+15864880415", "rate_center": "UTICA",
        "locality": "Utica", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-83.033500", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.626100", "postal_code": "48315"}, {"phone_number":
        "+15177014923", "rate_center": "CLINTON", "locality": "Clinton", "region":
        "MI", "address_requirements": "none", "friendly_name": "(*************", "longitude":
        "-82.919900", "iso_country": "US", "beta": <SCHEDULED_JOBS>, "lata": "340",
        "capabilities": {"voice": true, "SMS": true, "MMS": true}, "latitude": "42.587000",
        "postal_code": "49236"}, {"phone_number": "+17344990353", "rate_center": "ROMULUS",
        "locality": "Romulus", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-83.316600", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.203000", "postal_code": "48174"}, {"phone_number":
        "+173<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>309604", "rate_center": "WYANDOTTE",
        "locality": "Wyandotte", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-83.165000", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.195000", "postal_code": "48192"}, {"phone_number":
        "+17344724203", "rate_center": "TRENTON", "locality": "Trenton", "region":
        "MI", "address_requirements": "none", "friendly_name": "(*************", "longitude":
        "-83.218200", "iso_country": "US", "beta": <SCHEDULED_JOBS>, "lata": "340",
        "capabilities": {"voice": true, "SMS": true, "MMS": true}, "latitude": "42.133100",
        "postal_code": "48183"}, {"phone_number": "+173<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>304386",
        "rate_center": "WYANDOTTE", "locality": "Wyandotte", "region": "MI", "address_requirements":
        "none", "friendly_name": "(*************", "longitude": "-83.165000", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.195000", "postal_code": "48192"},
        {"phone_number": "+15863277830", "rate_center": "ROSEVILLE", "locality": "Roseville",
        "region": "MI", "address_requirements": "none", "friendly_name": "(*************",
        "longitude": "-82.936100", "iso_country": "US", "beta": <SCHEDULED_JOBS>,
        "lata": "340", "capabilities": {"voice": true, "SMS": true, "MMS": true},
        "latitude": "42.509600", "postal_code": "48066"}, {"phone_number": "+173<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>323795",
        "rate_center": "ROMULUS", "locality": "Romulus", "region": "MI", "address_requirements":
        "none", "friendly_name": "(*************", "longitude": "-83.316600", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.203000", "postal_code": "48174"},
        {"phone_number": "+15863278427", "rate_center": "ROSEVILLE", "locality": "Roseville",
        "region": "MI", "address_requirements": "none", "friendly_name": "(*************",
        "longitude": "-82.936100", "iso_country": "US", "beta": <SCHEDULED_JOBS>,
        "lata": "340", "capabilities": {"voice": true, "SMS": true, "MMS": true},
        "latitude": "42.509600", "postal_code": "48066"}, {"phone_number": "+17342803834",
        "rate_center": "WYANDOTTE", "locality": "Wyandotte", "region": "MI", "address_requirements":
        "none", "friendly_name": "(*************", "longitude": "-83.165000", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.195000", "postal_code": "48192"},
        {"phone_number": "+15863278762", "rate_center": "ROSEVILLE", "locality": "Roseville",
        "region": "MI", "address_requirements": "none", "friendly_name": "(*************",
        "longitude": "-82.936100", "iso_country": "US", "beta": <SCHEDULED_JOBS>,
        "lata": "340", "capabilities": {"voice": true, "SMS": true, "MMS": true},
        "latitude": "42.509600", "postal_code": "48066"}, {"phone_number": "+15863277910",
        "rate_center": "ROSEVILLE", "locality": "Roseville", "region": "MI", "address_requirements":
        "none", "friendly_name": "(*************", "longitude": "-82.936100", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.509600", "postal_code": "48066"},
        {"phone_number": "+15863001487", "rate_center": "MT CLEMENS", "locality":
        "Mount Clemens", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-82.878000", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.597300", "postal_code": "48043"}, {"phone_number":
        "+15864964380", "rate_center": "ROSEVILLE", "locality": "Roseville", "region":
        "MI", "address_requirements": "none", "friendly_name": "(*************", "longitude":
        "-82.936100", "iso_country": "US", "beta": <SCHEDULED_JOBS>, "lata": "340",
        "capabilities": {"voice": true, "SMS": true, "MMS": true}, "latitude": "42.509600",
        "postal_code": "48066"}, {"phone_number": "+17344419972", "rate_center": "ROCKWOOD",
        "locality": "Rockwood", "region": "MI", "address_requirements": "none", "friendly_name":
        "(*************", "longitude": "-83.226700", "iso_country": "US", "beta":
        <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true, "SMS": true,
        "MMS": true}, "latitude": "42.074600", "postal_code": "48173"}, {"phone_number":
        "+17342922657", "rate_center": "WAYNE", "locality": "Wayne", "region": "MI",
        "address_requirements": "none", "friendly_name": "(*************", "longitude":
        "-83.387700", "iso_country": "US", "beta": <SCHEDULED_JOBS>, "lata": "340",
        "capabilities": {"voice": true, "SMS": true, "MMS": true}, "latitude": "42.277100",
        "postal_code": "48184"}, {"phone_number": "+***********", "rate_center": "SOUTHFIELD",
        "locality": "Southfield", "region": "MI", "address_requirements": "none",
        "friendly_name": "(*************", "longitude": "-83.221900", "iso_country":
        "US", "beta": <SCHEDULED_JOBS>, "lata": "340", "capabilities": {"voice": true,
        "SMS": true, "MMS": true}, "latitude": "42.473400", "postal_code": "48033"}],
        "uri": "/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?NearLatLong=42.3314%2C-83.0<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>8&PageSize=20"}'
  recorded_at: Mon, 24 Mar 2025 19:37:12 GMT
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers.json
    body:
      encoding: UTF-8
      string: PhoneNumber=%2B***********&SmsUrl=http%3A%2F%2Falever.lvh.me%3A3001%2Ftelephony%2Fcollections%2Fcommunications%2Fsms_replies
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      User-Agent:
      - twilio-ruby/7.4.0 (darwin23 arm64) Ruby/3.2.7
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Content-Type:
      - application/json;charset=utf-8
      Content-Length:
      - '1396'
      Connection:
      - keep-alive
      Date:
      - Mon, 24 Mar 2025 19:37:13 GMT
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQc7eec694ad69300062b8d1<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>34a2aa2c
      Twilio-Request-Duration:
      - '1.069'
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 2f04b33f21912079fa9d6afaee0c5dd0.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - EWR53-C2
      X-Amz-Cf-Id:
      - CcyDslcsTd-a2P4TWfpgeGB8k3FN57VqT4ESEGDDNONnwuarXZsVjw==
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      Vary:
      - Origin
    body:
      encoding: UTF-8
      string: '{"account_sid": "<TWILIO_SID>", "address_requirements": "none", "address_sid":
        null, "api_version": "2010-04-01", "beta": <SCHEDULED_JOBS>, "bundle_sid":
        null, "capabilities": {"fax": <SCHEDULED_JOBS>, "mms": true, "sms": true,
        "voice": true}, "date_created": "Mon, 24 Mar 2025 19:37:13 +0000", "date_updated":
        "Mon, 24 Mar 2025 19:37:13 +0000", "emergency_address_sid": null, "emergency_address_status":
        "unregistered", "emergency_status": "Active", "friendly_name": "(*************",
        "identity_sid": null, "origin": "twilio", "phone_number": "+***********",
        "sid": "PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621",
        "sms_application_sid": "", "sms_fallback_method": "POST", "sms_fallback_url":
        "", "sms_method": "POST", "sms_url": "http://alever.lvh.me:3001/telephony/collections/communications/sms_replies",
        "status": "in-use", "status_callback": "", "status_callback_method": "POST",
        "subresource_uris": {"assigned_add_ons": "/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers/PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621/AssignedAddOns.json"},
        "trunk_sid": null, "uri": "/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers/PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621.json",
        "voice_application_sid": null, "voice_caller_id_lookup": <SCHEDULED_JOBS>,
        "voice_fallback_method": "POST", "voice_fallback_url": null, "voice_method":
        "POST", "voice_receive_mode": "voice", "voice_url": null}'
  recorded_at: Mon, 24 Mar 2025 19:37:13 GMT
- request:
    method: post
    uri: https://messaging.twilio.com/v1/Services/<TWILIO_COLLECTIONS_SERVICE_SID>/PhoneNumbers
    body:
      encoding: UTF-8
      string: PhoneNumberSid=PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      User-Agent:
      - twilio-ruby/7.4.0 (darwin23 arm64) Ruby/3.2.7
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json;charset=utf-8
      Content-Length:
      - '460'
      Connection:
      - keep-alive
      Date:
      - Mon, 24 Mar 2025 19:37:14 GMT
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQd9614bb6b149b28794f1f2dff21716ef
      Twilio-Request-Duration:
      - '0.316'
      X-Home-Region:
      - us1
      X-Api-Domain:
      - messaging.twilio.com
      Strict-Transport-Security:
      - max-age=********
      X-Cache:
      - Miss from cloudfront
      Via:
      - 1.1 1af2e71d065fc2eea37b6b349c843830.cloudfront.net (CloudFront)
      X-Amz-Cf-Pop:
      - JFK50-P9
      X-Amz-Cf-Id:
      - 0HfhLODT85EcwkV1_OSNg9Lk8WrMwgZc2aOc2DOLGGutFZ9L731XqA==
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      Vary:
      - Origin
    body:
      encoding: UTF-8
      string: '{"account_sid": "<TWILIO_SID>", "capabilities": ["MMS", "SMS", "Voice"],
        "country_code": "US", "date_created": "2025-03-24T19:37:14Z", "date_updated":
        "2025-03-24T19:37:14Z", "phone_number": "+***********", "service_sid": "<TWILIO_COLLECTIONS_SERVICE_SID>",
        "sid": "PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621",
        "url": "https://messaging.twilio.com/v1/Services/<TWILIO_COLLECTIONS_SERVICE_SID>/PhoneNumbers/PNb0b905c777d<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>c25c1d1c393c4f0d621"}'
  recorded_at: Mon, 24 Mar 2025 19:37:14 GMT
recorded_with: VCR 6.3.1
