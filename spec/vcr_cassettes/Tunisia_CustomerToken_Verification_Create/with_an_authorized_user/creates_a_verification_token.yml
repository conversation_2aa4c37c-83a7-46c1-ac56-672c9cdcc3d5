---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '338'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"152-2ac9Px9RhNdszPGfffmz7mi2/0k"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"AerCGIM8LqGU2boemjDhtyw1Q39MYvc8CGgbvrNqKIAktuSyMPeINOeGq4kdR7mP7IWQYsJbSRwACXK8txfZQNo6UZy8eECSHRtgv8kKSzXturJ_y2VOc4bWqz01NQq9VVTTfGtj7jp9ja_EqnECec8t1gGs2s8mJ7P21_DjSejHu9fv2Zz4Sa5y3tWMrJaBOevzW-B6WqH_Y6ehTfNIblvNiIwbHebeDapgFXFRaDmPaTijnlMh8_1tjnjumUs"}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
recorded_with: VCR 6.2.0
