---
http_interactions:
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.5.1
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 204
      message: No Content
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 07 Aug 2021 16:59:54 GMT
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTYyODM1NTU5NCwiZXhwIjoxNjI5NTY1MTk0LCJqdGkiOiI3ZTE4YzJkMi04NjJlLTQxMTUtYTAwZi0wZGJmOTM2N2IyN2MifQ.jJLhItc_LquDignFD4Bq4hPQe4SbZ7NJU40zlvp6RnA
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 86de683a-3666-472c-848c-da9bc14f3a55
      X-Runtime:
      - '0.006845'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      Via:
      - 1.1 vegur
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Sat, 07 Aug 2021 16:59:28 GMT
- request:
    method: get
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/screening_groups/713fe336-caa0-41bd-9b55-2a179d1578e6
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTYyODM1NTU5NCwiZXhwIjoxNjI5NTY1MTk0LCJqdGkiOiI3ZTE4YzJkMi04NjJlLTQxMTUtYTAwZi0wZGJmOTM2N2IyN2MifQ.jJLhItc_LquDignFD4Bq4hPQe4SbZ7NJU40zlvp6RnA
      User-Agent:
      - Faraday v1.5.1
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 07 Aug 2021 16:59:55 GMT
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept-Encoding
      Etag:
      - W/"a49365237a3506152649ffb5d0a0d775"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _the_closing_docs_session=Vmh3NG5kL2pQcmgwd0oraWZvbUltWEQ4MWVBTGtNOGxyMXdiMGpvUmVlSVVucy9VZW56bkZDazVaSTdRRFZ3MUpmWEpUVG80bkNFVkdldEJ6RmxKdmw3WVlNbzczTkl0Q2dzRnNhRzRETGJrRmJKY1dNMDBkQjBkMy92Z2VKN2hIbGVGeis0NC9oZlhXV3ZQNURiNmo1Vnlta2tUc1NQWWc5WktjZ2hCMWl3cXRTRkROdUpGRnpGUUlPQ2NUc1VoLS1xUlZ3N2dwa0U4TVh0R0tPbzZkczFnPT0%3D--73dbfe23be1136fb95a7dc2c170e8a6d7a5bb89b;
        path=/; expires=Sat, 21 Aug 2021 16:59:55 GMT; secure; HttpOnly
      X-Request-Id:
      - 71bf9edf-7244-4795-88ba-37e0feaaee3f
      X-Runtime:
      - '0.055325'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"id":"713fe336-caa0-41bd-9b55-2a179d1578e6","screenings":[{"id":"f176485a-c9c4-4c4a-a1dd-8a2b4ad45080","applicant_first_name":"Ellen","applicant_last_name":"Ripley","applicant_email":"<EMAIL>","screening_status":"completed","reason_completed":"finished"}],"status":"completed","property_street_address":"90851
        Gottlieb Street","unit_number":"90851 Gottlieb Street","monthly_rent_cents":43148,"approval_recommendation":"below","is_decision_maker_paying":true,"is_report_expired":<SSL_ENABLED>,"is_expired":<SSL_ENABLED>,"decision_maker_display_name":"Alever","income_multiplier_threshold":2.5,"correlation_id":null,"created_at_timestamp":1628272962104.8,"number_of_invitations_sent":2,"last_invitation_sent_timestamp":1628288551238.139}'
  recorded_at: Sat, 07 Aug 2021 16:59:29 GMT
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.5.1
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 204
      message: No Content
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 07 Aug 2021 16:59:55 GMT
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTYyODM1NTU5NSwiZXhwIjoxNjI5NTY1MTk1LCJqdGkiOiIxMjYxZDVjNS0wNTEyLTQyNGUtODdhZi00NTk1MTkyOTk1NDgifQ.nRovTFZb8ERZn9rWU8Ekm_xXN1mo4cIAq6CvTfn7_IY
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 1dd5a5cd-399f-46dd-8c63-75c3b71800ce
      X-Runtime:
      - '0.007313'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      Via:
      - 1.1 vegur
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Sat, 07 Aug 2021 16:59:29 GMT
- request:
    method: get
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/screening_groups/713fe336-caa0-41bd-9b55-2a179d1578e6/report/data
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTYyODM1NTU5NSwiZXhwIjoxNjI5NTY1MTk1LCJqdGkiOiIxMjYxZDVjNS0wNTEyLTQyNGUtODdhZi00NTk1MTkyOTk1NDgifQ.nRovTFZb8ERZn9rWU8Ekm_xXN1mo4cIAq6CvTfn7_IY
      User-Agent:
      - Faraday v1.5.1
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 07 Aug 2021 16:59:55 GMT
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept-Encoding, Origin
      Etag:
      - W/"1f301f63d0f46501ca59f00150b8ca2e"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _the_closing_docs_session=amMwR1RNM21IeUVud3NmMllOQW9ZUTFENFFZNzZZZGtDZzRZajRmZzdlT3BCOE9pNE9vWVFyTzZUWklrZkdCZjgvalJ6N3N0QXlJbzVBZXRBMHZING03OXU0TWRmL296eXY0b29leXZFRjlxNkNGOFhhUTdjWWkyb2ZORVErTE1WNWVscHhySkxwUkhoVU53S2F2QUJjS0dEZkl4MDlJTlZQdnplSnJJSEY3Q010bTFOR0dSVFI5T0hBM1J6MTl1LS1IeURzWjRWVTFMWCtNQzNUS3RldktBPT0%3D--507308a73e0e5025dbef36366e2dd99e00056ab9;
        path=/; expires=Sat, 21 Aug 2021 16:59:55 GMT; secure; HttpOnly
      X-Request-Id:
      - 4ea06be0-cb2a-4c65-a718-aac89db965d5
      X-Runtime:
      - '0.060654'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"screening_id":"f176485a-c9c4-4c4a-a1dd-8a2b4ad45080","recurring":{"total":2800000,"two_month_avg_monthly":0,"three_month_avg_monthly":0,"six_month_avg_monthly":200000,"nine_month_avg_monthly":266667,"twelve_month_avg_monthly":null},"non_recurring":{"total":400000,"two_month_avg_monthly":0,"three_month_avg_monthly":133333,"six_month_avg_monthly":66667,"nine_month_avg_monthly":44444,"twelve_month_avg_monthly":null},"summary_timespan_length":273,"bank_connections":[{"accounts":[{"name":"Saving
        Plus","type":"Savings","available_balance":200000,"current_balance":305000,"average_balance":305000,"holder_name":"Robin"},{"name":"Checking
        Account","type":"Checking","available_balance":150000,"current_balance":160000,"average_balance":-1476364,"holder_name":"Robin"},{"name":"Saving
        Plus","type":"Savings","available_balance":200000,"current_balance":305000,"average_balance":305000,"holder_name":"Robin"}],"income":{"recurring":{"total":2800000,"two_month_avg_monthly":0,"three_month_avg_monthly":0,"six_month_avg_monthly":200000,"nine_month_avg_monthly":266667,"twelve_month_avg_monthly":null,"income_streams":[{"name":"The
        Closing Docs Payroll","transactions":[{"amount":400000,"name":"The Closing
        Docs Payroll","date":"05/01/2021"},{"amount":400000,"name":"The Closing Docs
        Payroll","date":"04/01/2021"},{"amount":400000,"name":"The Closing Docs Payroll","date":"03/01/2021"},{"amount":400000,"name":"The
        Closing Docs Payroll","date":"02/01/2021"},{"amount":400000,"name":"The Closing
        Docs Payroll","date":"01/01/2021"},{"amount":400000,"name":"The Closing Docs
        Payroll","date":"12/01/2020"},{"amount":400000,"name":"The Closing Docs Payroll","date":"11/01/2020"},{"amount":400000,"name":"The
        Closing Docs Payroll","date":"10/01/2020"},{"amount":400000,"name":"The Closing
        Docs Payroll","date":"09/01/2020"}],"total":3600000,"monthly_income":396694,"start_date":"09/01/2020","end_date":"05/01/2021"}]},"non_recurring":{"total":400000,"two_month_avg_monthly":0,"three_month_avg_monthly":133333,"six_month_avg_monthly":66667,"nine_month_avg_monthly":44444,"twelve_month_avg_monthly":null,"income_streams":[{"name":"The
        Closing Docs Irregular Income","transactions":[{"amount":400000,"name":"The
        Closing Docs Irregular Income","date":"06/01/2021"}],"total":400000,"monthly_income":400000,"start_date":"06/01/2021","end_date":"06/01/2021"}]},"income_streams":[{"name":"The
        Closing Docs Payroll","transactions":[{"amount":400000,"name":"The Closing
        Docs Payroll","date":"05/01/2021"},{"amount":400000,"name":"The Closing Docs
        Payroll","date":"04/01/2021"},{"amount":400000,"name":"The Closing Docs Payroll","date":"03/01/2021"},{"amount":400000,"name":"The
        Closing Docs Payroll","date":"02/01/2021"},{"amount":400000,"name":"The Closing
        Docs Payroll","date":"01/01/2021"},{"amount":400000,"name":"The Closing Docs
        Payroll","date":"12/01/2020"},{"amount":400000,"name":"The Closing Docs Payroll","date":"11/01/2020"},{"amount":400000,"name":"The
        Closing Docs Payroll","date":"10/01/2020"},{"amount":400000,"name":"The Closing
        Docs Payroll","date":"09/01/2020"}],"total":3600000,"monthly_income":396694,"start_date":"09/01/2020","end_date":"05/01/2021"}],"non_recurring_deposit_streams":[{"name":"The
        Closing Docs Irregular Income","transactions":[{"amount":400000,"name":"The
        Closing Docs Irregular Income","date":"06/01/2021"}],"total":400000,"monthly_income":400000,"start_date":"06/01/2021","end_date":"06/01/2021"}],"start_date":"09/01/2020","end_date":"06/01/2021","transaction_timespan_length":273},"institution_name":"Dag
        Site"}],"identity":{"full_name":"Ellen Ripley","email":"<EMAIL>"},"income_explanation":null,"reason_completed":"finished","created_at":"08/06/2021","income_multiplier_threshold":2.5,"income_multiplier":0.0,"income_multiplier_result":"below"}'
  recorded_at: Sat, 07 Aug 2021 16:59:29 GMT
recorded_with: VCR 6.0.0
