---
http_interactions:
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:01 GMT
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065461&sid=************************************&s=Gk%2Fff5pFEF%2FiG%2F2%2By1LY0zoIbAZDVxSb3bsoJsMgdmw%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065461&sid=************************************&s=Gk%2Fff5pFEF%2FiG%2F2%2By1LY0zoIbAZDVxSb3bsoJsMgdmw%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept,Accept-Encoding
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2MSwiZXhwIjoxNzE3Mjc1MDYxLCJqdGkiOiIzMjNhN2Q1Ny1mYjlkLTQwOTctYjAwZi1mY2M1YjA4MjM3YTYifQ.Az5WMd4n_8FM32brohqz-3W7ZC2U82MzKHFmuXBJ9qA
      Etag:
      - W/"5c42fcbad7a8a2cb16f516fbb2df4d30"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      X-Request-Id:
      - 306a34f9-387f-45cd-bba4-a405d2173a88
      X-Runtime:
      - '0.036888'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"token":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2MSwiZXhwIjoxNzE3Mjc1MDYxLCJqdGkiOiIzMjNhN2Q1Ny1mYjlkLTQwOTctYjAwZi1mY2M1YjA4MjM3YTYifQ.Az5WMd4n_8FM32brohqz-3W7ZC2U82MzKHFmuXBJ9qA"}'
  recorded_at: Sat, 18 May 2024 20:51:01 GMT
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/screening_groups/aecb527a-ba4f-43f9-9ded-1112ed7e2bb2/complete
    body:
      encoding: UTF-8
      string: ''
    headers:
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2MSwiZXhwIjoxNzE3Mjc1MDYxLCJqdGkiOiIzMjNhN2Q1Ny1mYjlkLTQwOTctYjAwZi1mY2M1YjA4MjM3YTYifQ.Az5WMd4n_8FM32brohqz-3W7ZC2U82MzKHFmuXBJ9qA
      User-Agent:
      - Faraday v1.10.3
      Content-Length:
      - '0'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 204
      message: No Content
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:01 GMT
      Content-Length:
      - '0'
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065461&sid=************************************&s=Gk%2Fff5pFEF%2FiG%2F2%2By1LY0zoIbAZDVxSb3bsoJsMgdmw%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065461&sid=************************************&s=Gk%2Fff5pFEF%2FiG%2F2%2By1LY0zoIbAZDVxSb3bsoJsMgdmw%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Cache-Control:
      - no-cache
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      Set-Cookie:
      - _the_closing_docs_session=ME1UNG13ZHJsai9HNU5oeHlka3FVYVE2Tm1oQlZLdzYrSHZ6U0I5UTVyTTN4YVlGd014d2ZpRmlrbXZPd3RzK2I3ZmRZWTNMV3ZsZXZ5V3VuRkNJb1FXUmc2bitmTkJMTjRsSG02K3BpOEFxRzI3UTJseFdUaHVnVWxNR2RwQ08wVENsc1FBN29SR29LNHJla0VCaUZZeTY4aDhBSjNoMDVySFJIS0k2dUFLWU52Q2pWcVZYYldvdnprVkJTTHNlLS0vTTR6SEtYU3dLNFZkbFJkQWV3T3pRPT0%3D--f6a16a5c17fcd19ac889a4e13196cfa984ceb3f3;
        path=/; expires=Sat, 01 Jun 2024 20:51:01 GMT; secure; HttpOnly; SameSite=None
      X-Request-Id:
      - 960bb09c-b2f7-4e05-aad9-42a008c3cb33
      X-Runtime:
      - '0.131244'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Vary:
      - Origin
      Via:
      - 1.1 vegur
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Sat, 18 May 2024 20:51:01 GMT
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:04 GMT
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept,Accept-Encoding
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiIzNzM3ZTg3OC1lZTljLTQ1OTQtYWVhMS0zMzQxMjg4YzFiYmEifQ.JJamFxNjonsJBEjlt2x5whTWOD1DypcwTFPVTPyfs7g
      Etag:
      - W/"b255a7d8a54d6cc983e813be02c2b195"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      X-Request-Id:
      - 76846345-d8f1-4440-bca6-e3749c1114de
      X-Runtime:
      - '0.036903'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"token":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiIzNzM3ZTg3OC1lZTljLTQ1OTQtYWVhMS0zMzQxMjg4YzFiYmEifQ.JJamFxNjonsJBEjlt2x5whTWOD1DypcwTFPVTPyfs7g"}'
  recorded_at: Sat, 18 May 2024 20:51:04 GMT
- request:
    method: get
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/screening_groups/aecb527a-ba4f-43f9-9ded-1112ed7e2bb2
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiIzNzM3ZTg3OC1lZTljLTQ1OTQtYWVhMS0zMzQxMjg4YzFiYmEifQ.JJamFxNjonsJBEjlt2x5whTWOD1DypcwTFPVTPyfs7g
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:04 GMT
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept, Accept-Encoding, Origin
      Etag:
      - W/"8e3427d5bb4cb0caf8daff61027d0101"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      Set-Cookie:
      - _the_closing_docs_session=Mk93dWdwSFlyc1Jwb2NJZlU1QlRqQ0g2bHpqOTEvd3JHVm4wbEZDeEhZY3JDSlZTSVNMWHRGd0xRTG9IcDRKeXlUYmZsQ0k0MzhtSVBDbnovc0xGSWFzZVFWUTA5Rmh4aS9xOGxkV0w4RElUQnQ5N1J6RkRRMzF4ZnRWOUNzNkNpemNvVGdMdEhPc0R3SDNQdFFjQzZ1VHhBR1I2eE5DL3JsNjR0b3ZHa0NTUWRHamlzOHVLWnJCdGdMUVBBMld5LS1CRVR4WDF0WWZBQklCUC8zTnVYS053PT0%3D--da3f6fcab7d16df0d1dcf0845ae316053f19b160;
        path=/; expires=Sat, 01 Jun 2024 20:51:05 GMT; secure; HttpOnly; SameSite=None
      X-Request-Id:
      - e2a47799-2ed8-4e60-9026-eb9e64143efd
      X-Runtime:
      - '0.031677'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"id":"aecb527a-ba4f-43f9-9ded-1112ed7e2bb2","screenings":[{"id":"f0a873bb-a718-4b87-93cb-0fc0541482e9","applicant_first_name":"John","applicant_last_name":"Smith","applicant_email":"<EMAIL>","applicant_phone_number":null,"screening_status":"completed","reason_completed":"force_completed"},{"id":"e3008afd-7fc6-4a47-a66b-08d4f755dc2c","applicant_first_name":"Ellen","applicant_last_name":"Ripley","applicant_email":"<EMAIL>","applicant_phone_number":"248
        921 2775","screening_status":"completed","reason_completed":"force_completed"}],"status":"completed","property_name":"959
        Bernarda Spur","property_street_address":"959 Bernarda Spur","unit_number":"959
        Bernarda Spur","monthly_rent_cents":43223,"approval_recommendation":"unavailable","is_decision_maker_paying":true,"is_report_expired":<SSL_ENABLED>,"is_expired":<SSL_ENABLED>,"decision_maker_display_name":"Alever","income_multiplier_threshold":2.5,"correlation_id":null,"correlation_id2":null,"created_at_timestamp":1716063615065.95,"number_of_invitations_sent":1,"last_invitation_sent_timestamp":1716063615132.7942,"asset_verification_report_completed":<SSL_ENABLED>}'
  recorded_at: Sat, 18 May 2024 20:51:05 GMT
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:04 GMT
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept,Accept-Encoding
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiI5MDM2MjI4ZS0wOTAxLTRjY2MtYThkOS1iZDk0MTQ4OThmN2UifQ.eFP6yks-tdJz7G6iBabdqFBtP2GDvKnhpJqrUgwDUpY
      Etag:
      - W/"0138d9094e54814b6439417d1f6102f3"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      X-Request-Id:
      - cbd72914-f6e6-4e9c-a2de-41b77afe164d
      X-Runtime:
      - '0.007881'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"token":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiI5MDM2MjI4ZS0wOTAxLTRjY2MtYThkOS1iZDk0MTQ4OThmN2UifQ.eFP6yks-tdJz7G6iBabdqFBtP2GDvKnhpJqrUgwDUpY"}'
  recorded_at: Sat, 18 May 2024 20:51:05 GMT
- request:
    method: get
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/screening_groups/aecb527a-ba4f-43f9-9ded-1112ed7e2bb2/report/data
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI4MDk4MzE2OC0xYWM4LTQyNzgtODM2YS1iMDZiZGUxODgxMzYiLCJzY3AiOiJ0ZW5hbnQiLCJhdWQiOm51bGwsImlhdCI6MTcxNjA2NTQ2NSwiZXhwIjoxNzE3Mjc1MDY1LCJqdGkiOiI5MDM2MjI4ZS0wOTAxLTRjY2MtYThkOS1iZDk0MTQ4OThmN2UifQ.eFP6yks-tdJz7G6iBabdqFBtP2GDvKnhpJqrUgwDUpY
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - Cowboy
      Date:
      - Sat, 18 May 2024 20:51:05 GMT
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1716065465&sid=************************************&s=KpXjVo%2BZtt3e%2Bqb%2FG9d1ZYRSggG52ATZhOC%2B%2FAjOxcI%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - application/json; charset=utf-8
      Vary:
      - Accept, Accept-Encoding, Origin
      Etag:
      - W/"9fa9cfe4a515e5ace73130e09310f6dd"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Content-Security-Policy:
      - upgrade-insecure-requests; frame-ancestors 'self' jonisrealty.com ibecliving.com
        www.ibecliving.com
      Set-Cookie:
      - _the_closing_docs_session=bUJjaG9mRGhlV01OSEFjUWxzbVdOVFUxVEowRHFZbmZLU1Ywbjk3emxvNVpSaUFlanZkVzNBY0l5cGZLMjNXRURmNHpTbE1MS3psT0wvQjlrZVRic3NSWW9LYWhXeEU3M25jM2JsMm9Jejg2UC9oS2NrQVcvSytKdzFYWEwycnhJekZ6TVNIU3cvUUlqT3E1NG4rVkhxM05Za1pUL0dNSVlWWTYvTjRJRy9xYUZoUUFCZWdYUWhZTkRwTGgxUXdZLS1lREU2eTVaZ3JmYUxidnY5ZFNLSkhRPT0%3D--14a7c6ad70f667158cf2e0b244c693c7736e95a1;
        path=/; expires=Sat, 01 Jun 2024 20:51:05 GMT; secure; HttpOnly; SameSite=None
      X-Request-Id:
      - '09b250bf-aad9-4f98-994a-cbb7f3df6643'
      X-Runtime:
      - '0.096834'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: '{"reports":[{"screening_id":"f0a873bb-a718-4b87-93cb-0fc0541482e9","identity":{"full_name":"John
        Smith","email":"<EMAIL>"},"reason_completed":"not_completed","created_at":"05/18/2024"},{"screening_id":"e3008afd-7fc6-4a47-a66b-08d4f755dc2c","identity":{"full_name":"Ellen
        Ripley","email":"<EMAIL>"},"reason_completed":"not_completed","created_at":"05/18/2024"}],"recurring":{"total":0,"two_month_avg_monthly":null,"three_month_avg_monthly":null,"six_month_avg_monthly":null,"nine_month_avg_monthly":null,"twelve_month_avg_monthly":null},"non_recurring":{"total":0,"two_month_avg_monthly":null,"three_month_avg_monthly":null,"six_month_avg_monthly":null,"nine_month_avg_monthly":null,"twelve_month_avg_monthly":null},"summary_timespan_length":0,"created_at":"05/18/2024","income_multiplier_threshold":2.5,"income_multiplier":null,"income_multiplier_result":null,"property_monthly_rent":43223,"income_criteria_results":null}'
  recorded_at: Sat, 18 May 2024 20:51:05 GMT
recorded_with: VCR 6.2.0
