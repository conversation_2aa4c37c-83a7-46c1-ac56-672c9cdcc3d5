---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON>","last_name":"<PERSON>","email":"truedevs@<POSTGRES_USER>.co","address":"1234
        street","city":"Detroit","state":"MI","zip":"48223"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 24 Jun 2024 17:57:40 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Content-Length:
      - '777'
      Connection:
      - keep-alive
      Server:
      - Apache/2.4.59 (Debian)
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Vary:
      - Accept,Accept-Encoding
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11ef32533ff76b64ae0bb2ea
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11ef32533ff76b64ae0bb2ea","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"Tom","last_name":"Qin","email":"truedevs@<POSTGRES_USER>.co","address":"1234
        street","city":"Detroit","country":"USA","state":"MI","zip":"48223","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"created_user_id":"11e816510eb4209487387f58","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11ef32533ff76b64ae0bb2ea"}}}}'
  recorded_at: Mon, 24 Jun 2024 17:57:40 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"John Stemler","is_company":<SSL_ENABLED>,"contact_id":"11ef32533ff76b64ae0bb2ea","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"****************","exp_date":"1225","cvv":"999"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 24 Jun 2024 17:57:42 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Content-Length:
      - '973'
      Connection:
      - keep-alive
      Server:
      - Apache/2.4.59 (Debian)
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Vary:
      - Accept,Accept-Encoding
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef325340b135b2b36ab225
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11ef325340b135b2b36ab225","payment_method":"cc","title":null,"account_holder_name":"John
        Stemler","first_six":"411111","last_four":"1111","billing_address":null,"billing_zip":null,"exp_date":"1225","routing":null,"account_type":"visa","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11ef32533ff76b64ae0bb2ea","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":18,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":null,"dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"billing_country":null,"customer_id":null,"cau_summary_status_id":0,"cau_last_updated_ts":null,"card_bin":null,"created_user_id":"11e816510eb4209487387f58","token_import_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef325340b135b2b36ab225"}}}}'
  recorded_at: Mon, 24 Jun 2024 17:57:42 GMT
recorded_with: VCR 6.2.0
