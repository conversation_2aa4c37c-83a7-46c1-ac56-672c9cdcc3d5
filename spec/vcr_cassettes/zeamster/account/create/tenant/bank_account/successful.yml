---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"<PERSON>","is_company":"0","contact_id":"11e84cb95f943bf48d9b0a63","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","account_type":"checking","account_number":"37090","routing":"*********"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 30 Apr 2018 21:01:44 GMT
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e84cb9b010c52aae905968
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '391'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11e84cb9b010c52aae905968","payment_method":"ach","title":null,"account_holder_name":"Monte
        Paucek","first_six":"37090","last_four":"7090","billing_address":null,"billing_zip":null,"exp_date":null,"routing":"*********","account_type":"checking","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11e84cb95f943bf48d9b0a63","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":0,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":"PPD","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e84cb9b010c52aae905968"}}}}'
    http_version: 
  recorded_at: Mon, 30 Apr 2018 21:01:39 GMT
recorded_with: VCR 3.0.3
