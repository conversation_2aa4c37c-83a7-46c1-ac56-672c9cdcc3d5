---
http_interactions:
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/accountvaults"
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":null,"is_company":"0","contact_id":"11e84cb95f943bf48d9b0a63","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"1234","exp_date":"0919","cvv":"123"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 422
      message: Data Validation Failed.
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 21 May 2018 17:46:41 GMT
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '98'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"errors":{"account_number":["Account Number should contain at least
        13 characters."]}}'
    http_version: 
  recorded_at: Mon, 21 May 2018 17:46:35 GMT
recorded_with: VCR 3.0.3
