---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON><PERSON>","last_name":"<PERSON><PERSON><PERSON><PERSON>","email":"1<PERSON><PERSON>@crooksyost.net","address":"789
        Misc. Ave.","city":"Grand Rapids","state":"MI","zip":"49458"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.15.4
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache, public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 24 Jul 2019 23:00:56 GMT
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11e9ae66e542e5bab2a13e10
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '400'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11e9ae66e542e5bab2a13e10","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"Lavern","last_name":"Gusikowski1","email":"<EMAIL>","address":"789
        Misc. Ave.","city":"Grand Rapids","state":"MI","zip":"49458","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11e9ae66e542e5bab2a13e10"}}}}'
    http_version: 
  recorded_at: Wed, 24 Jul 2019 23:00:56 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":null,"is_company":<SSL_ENABLED>,"contact_id":"11e9ae66e542e5bab2a13e10","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"****************","exp_date":"0919","cvv":"123"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.15.4
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache, public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 24 Jul 2019 23:00:57 GMT
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e9ae66e5720f70c0a02975
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '404'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11e9ae66e5720f70c0a02975","payment_method":"cc","title":null,"account_holder_name":null,"first_six":"545454","last_four":"5454","billing_address":null,"billing_zip":null,"exp_date":"0919","routing":null,"account_type":"mc","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11e9ae66e542e5bab2a13e10","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":2,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":null,"dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"customer_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e9ae66e5720f70c0a02975"}}}}'
    http_version: 
  recorded_at: Wed, 24 Jul 2019 23:00:57 GMT
- request:
    method: get
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e9ae66e5720f70c0a02975
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache, public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 24 Jul 2019 23:00:57 GMT
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '404'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11e9ae66e5720f70c0a02975","payment_method":"cc","title":null,"account_holder_name":null,"first_six":"545454","last_four":"5454","billing_address":null,"billing_zip":null,"exp_date":"0919","routing":null,"account_type":"mc","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11e9ae66e542e5bab2a13e10","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":2,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":null,"dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"customer_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e9ae66e5720f70c0a02975"}}}}'
    http_version: 
  recorded_at: Wed, 24 Jul 2019 23:00:57 GMT
- request:
    method: get
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts/11e9ae66e542e5bab2a13e10
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache, public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 24 Jul 2019 23:00:57 GMT
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '400'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11e9ae66e542e5bab2a13e10","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"Lavern","last_name":"Gusikowski1","email":"<EMAIL>","address":"789
        Misc. Ave.","city":"Grand Rapids","state":"MI","zip":"49458","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11e9ae66e542e5bab2a13e10"}}}}'
    http_version: 
  recorded_at: Wed, 24 Jul 2019 23:00:57 GMT
recorded_with: VCR 4.0.0
