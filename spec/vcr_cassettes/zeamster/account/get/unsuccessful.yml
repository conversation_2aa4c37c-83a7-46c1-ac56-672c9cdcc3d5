---
http_interactions:
- request:
    method: get
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults/1234
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - df253
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache, public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 03 Jun 2019 21:16:30 GMT
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '86'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"name":"Not Found","message":"accountvault not found","code":0,"status":404}'
    http_version: 
  recorded_at: Mon, 03 Jun 2019 21:16:30 GMT
recorded_with: VCR 4.0.0
