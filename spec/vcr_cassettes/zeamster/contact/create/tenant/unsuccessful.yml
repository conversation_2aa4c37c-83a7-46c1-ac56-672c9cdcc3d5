---
http_interactions:
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/contacts"
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":null,"first_name":"<PERSON>","last_name":"<PERSON><PERSON>","email":"birdie_g<PERSON><PERSON><PERSON>@ratke.com","address":"9174
        Corwin Row","city":"North Siennabury","state":"Georgia","zip":"99827-0957"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 422
      message: Data Validation Failed.
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 21 May 2018 17:58:38 GMT
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '65'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"errors":{"location_id":["Location is required"]}}'
    http_version: 
  recorded_at: Mon, 21 May 2018 17:58:33 GMT
recorded_with: VCR 3.0.3
