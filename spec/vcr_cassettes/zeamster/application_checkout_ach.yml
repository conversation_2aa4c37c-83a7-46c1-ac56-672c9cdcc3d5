---
http_interactions:
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/contacts"
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON><PERSON>","last_name":"<PERSON><PERSON><PERSON>","email":"<EMAIL>","address":"1420
        Washington Blvd.","city":"Detroit","state":"Michigan","zip":"48226"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 21 May 2018 16:08:04 GMT
      Location:
      - "https://apiv2.sandbox.zeamster.com/v2/contacts/11e85d1124a9dda6ab2ffda2"
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '400'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11e85d1124a9dda6ab2ffda2","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"Kadin","last_name":"Kuvalis","email":"<EMAIL>","address":"1420
        Washington Blvd.","city":"Detroit","state":"Michigan","zip":"48226","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11e85d1124a9dda6ab2ffda2"}}}}'
    http_version: 
  recorded_at: Mon, 21 May 2018 16:07:59 GMT
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/accountvaults"
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"Kadin Kuvalis","is_company":"0","contact_id":"11e85d1124a9dda6ab2ffda2","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","account_type":"checking","account_number":"88931","routing":"*********"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 21 May 2018 16:08:04 GMT
      Location:
      - "https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e85d1124d1be029b46f738"
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '390'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11e85d1124d1be029b46f738","payment_method":"ach","title":null,"account_holder_name":"Kadin
        Kuvalis","first_six":"88931","last_four":"8931","billing_address":null,"billing_zip":null,"exp_date":null,"routing":"*********","account_type":"checking","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11e85d1124a9dda6ab2ffda2","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":0,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":"PPD","_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e85d1124d1be029b46f738"}}}}'
    http_version: 
  recorded_at: Mon, 21 May 2018 16:08:00 GMT
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/transactions"
    body:
      encoding: UTF-8
      string: '{"transaction":{"account_vault_id":"11e85d1124d1be029b46f738","contact_id":"11e85d1124a9dda6ab2ffda2","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","action":"debit","transaction_amount":"45.00"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 21 May 2018 16:08:06 GMT
      Location:
      - "https://apiv2.sandbox.zeamster.com/v2/transactions/11e85d1125b7401cb1b412c7"
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '821'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"transaction":{"id":"11e85d1125b7401cb1b412c7","payment_method":"ach","account_vault_id":"11e85d1124d1be029b46f738","recurring_id":null,"first_six":"88931","last_four":"8931","account_holder_name":"Kadin
        Kuvalis","transaction_amount":"45.00","description":null,"transaction_code":null,"avs":null,"batch":null,"order_num":"************","verbiage":null,"transaction_settlement_status":null,"effective_date":"2018-05-21T11:08:06.3095154-05:00","routing":"*********","return_date":null,"created_ts":**********,"modified_ts":**********,"transaction_api_id":null,"terms_agree":null,"notification_email_address":null,"notification_email_sent":true,"response_message":"","auth_amount":"45.00","auth_code":null,"status_id":131,"type_id":50,"location_id":"11e816510e9c03a6bcbc685e","reason_code_id":1000,"contact_id":"11e85d1124a9dda6ab2ffda2","billing_zip":null,"billing_street":null,"product_transaction_id":"11e816510ee9ec4285c8448a","tax":"0.00","customer_ip":null,"customer_id":null,"po_number":null,"avs_enhanced":null,"cvv_response":null,"billing_phone":null,"billing_city":null,"billing_state":null,"clerk_number":null,"tip_amount":"0.00","created_user_id":"11e816510eb4209487387f58","modified_user_id":"<ZEAMSTER_USER_ID>","settle_date":null,"charge_back_date":null,"void_date":null,"account_type":"checking","is_recurring":<SSL_ENABLED>,"is_accountvault":true,"transaction_c1":null,"transaction_c2":null,"transaction_c3":null,"additional_amounts":[],"terminal_serial_number":null,"entry_mode_id":"K","terminal_id":null,"quick_invoice_id":null,"ach_sec_code":"PPD","custom_data":null,"hosted_payment_page_id":null,"checkin_date":null,"checkout_date":null,"room_num":null,"room_rate":"0.00","advance_deposit":<SSL_ENABLED>,"no_show":<SSL_ENABLED>,"emv_receipt_data":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/transactions/11e85d1125b7401cb1b412c7"}}}}'
    http_version: 
  recorded_at: Mon, 21 May 2018 16:08:01 GMT
recorded_with: VCR 3.0.3
