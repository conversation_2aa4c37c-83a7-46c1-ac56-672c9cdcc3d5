---
http_interactions:
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/contacts"
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":"<PERSON><PERSON>","last_name":"<PERSON><PERSON>","email":"<EMAIL>","address":"27504
        Harber Street","city":"East Lera","state":"Alaska","zip":"30646-7716"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 21 Aug 2018 14:50:33 GMT
      Location:
      - "https://apiv2.sandbox.zeamster.com/v2/contacts/11e8a5518e92ceac9f7f78ce"
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '398'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11e8a5518e92ceac9f7f78ce","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":"Greyson","last_name":"Dicki","email":"<EMAIL>","address":"27504
        Harber Street","city":"East Lera","state":"Alaska","zip":"30646-7716","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11e8a5518e92ceac9f7f78ce"}}}}'
    http_version: 
  recorded_at: Tue, 21 Aug 2018 14:50:33 GMT
- request:
    method: post
    uri: "https://apiv2.sandbox.zeamster.com/v2/accountvaults"
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"Greyson Dicki","is_company":"0","contact_id":"11e8a5518e92ceac9f7f78ce","location_id":"11e816510e9c03a6bcbc685e","payment_method":"cc","account_number":"****************","exp_date":"1220","cvv":"123"}}'
    headers:
      User-Agent:
      - Faraday v0.13.1
      Content-Type:
      - application/json
      User-Id:
      - "11e816510eb4209487387f58"
      User-Api-Key:
      - "a913d5c32a551e7f4a2cd3bf"
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - public
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 21 Aug 2018 14:50:33 GMT
      Location:
      - "https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e8a5518eb8efc481279ccb"
      Server:
      - Apache
      Vary:
      - Accept-Encoding
      Content-Length:
      - '376'
      Connection:
      - keep-alive
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11e8a5518eb8efc481279ccb","payment_method":"cc","title":null,"account_holder_name":"Greyson
        Dicki","first_six":"545454","last_four":"5454","billing_address":null,"billing_zip":null,"exp_date":"1220","routing":null,"account_type":"mc","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11e8a5518e92ceac9f7f78ce","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":28,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11e8a5518eb8efc481279ccb"}}}}'
    http_version: 
  recorded_at: Tue, 21 Aug 2018 14:50:33 GMT
recorded_with: VCR 3.0.3
