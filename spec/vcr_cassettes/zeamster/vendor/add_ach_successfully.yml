---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":null,"last_name":"<PERSON><PERSON><PERSON>''s
        multi-byte gardeners","email":"<EMAIL>","address":"99556
        Hank Pike","city":"East Saundraborough","state":"New Mexico","zip":"12247-0075"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.17.0
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Fri, 10 Jul 2020 15:40:48 GMT
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11eac2c3b9e640acbfc62947
      Server:
      - nginx/1.14.0 (Ubuntu)
      Content-Length:
      - '437'
      Connection:
      - keep-alive
      Set-Cookie:
      - "___utmvaOVBuYlOKB=DFK\x01XoCc; path=/; Max-Age=900"
      - '___utmvbOVBuYlOKB=zZR XZFOCalX: UtS; path=/; Max-Age=900'
      - ___utmvmOVBuYlOKB=OrxiEfEhHiN; path=/; Max-Age=900
      - incap_ses_1253_1743271=8S7gYGwAu1VIdHAv4YxjEf+LCF8AAAAAkUsvs6zCBNAhQioZX7qSLg==;
        path=/; Domain=.sandbox.zeamster.com
      - nlbi_1743271=mI/xIPhTf3Y+o1GDNmpyoQAAAADxKc2r8UA3cvZC2WZ9MgLX; path=/; Domain=.sandbox.zeamster.com
      - visid_incap_1743271=NpLck5Y3TwyULUYudoLDwf+LCF8AAAAAQUIPAAAAAAA/4GHv4LSbpqMaeZeO+gcW;
        expires=Sat, 10 Jul 2021 06:14:36 GMT; HttpOnly; path=/; Domain=.sandbox.zeamster.com
      X-Cdn:
      - Incapsula
      X-Iinfo:
      - 7-********-******** NNNN CT(22 48 0) RT(************* 38) q(0 0 1 0) r(2 2)
        U5
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11eac2c3b9e640acbfc62947","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":null,"last_name":"Denesik''s
        multi-byte gardeners","email":"<EMAIL>","address":"99556
        Hank Pike","city":"East Saundraborough","state":"New Mexico","zip":"12247-0075","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11eac2c3b9e640acbfc62947"}}}}'
    http_version: null
  recorded_at: Fri, 10 Jul 2020 15:40:20 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"Denesik''s multi-byte gardeners","is_company":true,"contact_id":"11eac2c3b9e640acbfc62947","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","account_type":"checking","account_number":"31496","routing":"*********"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v0.17.0
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Access-Control-Allow-Origin:
      - "*"
      Cache-Control:
      - no-cache
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Fri, 10 Jul 2020 15:40:51 GMT
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11eac2c3ba3409689faf71b4
      Server:
      - nginx/1.14.0 (Ubuntu)
      Content-Length:
      - '449'
      Connection:
      - keep-alive
      Set-Cookie:
      - "___utmvaOVBuYlOKB=FHa\x01mhxY; path=/; Max-Age=900"
      - '___utmvbOVBuYlOKB=EZK XvXOxalB: Ctp; path=/; Max-Age=900'
      - ___utmvmOVBuYlOKB=wRCcdaCovBe; path=/; Max-Age=900
      - incap_ses_1253_1743271=JQjJT9a85HZIdHAv4YxjEQKMCF8AAAAAsqkdRY34sTlOnJb0YTdrgg==;
        path=/; Domain=.sandbox.zeamster.com
      - nlbi_1743271=rRvfT4SwhUR4/1TwNmpyoQAAAACrgvUnt47GHNK0V374fKJL; path=/; Domain=.sandbox.zeamster.com
      - visid_incap_1743271=NpLck5Y3TwyULUYudoLDwf+LCF8AAAAAQUIPAAAAAAA/4GHv4LSbpqMaeZeO+gcW;
        expires=Sat, 10 Jul 2021 06:14:35 GMT; HttpOnly; path=/; Domain=.sandbox.zeamster.com
      X-Cdn:
      - Incapsula
      X-Iinfo:
      - 11-********-******** NNNN CT(67 25 0) RT(************* 25) q(0 0 1 0) r(27
        27) U5
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11eac2c3ba3409689faf71b4","payment_method":"ach","title":null,"account_holder_name":"Denesik''s
        multi-byte gardeners","first_six":"31496","last_four":"1496","billing_address":null,"billing_zip":null,"exp_date":null,"routing":"*********","account_type":"checking","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11eac2c3b9e640acbfc62947","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":0,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":"CCD","dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"customer_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11eac2c3ba3409689faf71b4"}}}}'
    http_version: null
  recorded_at: Fri, 10 Jul 2020 15:40:23 GMT
recorded_with: VCR 5.1.0
