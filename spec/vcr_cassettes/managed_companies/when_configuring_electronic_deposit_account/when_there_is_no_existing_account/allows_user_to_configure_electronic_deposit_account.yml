---
http_interactions:
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/contacts
    body:
      encoding: UTF-8
      string: '{"contact":{"location_id":"11e816510e9c03a6bcbc685e","first_name":null,"last_name":"Company
        7 LLC","email":null,"address":"440 Ida Centers","city":"Port Russ","state":"Massachusetts","zip":"17595-8287"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Fri, 13 Sep 2024 14:49:14 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Content-Length:
      - '815'
      Connection:
      - keep-alive
      Server:
      - Apache/2.4.62 (Debian)
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Vary:
      - Accept,Accept-Encoding
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/contacts/11ef71df587128269975dd6b
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"contact":{"id":"11ef71df587128269975dd6b","location_id":"11e816510e9c03a6bcbc685e","account_number":null,"contact_api_id":null,"company_name":null,"first_name":null,"last_name":"Company
        7 LLC","email":null,"address":"440 Ida Centers","city":"Port Russ","country":"USA","state":"Massachusetts","zip":"17595-8287","home_phone":null,"cell_phone":null,"office_phone":null,"office_ext_phone":null,"email_trx_receipt":true,"created_ts":**********,"modified_ts":**********,"date_of_birth":null,"header_message":null,"header_message_type_id":0,"active":"1","contact_c1":null,"contact_c2":null,"contact_c3":null,"contact_balance":null,"parent_id":null,"created_user_id":"11e816510eb4209487387f58","token_import_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/contacts/11ef71df587128269975dd6b"}}}}'
  recorded_at: Fri, 13 Sep 2024 14:49:14 GMT
- request:
    method: post
    uri: https://apiv2.sandbox.zeamster.com/v2/accountvaults
    body:
      encoding: UTF-8
      string: '{"accountvault":{"account_holder_name":"Company 7 LLC","is_company":true,"contact_id":"11ef71df587128269975dd6b","location_id":"11e816510e9c03a6bcbc685e","payment_method":"ach","account_type":"checking","account_number":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>67890","routing":"*********"}}'
    headers:
      User-Id:
      - 11e816510eb4209487387f58
      User-Api-Key:
      - a913d5c32a551e7f4a2cd3bf
      Developer-Id:
      - "<ZEAMSTER_DEVELOPER_ID>"
      User-Agent:
      - Faraday v1.10.3
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Fri, 13 Sep 2024 14:49:17 GMT
      Content-Type:
      - application/json; charset=UTF-8
      Content-Length:
      - '987'
      Connection:
      - keep-alive
      Server:
      - Apache/2.4.62 (Debian)
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Origin, X-Requested-With, X-HTTP-Method-Override, Content-Type, Accept, Authorization,
        user-id, user-api-key, location_api_key, access-token, developer-id, hash-key,
        timestamp
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, PATCH, OPTIONS, HEAD
      Vary:
      - Accept,Accept-Encoding
      Location:
      - https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef71df5a449408a911999e
      Cache-Control:
      - no-cache
    body:
      encoding: ASCII-8BIT
      string: '{"accountvault":{"id":"11ef71df5a449408a911999e","payment_method":"ach","title":null,"account_holder_name":"Company
        7 LLC","first_six":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6","last_four":"7890","billing_address":null,"billing_zip":null,"exp_date":null,"routing":"*********","account_type":"checking","created_ts":**********,"modified_ts":**********,"account_vault_api_id":null,"contact_id":"11ef71df587128269975dd6b","location_id":"11e816510e9c03a6bcbc685e","expiring_in_months":0,"has_recurring":<SSL_ENABLED>,"accountvault_c1":null,"accountvault_c2":null,"accountvault_c3":null,"active":"1","ach_sec_code":"CCD","dl_number":null,"dl_state":null,"ssn4":null,"dob_year":null,"billing_state":null,"billing_city":null,"billing_phone":null,"billing_country":null,"customer_id":null,"cau_summary_status_id":null,"cau_last_updated_ts":null,"card_bin":null,"created_user_id":"11e816510eb4209487387f58","token_import_id":null,"_links":{"self":{"href":"https://apiv2.sandbox.zeamster.com/v2/accountvaults/11ef71df5a449408a911999e"}}}}'
  recorded_at: Fri, 13 Sep 2024 14:49:17 GMT
recorded_with: VCR 6.2.0
