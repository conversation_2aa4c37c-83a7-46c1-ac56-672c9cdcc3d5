---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/accounts"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"depositAccount","attributes":{"depositProduct":"checking","tags":{"subdomain":"alever","entity_id":"10"},"idempotencyKey":"7371b4f5-1551-477c-9cd5-56c8a13fd867"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 03 Jan 2024 18:09:57 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '520'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"208-JwDu1Jp7dNvVE85KaWRRVgTerlM"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"depositAccount","id":"2413772","attributes":{"name":"John
        <PERSON>ilva","createdAt":"2024-01-03T18:09:57.326Z","routingNumber":"*********","accountNumber":"**********","depositProduct":"checking","balance":0,"hold":0,"available":0,"tags":{"subdomain":"alever","entity_id":"10"},"currency":"USD","status":"Open","updatedAt":"2024-01-03T18:09:57.326Z"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}},"org":{"data":{"type":"org","id":"4381"}},"bank":{"data":{"type":"bank","id":"1"}}}}}'
  recorded_at: Wed, 03 Jan 2024 18:09:57 GMT
recorded_with: VCR 6.2.0
