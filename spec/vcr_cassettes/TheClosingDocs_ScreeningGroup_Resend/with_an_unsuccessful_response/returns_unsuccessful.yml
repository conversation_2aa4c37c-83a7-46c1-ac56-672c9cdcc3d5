---
http_interactions:
- request:
    method: post
    uri: https://<THE_CLOSING_DOCS_HOST>/api/v1/tenants/auth
    body:
      encoding: UTF-8
      string: '{"client_id":"<THE_CLOSING_DOCS_CLIENT_ID>","secret_key":"<THE_CLOSING_DOCS_SECRET_KEY>"}'
    headers:
      User-Agent:
      - Faraday v1.5.1
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Unauthorized
    headers:
      Server:
      - Cowboy
      Date:
      - Fri, 06 Aug 2021 22:24:30 GMT
      Connection:
      - keep-alive
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Content-Type:
      - text/html
      Vary:
      - Accept-Encoding
      Cache-Control:
      - no-cache
      X-Request-Id:
      - e87a01a5-0058-4d85-acd0-e17cc55af209
      X-Runtime:
      - '0.004785'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 vegur
    body:
      encoding: ASCII-8BIT
      string: ''
  recorded_at: Fri, 06 Aug 2021 22:24:05 GMT
recorded_with: VCR 6.0.0
