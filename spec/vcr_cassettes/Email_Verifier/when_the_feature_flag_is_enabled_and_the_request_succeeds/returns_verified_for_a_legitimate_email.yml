---
http_interactions:
- request:
    method: get
    uri: https://api.mailgun.net/v4/address/validate?address=john@<POSTGRES_USER>.co
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Faraday v1.8.0
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Access-Control-Allow-Credentials:
      - 'true'
      Access-Control-Allow-Headers:
      - Content-Type, x-requested-with, Authorization
      Access-Control-Allow-Methods:
      - GET, POST, PUT, DELETE, OPTIONS
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Max-Age:
      - '600'
      Cache-Control:
      - no-store
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Thu, 24 Feb 2022 17:32:14 GMT
      Server:
      - nginx
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Content-Length:
      - '131'
      Connection:
      - keep-alive
    body:
      encoding: UTF-8
      string: '{"address":"john@<POSTGRES_USER>.co","is_disposable_address":<SSL_ENABLED>,"is_role_address":<SSL_ENABLED>,"reason":[],"result":"deliverable","risk":"low"}

'
  recorded_at: Thu, 24 Feb 2022 17:32:14 GMT
recorded_with: VCR 6.0.0
