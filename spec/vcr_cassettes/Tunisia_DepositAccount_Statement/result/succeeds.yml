---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/statements/30225711/html"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/html
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 24 Jan 2024 00:06:26 GMT
      Content-Type:
      - text/html; charset=utf-8
      Content-Length:
      - '49671'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"c207-mg20kFChxDhuSc2k7TFBELmz2CI"
    body:
      encoding: UTF-8
      string: "<!DOCTYPE html>\n<html lang=\"en\">\n<head></head<body>Dec 01 2023 Dec 31 2023</body>\n</html>"
  recorded_at: Wed, 24 Jan 2024 00:06:26 GMT
recorded_with: VCR 6.2.0
