---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write","verificationToken":"a_verification_token","verificationCode":"000001"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 08 Jan 2024 17:23:47 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '448'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1c0-901WxKMOFtwl75K0NosqTwl/rFI"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.abcdefg","expiresIn":86400}}}'
  recorded_at: Mon, 08 Jan 2024 17:23:47 GMT
recorded_with: VCR 6.2.0
