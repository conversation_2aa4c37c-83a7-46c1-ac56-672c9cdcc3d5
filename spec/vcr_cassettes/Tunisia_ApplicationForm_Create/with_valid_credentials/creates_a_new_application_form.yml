---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/application-forms"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"applicationForm","attributes":{"tags":{"subdomain":"alever","entity_id":"14","created_by_gid":"gid://<POSTGRES_USER>/PropertyManager/13"},"allowedApplicationTypes":["MultipleMemberBusiness"],"applicantDetails":{"applicationType":"MultipleMemberBusiness","name":"Company
        1 Group","stateOfIncorporation":"DE","entityType":"NotForProfitOrganization","website":"http://schmitt.example/odell_bergnaum","dba":"Connelly-Goodwin","address":{"street":"<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>382
        Mertz Radial","street2":"Suite 856","city":"Emardborough","state":"CA","postalCode":"20634-0031","country":"US"},"businessVertical":"RealEstate"},"settingsOverride":{"redirectUrl":"http://alever.lvh.me:3001/organization/bank_accounts"},"idempotencyKey":"abc"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      X-Accept-Version:
      - V2024_06
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 12 Feb 2025 14:11:29 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2334'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"91e-BDoHOmnddssWSexFvQOb7t7GnpU"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"applicationFormV2","id":"837985","attributes":{"createdAt":"2025-02-12T14:11:29.356Z","updatedAt":"2025-02-12T14:11:29.356Z","tags":{"subdomain":"alever","entity_id":"14","created_by_gid":"gid://<POSTGRES_USER>/PropertyManager/13"},"applicationFormToken":{"token":"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","expiration":"2025-02-13T14:11:29.476Z"},"allowedApplicationTypes":["MultipleMemberBusiness"],"settingsOverride":{"redirectUrl":"http://alever.lvh.me:3001/organization/bank_accounts"},"applicationFormSettings":{"brandName":"<ALLIANCE_FTP_USERNAME>","privacyPolicyUrl":"https://www.<POSTGRES_USER>.co/privacy","electronicDisclosuresUrl":"https://www.<POSTGRES_USER>.co/legal/unit/electronic_disclosure_consent","depositTermsUrl":"https://www.<POSTGRES_USER>.co/legal/unit/deposit_account_end_user_agreement","clientTermsUrl":"https://www.<POSTGRES_USER>.co/terms","cardholderTermsUrl":"https://www.<POSTGRES_USER>.co/legal/unit/debit_cardholder_agreement","validatePhoneNumber":<SSL_ENABLED>}},"links":{"related":{"type":"text/html","href":"https://application-form.sh/view/****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}}}'
  recorded_at: Wed, 12 Feb 2025 14:11:29 GMT
recorded_with: VCR 6.3.1
