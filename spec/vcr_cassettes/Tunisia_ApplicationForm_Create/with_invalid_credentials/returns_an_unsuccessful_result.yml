---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/application-forms"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"applicationForm","attributes":{"tags":{},"lang":"en","allowedApplicationTypes":["Business"],"applicantDetails":{}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 401
      message: Unauthorized
    headers:
      Date:
      - Thu, 21 Dec 2023 17:24:14 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '96'
      Connection:
      - keep-alive
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"60-9cBFG0qQE8YooN5aufFoPY2jZxo"
    body:
      encoding: UTF-8
      string: '{"errors":[{"title":"Bearer token is invalid or expired","status":"401","code":"unauthorized"}]}'
  recorded_at: Thu, 21 Dec 2023 17:24:14 GMT
recorded_with: VCR 6.2.0
