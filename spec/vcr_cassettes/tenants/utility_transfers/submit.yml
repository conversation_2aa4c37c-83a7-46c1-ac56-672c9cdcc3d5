---
http_interactions:
- request:
    method: put
    uri: https://revela-uploads.s3.amazonaws.com/cache/test_uploads/alever/utilities_transfer/1/5f633ee4073b478ccbf7d0528f3c1615/original/utility_transfer.txt
    body:
      encoding: ASCII-8BIT
      string: 'A utility transfer document

'
    headers:
      Content-Type:
      - text/plain
      Accept-Encoding:
      - ''
      User-Agent:
      - aws-sdk-ruby2/2.4.4 ruby/2.5.3 x86_64-linux
      X-Amz-Acl:
      - public-read
      Content-Disposition:
      - inline; filename="utility_transfer.txt"; filename*=UTF-8''utility_transfer.txt
      Expect:
      - 100-continue
      Content-Md5:
      - p1fjgU6c5LeChNZP+z0WFg==
      X-Amz-Date:
      - 20190212T201631Z
      Host:
      - revela-uploads.s3.amazonaws.com
      X-Amz-Content-Sha256:
      - ab94e3ed7c785c4ab1cc0c77a7f9897566ad4d87c48d616eebc2de2035986569
      Authorization:
      - AWS4-HMAC-SHA256 Credential=<AWS_ACCESS_KEY_ID>/20190212/us-east-1/s3/aws4_request,
        SignedHeaders=content-disposition;content-md5;content-type;host;x-amz-acl;x-amz-content-sha256;x-amz-date,
        Signature=0fc10af1daab7bbc8b44849d54bc2d44bcb9a3b296b5e0f9e8df8bbc78c7d51d
      Content-Length:
      - '28'
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - gQ1xQOmqYpp1ob3LrTInCfXdRFQK3KvxzDiWAfYd3Aa3Mf70JQ63EVZ5wxD8MkC3ewbbcQID6nE=
      X-Amz-Request-Id:
      - E9FDE71DE97E5433
      Date:
      - Tue, 12 Feb 2019 20:16:35 GMT
      Etag:
      - '"a757e3814e9ce4b78284d64ffb3d1616"'
      Content-Length:
      - '0'
      Server:
      - AmazonS3
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Tue, 12 Feb 2019 20:16:31 GMT
- request:
    method: put
    uri: https://revela-uploads.s3.amazonaws.com/test_uploads/alever/utilities_transfer/1/4bae36cbb0e3fda6339cc6e694c818cb/original/utility_transfer.txt
    body:
      encoding: UTF-8
      string: ''
    headers:
      Content-Type:
      - text/plain
      Accept-Encoding:
      - ''
      User-Agent:
      - aws-sdk-ruby2/2.4.4 ruby/2.5.3 x86_64-linux
      X-Amz-Acl:
      - public-read
      Content-Disposition:
      - inline; filename="utility_transfer.txt"; filename*=UTF-8''utility_transfer.txt
      X-Amz-Copy-Source:
      - revela-uploads/cache/test_uploads/alever/utilities_transfer/1/5f633ee4073b478ccbf7d0528f3c1615/original/utility_transfer.txt
      X-Amz-Date:
      - 20190212T201631Z
      Host:
      - revela-uploads.s3.amazonaws.com
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=<AWS_ACCESS_KEY_ID>/20190212/us-east-1/s3/aws4_request,
        SignedHeaders=content-disposition;content-type;host;x-amz-acl;x-amz-content-sha256;x-amz-copy-source;x-amz-date,
        Signature=d37343fcbf173514a7b349ab2edaa46701beea0e1cbc79995865b328c0bdafcb
      Content-Length:
      - '0'
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - 4JAyVi2kaLsxuFxHHIl04iljx2LZs0IRlSOf5QooworV4PuoQQKzKmIsJSz//Jxaz9jhvG8goDk=
      X-Amz-Request-Id:
      - 482C4449B693B1FB
      Date:
      - Tue, 12 Feb 2019 20:16:36 GMT
      Content-Type:
      - application/xml
      Content-Length:
      - '234'
      Server:
      - AmazonS3
    body:
      encoding: UTF-8
      string: |-
        <?xml version="1.0" encoding="UTF-8"?>
        <CopyObjectResult xmlns="http://s3.amazonaws.com/doc/2006-03-01/"><LastModified>2019-02-12T20:16:36.000Z</LastModified><ETag>&quot;a757e3814e9ce4b78284d64ffb3d1616&quot;</ETag></CopyObjectResult>
    http_version: 
  recorded_at: Tue, 12 Feb 2019 20:16:31 GMT
recorded_with: VCR 3.0.3
