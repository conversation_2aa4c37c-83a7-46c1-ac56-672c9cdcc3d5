---
http_interactions:
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions
    body:
      encoding: UTF-8
      string: UniqueName=gid%3A%2F%2Falever%2FVendorAssignment%2F1
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 400
      message: BAD REQUEST
    headers:
      Date:
      - Mon, 01 Jun 2020 18:42:56 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '136'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQ07ed9679da594f3f81447d67d8c9c16c
      Twilio-Request-Duration:
      - '0.028'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=31536000
    body:
      encoding: UTF-8
      string: '{"code": 80603, "message": "Session UniqueName must be unique.", "more_info":
        "https://www.twilio.com/docs/errors/80603", "status": 400}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:42:56 GMT
recorded_with: VCR 5.1.0
