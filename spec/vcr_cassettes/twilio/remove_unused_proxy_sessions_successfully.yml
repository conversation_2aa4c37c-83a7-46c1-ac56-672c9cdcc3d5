---
http_interactions:
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions
    body:
      encoding: UTF-8
      string: UniqueName=gid%3A%2F%2Falever%2FVendorAssignment%2F1
    headers:
      User-Agent:
      - twilio-ruby/5.39.2 (ruby/x86_64-linux 2.7.2-p137)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic xyz
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 06 May 2021 13:20:15 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '888'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQb4e2ed99a966645a1f74aa234a67f060
      Twilio-Request-Duration:
      - '0.021'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"status": "open", "unique_name": "gid://alever/VendorAssignment/1",
        "closed_reason": null, "date_ended": null, "ttl": 0, "sid": "KC3db5c73f55a77b67ab2ed35af62794e2",
        "date_expiry": null, "account_sid": "<TWILIO_SID>", "date_updated": "2021-05-06T13:20:15Z",
        "mode": "voice-and-message", "date_last_interaction": "2020-1-1", "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2",
        "date_created": "2021-05-06T13:20:15Z", "date_started": null, "service_sid":
        "KSdc833993a92c1dbed69fdf00b45d4daa", "links": {"participants": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants",
        "interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Interactions"}}'
  recorded_at: Thu, 06 May 2021 13:19:44 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants
    body:
      encoding: UTF-8
      string: FriendlyName=Donnelly%27s+cross-platform+editors&Identifier=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.39.2 (ruby/x86_64-linux 2.7.2-p137)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic xyz
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 06 May 2021 13:20:15 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '884'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQc3226468e930c555d6d8cf42ea3a7c8d
      Twilio-Request-Duration:
      - '0.059'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"identifier": "+***********", "date_updated": "2021-05-06T13:20:15Z",
        "friendly_name": "Donnelly''s cross-platform editors", "date_deleted": null,
        "account_sid": "<TWILIO_SID>", "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants/KP0da72df3b2278be178e2670eac54649c",
        "proxy_identifier": "+***********", "proxy_identifier_sid": "PN589a7bf7f830a20a051185d6c60bc603",
        "sid": "KP0da72df3b2278be178e2670eac54649c", "date_created": "2021-05-06T13:20:15Z",
        "session_sid": "KC3db5c73f55a77b67ab2ed35af62794e2", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "links": {"message_interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants/KP0da72df3b2278be178e2670eac54649c/MessageInteractions"}}'
  recorded_at: Thu, 06 May 2021 13:19:44 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants
    body:
      encoding: UTF-8
      string: FriendlyName=Earle+Altenwerth2&Identifier=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.39.2 (ruby/x86_64-linux 2.7.2-p137)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 06 May 2021 13:20:15 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '868'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQfb7f1236ca53c16cf457fcbe78d085b4
      Twilio-Request-Duration:
      - '0.058'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"identifier": "+***********", "date_updated": "2021-05-06T13:20:15Z",
        "friendly_name": "Earle Altenwerth2", "date_deleted": null, "account_sid":
        "<TWILIO_SID>", "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants/KP1a37dcaa0b39b3d7f86c89f57f23ed64",
        "proxy_identifier": "+***********", "proxy_identifier_sid": "PN298c0f85e609c27cea7ee30125acc2a8",
        "sid": "KP1a37dcaa0b39b3d7f86c89f57f23ed64", "date_created": "2021-05-06T13:20:15Z",
        "session_sid": "KC3db5c73f55a77b67ab2ed35af62794e2", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "links": {"message_interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants/KP1a37dcaa0b39b3d7f86c89f57f23ed64/MessageInteractions"}}'
  recorded_at: Thu, 06 May 2021 13:19:44 GMT
- request:
    method: get
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.39.2 (ruby/x86_64-linux 2.7.2-p137)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 06 May 2021 13:20:16 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '888'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQa724fb86f45fc4de47e4ff264690f6c3
      Twilio-Request-Duration:
      - '0.032'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"status": "open", "unique_name": "gid://alever/VendorAssignment/1",
        "closed_reason": null, "date_ended": null, "ttl": 0, "sid": "KC3db5c73f55a77b67ab2ed35af62794e2",
        "date_expiry": null, "account_sid": "<TWILIO_SID>", "date_updated": "2021-05-06T13:20:15Z",
        "mode": "voice-and-message", "date_last_interaction": null, "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2",
        "date_created": "2021-05-06T13:20:15Z", "date_started": null, "service_sid":
        "KSdc833993a92c1dbed69fdf00b45d4daa", "links": {"participants": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Participants",
        "interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2/Interactions"}}'
  recorded_at: Thu, 06 May 2021 13:19:44 GMT
- request:
    method: delete
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC3db5c73f55a77b67ab2ed35af62794e2
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.39.2 (ruby/x86_64-linux 2.7.2-p137)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 204
      message: No Content
    headers:
      Date:
      - Thu, 06 May 2021 13:20:16 GMT
      Content-Type:
      - application/json; charset=utf-8
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQf7cbe5764c86f6ba519a7e68fdaf3a34
      Twilio-Request-Duration:
      - '0.032'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Thu, 06 May 2021 13:19:44 GMT
recorded_with: VCR 6.0.0
