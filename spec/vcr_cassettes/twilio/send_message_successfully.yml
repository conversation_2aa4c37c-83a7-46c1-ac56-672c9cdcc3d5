---
http_interactions:
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages.json
    body:
      encoding: UTF-8
      string: Body=Would+you+like+to+come+in+for+a+tour+of+the+property%3F&From=%2B***********&To=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.25.2 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic xxx
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: CREATED
    headers:
      Date:
      - Tue, 30 Jul 2019 21:47:48 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '817'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ4d1936fd90114e9c8531b85e36644309
      Twilio-Request-Duration:
      - '0.123'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"sid": "SM52aa8114758540d086b1604d82de464a", "date_created": "Tue,
        30 Jul 2019 21:47:48 +0000", "date_updated": "Tue, 30 Jul 2019 21:47:48 +0000",
        "date_sent": null, "account_sid": "<TWILIO_SID>", "to": "+***********", "from":
        "+***********", "messaging_service_sid": null, "body": "Would you like to
        come in for a tour of the property?", "status": "queued", "num_segments":
        "1", "num_media": "0", "direction": "outbound-api", "api_version": "2010-04-01",
        "price": null, "price_unit": "USD", "error_code": null, "error_message": null,
        "uri": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM52aa8114758540d086b1604d82de464a.json",
        "subresource_uris": {"media": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM52aa8114758540d086b1604d82de464a/Media.json"}}'
    http_version: 
  recorded_at: Tue, 30 Jul 2019 21:47:48 GMT
recorded_with: VCR 4.0.0
