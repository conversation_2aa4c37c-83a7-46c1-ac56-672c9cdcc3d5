---
http_interactions:
- request:
    method: delete
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers/PN0b09dff20a7f8b6818797d9ef778eebd.json
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.3.0 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic xxx
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 204
      message: NO CONTENT
    headers:
      Date:
      - Sun, 28 Jul 2019 17:27:49 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQedefa24503504755a5373407bb963080
      Twilio-Request-Duration:
      - '0.351'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=31536000
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Sun, 28 Jul 2019 17:27:49 GMT
recorded_with: VCR 4.0.0
