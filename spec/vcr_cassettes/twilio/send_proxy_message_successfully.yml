---
http_interactions:
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions
    body:
      encoding: UTF-8
      string: UniqueName=gid%3A%2F%2Falever%2FVendorAssignment%2F1
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: CREATED
    headers:
      Date:
      - Mon, 01 Jun 2020 18:57:35 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '888'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQcccfb82131544681968fa46623be7483
      Twilio-Request-Duration:
      - '0.015'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"status": "open", "unique_name": "gid://alever/VendorAssignment/1",
        "closed_reason": null, "date_ended": null, "ttl": 0, "sid": "KC9fe7aab7709be5b8377928cbc762b86d",
        "date_expiry": null, "account_sid": "<TWILIO_SID>", "date_updated": "2020-06-01T18:57:35Z",
        "mode": "voice-and-message", "date_last_interaction": null, "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d",
        "date_created": "2020-06-01T18:57:35Z", "date_started": null, "service_sid":
        "KSdc833993a92c1dbed69fdf00b45d4daa", "links": {"participants": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants",
        "interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Interactions"}}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:57:35 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants
    body:
      encoding: UTF-8
      string: FriendlyName=Cremin%27s+bluetooth+gardeners&Identifier=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: CREATED
    headers:
      Date:
      - Mon, 01 Jun 2020 18:57:36 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '879'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ23be761593cf4ffd852adf8307e119bb
      Twilio-Request-Duration:
      - '0.036'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"identifier": "+***********", "date_updated": "2020-06-01T18:57:36Z",
        "friendly_name": "Cremin''s bluetooth gardeners", "date_deleted": null, "account_sid":
        "<TWILIO_SID>", "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP7921526ea08e00466e9cc1c2f8da022e",
        "proxy_identifier": "+***********", "proxy_identifier_sid": "PNc48a1c84bb30efbc5b974a7d608d50e7",
        "sid": "KP7921526ea08e00466e9cc1c2f8da022e", "date_created": "2020-06-01T18:57:36Z",
        "session_sid": "KC9fe7aab7709be5b8377928cbc762b86d", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "links": {"message_interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP7921526ea08e00466e9cc1c2f8da022e/MessageInteractions"}}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:57:36 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants
    body:
      encoding: UTF-8
      string: FriendlyName=Dewey+Russel1&Identifier=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: CREATED
    headers:
      Date:
      - Mon, 01 Jun 2020 18:57:36 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '864'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ65f1b04d1d1e4b0cb34b9fc366519276
      Twilio-Request-Duration:
      - '0.036'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"identifier": "+***********", "date_updated": "2020-06-01T18:57:36Z",
        "friendly_name": "Dewey Russel1", "date_deleted": null, "account_sid": "<TWILIO_SID>",
        "url": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP0633f83a185a284c513ca8dcca8c561e",
        "proxy_identifier": "+***********", "proxy_identifier_sid": "PNc48a1c84bb30efbc5b974a7d608d50e7",
        "sid": "KP0633f83a185a284c513ca8dcca8c561e", "date_created": "2020-06-01T18:57:36Z",
        "session_sid": "KC9fe7aab7709be5b8377928cbc762b86d", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "links": {"message_interactions": "https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP0633f83a185a284c513ca8dcca8c561e/MessageInteractions"}}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:57:36 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP0633f83a185a284c513ca8dcca8c561e/MessageInteractions
    body:
      encoding: UTF-8
      string: Body=My+Messge+0
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 01 Jun 2020 18:57:37 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '896'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ125cff9d1b864b52a6334e8149ee22f6
      Twilio-Request-Duration:
      - '0.135'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"inbound_resource_status": null, "inbound_resource_sid": null, "outbound_resource_status":
        "sending", "outbound_resource_sid": "SM5cb3c8fd1a81c29f0ffb5d546b7af510",
        "inbound_resource_url": null, "type": "Message", "outbound_resource_url":
        "https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM5cb3c8fd1a81c29f0ffb5d546b7af510.json",
        "account_sid": "<TWILIO_SID>", "outbound_resource_type": "message", "date_created":
        "2020-06-01T18:57:37Z", "inbound_resource_type": null, "url": null, "date_updated":
        "2020-06-01T18:57:37Z", "sid": "KIc6a6641e5775ba6ab372fe2e5b5a0ba4", "outbound_participant_sid":
        "KP0633f83a185a284c513ca8dcca8c561e", "participant_sid": null, "session_sid":
        "KC9fe7aab7709be5b8377928cbc762b86d", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "data": "{\"body\":\"My Messge 0\"}", "inbound_participant_sid": null}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:57:37 GMT
- request:
    method: post
    uri: https://proxy.twilio.com/v1/Services/KSdc833993a92c1dbed69fdf00b45d4daa/Sessions/KC9fe7aab7709be5b8377928cbc762b86d/Participants/KP0633f83a185a284c513ca8dcca8c561e/MessageInteractions
    body:
      encoding: UTF-8
      string: Body=My+Messge+1
    headers:
      User-Agent:
      - twilio-ruby/5.31.1 (ruby/x86_64-linux 2.6.6-p146)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 01 Jun 2020 18:57:41 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '896'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQd2ace5634acf43729f1b00909fc69906
      Twilio-Request-Duration:
      - '0.116'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - proxy.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"inbound_resource_status": null, "inbound_resource_sid": null, "outbound_resource_status":
        "sending", "outbound_resource_sid": "SMedf7ff36ab25c88451e0eec6451d5d43",
        "inbound_resource_url": null, "type": "Message", "outbound_resource_url":
        "https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages/SMedf7ff36ab25c88451e0eec6451d5d43.json",
        "account_sid": "<TWILIO_SID>", "outbound_resource_type": "message", "date_created":
        "2020-06-01T18:57:41Z", "inbound_resource_type": null, "url": null, "date_updated":
        "2020-06-01T18:57:41Z", "sid": "KI2c8a3562b3fb6459803875a6c2222536", "outbound_participant_sid":
        "KP0633f83a185a284c513ca8dcca8c561e", "participant_sid": null, "session_sid":
        "KC9fe7aab7709be5b8377928cbc762b86d", "service_sid": "KSdc833993a92c1dbed69fdf00b45d4daa",
        "data": "{\"body\":\"My Messge 1\"}", "inbound_participant_sid": null}'
    http_version: null
  recorded_at: Mon, 01 Jun 2020 18:57:41 GMT
recorded_with: VCR 5.1.0
