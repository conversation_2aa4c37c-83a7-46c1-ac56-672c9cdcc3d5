---
http_interactions:
- request:
    method: get
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?AreaCode=313&PageSize=20
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.3.0 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic xxx
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Sun, 28 Jul 2019 14:49:30 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '7516'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ31bcbaae584c4918ac0ed8b8b802f053
      Twilio-Request-Duration:
      - '0.623'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"available_phone_numbers": [{"friendly_name": "(*************", "phone_number":
        "+***********", "lata": "340", "rate_center": "DETROITZN6", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13132413378", "lata": "340", "rate_center":
        "DETROITZN4", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13132469064", "lata": "340", "rate_center": "DETROITZN5", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13133296248", "lata": "340", "rate_center":
        "DETROITZN1", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13132961952", "lata": "340", "rate_center": "DETROITZN5", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13133296427", "lata": "340", "rate_center":
        "DETROITZN1", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13132468816", "lata": "340", "rate_center": "DETROITZN5", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13132469820", "lata": "340", "rate_center":
        "DETROITZN5", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13133273440", "lata": "340", "rate_center": "DETROITZN3", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13135588198", "lata": "340", "rate_center":
        "DETROITZN6", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13133492531", "lata": "340", "rate_center": "DETROITZN3", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13133950699", "lata": "340", "rate_center":
        "DETROITZN6", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13132468799", "lata": "340", "rate_center": "DETROITZN5", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13134668635", "lata": "340", "rate_center":
        "DETROITZN2", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48214", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13132511485", "lata": "340", "rate_center": "DETROITZN6", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13139928231", "lata": "340", "rate_center":
        "DETROITZN5", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13134258579", "lata": "340", "rate_center": "DETROITZN6", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48201", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+13132511878", "lata": "340", "rate_center":
        "DETROITZN6", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}, {"friendly_name": "(*************", "phone_number":
        "+13139243376", "lata": "340", "rate_center": "DETROITZN2", "latitude": "42.331400",
        "longitude": "-83.045700", "locality": "Detroit", "region": "MI", "postal_code":
        "48205", "iso_country": "US", "address_requirements": "none", "beta": <SSL_ENABLED>,
        "capabilities": {"voice": true, "SMS": true, "MMS": true, "fax": true}}, {"friendly_name":
        "(*************", "phone_number": "+***********", "lata": "340", "rate_center":
        "DETROITZN6", "latitude": "42.331400", "longitude": "-83.045700", "locality":
        "Detroit", "region": "MI", "postal_code": "48201", "iso_country": "US", "address_requirements":
        "none", "beta": <SSL_ENABLED>, "capabilities": {"voice": true, "SMS": true,
        "MMS": true, "fax": true}}], "uri": "/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?AreaCode=313&PageSize=20"}'
    http_version: 
  recorded_at: Sun, 28 Jul 2019 14:49:31 GMT
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers.json
    body:
      encoding: UTF-8
      string: PhoneNumber=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.3.0 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic xxx
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: CREATED
    headers:
      Date:
      - Sun, 28 Jul 2019 15:24:59 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '1082'
      Connection:
      - keep-alive
      Twilio-Concurrent-Requests:
      - '1'
      Twilio-Request-Id:
      - RQ386cbfaf175f4a57b21c28f688e9991e
      Twilio-Request-Duration:
      - '0.665'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"sid": "PN0b09dff20a7f8b6818797d9ef778eebd", "account_sid": "<TWILIO_SID>",
        "friendly_name": "(*************", "phone_number": "+***********", "voice_url":
        null, "voice_method": "POST", "voice_fallback_url": null, "voice_fallback_method":
        "POST", "voice_caller_id_lookup": <SSL_ENABLED>, "date_created": "Sun, 28
        Jul 2019 15:24:58 +0000", "date_updated": "Sun, 28 Jul 2019 15:24:58 +0000",
        "sms_url": "", "sms_method": "POST", "sms_fallback_url": "", "sms_fallback_method":
        "POST", "address_requirements": "none", "beta": <SSL_ENABLED>, "capabilities":
        {"voice": true, "sms": true, "mms": true, "fax": true}, "voice_receive_mode":
        "voice", "status_callback": "", "status_callback_method": "POST", "api_version":
        "2010-04-01", "voice_application_sid": null, "sms_application_sid": "", "origin":
        "twilio", "trunk_sid": null, "emergency_status": "Inactive", "emergency_address_sid":
        null, "address_sid": null, "identity_sid": null, "uri": "/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers/PN0b09dff20a7f8b6818797d9ef778eebd.json",
        "status": "in-use"}'
    http_version: 
  recorded_at: Sun, 28 Jul 2019 15:24:59 GMT
- request:
    method: get
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?AreaCode=313&PageSize=20
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.25.2 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 403
      message: FORBIDDEN
    headers:
      Date:
      - Mon, 29 Jul 2019 14:34:26 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '155'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQc6f09b07197f405fadb711ab735258ce
      Twilio-Request-Duration:
      - '0.028'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"code": 20008, "message": "Resource not accessible with Test Account
        Credentials", "more_info": "https://www.twilio.com/docs/errors/20008", "status":
        403}'
    http_version: 
  recorded_at: Mon, 29 Jul 2019 14:34:31 GMT
- request:
    method: get
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/AvailablePhoneNumbers/US/Local.json?AreaCode=313&PageSize=20
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.25.2 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 403
      message: FORBIDDEN
    headers:
      Date:
      - Mon, 29 Jul 2019 14:43:23 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '155'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQ1e18d8fdaa5749b6bdef6236c693d71f
      Twilio-Request-Duration:
      - '0.031'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"code": 20008, "message": "Resource not accessible with Test Account
        Credentials", "more_info": "https://www.twilio.com/docs/errors/20008", "status":
        403}'
    http_version: 
  recorded_at: Mon, 29 Jul 2019 14:43:28 GMT
recorded_with: VCR 4.0.0
