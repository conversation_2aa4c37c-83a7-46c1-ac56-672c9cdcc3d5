---
http_interactions:
- request:
    method: delete
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/IncomingPhoneNumbers/PN0b09dff20a7f8b6818797d9ef778eebd.json
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.3.0 (ruby/x86_64-linux 2.6.3-p62)
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic xxx
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 403
      message: FORBIDDEN
    headers:
      Date:
      - Sun, 28 Jul 2019 17:26:05 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '155'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQc671b3c9166e4faeb7995408f6402dc5
      Twilio-Request-Duration:
      - '0.028'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
      Strict-Transport-Security:
      - max-age=********
    body:
      encoding: UTF-8
      string: '{"code": 20008, "message": "Resource not accessible with Test Account
        Credentials", "more_info": "https://www.twilio.com/docs/errors/20008", "status":
        403}'
    http_version: 
  recorded_at: Sun, 28 Jul 2019 17:26:05 GMT
recorded_with: VCR 4.0.0
