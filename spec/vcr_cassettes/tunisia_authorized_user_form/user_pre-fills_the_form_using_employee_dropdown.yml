---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 04 Jan 2024 17:46:53 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2073'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"819-y2o6COW6ieFNwe8/5qThwr3M08s"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 04 Jan 2024 17:46:53 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms", "phone":{"countryCode":"1","number":"2489212775"}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 30 May 2024 20:16:13 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '459'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1cb-Swh6DGN2lgOP43tC0XOm0Xh9pgI"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"L2DfnXh59rvOIvkkaplIJNgwECe0DnQG9aYh5hWuA6n2vpYqLZ9sAwr7OZ9IUkasS2vwIH1afY3OtNoMYwg0XodBEO3DebRIr/SfdvfmF1mTxIWtVLnXJWsT7nvmq4RXn8+JqJDOP/lLzuZu00YsblpdQsnLI3o7hB69MY2GOLoohyT+LWz9ek5DwGoM5SPe+2EzksyhgX+GM1juawYp+KxzdeKGbBytEA7mI9xnNFRz4nbk+k60B4uH36mFmbgrCzpYpoCVcpjEmY7d5XYFYOQy3HI51emmfK9YuxECUZaWcp30FGrrptxBsYPvTmdKzmlHe4d289wNum8lKlf6UAgKiI/EKA","phoneLast4Digits":"2775"}}}'
  recorded_at: Thu, 30 May 2024 20:16:13 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write
        cards-write customers cards cards-sensitive cards-sensitive-write transactions
        authorizations accounts statements customers-write","verificationToken":"L2DfnXh59rvOIvkkaplIJNgwECe0DnQG9aYh5hWuA6n2vpYqLZ9sAwr7OZ9IUkasS2vwIH1afY3OtNoMYwg0XodBEO3DebRIr/SfdvfmF1mTxIWtVLnXJWsT7nvmq4RXn8+JqJDOP/lLzuZu00YsblpdQsnLI3o7hB69MY2GOLoohyT+LWz9ek5DwGoM5SPe+2EzksyhgX+GM1juawYp+KxzdeKGbBytEA7mI9xnNFRz4nbk+k60B4uH36mFmbgrCzpYpoCVcpjEmY7d5XYFYOQy3HI51emmfK9YuxECUZaWcp30FGrrptxBsYPvTmdKzmlHe4d289wNum8lKlf6UAgKiI/EKA","verificationCode":"<NELCO_CLIENT_ID>1"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 30 May 2024 20:16:14 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '619'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"26b-WA7Q+lsmRraJ6JwxTu408Vi0D60"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvKzEyNDg5MjEyNzc1IiwiZXhwIjoiMjAyNC0wNS0zMVQyMDoxNjoxNC4xNjBaIiwianRpIjpudWxsLCJvcmdJZCI6IjQzODEiLCJzY29wZSI6ImFjY291bnRzLXdyaXRlIGNhcmRzLXdyaXRlIGN1c3RvbWVycyBjYXJkcyBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyBhdXRob3JpemF0aW9ucyBhY2NvdW50cyBzdGF0ZW1lbnRzIGN1c3RvbWVycy13cml0ZSIsImN1c3RvbWVySWQiOiIxNDk3NzU2IiwidXNlclR5cGUiOiJjdXN0b21lciJ9GrNV6sy1ziECsiEaLw6dSXAJy6CcMgb0JK8e9gyegXx6e3Z0Y3h5gRLop2K9i41TRuebYv061pBCnPYJlfOtDA","expiresIn":86400}}}'
  recorded_at: Thu, 30 May 2024 20:16:14 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/authorized-users"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"addAuthorizedUsers","attributes":{"authorizedUsers":[{"fullName":{"first":"Elvie","last":"Kemmer3"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}}]}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 30 May 2024 20:16:15 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1946'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"79a-RhLr4vVFRc//dDwLHwccKVpomFA"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Wed, 12 Jun 2024 15:11:33 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 30 May 2024 20:16:15 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '1946'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"79a-RhLr4vVFRc//dDwLHwccKVpomFA"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Wed, 12 Jun 2024 15:11:33 GMT
recorded_with: VCR 6.2.0
