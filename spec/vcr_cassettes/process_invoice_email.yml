---
http_interactions:
- request:
    method: put
    uri: https://revela-invoices.s3.amazonaws.com/015e44165a22437d1b1d70d9b5940eda329a5bd0/invoice.pdf
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        JVBERi0xLjQKMSAwIG9iago8PAovVGl0bGUgKP7/ADEAMQA4ADAAIABAACAA
        JUNKOMITTEDFORBREVITYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        IDAgUgovUm9vdCAxNSAwIFIKPj4Kc3RhcnR4cmVmCjMzNTkwCiUlRU9GCg==
    headers:
      Content-Type:
      - application/pdf
      Accept-Encoding:
      - ''
      User-Agent:
      - aws-sdk-ruby2/2.4.4 ruby/2.3.3 x86_64-linux resources
      X-Amz-Acl:
      - public-read
      X-Amz-Server-Side-Encryption:
      - AES256
      Expect:
      - 100-continue
      Content-Md5:
      - 40ip/mj+nOAVO6CqQnhHXw==
      X-Amz-Date:
      - 20170206T233155Z
      Host:
      - revela-invoices.s3.amazonaws.com
      X-Amz-Content-Sha256:
      - 324eed3c130c0c8acdfdf1da1c1c05ccbe5910d712347e564515bb559dae4310
      Authorization:
      - AWS4-HMAC-SHA256 Credential=<AWS_ACCESS_KEY_ID>/20170206/us-east-1/s3/aws4_request,
        SignedHeaders=content-md5;content-type;host;x-amz-acl;x-amz-content-sha256;x-amz-date;x-amz-server-side-encryption,
        Signature=edfd8c05ac4582d9fa57d33dc171eca247e4c4b4a0b84b8615736c3be58fccb2
      Content-Length:
      - '34370'
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - CM/CisfLc6e0xKJ7MliFqcHtFyBWLN6gAiAisbukWnavfQ6+zVmDWp0dU9rG+6n4stKxxzGhYcM=
      X-Amz-Request-Id:
      - 05DDE2CB2B360AAC
      Date:
      - Mon, 06 Feb 2017 23:32:03 GMT
      X-Amz-Server-Side-Encryption:
      - AES256
      Etag:
      - '"e348a9fe68fe9ce0153ba0aa4278475f"'
      Content-Length:
      - '0'
      Server:
      - AmazonS3
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Mon, 06 Feb 2017 23:31:55 GMT
recorded_with: VCR 3.0.3
