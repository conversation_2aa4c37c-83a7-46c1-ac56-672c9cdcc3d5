---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/cards"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessDebitCard","attributes":{"fullName":{"first":"first","last":"last"},"address":{"street":"46751
        Mac Way","street2":"Suite 339","city":"Port Bryon","state":"MI","postalCode":"09080","country":"US"},"dateOfBirth":"2000-01-01","email":"<EMAIL>","phone":{"countryCode":"1","number":"**********"},"limits":{"dailyWithdrawal":10000,"dailyPurchase":10000,"monthlyWithdrawal":1<NELCO_CLIENT_ID>,"monthlyPurchase":1<NELCO_CLIENT_ID>},"idempotencyKey":"1","ssn":"*********","shippingAddress":{"street":"46751
        Mac Way","street2":"Suite 339","city":"Port Bryon","state":"MI","postalCode":"09080","country":"US"},"design":"logo_debit"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2413772"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 06 Jun 2024 20:53:06 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '641'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"281-wIN/tQhHCKVsLgyMgPIiCg/J6Ok"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessVirtualDebitCard","id":"2040854","attributes":{"createdAt":"2024-06-05T21:29:52.797Z","last4Digits":"3111","expirationDate":"2028-06","address":{"street":"45188
        Marylouise Mill","street2":"Apt. 524","city":"Weissnatborough","state":"MI","postalCode":"90741-9226","country":"US"},"fullName":{"first":"first","last":"last"},"phone":{"countryCode":"1","number":"**********"},"email":"<EMAIL>","dateOfBirth":"2000-01-01","ssn":"*********","status":"Active","tags":{},"bin":"*********"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}},"account":{"data":{"type":"account","id":"2413772"}}}}}'
  recorded_at: Thu, 06 Jun 2024 20:53:06 GMT
recorded_with: VCR 6.2.0
