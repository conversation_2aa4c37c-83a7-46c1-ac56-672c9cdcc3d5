---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/check-payments/111/approve"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"additionalVerification"}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 01 May 2025 19:21:26 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '687'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"2af-QFx3lkgpVzQNv7YRDnGOemRWr8o"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"checkPayment","id":"111","attributes":{"createdAt":"2025-05-01T17:09:17.392Z","updatedAt":"2025-05-01T19:21:26.759Z","amount":2600000,"description":"Check
        Payment | 12345","status":"Processed","additionalVerificationStatus":"Approved","checkNumber":"12345","onUsAuxiliary":"12345","onUs":"**********/","counterpartyRoutingNumber":"*********","returnCutoffTime":"2025-05-02T15:50:00.000Z","originated":false,"isAccountClosureCheck":false},"relationships":{"account":{"data":{"type":"account","id":"5302494"}},"customer":{"data":{"type":"customer","id":"2014236"}},"org":{"data":{"type":"org","id":"4381"}},"transaction":{"data":{"type":"transaction","id":"9859078"}}}}}'
  recorded_at: Thu, 01 May 2025 19:21:26 GMT
recorded_with: VCR 6.3.1
