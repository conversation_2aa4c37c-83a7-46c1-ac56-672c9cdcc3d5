---
http_interactions:
- request:
    method: post
    uri: "<INSPECTIFY_API_URL>/inspections/cancel"
    body:
      encoding: UTF-8
      string: '{"order_id":"213306"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - Bearer <INSPECTIFY_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Date:
      - Mon, 07 Apr 2025 18:27:54 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '163'
      Connection:
      - keep-alive
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1744050474&sid=************************************&s=uTKc6h4nQJ%2B0KHs6QkoCHXnNL%2B8d6cxVKVr17qDmvag%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1744050474&sid=************************************&s=uTKc6h4nQJ%2B0KHs6QkoCHXnNL%2B8d6cxVKVr17qDmvag%3D
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - '0'
      X-Content-Type-Options:
      - nosniff
      X-Download-Options:
      - noopen
      X-Permitted-Cross-Domain-Policies:
      - none
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Vary:
      - Accept, Origin
      Cache-Control:
      - no-cache
      Content-Security-Policy:
      - ''
      Set-Cookie:
      - _inspectify_session=d9HMFDLJu7kQgGMoIB0zdJoHehYgn2Iyzvx0VAlVic84ZBxCsrHrIqiBe7mh9hJXMn27J1pFO98iop%2BtstTfFNmbHbmfHtrwxvFomx%2BrEngK0T4NYLTkfwlH%2FPCKFLF2n%2BgpI8aJmeYhEh%2FXyx3r2x%2BSI2qFSGRBIlViwyh2V0j%2B0V69FCRsV97cMg%3D%3D--SmcWkJNIkjNvFKbf--7qbbZtc5%2BAKWX7Md2CaKhw%3D%3D;
        path=/; secure; HttpOnly; SameSite=Lax
      X-Request-Id:
      - 3d448234-b692-4c01-984a-9856447e68d7
      X-Runtime:
      - '0.016866'
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains
      Via:
      - 1.1 vegur
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 92cb94eb483aef9f-EWR
      Alt-Svc:
      - h3=":443"; ma=86400
      Server-Timing:
      - cfL4;desc="?proto=TCP&rtt=33516&min_rtt=29534&rtt_var=15211&sent=4&recv=8&lost=0&retrans=0&sent_bytes=2802&recv_bytes=936&delivery_rate=62211&cwnd=105&unsent_bytes=0&cid=a55baf8539336104&ts=127&x=0"
    body:
      encoding: UTF-8
      string: '{"errors":{"message":"Inspection with id 291385 is started or about
        to start. Please reach Inspectify customer success directly for <NAME_EMAIL>"}}'
  recorded_at: Mon, 07 Apr 2025 18:27:54 GMT
recorded_with: VCR 6.3.1
