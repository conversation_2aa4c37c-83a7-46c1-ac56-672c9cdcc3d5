---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 19 Feb 2025 14:29:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '3543'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"dd7-33pugCfmpNcJqlspq4Bn8MLx0IU"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Darrell","last":"Botsford2"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"3084758364"}},{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jonathanevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Tom","last":"Win"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7348812543"}},{"fullName":{"first":"Burl","last":"Goyette1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"30<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>623915"}},{"fullName":{"first":"Chung","last":"Vandervort1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Reed","last":"Mills1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Antonia","last":"Schmeler1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Elvie","last":"Kemmer3"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Tony","last":"Hermiston1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"23<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678901"}},{"fullName":{"first":"Erlich","last":"Backman"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"23<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678901"}},{"fullName":{"first":"Ellen
        ","last":" Ripley "},"email":"<EMAIL>","phone":{"countryCode":"1","number":"2813308004"}},{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},{"fullName":{"first":"Erlick","last":"Bachman"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7346783<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6"}},{"fullName":{"first":"Jow","last":"Gifford"},"email":"jgifford@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Freda","last":"Dickens1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"61525613<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"5849072"},{"type":"authorizedUserResource","id":"5849073"},{"type":"authorizedUserResource","id":"5849074"},{"type":"authorizedUserResource","id":"5849075"},{"type":"authorizedUserResource","id":"5849076"},{"type":"authorizedUserResource","id":"5849077"},{"type":"authorizedUserResource","id":"5849078"},{"type":"authorizedUserResource","id":"5849079"},{"type":"authorizedUserResource","id":"5849080"},{"type":"authorizedUserResource","id":"5849081"},{"type":"authorizedUserResource","id":"5849082"},{"type":"authorizedUserResource","id":"5849083"},{"type":"authorizedUserResource","id":"5849085"},{"type":"authorizedUserResource","id":"18827238"},{"type":"authorizedUserResource","id":"18828381"},{"type":"authorizedUserResource","id":"18829406"}]}}}}'
  recorded_at: Wed, 19 Feb 2025 14:29:51 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 19 Feb 2025 14:29:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - "<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>9"
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1cb-6mm5Qw/1hgSeP1flNIjVYvFA+nc"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"NfbuuUElqwccu+ANgiLSwLeHTug47fS7br99NNwdeeQdAUgz6hgYFMPNvqJs326KsrRla87GcpK2Ef+RP1RBh9LMEadsxYjEgeMD9q8oZ/mIZIHtrX0S5TZSFKyZ9qLfEhXYbeKsLgXUoGlp4NLkYwWGik5ByrIcEi/vEqHcQ195BTLuzMlLUZ7tPVzVBWlSXBEi+cdLkY+/VJxw+z7ctUfZwhLCOCdmAQhUio+tM1GQQ/nKX5Y9J4/5oVT3MtwDRJc2QnvmSe4KGG4sG84qIl9YhPxGYJ2gjzZE6dbcFuzLRALNFOAb50znVJHMT9FgOlWMulQ2sDgp3Vp9gQriaHSWHLS3mw","phoneLast4Digits":"2775"}}}'
  recorded_at: Wed, 19 Feb 2025 14:29:51 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write
        cards-write customers cards cards-sensitive cards-sensitive-write transactions
        authorizations accounts statements customers-write payments-write","verificationToken":"NfbuuUElqwccu+ANgiLSwLeHTug47fS7br99NNwdeeQdAUgz6hgYFMPNvqJs326KsrRla87GcpK2Ef+RP1RBh9LMEadsxYjEgeMD9q8oZ/mIZIHtrX0S5TZSFKyZ9qLfEhXYbeKsLgXUoGlp4NLkYwWGik5ByrIcEi/vEqHcQ195BTLuzMlLUZ7tPVzVBWlSXBEi+cdLkY+/VJxw+z7ctUfZwhLCOCdmAQhUio+tM1GQQ/nKX5Y9J4/5oVT3MtwDRJc2QnvmSe4KGG4sG84qIl9YhPxGYJ2gjzZE6dbcFuzLRALNFOAb50znVJHMT9FgOlWMulQ2sDgp3Vp9gQriaHSWHLS3mw","verificationCode":"<NELCO_CLIENT_ID>2"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Wed, 19 Feb 2025 14:29:52 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '175'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"af-gDrThrBpQyPOL4e4Tka8igZD1Bg"
    body:
      encoding: UTF-8
      string: '{"errors":[{"title":"Verification attempt failed","status":"404","code":"verification_failed","details":"Verification
        attempt failed","detail":"Verification attempt failed"}]}'
  recorded_at: Wed, 19 Feb 2025 14:29:52 GMT
recorded_with: VCR 6.3.1
