---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 19 Feb 2025 14:49:39 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '3543'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"dd7-33pugCfmpNcJqlspq4Bn8MLx0IU"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Darrell","last":"Botsford2"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"3084758364"}},{"fullName":{"first":"Jonathan","last":"Evans"},"email":"jonathanevans@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Tom","last":"Win"},"email":"xqin@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7348812543"}},{"fullName":{"first":"Burl","last":"Goyette1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"30<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>623915"}},{"fullName":{"first":"Chung","last":"Vandervort1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Reed","last":"Mills1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Antonia","last":"Schmeler1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Elvie","last":"Kemmer3"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Tony","last":"Hermiston1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Sakif","last":"Imtiaz"},"email":"simtiaz@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"23<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678901"}},{"fullName":{"first":"Erlich","last":"Backman"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"23<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>678901"}},{"fullName":{"first":"Ellen
        ","last":" Ripley "},"email":"<EMAIL>","phone":{"countryCode":"1","number":"2813308004"}},{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},{"fullName":{"first":"Erlick","last":"Bachman"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"7346783<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>6"}},{"fullName":{"first":"Jow","last":"Gifford"},"email":"jgifford@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"7341112222"}},{"fullName":{"first":"Freda","last":"Dickens1"},"email":"<EMAIL>","phone":{"countryCode":"1","number":"61525613<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}},"authorizedUserResources":{"data":[{"type":"authorizedUserResource","id":"5849072"},{"type":"authorizedUserResource","id":"5849073"},{"type":"authorizedUserResource","id":"5849074"},{"type":"authorizedUserResource","id":"5849075"},{"type":"authorizedUserResource","id":"5849076"},{"type":"authorizedUserResource","id":"5849077"},{"type":"authorizedUserResource","id":"5849078"},{"type":"authorizedUserResource","id":"5849079"},{"type":"authorizedUserResource","id":"5849080"},{"type":"authorizedUserResource","id":"5849081"},{"type":"authorizedUserResource","id":"5849082"},{"type":"authorizedUserResource","id":"5849083"},{"type":"authorizedUserResource","id":"5849085"},{"type":"authorizedUserResource","id":"18827238"},{"type":"authorizedUserResource","id":"18828381"},{"type":"authorizedUserResource","id":"18829406"}]}}}}'
  recorded_at: Wed, 19 Feb 2025 14:49:39 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 19 Feb 2025 14:49:39 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - "<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>9"
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1cb-SM28Is2YnQIfeXnn2fzn/UUWr6w"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"tjEUBe4uuILH3C83xH0ibFasZCQDiI7h1ktwQ6ZYU9IXezEoQbssaSexXzV4mu/8fuJrMvS8Dx8n1EN+Xnx5sjYjl+eWAqvvfWcVUHR3LYvv2ffvm6ybtt8CTPQ4iYYO75M5clTdf2gcMp/PZTWIx6SOWvBQTfn+Tjqi3bqV6RV5rkWm7mnrBGXFTreHPDhxfAtvIWqxbWmfmNvvU/HZQPVC7T6kv0k7bmzRyHT3hazfd4PLkVeFuBsKPk7tzbIR3jyV5AF9SU0hCKwO3VjqnQaLjG7O680NJPp+B1P3q5oHEYK7s8sCqDeZjf2pTZbNTSTZ53prch5C+QjHn2y3CKyXukaSCg","phoneLast4Digits":"2775"}}}'
  recorded_at: Wed, 19 Feb 2025 14:49:39 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write
        cards-write customers cards cards-sensitive cards-sensitive-write transactions
        authorizations accounts statements customers-write payments-write","verificationToken":"tjEUBe4uuILH3C83xH0ibFasZCQDiI7h1ktwQ6ZYU9IXezEoQbssaSexXzV4mu/8fuJrMvS8Dx8n1EN+Xnx5sjYjl+eWAqvvfWcVUHR3LYvv2ffvm6ybtt8CTPQ4iYYO75M5clTdf2gcMp/PZTWIx6SOWvBQTfn+Tjqi3bqV6RV5rkWm7mnrBGXFTreHPDhxfAtvIWqxbWmfmNvvU/HZQPVC7T6kv0k7bmzRyHT3hazfd4PLkVeFuBsKPk7tzbIR3jyV5AF9SU0hCKwO3VjqnQaLjG7O680NJPp+B1P3q5oHEYK7s8sCqDeZjf2pTZbNTSTZ53prch5C+QjHn2y3CKyXukaSCg","verificationCode":"<NELCO_CLIENT_ID>1"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 19 Feb 2025 14:49:39 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '696'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"2b8-oGxo9lxrsYp1YPpQcSF/A06qL94"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvKzEyNDg5MjEyNzc1IiwiZXhwIjoiMjAyNS0wMi0yMFQxNDo0OTozOS44MTdaIiwianRpIjoiY3VzdG9tZXItNmVjMDAwZWUtNmM0NC00MzhkLTgzOGYtYjU0ZGFhYmJhMTcyIiwib3JnSWQiOiI0MzgxIiwic2NvcGUiOiJhY2NvdW50cy13cml0ZSBjYXJkcy13cml0ZSBjdXN0b21lcnMgY2FyZHMgY2FyZHMtc2Vuc2l0aXZlIGNhcmRzLXNlbnNpdGl2ZS13cml0ZSB0cmFuc2FjdGlvbnMgYXV0aG9yaXphdGlvbnMgYWNjb3VudHMgc3RhdGVtZW50cyBjdXN0b21lcnMtd3JpdGUgcGF5bWVudHMtd3JpdGUiLCJjdXN0b21lcklkIjoiMTQ5Nzc1NiIsInVzZXJUeXBlIjoiY3VzdG9tZXIifdj-DCuLS0itZJMrpFY3NuNVsa3MVlAPVlslC-JtAg8RVVqkhlGhuewmiVzb41YYE9UytTLQa1iOvH_vvx_weQ8","expiresIn":86400}}}'
  recorded_at: Wed, 19 Feb 2025 14:49:39 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":20000,"description":"Internal
        Transfer","idempotencyKey":"d42d21498e2517986a6a7c186f9c8cc5"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2413772"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"2657218"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 19 Feb 2025 14:49:41 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '577'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"241-DckcHnp3qZ7203YgseVYG1D5NmA"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","id":"5870027","attributes":{"createdAt":"2025-02-19T14:49:40.974Z","amount":20000,"direction":"Credit","description":"Internal
        Transfer","status":"Rejected","reason":"InsufficientFunds"},"relationships":{"account":{"data":{"type":"account","id":"2413772"}},"customer":{"data":{"type":"customer","id":"1497756"}},"customers":{"data":[{"type":"customer","id":"1497756"}]},"counterpartyAccount":{"data":{"type":"account","id":"2657218"}},"counterpartyCustomer":{"data":{"type":"customer","id":"1595761"}},"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Wed, 19 Feb 2025 14:49:41 GMT
recorded_with: VCR 6.3.1
