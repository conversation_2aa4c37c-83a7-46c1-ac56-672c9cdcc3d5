---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
            <Username>PAYLEASE_USER_ID</Username>
            <Password>PAYLEASE_PASSWORD</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>2</PayerReferenceId>
              <PayerFirstName>Ilana</PayerFirstName>
              <PayerLastName>Barton5</PayerLastName>
              <CreditCardType>MasterCard</CreditCardType>
              <CreditCardNumber>****************</CreditCardNumber>
              <CreditCardExpMonth>12</CreditCardExpMonth>
              <CreditCardExpYear>20</CreditCardExpYear>
              <CreditCardCcv2>123</CreditCardCcv2>
              <BillingFirstName>Ilana</BillingFirstName>
              <BillingLastName>Barton5</BillingLastName>
              <BillingStreetAddress>987 Misc. Ave., Apt 1.</BillingStreetAddress>
              <BillingCity>Sydney</BillingCity>
              <BillingState>MI</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>49426</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 24 Jul 2019 19:55:45 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=ro4g0avq98vl5aqms83ellqdkn; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '559'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
           <Username>PAYLEASE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
            <PayerReferenceId>2</PayerReferenceId>
            <GatewayPayerId>1608521</GatewayPayerId>
            <Code>10</Code>
            <Status>Approved</Status>
            <Message>Payer Account has been created successfully.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Wed, 24 Jul 2019 19:55:46 GMT
recorded_with: VCR 4.0.0
