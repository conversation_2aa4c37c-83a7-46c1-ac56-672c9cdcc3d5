---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
            <Username>PAYLEASE_USER_ID</Username>
            <Password>PAYLEASE_PASSWORD</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>TransactionDetail</TransactionAction>
              <TransactionId>29246599</TransactionId>
              <AlwaysShowCurrency>Y</AlwaysShowCurrency>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 12 Aug 2019 15:19:12 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=tvi0p71cibcck5t8m8992cvn2t; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '767'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>PAYLEASE_MERCHANT_ID</GatewayId>
           <Username>PAYLEASE_USER_ID</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>TransactionDetail</TransactionAction>
            <TransactionId>29246599</TransactionId>
            <PaymentReferenceId>1</PaymentReferenceId>
            <PayeeId>PAYLEASE_PAYEE_ID</PayeeId>
            <FeeAmount>0.00</FeeAmount>
            <TotalAmount>12.34</TotalAmount>
            <TransactionDate>07/27/2019</TransactionDate>
            <PayerReferenceId>1</PayerReferenceId>
            <PayerSecondaryReferenceId />
            <Code>8</Code>
            <Status>Approved</Status>
            <Message>This credit card transaction has been approved.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 12 Aug 2019 15:19:15 GMT
recorded_with: VCR 4.0.0
