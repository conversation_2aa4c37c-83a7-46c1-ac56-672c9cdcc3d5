---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/TransactionReporting.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:CreditsandDebitsTransactionDetailReport><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:batchId>2543961730</tns:batchId></tns:CreditsandDebitsTransactionDetailReport></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/CreditsandDebitsTransactionDetailReport"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '540'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Mon, 15 May 2017 12:23:29 GMT
      Content-Length:
      - '13350'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><CreditsandDebitsTransactionDetailReportResponse
        xmlns="https://ssl.selectpayment.com/PV"><CreditsandDebitsTransactionDetailReportResult><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Leslie
        Spinka</BatchDescription><Reason /><Amount>0.8400</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>jast11/Tenant/3</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T15:29:00</EffectiveDate><TransactionDate>2017-04-16T15:29:28.037</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>0.8400</TotalAmount><ReferenceNumber>17SLP5WGBA3</ReferenceNumber><TransactionNumber>kulasberge1/Payment/1</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>1623</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>52.7000</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Uncollected_NSF</TransactionStatus><SettlementStatus>Charged_Back</SettlementStatus><EffectiveDate>2017-04-17T13:12:00</EffectiveDate><TransactionDate>2017-04-17T13:12:28.957</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>52.7000</TotalAmount><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>95.9000</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:12:00</EffectiveDate><TransactionDate>2017-04-17T13:12:29.58</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>95.9000</TotalAmount><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>11.1700</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:12:00</EffectiveDate><TransactionDate>2017-04-17T13:12:29.863</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>11.1700</TotalAmount><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>92.2300</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Invalid__Closed_Account</TransactionStatus><SettlementStatus>Charged_Back</SettlementStatus><EffectiveDate>2017-04-17T13:12:00</EffectiveDate><TransactionDate>2017-04-17T13:12:30.003</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>92.2300</TotalAmount><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>67.7400</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:30.827</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>67.7400</TotalAmount><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>35.3600</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Invalid__Closed_Account</TransactionStatus><SettlementStatus>Charged_Back</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:30.93</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>35.3600</TotalAmount><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>35.9100</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:31.11</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>35.9100</TotalAmount><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>18.0900</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:31.243</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>18.0900</TotalAmount><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>60.4100</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Processed</TransactionStatus><SettlementStatus>Settled</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:31.72</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>60.4100</TotalAmount><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch><WSSettlementBatch><EntryType>Sale</EntryType><BatchDescription>Elfrieda
        Champlin</BatchDescription><Reason /><Amount>23.4800</Amount><transactionDetails><EntityId>375446</EntityId><LocationId>2196298</LocationId><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><PaymentOrigin>Internet</PaymentOrigin><AccountType>Checking</AccountType><OperationType>Sale</OperationType><TransactionStatus>Unauthorized</TransactionStatus><SettlementStatus>Charged_Back</SettlementStatus><EffectiveDate>2017-04-17T13:13:00</EffectiveDate><TransactionDate>2017-04-17T13:12:32.17</TransactionDate><Description>An
        Invoice Payment</Description><SourceApplication>Web_Service</SourceApplication><OriginatingAs>ACH</OriginatingAs><AuthResponse>Success</AuthResponse><TotalAmount>23.4800</TotalAmount><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><Field1
        /><Field2 /><Field3 /><DisplayAccountNumber>7220</DisplayAccountNumber><EmailAddress><EMAIL></EmailAddress><NotificationMethod>Merchant_Notify</NotificationMethod><FaceFeeType>Face</FaceFeeType></transactionDetails></WSSettlementBatch></CreditsandDebitsTransactionDetailReportResult></CreditsandDebitsTransactionDetailReportResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Mon, 15 May 2017 12:23:30 GMT
recorded_with: VCR 3.0.3
