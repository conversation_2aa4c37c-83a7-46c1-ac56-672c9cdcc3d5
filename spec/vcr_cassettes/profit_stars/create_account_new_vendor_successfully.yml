---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:RegisterCustomer><tns:storeId>1224290</tns:storeId><tns:storeKey>TcxVALKSdqKzLrC5NJQ45YTDlnO9</tns:storeKey><tns:entityId>378186</tns:entityId><tns:customer><tns:EntityId>378186</tns:EntityId><tns:IsCompany>true</tns:IsCompany><tns:CustomerNumber>bartoletti58/Vendor/4</tns:CustomerNumber><tns:FirstName
        xsi:nil="true"/><tns:LastName>Berge&#39;s open-source chemists</tns:LastName><tns:Email><EMAIL></tns:Email><tns:Address1>72875
        Zulauf Shores</tns:Address1><tns:Address2>Apt. 784</tns:Address2><tns:City>Herminiafort</tns:City><tns:StateRegion>Florida</tns:StateRegion><tns:PostalCode>70632</tns:PostalCode><tns:Country>United
        States</tns:Country></tns:customer></tns:RegisterCustomer></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/RegisterCustomer"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '986'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Fri, 23 Jun 2017 13:19:39 GMT
      Content-Length:
      - '462'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><RegisterCustomerResponse
        xmlns="https://ssl.selectpayment.com/PV"><RegisterCustomerResult><returnValue>Success</returnValue><message>Operation
        successful</message></RegisterCustomerResult></RegisterCustomerResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Fri, 23 Jun 2017 13:19:38 GMT
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:RegisterAccount><tns:storeId>1224290</tns:storeId><tns:storeKey>TcxVALKSdqKzLrC5NJQ45YTDlnO9</tns:storeKey><tns:entityId>378186</tns:entityId><tns:account><tns:CustomerNumber>bartoletti58/Vendor/4</tns:CustomerNumber><tns:AccountType>Checking</tns:AccountType><tns:NameOnAccount>Berge&#39;s
        open-source chemists</tns:NameOnAccount><tns:AccountNumber>80152</tns:AccountNumber><tns:RoutingNumber>*********</tns:RoutingNumber><tns:AccountReferenceId>bartoletti58/BankAccount/5</tns:AccountReferenceId></tns:account></tns:RegisterAccount></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/RegisterAccount"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '825'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Fri, 23 Jun 2017 13:19:38 GMT
      Content-Length:
      - '458'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><RegisterAccountResponse
        xmlns="https://ssl.selectpayment.com/PV"><RegisterAccountResult><returnValue>Success</returnValue><message>Operation
        successful</message></RegisterAccountResult></RegisterAccountResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Fri, 23 Jun 2017 13:19:38 GMT
recorded_with: VCR 3.0.3
