---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:GetRegisteredAccounts><tns:storeId>1224290</tns:storeId><tns:storeKey>TcxVALKSdqKzLrC5NJQ45YTDlnO9</tns:storeKey><tns:entityId>378186</tns:entityId><tns:customerNumber>bartoletti58/Vendor/4</tns:customerNumber></tns:GetRegisteredAccounts></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/GetRegisteredAccounts"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '529'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Fri, 23 Jun 2017 13:44:16 GMT
      Content-Length:
      - '1016'
    body:
      encoding: UTF-8
      string: '<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><GetRegisteredAccountsResponse
        xmlns="https://ssl.selectpayment.com/PV"><GetRegisteredAccountsResult><WSAccount><CustomerNumber>bartoletti58/Vendor/4</CustomerNumber><AccountType>Checking</AccountType><NameOnAccount>Berge''s
        open-source chemists</NameOnAccount><AccountName>Checking: XXXXXX0152</AccountName><AccountNumber>XXXXXX0152</AccountNumber><RoutingNumber>********</RoutingNumber><ExpirationMonth>0</ExpirationMonth><ExpirationYear>0</ExpirationYear><BillAddress1
        /><BillAddress2 /><BillCity /><BillStateRegion /><BillPostalCode /><BillCountry
        /><Approved><SSL_ENABLED></Approved><UsedForMatching><SSL_ENABLED></UsedForMatching><AccountReferenceID>7be3945f-43ba-47c9-b3c8-23574d32728f</AccountReferenceID></WSAccount></GetRegisteredAccountsResult></GetRegisteredAccountsResponse></soap:Body></soap:Envelope>'
    http_version: 
  recorded_at: Fri, 23 Jun 2017 13:44:16 GMT
recorded_with: VCR 3.0.3
