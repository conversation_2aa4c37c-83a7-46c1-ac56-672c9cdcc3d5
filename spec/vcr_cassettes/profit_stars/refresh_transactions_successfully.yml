---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/TransactionReporting.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:GetTransactionReport><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationIds>2196298</tns:locationIds><tns:beginTransDate>2017-04-16T20:19:08Z</tns:beginTransDate><tns:endTransDate>2017-04-19T20:19:08Z</tns:endTransDate><tns:wsdateType>Transactions_Created</tns:wsdateType><tns:wsopType>__NONE</tns:wsopType><tns:wsdisplayFields>__NONE</tns:wsdisplayFields></tns:GetTransactionReport></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/GetTransactionReport"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '762'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Wed, 19 Apr 2017 20:19:05 GMT
      Content-Length:
      - '6143'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><GetTransactionReportResponse
        xmlns="https://ssl.selectpayment.com/PV"><GetTransactionReportResult><WSTransactionReport><TransactionStatus>Uncollected
        NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:28.957</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:29.58</TransactionDateTime><TotalAmount>95.9000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:29.863</TransactionDateTime><TotalAmount>11.1700</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:30.003</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:30.827</TransactionDateTime><TotalAmount>67.7400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:30.93</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:31.11</TransactionDateTime><TotalAmount>35.9100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:31.243</TransactionDateTime><TotalAmount>18.0900</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:31.72</TransactionDateTime><TotalAmount>60.4100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport><WSTransactionReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>2017-04-17T13:12:32.17</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId></WSTransactionReport></GetTransactionReportResult></GetTransactionReportResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Wed, 19 Apr 2017 20:19:09 GMT
recorded_with: VCR 3.0.3
