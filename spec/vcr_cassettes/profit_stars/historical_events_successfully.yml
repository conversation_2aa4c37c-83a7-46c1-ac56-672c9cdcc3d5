---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/TransactionReporting.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:GetHistoricalEvents><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationIds>2196298</tns:locationIds><tns:beginTransDate>2017-04-16T20:19:08Z</tns:beginTransDate><tns:endTransDate>2017-04-19T20:19:08Z</tns:endTransDate><tns:wsauthResponseCode>__NONE</tns:wsauthResponseCode><tns:wsopType>__NONE</tns:wsopType><tns:wspaymentType>__NONE</tns:wspaymentType><tns:wstransEvent>__NONE</tns:wstransEvent><tns:wsdisplayFields>__NONE</tns:wsdisplayFields></tns:GetHistoricalEvents></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/GetHistoricalEvents"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '850'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Mon, 15 May 2017 12:37:08 GMT
      Content-Length:
      - '31784'
    body:
      encoding: UTF-8
      string: '<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><GetHistoricalEventsResponse
        xmlns="https://ssl.selectpayment.com/PV"><GetHistoricalEventsResult><WSEventReport><TransactionStatus>Uncollected
        NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:28.98</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:29.067</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>95.9000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:29.62</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>11.1700</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:29.673</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:29.987</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>18.0900</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:30.317</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>67.7400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:30.853</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.9100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:30.923</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:31.27</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>60.4100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T13:12:31.533</EventDateTime><EventType>Approved</EventType><EventDatastring>Success</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.9100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Leslie
        Spinka</NameOnAccount><TransactionNumber>kulasberge1/Payment/1</TransactionNumber><ReferenceNumber>17SLP5WGBA3</ReferenceNumber><CustomerNumber>jast11/Tenant/3</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>0.8400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Uncollected NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Invalid / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>11.1700</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>95.9000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>60.4100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>67.7400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>18.0900</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-17T18:00:00</EventDateTime><EventType>Processed</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Invalid / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Invalid / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>11.1700</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>60.4100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>67.7400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>18.0900</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Leslie
        Spinka</NameOnAccount><TransactionNumber>kulasberge1/Payment/1</TransactionNumber><ReferenceNumber>17SLP5WGBA3</ReferenceNumber><CustomerNumber>jast11/Tenant/3</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>0.8400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Uncollected NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Invalid / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Invalid / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/10</TransactionNumber><ReferenceNumber>J0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>11.1700</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>95.9000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/11</TransactionNumber><ReferenceNumber>4NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>95.9000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/4</TransactionNumber><ReferenceNumber>Y0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>60.4100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.9100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/6</TransactionNumber><ReferenceNumber>S0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.9100</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/7</TransactionNumber><ReferenceNumber>8NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>67.7400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/8</TransactionNumber><ReferenceNumber>M0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>18.0900</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Processed</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Leslie
        Spinka</NameOnAccount><TransactionNumber>kulasberge1/Payment/1</TransactionNumber><ReferenceNumber>17SLP5WGBA3</ReferenceNumber><CustomerNumber>jast11/Tenant/3</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>0.8400</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Originated</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Uncollected NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-18T00:00:00</EventDateTime><EventType>Settled</EventType><EventDatastring
        /></WSEventReport><WSEventReport><TransactionStatus>Uncollected NSF</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/13</TransactionNumber><ReferenceNumber>3NQPK5WGBA2</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>52.7000</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-19T08:06:00</EventDateTime><EventType>Returned
        NSF</EventType><EventDatastring>Reason: Insufficient Funds</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/12</TransactionNumber><ReferenceNumber>90QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>92.2300</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-19T08:06:00</EventDateTime><EventType>Returned
        Bad Account</EventType><EventDatastring>Reason: Account Closed</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Invalid
        / Closed Account</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/9</TransactionNumber><ReferenceNumber>L0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>35.3600</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-19T08:07:00</EventDateTime><EventType>Returned
        Bad Account</EventType><EventDatastring>Reason: Invalid Account Number Structure</EventDatastring></WSEventReport><WSEventReport><TransactionStatus>Unauthorized</TransactionStatus><PaymentType>Checking</PaymentType><NameOnAccount>Elfrieda
        Champlin</NameOnAccount><TransactionNumber>marketplace-sandbox/Payment/5</TransactionNumber><ReferenceNumber>W0QSP5WGBA3</ReferenceNumber><CustomerNumber>marketplace-sandbox/Tenant/2060</CustomerNumber><OperationType>Sale</OperationType><LocationName>Location
        1</LocationName><TransactionDateTime>0001-01-01T00:00:00</TransactionDateTime><TotalAmount>23.4800</TotalAmount><OwnerAppReferenceId>0</OwnerAppReferenceId><EventDateTime>2017-04-19T08:07:00</EventDateTime><EventType>Unauthorized</EventType><EventDatastring>Reason:
        Unauthorized Debit To Customer Account</EventDatastring></WSEventReport></GetHistoricalEventsResult></GetHistoricalEventsResponse></soap:Body></soap:Envelope>'
    http_version: 
  recorded_at: Mon, 15 May 2017 12:37:07 GMT
recorded_with: VCR 3.0.3
