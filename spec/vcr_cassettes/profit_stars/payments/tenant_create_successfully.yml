---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:SaleFromBankAccount><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationId>2196298</tns:locationId><tns:accountReferenceId>7647391b-bc76-4aca-8758-e8aa12587a67</tns:accountReferenceId><tns:Amount>0.84</tns:Amount><tns:TransactionNumber>kulasberge1/Payment/1</tns:TransactionNumber><tns:Description>An
        Invoice Payment</tns:Description><tns:clientIP>127.0.0.1</tns:clientIP></tns:SaleFromBankAccount></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/SaleFromBankAccount"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '776'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Sun, 16 Apr 2017 20:29:27 GMT
      Content-Length:
      - '635'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><SaleFromBankAccountResponse
        xmlns="https://ssl.selectpayment.com/PV"><SaleFromBankAccountResult><ReferenceNumber>17SLP5WGBA3</ReferenceNumber><Success>true</Success><Error><SSL_ENABLED></Error><ResponseCode>Success</ResponseCode><ActualDate>2017-04-17T15:29:28.0204969-05:00</ActualDate><ResponseMessage
        /><OriginatedAs>ACH</OriginatedAs></SaleFromBankAccountResult></SaleFromBankAccountResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Sun, 16 Apr 2017 20:29:28 GMT
recorded_with: VCR 3.0.3
