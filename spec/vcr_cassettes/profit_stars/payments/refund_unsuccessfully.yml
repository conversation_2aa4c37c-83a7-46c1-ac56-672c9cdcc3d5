---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:RefundTransaction><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationId>2196298</tns:locationId><tns:originalReferenceNumber>17SLP5WGBA3</tns:originalReferenceNumber></tns:RefundTransaction></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/RefundTransaction"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '569'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Wed, 10 May 2017 15:35:18 GMT
      Content-Length:
      - '775'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><RefundTransactionResponse
        xmlns="https://ssl.selectpayment.com/PV"><RefundTransactionResult><ReferenceNumber>T:GXR1JYZGBA2</ReferenceNumber><Success><SSL_ENABLED></Success><Error>true</Error><ResponseCode>Error_Not_Subscribed</ResponseCode><ActualDate>9999-12-31T23:59:59.9999999</ActualDate><ResponseMessage>You
        are currently not configured to use the service you requested. Please contact
        customer service to enable this feature.</ResponseMessage><OriginatedAs>None</OriginatedAs></RefundTransactionResult></RefundTransactionResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Wed, 10 May 2017 15:35:12 GMT
recorded_with: VCR 3.0.3
