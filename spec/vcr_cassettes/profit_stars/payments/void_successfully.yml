---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:VoidTransaction><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationId>2196298</tns:locationId><tns:originalReferenceNumber>T:8Q9MKYZGBA3</tns:originalReferenceNumber></tns:VoidTransaction></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/VoidTransaction"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '567'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Tue, 09 May 2017 16:11:35 GMT
      Content-Length:
      - '613'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><VoidTransactionResponse
        xmlns="https://ssl.selectpayment.com/PV"><VoidTransactionResult><ReferenceNumber>T:Y5BMKYZGBA3</ReferenceNumber><Success>true</Success><Error><SSL_ENABLED></Error><ResponseCode>Success</ResponseCode><ActualDate>2017-05-09T00:00:00-05:00</ActualDate><ResponseMessage
        /><OriginatedAs>ACH</OriginatedAs></VoidTransactionResult></VoidTransactionResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Tue, 09 May 2017 16:11:29 GMT
recorded_with: VCR 3.0.3
