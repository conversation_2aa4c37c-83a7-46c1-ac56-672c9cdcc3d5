---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:VoidTransaction><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationId>2196298</tns:locationId><tns:originalReferenceNumber>T:8Q9MKYZGBA2</tns:originalReferenceNumber></tns:VoidTransaction></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/VoidTransaction"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '567'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      Vary:
      - Accept-Encoding
      X-Frame-Options:
      - SAMEORIGIN
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=15552000; includeSubDomains
      X-Xss-Protection:
      - '1'
      X-Content-Type-Options:
      - nosniff
      Date:
      - Thu, 07 Feb 2019 18:49:48 GMT
      Content-Length:
      - '554'
    body:
      encoding: ASCII-8BIT
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><VoidTransactionResponse
        xmlns="https://ssl.selectpayment.com/PV"><VoidTransactionResult><ReferenceNumber>T:8Q9MKYZGBA2</ReferenceNumber><Success><SSL_ENABLED></Success><Error>true</Error><ResponseCode>Error_Transaction_Not_Found</ResponseCode><ActualDate>2019-02-07T12:49:49.1359841-06:00</ActualDate><ResponseMessage>Unable
        to perform this operation. The original transaction reference number ( T:8Q9MKYZGBA2
        ) was not found.</ResponseMessage><OriginatedAs>None</OriginatedAs></VoidTransactionResult></VoidTransactionResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Thu, 07 Feb 2019 18:49:48 GMT
recorded_with: VCR 3.0.3
