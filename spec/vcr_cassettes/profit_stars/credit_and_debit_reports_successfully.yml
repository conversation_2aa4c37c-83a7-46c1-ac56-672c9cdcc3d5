---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/TransactionReporting.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:CreditandDebitReports><tns:storeId>1212366</tns:storeId><tns:storeKey>MWvokxgXLrYjs6aT70uDZ+QNVH1j</tns:storeKey><tns:entityId>375446</tns:entityId><tns:locationIds>2196298</tns:locationIds><tns:beginDate>2017-04-16T20:19:08Z</tns:beginDate><tns:endDate>2017-04-19T20:19:08Z</tns:endDate></tns:CreditandDebitReports></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/CreditandDebitReports"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '607'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      X-Frame-Options:
      - sameorigin
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=5184000; includeSubDomains
      Date:
      - Mon, 15 May 2017 12:19:17 GMT
      Content-Length:
      - '1570'
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><CreditandDebitReportsResponse
        xmlns="https://ssl.selectpayment.com/PV"><CreditandDebitReportsResult><xs:schema
        id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata"><xs:element
        name="NewDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true"><xs:complexType><xs:choice
        minOccurs="0" maxOccurs="unbounded"><xs:element name="Table"><xs:complexType><xs:sequence><xs:element
        name="BatchStatus" type="xs:string" minOccurs="0" /><xs:element name="EffectiveDate"
        type="xs:dateTime" minOccurs="0" /><xs:element name="BatchID" type="xs:long"
        minOccurs="0" /><xs:element name="Description" type="xs:string" minOccurs="0"
        /><xs:element name="Amount" type="xs:decimal" minOccurs="0" /></xs:sequence></xs:complexType></xs:element></xs:choice></xs:complexType></xs:element></xs:schema><diffgr:diffgram
        xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:diffgr="urn:schemas-microsoft-com:xml-diffgram-v1"><NewDataSet
        xmlns=""><Table diffgr:id="Table1" msdata:rowOrder="0"><BatchStatus>Processed</BatchStatus><EffectiveDate>2017-04-18T00:00:00-05:00</EffectiveDate><BatchID>2543961730</BatchID><Description>Settlement</Description><Amount>493.8300</Amount></Table></NewDataSet></diffgr:diffgram></CreditandDebitReportsResult></CreditandDebitReportsResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Mon, 15 May 2017 12:19:18 GMT
recorded_with: VCR 3.0.3
