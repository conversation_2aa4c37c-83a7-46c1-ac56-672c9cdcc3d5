---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/PaymentVault.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:VoidTransaction><tns:storeId>1224290</tns:storeId><tns:storeKey>N9F8MD1vKmL05RVa6giUOpOhrSZW</tns:storeKey><tns:entityId>378186</tns:entityId><tns:locationId>2219374</tns:locationId><tns:originalReferenceNumber>V17B9XYJLA5</tns:originalReferenceNumber></tns:VoidTransaction></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/VoidTransaction"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '565'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      Vary:
      - Accept-Encoding
      X-Frame-Options:
      - SAMEORIGIN
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=15552000; includeSubDomains
      X-Xss-Protection:
      - '1'
      X-Content-Type-Options:
      - nosniff
      Date:
      - Thu, 07 Feb 2019 18:49:16 GMT
      Content-Length:
      - '464'
    body:
      encoding: ASCII-8BIT
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><VoidTransactionResponse
        xmlns="https://ssl.selectpayment.com/PV"><VoidTransactionResult><ReferenceNumber>QP63BXYJLA1</ReferenceNumber><Success>true</Success><Error><SSL_ENABLED></Error><ResponseCode>Success</ResponseCode><ActualDate>2019-02-07T00:00:00-06:00</ActualDate><ResponseMessage
        /><OriginatedAs>ACH</OriginatedAs></VoidTransactionResult></VoidTransactionResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Thu, 07 Feb 2019 18:49:16 GMT
recorded_with: VCR 3.0.3
