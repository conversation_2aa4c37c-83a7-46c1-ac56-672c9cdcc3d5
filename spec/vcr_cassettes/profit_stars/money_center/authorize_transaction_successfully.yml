---
http_interactions:
- request:
    method: post
    uri: https://ws.eps.profitstars.com/PV/moneycenter.asmx
    body:
      encoding: UTF-8
      string: <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="https://ssl.selectpayment.com/PV"
        xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"><env:Body><tns:AuthorizeMoneyCenterTransaction><tns:storeId>1224290</tns:storeId><tns:storeKey>N9F8MD1vKmL05RVa6giUOpOhrSZW</tns:storeKey><tns:customerNumber>bartoletti58/Vendor/4</tns:customerNumber><tns:accountReferenceId>7be3945f-43ba-47c9-b3c8-23574d32728f</tns:accountReferenceId><tns:moneycenterTransaction><tns:EntityId>378186</tns:EntityId><tns:LocationId>2219374</tns:LocationId><tns:PaymentOrigin>Signature_Original</tns:PaymentOrigin><tns:AccountType>Checking</tns:AccountType><tns:OperationType>Credit</tns:OperationType><tns:Description>An
        Invoice Payment</tns:Description><tns:IpAddressOfOriginator>127.0.0.1</tns:IpAddressOfOriginator><tns:TotalAmount>91.05</tns:TotalAmount><tns:TransactionNumber>alever/Payment/1</tns:TransactionNumber><tns:IsBusinessPayment>1</tns:IsBusinessPayment><tns:NameOnAccount>Uriel
        Wiegand</tns:NameOnAccount><tns:BillingStateRegion>MT</tns:BillingStateRegion><tns:EmailAddress><EMAIL></tns:EmailAddress></tns:moneycenterTransaction></tns:AuthorizeMoneyCenterTransaction></env:Body></env:Envelope>
    headers:
      Soapaction:
      - '"https://ssl.selectpayment.com/PV/AuthorizeMoneyCenterTransaction"'
      Content-Type:
      - text/xml;charset=UTF-8
      Content-Length:
      - '1309'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private, max-age=0
      Content-Type:
      - text/xml; charset=utf-8
      Vary:
      - Accept-Encoding
      X-Frame-Options:
      - SAMEORIGIN
      X-Robots-Tag:
      - none
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      X-Xss-Protection:
      - '1'
      X-Content-Type-Options:
      - nosniff
      Date:
      - Thu, 07 Feb 2019 18:40:19 GMT
      Content-Length:
      - '489'
    body:
      encoding: ASCII-8BIT
      string: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><AuthorizeMoneyCenterTransactionResponse
        xmlns="https://ssl.selectpayment.com/PV"><AuthorizeMoneyCenterTransactionResult><ReferenceNumber>V17B9XYJLA5</ReferenceNumber><Success>true</Success><Error><SSL_ENABLED></Error><ResponseCode>Success</ResponseCode><ActualDate>2019-02-07T12:40:20.2878599-06:00</ActualDate><ResponseMessage
        /><OriginatedAs>ACH</OriginatedAs></AuthorizeMoneyCenterTransactionResult></AuthorizeMoneyCenterTransactionResponse></soap:Body></soap:Envelope>
    http_version: 
  recorded_at: Thu, 07 Feb 2019 18:40:19 GMT
recorded_with: VCR 3.0.3
