---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/accounts/2471787/transactions/8432770"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Sat, 14 Sep 2024 02:54:42 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '849'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"351-Z8AERk5Oo3/7ABGHmFgNPz1k1sg"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"purchaseTransaction","id":"8432770","attributes":{"createdAt":"2024-09-11T18:03:02.902Z","amount":3000,"direction":"Debit","balance":1576763,"summary":"Purchase
        from Apple Inc.  |  Address: Cupertino, CA  |  **0169","cardLast4Digits":"0169","merchant":{"name":"Apple
        Inc.","type":2000,"category":"","location":"Cupertino, CA"},"coordinates":{"longitude":-77.0364,"latitude":38.8951},"recurring":<SSL_ENABLED>,"interchange":null,"grossInterchange":null,"ecommerce":<SSL_ENABLED>,"cardPresent":true,"internationalServiceFee":null,"paymentMethod":"Swipe","cardNetwork":"Visa"},"relationships":{"account":{"data":{"type":"account","id":"2471787"}},"customer":{"data":{"type":"customer","id":"1584794"}},"customers":{"data":[{"type":"customer","id":"1584794"}]},"org":{"data":{"type":"org","id":"4381"}},"card":{"data":{"type":"card","id":"2286206"}}}}}'
  recorded_at: Sat, 14 Sep 2024 02:54:42 GMT
recorded_with: VCR 6.2.0
