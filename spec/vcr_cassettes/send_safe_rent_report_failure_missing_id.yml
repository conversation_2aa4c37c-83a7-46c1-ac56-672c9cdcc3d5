---
http_interactions:
- request:
    method: post
    uri: https://vendors.residentscreening.net/b2b/demits.aspx
    body:
      encoding: UTF-8
      string: |-
        <?xml version="1.0"?>
          <ApplicantScreening xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:MITS="http://my-company.com/namespace">
            <Request>
                <PropertyID>
                    <MITS:Identification Type="other">
                        <MITS:PrimaryID></MITS:PrimaryID>
                        <MITS:MarketingName></MITS:MarketingName>
                        <MITS:WebSite>http://postbacks-receiver.srs.saferent.com/api/ResponseLog</MITS:WebSite>
                    </MITS:Identification>
                </PropertyID>
                <RequestType>ViewPDF</RequestType>
                <ReportOptions>
                    <ReportName></ReportName>
                </ReportOptions>
                <ReportID>69025727</ReportID>
            <OriginatorID>100005</OriginatorID>
            <MarketingSource/>
            </Request>
            <LeaseTerms xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <MonthlyRent>700</MonthlyRent>
                <LeaseMonths>12</LeaseMonths>
                <SecurityDeposit>500</SecurityDeposit>
            </LeaseTerms>
            <Applicant xmlns:xsi="http://wwww.w3.org/2001/XMLSchema-instance">
                <Other>
                    <CurrentRent>650</CurrentRent>
                    <ConsentObtained>YES</ConsentObtained>
                </Other>
                <Income>
                    <EmploymentStatus/>
                    <EmploymentType>Current</EmploymentType>
                    <EmploymentGrossIncome>4</EmploymentGrossIncome>
                    <OtherIncomeAmount>0.0000</OtherIncomeAmount>
                    <OtherIncomeType/>
                    <OtherIncomePeriod>Month</OtherIncomePeriod>
                    <Assets>0.0000</Assets>
                    <TotalLiabilities>0.0000</TotalLiabilities>
                </Income>
                <AS_Information>
                    <ApplicantIdentifier>t001</ApplicantIdentifier>
                    <ApplicantType>Applicant</ApplicantType>
                    <GuarantorFor/>
                    <Spouse/>
                    <Birthdate>1927-06-25</Birthdate>
                    <SocSecNumber>666544745</SocSecNumber>
                    <DriversLicenseID></DriversLicenseID>
                    <DriversLicenseState></DriversLicenseState>
                </AS_Information>
                <Customers>
                    <MITS:Customer Type="applicant">
                    <MITS:CustomerID>t001</MITS:CustomerID>
                    <MITS:Name>
                        <MITS:NamePrefix/>
                        <MITS:FirstName>EDWIN</MITS:FirstName>
                        <MITS:NoMiddleName>false</MITS:NoMiddleName>
                        <MITS:MiddleName></MITS:MiddleName>
                        <MITS:LastName>AVILA</MITS:LastName>
                        <MITS:MaidenName/>
                        <MITS:NameSuffix/>
                    </MITS:Name>
                    <MITS:Address Type="current">
                        <MITS:Address1>85 Rebecca LN </MITS:Address1>
                        <MITS:Address2>#01</MITS:Address2>
                        <MITS:City>YOUNGSVILLE</MITS:City>
                        <MITS:State>NC</MITS:State>
                        <MITS:PostalCode>27596</MITS:PostalCode>
                        <MITS:Country>United States</MITS:Country>
                        <MITS:UnparsedAddress>85 Rebecca LN</MITS:UnparsedAddress>
                        <MITS:Email><EMAIL></MITS:Email>
                    </MITS:Address>
                    <MITS:Phone Type="home">
                        <MITS:PhoneNumber/>
                    </MITS:Phone>
                    <MITS:Phone Type="cell">
                        <MITS:PhoneNumber>2500000001</MITS:PhoneNumber>
                    </MITS:Phone>
                    <MITS:Phone Type="office">
                        <MITS:PhoneNumber/>
                    </MITS:Phone>
                    </MITS:Customer>
                </Customers>
            </Applicant>
        </ApplicantScreening>
        
    headers:
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
        - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 28 Feb 2024 17:25:57 GMT
      Content-Type:
      - text/xml; charset=utf-8
      Transfer-Encoding:
      - chunked
      Connection:
      - close
      Cache-Control:
      - private
      Pragma:
      - no-cache
      Set-Cookie:
      - ASP.NET_SessionId=2pz31emozahtvpibmljsfvvp; path=/; HttpOnly; SameSite=Lax
      - ASP.NET_SessionId=2pz31emozahtvpibmljsfvvp; path=/; HttpOnly; SameSite=Lax
      - __AntiXsrfToken=0895fcdfc8944b84bf14fe4a6978b01f; path=/; HttpOnly
      - __cf_bm=J5EwbXz8.C079EOqbiCuotTnu9stquvcxc6mdOq8LCs-1709141157-1.0-AYJqpyllWOl66r/Wufj8FNet+9KXkg7oR+3gIzjmDYZi93ZL08s9drH2UIe2BNws35gWvnCbS49yTyOh+6dfrBM=;
        path=/; expires=Wed, 28-Feb-24 17:55:57 GMT; domain=.residentscreening.net;
        HttpOnly; Secure; SameSite=None
      Cachecontrol:
      - no-cache
      X-Powered-By:
      - ASP.NET
      Cf-Cache-Status:
      - DYNAMIC
      Server:
      - cloudflare
      Cf-Ray:
      - 85ca5e571e7a60a7-ORD
    body:
      encoding: UTF-8
      string: |-
        <html>

            <head>
            </head>

            <body>
                Error loading Document :<br />
                                                ----------------------------<br />
                                                Error Code : <label id="errorCodeLabel">3002</label><br />
                                                Reason:
                <label id = "errorDescriptionLabel">Invalid request XML for ViewPDF: missing ReportId value</label>
            </body>

            </html>
  recorded_at: Wed, 28 Feb 2024 17:25:57 GMT
recorded_with: VCR 6.2.0
