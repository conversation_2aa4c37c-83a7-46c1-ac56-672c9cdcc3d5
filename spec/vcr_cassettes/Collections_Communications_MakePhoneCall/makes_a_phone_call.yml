---
http_interactions:
- request:
    method: get
    uri: https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.77.0 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 28 Aug 2023 16:33:51 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '840'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQ867d073c3b465b060898c0bf956acabe
      Twilio-Request-Duration:
      - '0.011'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      Strict-Transport-Security:
      - max-age=********
      Twilio-Concurrent-Requests:
      - '1'
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - messaging.twilio.com
    body:
      encoding: UTF-8
      string: '{"phone_numbers": [{"phone_number": "+***********", "date_updated":
        "2023-08-22T15:01:16Z", "capabilities": ["MMS", "SMS", "Voice"], "account_sid":
        "<TWILIO_SID>", "url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers/PN4c93a2ea601026f0dbb16ee1f93c42b0",
        "country_code": "US", "sid": "PN4c93a2ea601026f0dbb16ee1f93c42b0", "date_created":
        "2023-08-22T15:01:16Z", "service_sid": "MG78c6211de019ffab6996ae056075c558"}],
        "meta": {"page": 0, "page_size": 50, "first_page_url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers?PageSize=50&Page=0",
        "previous_page_url": null, "url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers?PageSize=50&Page=0",
        "next_page_url": null, "key": "phone_numbers"}}'
  recorded_at: Mon, 28 Aug 2023 16:33:51 GMT
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Calls.json
    body:
      encoding: UTF-8
      string: From=%2B***********&Method=GET&StatusCallback=http%3A%2F%2Falever.lvh.me%3A3001%2Ftelephony%2Fcollections%2Fcommunications%2Fphone_calls%2F33&StatusCallbackMethod=PATCH&To=%2B***********&Url=http%3A%2F%2Falever.lvh.me%3A3001%2Ftelephony%2Fcollections%2Fcommunications%2Fphone_calls%2F33
    headers:
      User-Agent:
      - twilio-ruby/5.77.0 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Mon, 28 Aug 2023 16:33:51 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '2107'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQcae622b297e5866d32b1e9cbf83d439d
      Twilio-Request-Duration:
      - '0.080'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      Strict-Transport-Security:
      - max-age=********
      Twilio-Concurrent-Requests:
      - '1'
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
    body:
      encoding: UTF-8
      string: '{"date_updated": null, "price_unit": "USD", "parent_call_sid": null,
        "caller_name": null, "duration": null, "from": "+***********", "to": "+***********",
        "annotation": null, "answered_by": null, "sid": "CA22da16eff8bc04d121ab3110ef7a2599",
        "queue_time": "0", "price": null, "api_version": "2010-04-01", "status": "queued",
        "direction": "outbound-api", "start_time": null, "date_created": null, "from_formatted":
        "(*************", "group_sid": null, "trunk_sid": null, "forwarded_from":
        null, "uri": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599.json",
        "account_sid": "<TWILIO_SID>", "end_time": null, "to_formatted": "(*************",
        "phone_number_sid": "PN4c93a2ea601026f0dbb16ee1f93c42b0", "subresource_uris":
        {"feedback": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Feedback.json",
        "user_defined_messages": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/UserDefinedMessages.json",
        "notifications": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Notifications.json",
        "recordings": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Recordings.json",
        "streams": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Streams.json",
        "payments": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Payments.json",
        "user_defined_message_subscriptions": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/UserDefinedMessageSubscriptions.json",
        "siprec": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Siprec.json",
        "events": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/CA22da16eff8bc04d121ab3110ef7a2599/Events.json",
        "feedback_summaries": "/2010-04-01/Accounts/<TWILIO_SID>/Calls/FeedbackSummary.json"}}'
  recorded_at: Mon, 28 Aug 2023 16:33:51 GMT
recorded_with: VCR 6.2.0
