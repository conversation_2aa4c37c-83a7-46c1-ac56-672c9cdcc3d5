---
http_interactions:
- request:
    method: get
    uri: http://api.geonames.org/timezoneJSON?lat=42.3314&lng=-83.0<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>8&username=<GEONAMES_USERNAME>
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      User-Agent:
      - rest-client/2.1.0 (freebsd14.1 x86_64) ruby/3.1.6p260
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Host:
      - api.geonames.org
  response:
    status:
      code: 200
      message: '200'
    headers:
      Date:
      - Sun, 26 Jan 2025 17:41:04 GMT
      Server:
      - Apache/2.4.6 (CentOS) mod_jk/1.2.46 OpenSSL/1.0.2k-fips
      Access-Control-Allow-Origin:
      - "*"
      X-Frame-Options:
      - sameorigin
      Cache-Control:
      - no-cache
      Transfer-Encoding:
      - chunked
      Content-Type:
      - application/json;charset=UTF-8
    body:
      encoding: UTF-8
      string: '{"sunrise":"2025-01-26 07:51","lng":-83.0<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>8,"countryCode":"US","gmtOffset":-5,"rawOffset":-5,"sunset":"2025-01-26
        17:39","timezoneId":"America/Detroit","dstOffset":-4,"countryName":"United
        States","time":"2025-01-26 12:41","lat":42.3314}'
  recorded_at: Sun, 26 Jan 2025 17:41:04 GMT
recorded_with: VCR 6.3.1
