---
http_interactions:
- request:
    method: get
    uri: https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - twilio-ruby/5.77.0 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 22 Aug 2023 15:10:24 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '840'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQf9a9f6d28d9ffe66aa41aced69614887
      Twilio-Request-Duration:
      - '0.027'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      Strict-Transport-Security:
      - max-age=********
      Twilio-Concurrent-Requests:
      - '1'
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - messaging.twilio.com
    body:
      encoding: UTF-8
      string: '{"phone_numbers": [{"phone_number": "+***********", "date_updated":
        "2023-08-22T15:01:16Z", "capabilities": ["MMS", "SMS", "Voice"], "account_sid":
        "<TWILIO_SID>", "url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers/PN4c93a2ea601026f0dbb16ee1f93c42b0",
        "country_code": "US", "sid": "PN4c93a2ea601026f0dbb16ee1f93c42b0", "date_created":
        "2023-08-22T15:01:16Z", "service_sid": "MG78c6211de019ffab6996ae056075c558"}],
        "meta": {"page": 0, "page_size": 50, "first_page_url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers?PageSize=50&Page=0",
        "previous_page_url": null, "url": "https://messaging.twilio.com/v1/Services/MG78c6211de019ffab6996ae056075c558/PhoneNumbers?PageSize=50&Page=0",
        "next_page_url": null, "key": "phone_numbers"}}'
  recorded_at: Tue, 22 Aug 2023 15:10:24 GMT
- request:
    method: post
    uri: https://api.twilio.com/2010-04-01/Accounts/<TWILIO_SID>/Messages.json
    body:
      encoding: UTF-8
      string: Body=You+are+more+than+30+days+past+due+on+your+balance+owed.+Please+log+in+to+your+account+here+to+make+a+payment%3A+http%3A%2F%2Falever.lvh.me%3A3001%2Ftenants%2Flogin&From=%2B***********&To=%2B***********
    headers:
      User-Agent:
      - twilio-ruby/5.77.0 (darwin22 arm64) Ruby/3.1.3
      Accept-Charset:
      - utf-8
      Content-Type:
      - application/x-www-form-urlencoded
      Accept:
      - application/json
      Authorization:
      - Basic ********************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Tue, 22 Aug 2023 15:10:24 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '914'
      Connection:
      - keep-alive
      Twilio-Request-Id:
      - RQ5ff53bca7904ff331630d5869458a331
      Twilio-Request-Duration:
      - '0.223'
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Headers:
      - Accept, Authorization, Content-Type, If-Match, If-Modified-Since, If-None-Match,
        If-Unmodified-Since, Idempotency-Key
      Access-Control-Allow-Methods:
      - GET, POST, DELETE, OPTIONS
      Access-Control-Expose-Headers:
      - ETag
      Access-Control-Allow-Credentials:
      - 'true'
      X-Powered-By:
      - AT-5000
      Strict-Transport-Security:
      - max-age=********
      Twilio-Concurrent-Requests:
      - '1'
      X-Shenanigans:
      - none
      X-Home-Region:
      - us1
      X-Api-Domain:
      - api.twilio.com
    body:
      encoding: UTF-8
      string: '{"body": "You are more than 30 days past due on your balance owed.
        Please log in to your account here to make a payment: http://alever.lvh.me:3001/tenants/login",
        "num_segments": "1", "direction": "outbound-api", "from": "+***********",
        "date_updated": "Tue, 22 Aug 2023 15:10:24 +0000", "price": null, "error_message":
        null, "uri": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM5ff53bca7904ff331630d5869458a331.json",
        "account_sid": "<TWILIO_SID>", "num_media": "0", "to": "+***********", "date_created":
        "Tue, 22 Aug 2023 15:10:24 +0000", "status": "queued", "sid": "SM5ff53bca7904ff331630d5869458a331",
        "date_sent": null, "messaging_service_sid": null, "error_code": null, "price_unit":
        "USD", "api_version": "2010-04-01", "subresource_uris": {"media": "/2010-04-01/Accounts/<TWILIO_SID>/Messages/SM5ff53bca7904ff331630d5869458a331/Media.json"}}'
  recorded_at: Tue, 22 Aug 2023 15:10:24 GMT
recorded_with: VCR 6.2.0
