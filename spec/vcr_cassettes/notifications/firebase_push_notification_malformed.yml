---
http_interactions:
- request:
    method: post
    uri: https://fcm.googleapis.com/fcm/send
    body:
      encoding: UTF-8
      string: '{"value":"incorrect"}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - key=<FIREBASE_FCM_SERVER_KEY>
      Connection:
      - close
      Host:
      - fcm.googleapis.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Content-Type:
      - text/plain; charset=UTF-8
      Date:
      - Fri, 29 Jun 2018 13:24:01 GMT
      Expires:
      - Fri, 29 Jun 2018 13:24:01 GMT
      Cache-Control:
      - private, max-age=0
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      Server:
      - GSE
      Alt-Svc:
      - quic=":443"; ma=2592000; v="43,42,41,39,35"
      Accept-Ranges:
      - none
      Vary:
      - Accept-Encoding
      Connection:
      - close
    body:
      encoding: UTF-8
      string: 'to

'
    http_version: 
  recorded_at: Fri, 29 Jun 2018 13:24:01 GMT
recorded_with: VCR 3.0.3
