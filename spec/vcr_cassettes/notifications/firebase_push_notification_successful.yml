---
http_interactions:
- request:
    method: post
    uri: https://fcm.googleapis.com/fcm/send
    body:
      encoding: UTF-8
      string: '{"registration_ids":["token_abc","token_xyz"],"priority":"high","content_avialable":true,"delay_while_idle":<SSL_ENABLED>,"time_to_live":86400,"notification":{"title":"Title","body":"Body"}}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - key=<FIREBASE_FCM_SERVER_KEY>
      Connection:
      - close
      Host:
      - fcm.googleapis.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Fri, 29 Jun 2018 13:36:12 GMT
      Expires:
      - Fri, 29 Jun 2018 13:36:12 GMT
      Cache-Control:
      - private, max-age=0
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      Server:
      - GSE
      Alt-Svc:
      - quic=":443"; ma=2592000; v="43,42,41,39,35"
      Accept-Ranges:
      - none
      Vary:
      - Accept-Encoding
      Connection:
      - close
    body:
      encoding: UTF-8
      string: '{"multicast_id":5465512133909590400,"success":2,"failure":0,"canonical_ids":0,"results":[{"message_id":"0:1530279372932868%34a5cdb034a5cdb0"},{"message_id":"0:1530279372933539%34a5cdb034a5cdb0"}]}'
    http_version: 
  recorded_at: Fri, 29 Jun 2018 13:36:12 GMT
recorded_with: VCR 3.0.3
