---
http_interactions:
- request:
    method: post
    uri: https://fcm.googleapis.com/fcm/send
    body:
      encoding: UTF-8
      string: '{"registration_ids":["token_abc","token_xyz"],"notification":{"title":"Impedit
        eum perspiciatis debitis numquam.","body":"Quos et aut est molestias quia
        at ullam illum."}}'
    headers:
      Content-Type:
      - application/json
      Authorization:
      - key=<FIREBASE_FCM_SERVER_KEY>
      Connection:
      - close
      Host:
      - fcm.googleapis.com
      User-Agent:
      - http.rb/5.2.0
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Fri, 29 Jun 2018 13:29:02 GMT
      Expires:
      - Fri, 29 Jun 2018 13:29:02 GMT
      Cache-Control:
      - private, max-age=0
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      Server:
      - GSE
      Alt-Svc:
      - quic=":443"; ma=2592000; v="43,42,41,39,35"
      Accept-Ranges:
      - none
      Vary:
      - Accept-Encoding
      Connection:
      - close
    body:
      encoding: UTF-8
      string: '{"multicast_id":8840392948876291322,"success":1,"failure":1,"canonical_ids":0,"results":[{"fake":"data"},{"error":"InvalidRegistration"}]}'
    http_version: 
  recorded_at: Fri, 29 Jun 2018 13:29:02 GMT
recorded_with: VCR 3.0.3
