---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/statements/2413772/bank/pdf"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Down/5.4.1
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Fri, 19 Jan 2024 17:53:19 GMT
      Content-Type:
      - application/pdf
      Content-Length:
      - '103432'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Content-Disposition:
      - attachment; filename=2413772_bank_verification.pdf
      Etag:
      - W/"19408-3S5O2STLC9qtS5xjrN9GgCg4/CE"
    body:
      encoding: ASCII-8BIT
      string: A Bank Verification Letter PDF
  recorded_at: Fri, 19 Jan 2024 17:53:19 GMT
recorded_with: VCR 6.2.0
