---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/cards?filter%5BcustomerId%5D=tunisia_customer_id"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 09 Jul 2025 17:25:46 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2021'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"7e5-ZblXdrvzMa/m9AnBh850kufz6wU"
    body:
      encoding: UTF-8
      string: '{"data":[{"type":"businessVirtualDebitCard","id":"tunisia_card_id","attributes":{"createdAt":"2025-05-21T21:15:31.854Z","last4Digits":"1234","expirationDate":"2029-05","address":{"street":"1343
        Millbrook Trl","city":"Ann Arbor","state":"MI","postalCode":"48108","country":"US"},"fullName":{"first":"Sakif","last":"Imtiaz"},"phone":{"countryCode":"1","number":"73<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>854157"},"email":"<EMAIL>","dateOfBirth":"1994-02-28","ssn":"*********","status":"Active","tags":{},"bin":"424242<ALLIANCE_RENT_ROLL_FUZZ_FACTOR>9"},"relationships":{"customer":{"data":{"type":"customer","id":"tunisia_customer_id"}},"account":{"data":{"type":"account","id":"tunisia_id"}}}}],"meta":{"pagination":{"total":3,"limit":100,"offset":0}}}'
  recorded_at: Wed, 09 Jul 2025 17:25:46 GMT
recorded_with: VCR 6.3.1
