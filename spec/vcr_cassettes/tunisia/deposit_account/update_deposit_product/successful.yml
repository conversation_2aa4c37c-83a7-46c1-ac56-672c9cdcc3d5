---
http_interactions:
- request:
    method: patch
    uri: "<UNIT_API_URL>/accounts/3604349"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"depositAccount","attributes":{"depositProduct":"business_small"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 04 Dec 2024 17:27:31 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '78'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"4e-txN4rlZ8mQljkABgFLFWzI277C4"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"depositAccount","id":"3604349","attributes":{"name":"Account Name","createdAt":"2024-07-01T13:30:01.132Z","routingNumber":"8123678","accountNumber":"**********","depositProduct":"business_small","balance":0,"hold":0,"available":0,"tags":{"entity_id":"1","subdomain":"alever"},"currency":"USD","status":"Open","updatedAt":"2024-07-01T13:30:01.132Z"},"relationships":{"customer":{"data":{"type":"customer","id":"1497756"}},"org":{"data":{"type":"org","id":"4381"}},"bank":{"data":{"type":"bank","id":"1"}}}}}'
  recorded_at: Wed, 04 Dec 2024 17:27:31 GMT
recorded_with: VCR 6.2.0
