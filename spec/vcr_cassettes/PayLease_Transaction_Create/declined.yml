---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
            <Username>|PAYLEASE_USER_ID|</Username>
            <Password>|PAYLEASE_PASSWORD|</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>1</PayerReferenceId>
              <PayerFirstName>Norbert</PayerFirstName>
              <PayerLastName>Streich2</PayerLastName>
              <CreditCardType>Visa</CreditCardType>
              <CreditCardNumber>****************</CreditCardNumber>
              <CreditCardExpMonth>09</CreditCardExpMonth>
              <CreditCardExpYear>19</CreditCardExpYear>
              <CreditCardCcv2>321</CreditCardCcv2>
              <BillingFirstName>Norbert</BillingFirstName>
              <BillingLastName>Streich2</BillingLastName>
              <BillingStreetAddress>8217 Hartmann Curve, 65364 Gleichner Drives Apt. 420</BillingStreetAddress>
              <BillingCity>Daniloside</BillingCity>
              <BillingState>UT</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>34007</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 29 Jul 2019 15:59:56 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=********; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=srujksuk910u9mv8prrhkk7tlj; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '559'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
           <Username>|PAYLEASE_USER_ID|</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
            <PayerReferenceId>1</PayerReferenceId>
            <GatewayPayerId>1608723</GatewayPayerId>
            <Code>10</Code>
            <Status>Approved</Status>
            <Message>Payer Account has been created successfully.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 29 Jul 2019 16:00:00 GMT
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
            <Username>|PAYLEASE_USER_ID|</Username>
            <Password>|PAYLEASE_PASSWORD|</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>AccountPayment</TransactionAction>
              <PaymentReferenceId>1</PaymentReferenceId>
              <PaymentTraceId>fc72fa9692304e93f500a0ab5b7834</PaymentTraceId>
              <PayeeId>|PAYLEASE_PAYEE_ID|</PayeeId>
              <PayerReferenceId>1</PayerReferenceId>
              <GatewayPayerId>1608723</GatewayPayerId>
              <CurrencyCode>USD</CurrencyCode>
              <TotalAmount>1000000.0</TotalAmount>
              <IncurFee>Yes</IncurFee>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 29 Jul 2019 16:00:00 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=********; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=ol3d551vsais7j10dlou1ag8je; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '630'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
           <Username>|PAYLEASE_USER_ID|</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>AccountPayment</TransactionAction>
            <PaymentReferenceId>1</PaymentReferenceId>
            <PaymentTraceId>fc72fa9692304e93f500a0ab5b7834</PaymentTraceId>
            <CurrencyCode>USD</CurrencyCode>
            <Code>1000</Code>
            <Status>Error</Status>
            <Message>-1 - Transaction amount [1000000.00] exceeded limit of [25000.00].</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Mon, 29 Jul 2019 16:00:02 GMT
recorded_with: VCR 4.0.0
