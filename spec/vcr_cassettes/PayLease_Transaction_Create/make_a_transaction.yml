---
http_interactions:
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
            <Username>|PAYLEASE_USER_ID|</Username>
            <Password>|PAYLEASE_PASSWORD|</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
              <PayerReferenceId>1</PayerReferenceId>
              <PayerFirstName>Luigi</PayerFirstName>
              <PayerLastName>Emmerich2</PayerLastName>
              <CreditCardType>Visa</CreditCardType>
              <CreditCardNumber>****************</CreditCardNumber>
              <CreditCardExpMonth>09</CreditCardExpMonth>
              <CreditCardExpYear>19</CreditCardExpYear>
              <CreditCardCcv2>123</CreditCardCcv2>
              <BillingFirstName><PERSON></BillingFirstName>
              <BillingLastName>Emmerich2</BillingLastName>
              <BillingStreetAddress>4236 Maricruz Centers, 36462 O'Kon Glen Suite 936</BillingStreetAddress>
              <BillingCity>South Alainahaven</BillingCity>
              <BillingState>WV</BillingState>
              <BillingCountry>US</BillingCountry>
              <BillingZip>04124-7842</BillingZip>
              <CurrencyCode>USD</CurrencyCode>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Sat, 27 Jul 2019 20:01:32 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=********; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=t78u6rj2uhi9erkveoad92g1fi; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '559'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
           <Username>|PAYLEASE_USER_ID|</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>CreateCreditCardPayerAccount</TransactionAction>
            <PayerReferenceId>1</PayerReferenceId>
            <GatewayPayerId>1608676</GatewayPayerId>
            <Code>10</Code>
            <Status>Approved</Status>
            <Message>Payer Account has been created successfully.</Message>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Sat, 27 Jul 2019 20:01:34 GMT
- request:
    method: post
    uri: https://test.paylease.net/gapi/request.php
    body:
      encoding: UTF-8
      string: |
        XML=<?xml version="1.0" encoding="UTF-8"?>
        <PayLeaseGatewayRequest>
          <Credentials>
            <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
            <Username>|PAYLEASE_USER_ID|</Username>
            <Password>|PAYLEASE_PASSWORD|</Password>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
            <Transaction>
              <TransactionAction>AccountPayment</TransactionAction>
              <PaymentReferenceId>1</PaymentReferenceId>
              <PaymentTraceId>30 random chars</PaymentTraceId>
              <PayeeId>|PAYLEASE_PAYEE_ID|</PayeeId>
              <PayerReferenceId>1</PayerReferenceId>
              <GatewayPayerId>1608676</GatewayPayerId>
              <CurrencyCode>USD</CurrencyCode>
              <TotalAmount>12.34</TotalAmount>
              <IncurFee>Yes</IncurFee>
            </Transaction>
          </Transactions>
        </PayLeaseGatewayRequest>
    headers:
      User-Agent:
      - Faraday v0.15.4
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Sat, 27 Jul 2019 20:01:34 GMT
      Server:
      - Apache
      Strict-Transport-Security:
      - max-age=********; includeSubdomains;
      P3p:
      - CP="CAO PSA OUR"
      Set-Cookie:
      - PHPSESSID=2kj9an1pmapqm9f5tnblqegtb0; path=/; secure; HttpOnly
      Expires:
      - Thu, 19 Nov 1981 08:52:00 GMT
      Cache-Control:
      - no-store, no-cache, must-revalidate
      Pragma:
      - no-cache
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '795'
      Content-Type:
      - text/xml;charset=UTF-8
    body:
      encoding: UTF-8
      string: |+
        <?xml version="1.0"?>
         <PayLeaseGatewayResponse>
          <Credentials>
           <GatewayId>|PAYLEASE_MERCHANT_ID|</GatewayId>
           <Username>|PAYLEASE_USER_ID|</Username>
          </Credentials>
          <Mode>Test</Mode>
          <Transactions>
           <Transaction>
            <TransactionAction>AccountPayment</TransactionAction>
            <PaymentReferenceId>1</PaymentReferenceId>
            <PaymentTraceId>30 random chars</PaymentTraceId>
            <CurrencyCode>USD</CurrencyCode>
            <TransactionId>********</TransactionId>
            <Code>8</Code>
            <Status>Approved</Status>
            <Message>This credit card transaction has been approved.</Message>
            <ApprovalCode>*****************</ApprovalCode>
            <UnitAmount>12.34</UnitAmount>
            <FeeAmount>0.00</FeeAmount>
            <TotalAmount>12.34</TotalAmount>
           </Transaction>
          </Transactions>
         </PayLeaseGatewayResponse>

    http_version: 
  recorded_at: Sat, 27 Jul 2019 20:01:36 GMT
recorded_with: VCR 4.0.0
