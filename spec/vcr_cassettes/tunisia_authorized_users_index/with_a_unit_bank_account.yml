---
http_interactions:
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 10 Jul 2024 00:31:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2190'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"88e-mDlC6qr6m6TVxpbT6stp0CLazWQ"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"<PERSON>","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Wed, 10 Jul 2024 00:31:51 GMT
- request:
    method: get
    uri: "<UNIT_API_URL>/customers/1497756"
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 10 Jul 2024 00:31:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '2190'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"88e-mDlC6qr6m6TVxpbT6stp0CLazWQ"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"businessCustomer","id":"1497756","attributes":{"createdAt":"2023-12-04T20:40:24.007Z","name":"John
        DeSilva","address":{"street":"Street name 1","city":"City","state":"CA","postalCode":"11111","country":"US"},"phone":{"countryCode":"1","number":"2489212775"},"stateOfIncorporation":"CA","ein":"123456789","entityType":"Corporation","contact":{"fullName":{"first":"John","last":"DeSilva"},"email":"john@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}},"tags":{},"authorizedUsers":[{"fullName":{"first":"Tom","last":"Qin"},"email":"tom@<POSTGRES_USER>.co","phone":{"countryCode":"1","number":"2489212775"}}],"status":"Active"},"relationships":{"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Wed, 10 Jul 2024 00:31:51 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token/verification"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"channel":"sms"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jul 2024 00:31:51 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '459'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"1cb-/d4w0Hg5qwU4eKbIsFsj7BjRCjQ"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerTokenVerification","attributes":{"verificationToken":"BhU7WJ+EI/NRtR9LbjyAw4hjrUgRrnyaTWPF+UiapukDFqedXb6ib2gvOGqo7DkoGiwl202SRF+frFugKj7rDlwwrqQswCEt2AQ8FgON7BKif+peRBkLS4It+D2VpjZAW18dm+neMbazwR6HuneCRFKpMA1gHqLZlQyzHD0GQj8QfGY9UAFTHpk5zxNkLp3i84UmBdjblKR7k14vanoQtuAMpZLgEeESvDBDpgYw/4xfMmrX5PQw3UvuKRGYCE8uRQX3XI0oEa7Z+TknOFG7dSc4Eb7m0EzYH3aG305MsYghlxC1poPoMA+DhS4gLCmpfDmM08Z/ItYXKLUS0Rk6ufmzHns3Zg","phoneLast4Digits":"2775"}}}'
  recorded_at: Wed, 10 Jul 2024 00:31:51 GMT
- request:
    method: post
    uri: "<UNIT_API_URL>/customers/1497756/token"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerToken","attributes":{"scope":"accounts-write
        cards-write customers cards cards-sensitive cards-sensitive-write transactions
        authorizations accounts statements customers-write payments-write","verificationToken":"BhU7WJ+EI/NRtR9LbjyAw4hjrUgRrnyaTWPF+UiapukDFqedXb6ib2gvOGqo7DkoGiwl202SRF+frFugKj7rDlwwrqQswCEt2AQ8FgON7BKif+peRBkLS4It+D2VpjZAW18dm+neMbazwR6HuneCRFKpMA1gHqLZlQyzHD0GQj8QfGY9UAFTHpk5zxNkLp3i84UmBdjblKR7k14vanoQtuAMpZLgEeESvDBDpgYw/4xfMmrX5PQw3UvuKRGYCE8uRQX3XI0oEa7Z+TknOFG7dSc4Eb7m0EzYH3aG305MsYghlxC1poPoMA+DhS4gLCmpfDmM08Z/ItYXKLUS0Rk6ufmzHns3Zg","verificationCode":"<NELCO_CLIENT_ID>1"}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 10 Jul 2024 00:31:52 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '639'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"27f-n4+2Izga2l1PebeG4ratJS/xohk"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"customerBearerToken","attributes":{"token":"v2.public.eyJyb2xlIjoiY3VzdG9tZXIiLCJ1c2VySWQiOm51bGwsInN1YiI6ImN1c3RvbWVyLzE0OTc3NTYvKzEyNDg5MjEyNzc1IiwiZXhwIjoiMjAyNC0wNy0xMVQwMDozMTo1Mi4zODBaIiwianRpIjpudWxsLCJvcmdJZCI6IjQzODEiLCJzY29wZSI6ImFjY291bnRzLXdyaXRlIGNhcmRzLXdyaXRlIGN1c3RvbWVycyBjYXJkcyBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyBhdXRob3JpemF0aW9ucyBhY2NvdW50cyBzdGF0ZW1lbnRzIGN1c3RvbWVycy13cml0ZSBwYXltZW50cy13cml0ZSIsImN1c3RvbWVySWQiOiIxNDk3NzU2IiwidXNlclR5cGUiOiJjdXN0b21lciJ9yX_YHXqAWYPoqdx2ZSngA2PqnnoSxjAU1Qgo915oNORdGyucr-TQaJFsqmqHOULcy_2BtRnBBdF9gP-jclX8BA","expiresIn":86400}}}'
  recorded_at: Wed, 10 Jul 2024 00:31:52 GMT
recorded_with: VCR 6.2.0
