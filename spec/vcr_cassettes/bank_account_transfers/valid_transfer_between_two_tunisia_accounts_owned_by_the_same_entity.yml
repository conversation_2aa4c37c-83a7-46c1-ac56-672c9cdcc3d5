---
http_interactions:
- request:
    method: post
    uri: "<UNIT_API_URL>/payments"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","attributes":{"amount":100,"description":"My
        Memo","idempotencyKey":"33b3af26-72e5-4b78-a322-09f15208b6d0"},"relationships":{"account":{"data":{"type":"depositAccount","id":"2413772"}},"counterpartyAccount":{"data":{"type":"depositAccount","id":"2385052"}}}}}'
    headers:
      Content-Type:
      - application/vnd.api+json
      Authorization:
      - Bearer <UNIT_API_TOKEN>
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Thu, 29 Feb 2024 21:05:42 GMT
      Content-Type:
      - application/vnd.api+json; charset=utf-8
      Content-Length:
      - '593'
      Connection:
      - keep-alive
      Strict-Transport-Security:
      - max-age=********; includeSubDomains; preload
      Access-Control-Allow-Origin:
      - "*"
      Etag:
      - W/"251-GfRVRVcPuLJWxWLw6dk5o8XFjc8"
    body:
      encoding: UTF-8
      string: '{"data":{"type":"bookPayment","id":"3823397","attributes":{"createdAt":"2024-02-29T21:05:42.004Z","amount":100,"direction":"Credit","description":"My
        Memo","status":"Sent"},"relationships":{"account":{"data":{"type":"account","id":"2413772"}},"customer":{"data":{"type":"customer","id":"1497756"}},"customers":{"data":[{"type":"customer","id":"1497756"}]},"counterpartyAccount":{"data":{"type":"account","id":"2385052"}},"counterpartyCustomer":{"data":{"type":"customer","id":"1497757"}},"transaction":{"data":{"type":"transaction","id":"6772650"}},"org":{"data":{"type":"org","id":"4381"}}}}}'
  recorded_at: Thu, 29 Feb 2024 21:05:42 GMT
recorded_with: VCR 6.2.0
