---
http_interactions:
- request:
    method: post
    uri: https://api.postgrid.com/print-mail/v1/letters
    body:
      encoding: UTF-8
      string: '{"from":{"companyName":"Revela, Inc.","addressLine1":"1401 Vermont
        Street","addressLine2":"Suite 210","city":"Detroit","postalOrZip":"48216","provinceOrState":"MI","country":"US"},"to":{"firstName":"Na","lastName":"Bins1","addressLine1":"29964
        Denver Terrace","addressLine2":"Apt. 833","city":"Felicestad","postalOrZip":"38736","provinceOrState":"KY","country":"US"},"html":"<html>\n<body>\n<img
        src=''/images/alever_logo.svg''>\n</body>\n</html>\n<h3>\nNOTICE OF AGREEMENT
        VIOLATION & POTENTIAL ACTION\n</h3>\n<p>\n10/04/23\n</p>\n<br>\nDear Na,\n</br>\n<p>\nThis
        is an attempt to collect a past due balance. You are hereby notified by Revela
        Inc. on behalf of Greek Housing Management that you have a past due balance
        owed to the Edmundo Union Chapter of Sigma Alpha Epsilon Fraternity. If you
        have already paid this balance, you may ignore this letter.\n</p>\n<p>\nIf
        you owe this balance, you must log in to your Revela account by going to\n<b>http://alever.lvh.me:3001/tenants/login</b>\nand
        do one of the following:\n<ul>\n<li>Pay the balance owed in full; OR</li>\n<li>Set
        up a payment plan in the Member Portal.</li>\n</ul>\n</p>\n<p>\nIf you fail
        to take one of the above actions, we may initiate a lawsuit for the amount
        owed, evict you from the premises, or report your unpaid balance to the credit
        bureaus potentially impacting your credit score.\n</p>\n<p>\nWe appreciate
        your prompt attention to this matter. If you have any questions, please send
        us an email using the Messages tab in your Member Portal or reach out to support@<POSTGRES_USER>.co.\n</p>\n<p>\nRegards,\n<br>\nRevela
        Inc. on behalf of Greek Housing Management, LLC and the Edmundo Union Chapter
        of Sigma Alpha Epsilon Fraternity\n</p>\n<footer>\n540 Cory Grove, Suite 337,
        Ziemannshire, Illinois 57261\n</footer>\n<style>\n  @import url(''https://fonts.googleapis.com/css2?family=Lato&display=swap'');\n  \n  *
        {\n    box-sizing: border-box;\n  }\n  \n  html {\n    height: 11in;\n    width:
        8.5in;\n  \n    padding: 0.85in;\n    padding-top: calc((8.5in / 3) + 0.85in);\n  \n    font-size:
        12px;\n    font-family: ''Lato'';\n    line-height: 1.4em;\n  }\n  \n  img
        {\n    height: 1in;\n  }\n  \n  h3 {\n    text-align: center;\n  }\n  \n  footer
        {\n    text-align: center;\n    position: absolute;\n    bottom: 0.85in;\n    left:
        0;\n    right: 0;\n  }\n</style>\n"}'
    headers:
      Content-Type:
      - application/json
      X-Api-Key:
      - "<POSTGRID_API_KEY>"
      User-Agent:
      - Faraday v1.10.3
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Date:
      - Wed, 04 Oct 2023 18:46:29 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '2939'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Access-Control-Allow-Credentials:
      - 'true'
      Etag:
      - W/"b7b-frbVRsdNtcshrce0Hr/ItHAEDLM"
      Cf-Cache-Status:
      - DYNAMIC
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v3?s=LvBNF8Yq4cNkJfBY0FxM01Yg8fiafXTqHKJAa9kkhpzamzvWtgiyaXBYK4wkXrejpIv1ejdphlhQIIvNRQa4GuaTQyLsMPxNvy90q7IkbCZL3IZvC3j%2B4dnKPxOPXN%2FmsEpRkM3oErDu8o1G5uwF"}],"group":"cf-nel","max_age":604800}'
      Nel:
      - '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}'
      Server:
      - cloudflare
      Cf-Ray:
      - 810f96833a95e262-ORD
      Alt-Svc:
      - h3=":443"; ma=86400
    body:
      encoding: UTF-8
      string: '{"id":"letter_753B69pbRdbN8tJ3LoUf9w","object":"letter","live":<SSL_ENABLED>,"addressPlacement":"top_first_page","attachedPDF":null,"color":<SSL_ENABLED>,"doubleSided":<SSL_ENABLED>,"envelopeType":"standard_double_window","from":{"id":"contact_ohaPaPCX5vauaZPWv5CeS1","object":"contact","addressLine1":"1401
        VERMONT STREET","addressLine2":"SUITE 210","addressStatus":"verified","city":"DETROIT","companyName":"Revela,
        Inc.","country":"UNITED STATES","countryCode":"US","postalOrZip":"48216","provinceOrState":"MI"},"html":"<html>\n<body>\n<img
        src=''/images/alever_logo.svg''>\n</body>\n</html>\n<h3>\nNOTICE OF AGREEMENT
        VIOLATION & POTENTIAL ACTION\n</h3>\n<p>\n10/04/23\n</p>\n<br>\nDear Na,\n</br>\n<p>\nThis
        is an attempt to collect a past due balance. You are hereby notified by Revela
        Inc. on behalf of Greek Housing Management that you have a past due balance
        owed to the Edmundo Union Chapter of Sigma Alpha Epsilon Fraternity. If you
        have already paid this balance, you may ignore this letter.\n</p>\n<p>\nIf
        you owe this balance, you must log in to your Revela account by going to\n<b>http://alever.lvh.me:3001/tenants/login</b>\nand
        do one of the following:\n<ul>\n<li>Pay the balance owed in full; OR</li>\n<li>Set
        up a payment plan in the Member Portal.</li>\n</ul>\n</p>\n<p>\nIf you fail
        to take one of the above actions, we may initiate a lawsuit for the amount
        owed, evict you from the premises, or report your unpaid balance to the credit
        bureaus potentially impacting your credit score.\n</p>\n<p>\nWe appreciate
        your prompt attention to this matter. If you have any questions, please send
        us an email using the Messages tab in your Member Portal or reach out to support@<POSTGRES_USER>.co.\n</p>\n<p>\nRegards,\n<br>\nRevela
        Inc. on behalf of Greek Housing Management, LLC and the Edmundo Union Chapter
        of Sigma Alpha Epsilon Fraternity\n</p>\n<footer>\n540 Cory Grove, Suite 337,
        Ziemannshire, Illinois 57261\n</footer>\n<style>\n  @import url(''https://fonts.googleapis.com/css2?family=Lato&display=swap'');\n  \n  *
        {\n    box-sizing: border-box;\n  }\n  \n  html {\n    height: 11in;\n    width:
        8.5in;\n  \n    padding: 0.85in;\n    padding-top: calc((8.5in / 3) + 0.85in);\n  \n    font-size:
        12px;\n    font-family: ''Lato'';\n    line-height: 1.4em;\n  }\n  \n  img
        {\n    height: 1in;\n  }\n  \n  h3 {\n    text-align: center;\n  }\n  \n  footer
        {\n    text-align: center;\n    position: absolute;\n    bottom: 0.85in;\n    left:
        0;\n    right: 0;\n  }\n</style>\n","mailingClass":"first_class","sendDate":"2023-10-04T18:46:29.820Z","size":"us_letter","status":"ready","to":{"id":"contact_4WVg1yk2EBBVFRRJNyD1Lq","object":"contact","addressLine1":"29964
        DENVER TERRACE","addressLine2":"APT. 833","addressStatus":"verified","city":"FELICESTAD","country":"UNITED
        STATES","countryCode":"US","firstName":"Na","lastName":"Bins1","postalOrZip":"38736","provinceOrState":"KY"},"createdAt":"2023-10-04T18:46:29.826Z","updatedAt":"2023-10-04T18:46:29.826Z"}'
  recorded_at: Wed, 04 Oct 2023 18:46:29 GMT
recorded_with: VCR 6.2.0
