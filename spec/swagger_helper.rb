# frozen_string_literal: true

require 'rails_helper'

description = <<~HERE
  <p>
    Welcome to the V2 Revela API!
  </p>
  <p>
    This API conforms to the
    <a href="https://jsonapi.org">JSON:API specification</a>,
    so this API should be familiar to users who have used other JSON:API endpoints.
  </p>
  <p>
    This means that requests can specify query parameters such as
    <code>?page[size]=250</code>,
    <code>?filter[updated_after]=2000-01-01</code>,
    or <code>?include=properties,properties.address</code>.
  </p>
  <p>
    Each request must be authenticated with token bearer authentication.
    This means that each request should include a header like <code>Authorization: Bearer {api_token}</code>.
  </p>
  <p>
    The below examples are interactive.
    After clicking the &ldquo;Authorize&rdquo; button and entering a valid token,
    you can click &ldquo;Try it out&rdquo; to perform an API request in the browser.
  </p>
  <p>
    For additional support or assitance, please reach out via <NAME_EMAIL>.
  </p>
  <p>
    <h3>Changelog</h3>
    <dl>
      <dt>2025-05-13</dt>
      <dd>Added <var>start_date</var> and <var>end_date</var> filters to <var>accounting/amounts</var> endpoint</dd>
      <dd>Added <var>start_date</var> and <var>end_date</var> filters to <var>accounting/entries</var> endpoint</dd>
      <dt>2025-01-10</dt>
      <dd>Published <var>inspections</var> endpoints</dd>
      <dt>2024-11-01</dt>
      <dd>Added <var>applied_payments</var> endpoint</dd>
      <dt>2024-10-30</dt>
      <dd>Added <var>payments</var> endpoint</dd>
      <dt>2024-10-15</dt>
      <dd>Added Buyer and Seller relationships to <var>invoices</var> endpoint</dd>
      <dt>2024-09-22</dt>
      <dd>Published <var>management_contracts</var> endpoint</dd>
      <dt>2024-09-21</dt>
      <dd>Published <var>work_order_appointments</var> endpoint</dd>
      <dt>2024-09-11</dt>
      <dd>Published <var>pets</var> endpoint</dd>
      <dt>2024-09-09</dt>
      <dd>Added approval timestamps to <var>work_orders</var> endpoint</dd>
      <dt>2024-09-09</dt>
      <dd>Added <var>date_of_birth</var> to <var>tenants</var> endpoint</dd>
      <dt>2024-07-31</dt>
      <dd>Added <var>estimates</var> endpoint</dd>
      <dt>2024-06-12</dt>
      <dd>Added <var>latitude</var> and <var>longitude</var> to <var>addresses</var> endpoint</dd>
      <dd>Added <var>archived_at</var> to <var>vendors</var> endpoint</dd>
      <dt>2024-02-11</dt>
      <dd>Added <var>total_monthly_reported_income</var> to <var>lease_applications</var> endpoint</dd>
      <dt>2024-01-17</dt>
      <dd>Added <var>published_at</var> to <var>listings</var> endpoint</dd>
      <dt>2023-12-15</dt>
      <dd>Added <var>original_end_date</var> to <var>leases</var> endpoint</dd>
      <dt>2023-11-13</dt>
      <dd>Published <var>line_items</var> endpoint</dd>
      <dt>2023-11-01</dt>
      <dd>Added <var>lease</var> relationship to <var>invoices</var> endpoint</dd>
      <dt>2023-10-31</dt>
      <dd>Added <var>balance</var> to <var>invoices</var> endpoint</dd>
      <dt>2023-10-19</dt>
      <dd>Published <var>invoices</var> endpoint</dd>
      <dt>2023-10-12</dt>
      <dd>Add <var>tags</var> to <var>tenants</var> endpoint</dd>
      <dt>2023-10-11</dt>
      <dd>Add <var>archived_at</var> to <var>entities</var> endpoint</dd>
      <dd>Add <var>archived_at</var> to <var>owners</var> endpoint</dd>
      <dd>Add <var>archived_at</var> to <var>properties</var> endpoint</dd>
      <dd>Add <var>tags</var> to <var>entities</var> endpoint</dd>
      <dd>Add <var>tags</var> to <var>owners</var> endpoint</dd>
      <dd>Add <var>tags</var> to <var>properties</var> endpoint</dd>
      <dt>2023-10-02</dt>
      <dd>Document <var>employees</var> endpoint</dd>
      <dt>2023-09-24</dt>
      <dd>Added <var>renewal_status</var> to <var>leases</var> endpoint</dd>
      <dt>2023-09-23</dt>
      <dd>Added <var>custom_data</var> to <var>work_orders</var> endpoint</dd>
      <dt>2023-08-16</dt>
      <dd>Added <var>rent_roll</var> endpoint</dd>
      <dt>2023-08-02</dt>
      <dd>Added <var>reason</var> to <var>notices_of_non_renewal</var> endpoint</dd>
      <dt>2023-07-26</dt>
      <dd>Added <var>owners</var> endpoint</dd>
      <dt>2023-07-25</dt>
      <dd>Added <var>entities</var> endpoint</dd>
      <dt>2023-07-20</dt>
      <dd>Added <var>portfolios</var> endpoint</dd>
      <dt>2023-07-10</dt>
      <dd>Added <var>status</var> to <var>units</var> endpoint</dd>
      <dt>2023-07-05</dt>
      <dd>Added <var>status</var> to <var>tenants</var> endpoint</dd>
      <dt>2023-06-26</dt>
      <dd>Added <var>base_rent_amount</var> to <var>leases</var> endpoint</dd>
      <dt>2023-06-21</dt>
      <dd>Added <var>cost</var> to <var>work_orders</var> endpoint</dd>
      <dt>2023-06-19</dt>
      <dd>Published <var>prospects</var> endpoint</dd>
      <dt>2023-06-17</dt>
      <dd>Added <var>requested_move_in_date</var> to <var>guest_cards</var> endpoint</dd>
    </dl>
  </p>
HERE

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('swagger').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under openapi_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a openapi_spec tag to the
  # the root example_group in your specs, e.g. describe '...', openapi_spec: 'v2/swagger.json'
  config.openapi_specs = {
    'v2/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'API V2',
        version: 'v2',
        description: description
      },
      components: {
        securitySchemes: {
          bearer_auth: {
            type: :http,
            scheme: :bearer
          }
        }
      },
      paths: {},
      servers: [
        {
          url: 'https://{defaultHost}',
          variables: {
            defaultHost: {
              default: 'www.revela.co'
            }
          }
        }
      ]
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The openapi_specs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
