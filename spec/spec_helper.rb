if ENV['CI'] && ENV['RAILS_ENV'] == 'test'
  require 'simplecov'
  SimpleCov.start 'rails' do
    formatter SimpleCov::Formatter::HTMLFormatter
    add_group 'Admin', 'app/admin'
    add_group 'Channels', 'app/channels'
    add_group 'Importers', 'lib/importers'
    add_group 'Queries', 'app/queries'
    add_group 'Reports', 'lib/reports'
    add_group 'Serializers', 'app/serializers'
    add_group 'Services', 'app/services'
  end
end

require 'capybara/email/rspec'
require 'support/matchers/change_it'
require 'support/matchers/clone'
require 'support/matchers/create_records'
require 'support/matchers/appear_order'
require 'support/matchers/have_validation_error_on'
require 'support/matchers/destroy'
require 'support/matchers/produce_xml'

# This file was generated by the `rails generate rspec:install` command.
# Conventionally, all specs live under a `spec` directory, which <PERSON><PERSON> adds to
# the `$LOAD_PATH`.  The generated `.rspec` file contains `--require
# spec_helper` which will cause this file to always be loaded, without a need
# to explicitly require it in any files.
#
# Given that it is always loaded, you are encouraged to keep this file as
# light-weight as possible. Requiring heavyweight dependencies from this file
# will add to the boot time of your test suite on EVERY test run, even for an
# individual file that may not need all of that loaded. Instead, consider making
# a separate helper file that requires the additional dependencies and performs
# the additional setup, and require it from the spec files that actually need
# it.
#
# The `.rspec` file also contains a few flags that are not defaults but that
# users commonly want.
#
# See http://rubydoc.info/gems/rspec-core/RSpec/Core/Configuration
RSpec.configure do |config|
  # rspec-expectations config goes here. You can use an alternate
  # assertion/expectation library such as wrong or the stdlib/minitest
  # assertions if you prefer.
  config.expect_with :rspec do |expectations|
    # This option will default to `true` in RSpec 4. It makes the `description`
    # and `failure_message` of custom matchers include text for helper methods
    # defined using `chain`, e.g.:
    # be_bigger_than(2).and_smaller_than(4).description
    #   # => "be bigger than 2 and smaller than 4"
    # ...rather than:
    #   # => "be bigger than 2"
    expectations.include_chain_clauses_in_custom_matcher_descriptions = true
  end

  # rspec-mocks config goes here. You can use an alternate test double
  # library (such as bogus or mocha) by changing the `mock_with` option here.
  config.mock_with :rspec do |mocks|
    # Prevents you from mocking or stubbing a method that does not exist on
    # a real object. This is generally recommended, and will default to
    # `true` in RSpec 4.
    mocks.verify_partial_doubles = true
  end

  config.example_status_persistence_file_path = '.rspec-failures'
end

RSpec::Matchers.define_negated_matcher :not_change, :change
RSpec::Matchers.define_negated_matcher :not_enqueue_job, :enqueue_job
