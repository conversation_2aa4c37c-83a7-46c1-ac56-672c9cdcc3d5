ENV['RAILS_ENV'] ||= 'test'
require 'spec_helper'
require File.expand_path('../config/environment', __dir__)
require 'rspec/rails'
require 'rspec/retry'
# Add additional requires below this line. Rails is not loaded until this point!
require 'support/request_helpers'
require 'support/capybara_helper'
require 'support/feature_flag_helpers'
require 'support/rbac_helpers'
require 'support/fixture_helper'
require 'support/login_helpers'
require 'support/active_record_helpers'
require 'support/billy'
require 'support/factory_girl'
require 'support/schema_helpers'
require 'support/security_deposit_helper'
require 'support/approvals_helpers'
require 'support/download_helpers'
require 'support/vcr'

require 'goldeen/rspec'
require 'rack_session_access/capybara'

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
# Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }

# Checks for pending migrations before tests are run.
# If you are not using ActiveRecord, you can remove this line.
ActiveRecord::Migration.maintain_test_schema!

RSpec.configure do |config|
  config.raise_errors_for_deprecations!

  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_path = "#{Rails.root}/spec/fixtures"

  if ENV['CI']
    # Retry sporadically failing feature specs
    config.verbose_retry = true
    config.display_try_failure_messages = true
    config.default_retry_count = ENV.fetch('RETRY_COUNT', 3)
  end

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, :type => :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!
  config.include Devise::Test::ControllerHelpers, type: :controller
  config.include Devise::Test::IntegrationHelpers, type: :request

  config.include FeatureFlagHelpers

  config.include RBACHelpers

  config.include FixtureHelper

  config.include CapybaraHelper, type: :system

  config.include Capybara::Email::DSL, type: :job

  config.include SchemaHelpers

  config.include ApprovalsHelpers

  config.include DownloadHelpers

  config.include ActiveRecordHelpers

  config.include LoginHelpers::CapybaraUser, capybara_login: :user

  config.include LoginHelpers::CapybaraVendor, capybara_login: :vendor
  config.include LoginHelpers::CapybaraMorocco, capybara_login: :morocco

  config.include LoginHelpers::DevisePropertyManager,
                 devise_login: :property_manager
  config.include LoginHelpers::CapybaraPropertyManager,
                 capybara_login: :property_manager
  config.include LoginHelpers::ApiPropertyManager,
                 api_login: :property_manager

  config.include LoginHelpers::DeviseTenant, devise_login: :tenant
  config.include LoginHelpers::CapybaraTenant, capybara_login: :tenant

  config.include LoginHelpers::DeviseOwner, devise_login: :owner
  config.include LoginHelpers::CapybaraOwner, capybara_login: :owner

  config.include LoginHelpers::DeviseAdmin, devise_login: :admin
  config.include LoginHelpers::CapybaraAdmin, capybara_login: :admin

  config.include LoginHelpers::DeviseInvoiceProcessor, devise_login: :invoice_processor
  config.include LoginHelpers::CapybaraInvoiceProcessor, capybara_login: :invoice_processor

  config.include RequestHelpers::ControllerIpStubbing, type: :controller
  config.include RequestHelpers::SystemIpStubbing, type: :system

  config.include ActiveSupport::Testing::TimeHelpers

  config.include ActiveJob::TestHelper

  config.include Warden::Test::Helpers
  config.before(:suite) { Warden.test_mode! }

  config.before(:all, :elasticsearch) do
    Searchable.models.each do |model|
      model.__elasticsearch__.create_index!(
        force: true, settings: { index: { number_of_shards: 1 } }
      )
      model.__elasticsearch__.refresh_index!
    end
  end

  # Don't hit mailgun for email verifications in test env
  config.before do |example|
    unless example.metadata[:mailgun_verify] == true
      result = Email::Verifier::Result.new(true, nil)
      allow(Email::Verifier).to receive(:verify).and_return(result)
    end
  end

  if Bullet.enable?
    config.before do
      Bullet.start_request
    end

    config.after do
      Bullet.perform_out_of_channel_notifications if Bullet.notification?
      Bullet.end_request
    end
  end

  config.approvals_default_format = :json

  # Quiet tests
  if ENV['CI']
    config.before do
      [$stdout, $stderr].each do |output|
        %i[puts print write].each do |writer|
          expect(output).not_to receive(writer)
        end
      end
    end
  end
end

RSpec::Matchers.define :have_action_item do |text, options = {}|
  match do |actual|
    menu = actual.find('.actions .menu')

    return false unless menu.has_link?(text)

    classes = menu.find_link(text)[:class]

    options[:disabled] ||= false

    if options[:disabled]
      classes.include?('disabled')
    else
      classes.exclude?('disabled')
    end
  end
end

chrome_args = %w[
  disable-popup-blocking
  window-size=1920x1080
  blink-settings=imagesEnabled=false
  no-sandbox
]

chrome_args << 'headless' unless ENV['CHROME_HEADLESS'] == 'false'

if (capybara_wait_time = ENV.fetch('CAPYBARA_DEFAULT_WAIT_TIME', nil)).present?
  Capybara.default_max_wait_time = capybara_wait_time.to_i
end

Capybara.register_driver :headless_chrome do |app|
  options = Selenium::WebDriver::Chrome::Options.new(args: chrome_args)
  options.add_preference(:download, prompt_for_download: false, default_directory: DownloadHelpers::PATH.to_s)
  options.add_preference(:browser, set_download_behavior: { behavior: 'allow' })
  options.add_preference('profile.password_manager_leak_detection', false)

  Capybara::Selenium::Driver.new(app, browser: :chrome, options: options)
end

Capybara.register_driver :headless_chrome_billy do |app|
  args = chrome_args + %W[
    ignore-certificate-errors
    proxy-server=#{Billy.proxy.host}:#{Billy.proxy.port}
    proxy-bypass-list=127.0.0.1;localhost;alever.lvh.me
  ]

  options = Selenium::WebDriver::Chrome::Options.new(
    args: args,
    accept_insecure_certs: true
  )

  Capybara::Selenium::Driver.new(app, browser: :chrome, options: options)
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

# Automatically take screenshots on test failures
require 'capybara-screenshot/rspec'
Capybara::Screenshot.prune_strategy = { keep: 10 }
if ENV['S3_TEST_SCREENSHOTS']
  Capybara::Screenshot.s3_configuration = {
    s3_client_credentials: {
      access_key_id: ENV.fetch('AWS_ACCESS_KEY_ID'),
      secret_access_key: ENV.fetch('AWS_SECRET_ACCESS_KEY'),
      region: 'us-east-1'
    },
    bucket_name: 'revela-test-screenshots',
    key_prefix: "#{Time.zone.now}/"
  }
  Capybara::Screenshot.s3_object_configuration = {
    acl: 'public-read'
  }
end
Capybara::Screenshot.register_driver(:headless_chrome) do |driver, path|
  driver.browser.save_screenshot(path)
end
Capybara::Screenshot.register_driver(:headless_chrome_billy) do |driver, path|
  driver.browser.save_screenshot(path)
end

RSpec.configure do |config|
  config.around do |example|
    unless Apartment::Tenant.schemas.include?('alever')
      Apartment::Tenant.create('alever')
    end
    Apartment::Tenant.switch!('alever')

    unless Customer.exists?(subdomain: 'alever')
      FactoryBot.create(:customer, subdomain: 'alever', name: 'Alever')
    end

    unless Customer.current.client_entity
      Customer.current.update!(client_entity: create(:company, :client_entity))
    end

    example.run
  end

  config.before(:each, type: :system) do
    driven_by :rack_test
  end

  config.before(:each, :js, type: :system) do
    clear_downloads
    driven_by :headless_chrome
  end

  config.before(:each, :billy, type: :system) do
    driven_by :headless_chrome_billy
  end

  config.before(:each, type: :system) do
    Capybara.server = :puma, { Silent: true }
    Capybara.app_host = 'http://alever.lvh.me'

    # Not rack_test
    if Capybara.page.driver.browser.respond_to?(:manage)
      Capybara.page.driver.browser.manage.window.maximize
      Capybara.server_port = Capybara.current_session.server.port
      Capybara.always_include_port = true

      port = Capybara.server_port

      Rails.application.config.action_mailer.default_url_options[:port] = port
      Rails.application.routes.default_url_options[:port] = port
    else
      Capybara.page.driver.header('user-agent', 'Chrome')
    end
  end

  # Fixes "too many connections issue" with Capybara
  WebMock.allow_net_connect!(net_http_connect_on_start: true)
end
