require 'rails_helper'

RSpec.describe PortfolioSetup::ConfigurationsController,
               devise_login: :property_manager do
  let!(:portfolio) { create(:portfolio, setup: false) }

  let(:params) { { portfolio_id: portfolio.id } }

  describe 'GET #chart_of_accounts' do
    it 'returns http success' do
      get :chart_of_accounts, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #screening' do
    it 'returns http success' do
      get :screening, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #leases' do
    it 'returns http success' do
      get :leases, params: params
      expect(response).to have_http_status(:success)
    end
  end
end
