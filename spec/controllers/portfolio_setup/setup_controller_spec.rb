require 'rails_helper'

RSpec.describe PortfolioSetup::SetupController,
               devise_login: :property_manager do
  let!(:portfolio) { create(:portfolio, setup: false) }

  let(:params) { { portfolio_id: portfolio.id } }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #configuration' do
    it 'returns http success' do
      get :configuration, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #review' do
    it 'returns http success' do
      get :review, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #finish' do
    it 'redirects to the portfolio' do
      get :finish, params: params
      expect(response).to redirect_to(portfolio_path(portfolio))
    end
  end
end
