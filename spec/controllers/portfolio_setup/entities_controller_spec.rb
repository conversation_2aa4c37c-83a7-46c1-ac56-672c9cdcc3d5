require 'rails_helper'

RSpec.describe PortfolioSetup::EntitiesController,
               devise_login: :property_manager do
  let!(:portfolio) { create(:portfolio, setup: false) }
  let(:params) { { portfolio_id: portfolio.id } }

  describe 'GET #index' do
    it 'returns http success' do
      get :index, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirect to the ownership page' do
      post :create, params: params.merge(
        company: {
          name: 'New Company'
        }
      )

      entity = portfolio.companies.last!

      expect(response).to redirect_to(
        ownership_portfolio_setup_entity_path(portfolio, entity)
      )
    end
  end

  context 'with an entity' do
    let!(:entity) { create(:company, portfolio: portfolio) }
    let(:params) { { portfolio_id: portfolio.id, id: entity.id } }

    describe 'GET #information' do
      it 'returns http success' do
        get :information, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #ownership' do
      it 'returns http success' do
        get :ownership, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #beginning_balances' do
      it 'returns http success for html' do
        get :beginning_balances, params: params
        expect(response).to have_http_status(:success)
      end

      it 'returns http success for xlsx' do
        get :beginning_balances, format: :xlsx, params: params
        expect(response).to have_http_status(:success)
      end
    end
  end
end
