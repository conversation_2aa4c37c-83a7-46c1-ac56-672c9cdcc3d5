require 'rails_helper'

RSpec.describe Accounting::EntriesController,
               devise_login: :property_manager do
  let(:journal) { create(:company) }

  let(:accounts) do
    Array.new(2) { create(:account, tenant: journal.chart_of_accounts) }
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { journal_id: journal.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'successful' do
      let(:params) do
        {
          journal_id: journal.id,
          entry: {
            description: 'Memo',
            amounts_attributes: {
              0 => {
                account_id: accounts.first.id,
                credit_amount: '$0.00', debit_amount: '$300.00'
              },
              1 => {
                account_id: accounts.second.id,
                credit_amount: '$300.00', debit_amount: '$0.00'
              }
            }
          }
        }
      end

      it 'redirects to entry show' do
        post :create, params: params
        entry = Plutus::Entry.last!
        expect(response).to redirect_to(
          accounting_journal_entry_path(journal, entry)
        )
      end
    end
  end

  context 'existing entry' do
    let(:entry) { create(:account_entry, journal: journal) }

    let(:params) { { journal_id: journal.id, id: entry.id } }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'DELETE #destroy' do
      it 'returns to the journal' do
        delete :destroy, params: params
        expect(response).to redirect_to(accounting_journal_path(journal))
      end
    end

    # Mutation should be prevented for entries that represent a commercial
    # document. Edit the source document instead.
    context 'with a commercial document' do
      let!(:payment) do
        create(:payment, :unapplied).tap do |document|
          entry.update!(commercial_document: document)
        end
      end

      describe 'GET #edit' do
        it 'redirects to the show page' do
          get :edit, params: params
          expect(response).to \
            redirect_to(accounting_journal_entry_path(journal, entry))
        end
      end

      describe 'PATCH #update' do
        it 'redirects to the show page' do
          patch :update, params: params
          expect(response).to \
            redirect_to(accounting_journal_entry_path(journal, entry))
        end
      end

      describe 'DELETE #destroy' do
        it 'redirects to the show page' do
          expect do
            delete :destroy, params: params
          end.not_to change { Plutus::Entry.count }

          expect(response).to \
            redirect_to(accounting_journal_entry_path(journal, entry))
        end
      end
    end
  end
end
