require 'rails_helper'

RSpec.describe Accounting::Accounts<PERSON>ontroller,
               devise_login: :property_manager do
  let(:account) { create(:account) }
  let(:company) { create(:company, chart_of_accounts: account.tenant) }

  describe 'GET #show' do
    it 'returns http success' do
      get :show, params: { journal_id: company.id, id: account.id }
      expect(response).to have_http_status(:success)
    end
  end
end
