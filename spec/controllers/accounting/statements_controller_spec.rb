require 'rails_helper'

RSpec.describe Accounting::StatementsController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the new statement' do
      company = create(:company)

      post :create, params: {
        statement: {
          type: 'Maintenance::WorkPerformedStatement',
          company_id: company.id,
          start_date: Time.zone.today.beginning_of_month,
          end_date: Time.zone.today.end_of_month
        }
      }

      statement = Maintenance::WorkPerformedStatement.last!

      expect(response).to \
        redirect_to(accounting_statement_path(statement))
    end
  end
end
