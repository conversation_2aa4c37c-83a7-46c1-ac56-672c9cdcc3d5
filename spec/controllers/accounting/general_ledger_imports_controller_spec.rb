require 'rails_helper'

RSpec.describe Accounting::GeneralLedgerImportsController,
               devise_login: :property_manager do
  let(:journal) { create(:company) }

  let(:params) { { journal_id: journal.id } }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the journal' do
      create(:asset_account, gl_code: 1000,
                             name: 'Chase Operating',
                             tenant: journal.chart_of_accounts)

      create(:equity_account, gl_code: 3000,
                              name: 'Owner Contribution',
                              tenant: journal.chart_of_accounts)

      create(:expense_account, gl_code: 5431,
                               name: 'Merchant Processing Fees',
                               tenant: journal.chart_of_accounts)

      upload = Rack::Test::UploadedFile.new \
        'spec/fixtures/importing/general_ledger.xlsx'

      post :create, params: params.merge(
        upload: upload,
        period_start: Time.zone.today.beginning_of_year,
        period_end: Time.zone.today
      )

      expect(response).to redirect_to(accounting_journal_path(journal))
    end
  end
end
