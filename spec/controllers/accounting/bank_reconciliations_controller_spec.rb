require 'rails_helper'

RSpec.describe Accounting::BankReconciliationsController,
               devise_login: :property_manager do
  let(:bank_account) { create(:bank_account, owner: create(:company)) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { bank_account_id: bank_account.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the edit reconciliation path' do
      post :create, params: {
        bank_account_id: bank_account.id,
        reconciliation: {
          statement_date: Time.zone.today,
          statement_ending_balance: '$100.00'
        }
      }

      reconciliation = bank_account.reconciliations.last!

      expect(response).to redirect_to \
        edit_organization_bank_account_reconciliation_path \
          bank_account, reconciliation
    end
  end

  context 'existing reconciliation' do
    let!(:reconciliation) do
      create(:bank_account_reconciliation, bank_account: bank_account)
    end

    let(:params) do
      { bank_account_id: bank_account.id, id: reconciliation.id }
    end

    describe 'GET #edit' do
      it 'returns http success for html' do
        get :edit, params: params

        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #show' do
      before { reconciliation.update(submitted_at: Time.zone.now) }

      it 'returns http success for html' do
        get :show, params: params

        expect(response).to have_http_status(:success)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the bank account' do
        expect { delete :destroy, params: params }.to \
          change { BankAccount::Reconciliation.count }.by(-1)

        expect(response).to redirect_to \
          organization_bank_account_path(bank_account)
      end
    end
  end
end
