require 'rails_helper'

RSpec.describe Accounting::JournalsController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  context 'existing journal do' do
    let(:journal) { create(:company) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: journal.id }
        expect(response).to have_http_status(:success)
      end
    end
  end
end
