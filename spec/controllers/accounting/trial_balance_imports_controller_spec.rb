require 'rails_helper'

RSpec.describe Accounting::TrialBalanceImportsController,
               devise_login: :property_manager do
  let(:journal) { create(:company) }

  let(:params) { { journal_id: journal.id } }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #show xlsx' do
    it 'returns http succcess' do
      get :show, format: :xlsx, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the journal' do
      allow(TrialBalanceImporter).to \
        receive(:import) { OpenStruct.new(successful?: true) }

      upload = Rack::Test::UploadedFile.new \
        'spec/fixtures/importing/beginning_balances.xlsx'

      post :create, params: params.merge(upload: upload)

      expect(response).to redirect_to \
        accounting_journal_path(journal)
    end
  end
end
