require 'rails_helper'

RSpec.describe Accounting::PaymentsController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success for html' do
      get :index, format: :html
      expect(response).to have_http_status(:success)
    end
  end

  context 'an existing payment' do
    let!(:payment) { create(:payment) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: payment.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: payment.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PUT #update' do
      it 'redirects to the payment' do
        description = 'Updated'

        put :update, format: :js, xhr: true, params: {
          id: payment.id, payment: {
            description: description,
            payer: payment.payer.to_sgid.to_s,
            payee: payment.payee.to_sgid.to_s
          }
        }

        expect(payment.reload.description).to eq(description)

        expect(response).to redirect_to(
          accounting_payment_path(payment)
        )
      end
    end

    describe 'GET #void' do
      it 'redirects to the payment' do
        allow(Payment::Void).to \
          receive(:call) { OpenStruct.new(successful?: true) }

        get :void, params: { id: payment.id }

        expect(response).to redirect_to(
          accounting_payment_path(payment)
        )
      end
    end

    describe 'GET #refund' do
      it 'redirects to the payment' do
        allow(Payment::Refund).to \
          receive(:call) { OpenStruct.new(successful?: true) }

        get :refund, params: { id: payment.id }

        expect(response).to redirect_to(
          accounting_payment_path(payment)
        )
      end
    end

    describe 'DELETE #destroy' do
      it 'deletes a payment' do
        expect do
          delete :destroy, params: { id: payment.id }
          expect(response).to redirect_to(accounting_payments_path)
        end.to change { Payment.count }.by(-1)
      end
    end
  end
end
