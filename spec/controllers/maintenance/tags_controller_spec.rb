require 'rails_helper'

RSpec.describe Maintenance::TagsController,
               devise_login: :property_manager do
  describe 'POST #create' do
    it 'redirects to the index' do
      value = 'Value'

      post :create, params: {
        tag: {
          tag: value
        }
      }

      expect(response).to redirect_to(maintenance_tags_path)

      tag = Tag.last!
      expect(tag.tag).to eq(value)
    end
  end

  describe 'POST #upload' do
    it 'redirects to the index' do
      fixture_path = 'spec/fixtures/importing/maintenance_tag_list.xlsx'
      upload = Rack::Test::UploadedFile.new(fixture_path)

      expect do
        post :upload, params: { upload: upload }

        expect(response).to redirect_to(maintenance_tags_path)
      end.to change { Tag.count }.by(3)

      tag = Tag.last!
      expect(tag.tag).to eq('Kitchen Sink')
    end
  end

  context 'existing tag' do
    let!(:tag) { create(:tag) }

    describe 'GET #index' do
      it 'returns http success' do
        get :index
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'redirects to the index' do
        value = 'Updated'

        patch :update, params: {
          id: tag.id,
          tag: {
            tag: value
          }
        }

        expect(response).to redirect_to(maintenance_tags_path)

        expect(tag.reload.tag).to eq(value)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the index' do
        expect do
          delete :destroy, params: { id: tag.id }

          expect(response).to redirect_to(maintenance_tags_path)
        end.to change { Tag.count }.by(-1)
      end
    end
  end
end
