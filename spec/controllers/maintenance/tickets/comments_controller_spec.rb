require 'rails_helper'

RSpec.describe Maintenance::Tickets::CommentsController,
               devise_login: :property_manager do
  let!(:ticket) { create(:maintenance_ticket) }

  describe 'POST #create' do
    it 'redirects to the ticket' do
      body = 'Hello'

      post :create, params: { ticket_id: ticket.id, comment: body }

      expect(response).to redirect_to(maintenance_ticket_path(ticket))

      message = ticket.events.comment.last!
      expect(message.author).to eq(manager)
      expect(message.body).to eq(body)
    end
  end
end
