require 'rails_helper'

RSpec.describe Maintenance::TicketsController, devise_login:
                                               :property_manager do
  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    let(:parameters) do
      property = create(:property)

      {
        maintenance_ticket: {
          subject: 'Lawn Mowing',
          description: 'Use the lawnmower'
        },
        regarding: "property-#{property.id}"
      }
    end

    it 'creates a maintenance ticket' do
      post :create, params: parameters

      ticket = MaintenanceTicket.last

      expect(ticket.subject).to eq('Lawn Mowing')
      expect(ticket.description).to eq('Use the lawnmower')
      expect(ticket).to be_real
    end

    it 'creates a recurring maintenance template' do
      start_date = Date.tomorrow

      schedule_params = {
        unit: 'months',
        amount: '3',
        start_date: start_date
      }

      post :create, params: parameters.merge(schedule: schedule_params)

      ticket = MaintenanceTicket.last

      expect(ticket).to be_template

      schedule = ticket.schedule

      expect(schedule.to_s).to eq('Every 3 months')
      expect(schedule.start_time).to eq(start_date.in_time_zone)
    end
  end

  context 'existing ticket' do
    let!(:ticket) { create(:maintenance_ticket) }

    describe 'GET #show' do
      before { get :show, params: { id: ticket.id } }

      it 'returns http success' do
        expect(response).to have_http_status(:success)
      end

      it 'clears notifications' do
        expect(
          ClearNotificationsJob
        ).to have_been_enqueued.with(
          ticket, manager
        )
      end
    end

    describe 'POST #update' do
      it 'redirects to the maintenance ticket' do
        allow(MaintenanceTicket::Persist).to receive(:call) do
          OpenStruct.new(successful?: true, ticket: ticket)
        end

        post :update, params: { id: ticket.id }

        expect(response).to redirect_to(maintenance_ticket_path(ticket))
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the feed' do
        expect do
          delete :destroy, params: { id: ticket.id }
        end.to change { MaintenanceTicket.count }.by(-1)

        expect(response).to redirect_to(maintenance_tickets_path)
      end
    end
  end
end
