require 'rails_helper'

RSpec.describe Maintenance::ReceiptsController,
               devise_login: :property_manager do
  let(:ticket) { create(:maintenance_ticket) }

  describe 'POST #create' do
    it 'redirects to the ticket' do
      allow(MaintenanceTicket::AttachReceipt).to \
        receive(:call) { OpenStruct.new(successful?: true) }

      post :create, params: { ticket_id: ticket.id }

      expect(response).to redirect_to(maintenance_ticket_path(ticket))
    end
  end
end
