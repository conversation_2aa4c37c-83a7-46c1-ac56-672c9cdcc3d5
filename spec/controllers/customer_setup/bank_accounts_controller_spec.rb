require 'rails_helper'

RSpec.describe CustomerSetup::BankAccountsController,
               devise_login: :property_manager do
  before { Customer.current.setup_required! }

  let!(:company) { create(:company, name: 'Grove, LLC') }
  let!(:bank_account) { create(:bank_account, owner: company) }

  describe 'GET #index' do
    it 'returns http success for html' do
      get :index, format: :html
      expect(response).to have_http_status(:success)
    end

    it 'returns http success for xlsx' do
      get :index, format: :xlsx
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'returns http success' do
      fixture_path = 'spec/fixtures/importing/bank_account_list.xlsx'
      upload = Rack::Test::UploadedFile.new(fixture_path)
      post :create, params: { upload: upload }
      expect(response).to have_http_status(:success)
    end
  end
end
