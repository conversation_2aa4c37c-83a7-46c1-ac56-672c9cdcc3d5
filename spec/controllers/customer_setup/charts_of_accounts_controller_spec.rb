require 'rails_helper'

RSpec.describe CustomerSetup::ChartsOfAccountsController,
               devise_login: :property_manager do
  before { Customer.current.setup_required! }

  let!(:client_entity) { Customer.current.client_entity }
  let!(:chart) { client_entity.chart_of_accounts }
  let!(:account) { create(:account, tenant: chart) }

  describe 'GET #show.xlsx' do
    it 'returns http success' do
      get :show, format: :xlsx
      expect(response).to have_http_status(:success)
    end
  end
end
