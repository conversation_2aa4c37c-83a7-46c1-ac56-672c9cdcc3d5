require 'rails_helper'

RSpec.describe Organization::Tunisia::BankVerificationLettersController do
  describe 'GET #show' do
    it 'sends a PDF', :vcr, devise_login: :property_manager do
      deposit_account = create(:tunisia_deposit_account)

      get :show, params: { id: deposit_account.id }

      expect(response).to have_http_status(:success)

      expect(response.body).to have_content(/a bank verification letter/i)
    end
  end
end
