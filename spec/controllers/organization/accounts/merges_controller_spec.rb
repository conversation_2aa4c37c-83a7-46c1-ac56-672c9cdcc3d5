require 'rails_helper'

RSpec.describe Organization::Accounts::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
               devise_login: :property_manager do
  let(:account) { create(:account) }
  let(:chart_of_accounts) { account.tenant }

  let(:params) do
    { chart_of_accounts_id: chart_of_accounts.id, account_id: account.id }
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params

      expect(response).to have_http_status(:success)
    end
  end

  context 'with a second account' do
    let(:account_two) { create(:account, tenant: chart_of_accounts) }

    describe 'POST #create' do
      it 'redirects to the other account' do
        post :create, params: params.merge(into_account_id: account_two.id)

        expect(response).to redirect_to(
          organization_chart_of_accounts_account_path(
            chart_of_accounts, account_two
          )
        )
      end
    end
  end
end
