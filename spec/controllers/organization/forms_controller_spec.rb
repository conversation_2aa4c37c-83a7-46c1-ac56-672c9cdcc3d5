require 'rails_helper'

RSpec.describe Organization::FormsController,
               devise_login: :property_manager do
  describe 'GET #show' do
    let!(:form) do
      form = CustomForms::Template::EventRegistration::V1.new('name').create!
      CustomForms::AutomationSettings::EventDateTime::V1.find_by(form: form).update!(event_at: Time.zone.now.tomorrow)

      form
    end

    it 'returns http success' do
      get :show, params: { id: form.id }
      expect(response).to have_http_status(:success)
    end

    it 'renders the show template' do
      get :show, params: { id: form.id }
      expect(response).to render_template(:show)
    end
  end

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end
end
