require 'rails_helper'

RSpec.describe Organization::CompaniesController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'returns http created' do
      post :create, params: {
        company: {
          portfolio_id: create(:portfolio).id,
          name: 'Company'
        }
      }

      company = Company.last!

      expect(response).to redirect_to \
        organization_company_path(company)
    end
  end

  context 'existing company' do
    let(:company) { create(:company, customer_managed: false) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: company.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: company.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      before { patch :update, params: params }

      context 'successful' do
        let(:params) { { id: company.id, company: { name: 'New Name' } } }

        it 'redirects to the company' do
          expect(response).to redirect_to(organization_company_path(company))
        end
      end

      context 'unsuccessful' do
        let(:params) { { id: company.id, company: { name: '' } } }

        it 'renders the edit template' do
          expect(response).to render_template(:edit)
        end
      end
    end

    describe 'DELETE #destroy' do
      it 'returns redirects to the index' do
        delete :destroy, params: { id: company.id }
        expect(response).to redirect_to(organization_companies_path)
      end
    end
  end
end
