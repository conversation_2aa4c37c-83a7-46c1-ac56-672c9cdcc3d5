require 'rails_helper'

RSpec.describe Organization::Employees<PERSON><PERSON>roller, devise_login:
  :property_manager do
  include ActiveJob::TestHelper

  describe 'GET #index' do
    it 'returns http success for html' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'returns http success for xlsx' do
      get :index, format: :xlsx
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #show' do
    it 'returns http success for html' do
      get :show, params: { id: manager.id }
      expect(response).to have_http_status(:success)
    end

    it 'returns http success for json' do
      get :show, params: { id: manager.id }, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    let(:role) { create(:role) }

    before do
      data = {
        property_manager: {
          first_name: 'First',
          last_name: 'Last',
          email: '<EMAIL>',
          role_id: role.id
        }
      }

      perform_enqueued_jobs { post :create, params: data }
    end

    it 'creates a new employee' do
      employee = PropertyManager.last
      expect(employee.first_name).to eq('First')
      expect(employee.last_name).to eq('Last')
      expect(employee.email).to eq('<EMAIL>')
      expect(employee.role).to eq(role)
    end

    it 'sends a welcome email' do
      mail = ActionMailer::Base.deliveries.last
      expect(mail.to.first).to eq('<EMAIL>')
    end
  end

  context 'existing employee' do
    let(:employee) { create(:property_manager) }
    let(:params) { { id: employee.id } }

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: employee.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #archive' do
      it 'redirects to the employee' do
        get :archive, params: params
        expect(response).to redirect_to(organization_employee_path(employee))
      end
    end

    describe 'GET #unarchive' do
      it 'redirects to the employee' do
        get :unarchive, params: params
        expect(response).to redirect_to(organization_employee_path(employee))
      end
    end

    describe 'GET #audits' do
      it 'returns http success for html' do
        get :audits, params: params, format: :html
        expect(response).to have_http_status(:success)
      end

      it 'returns http success for xlsx' do
        get :audits, params: params, format: :xlsx
        expect(response).to have_http_status(:success)
      end
    end
  end
end
