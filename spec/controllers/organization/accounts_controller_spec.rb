require 'rails_helper'

RSpec.describe Organization::Accounts<PERSON><PERSON>roller,
               devise_login: :property_manager do
  context 'with chart of accounts' do
    let(:chart_of_accounts) { create(:chart_of_accounts) }

    describe 'GET #index json' do
      it 'returns http success' do
        get :index, params: {
          chart_of_accounts_id: chart_of_accounts.id
        }, format: :json
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #new' do
      it 'returns http success' do
        get :new, params: { chart_of_accounts_id: chart_of_accounts.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'POST #create' do
      it 'redirects to the account' do
        gl_code = '14'

        post :create, params: {
          chart_of_accounts_id: chart_of_accounts.id,
          account: {
            name: 'Bank Fees',
            gl_code: gl_code,
            type: 'Plutus::Expense/Overhead'
          }
        }

        account = Plutus::Expense.last
        expect(account.name).to eq('Bank Fees')
        expect(account.gl_code).to eq(gl_code)
        expect(account.category).to eq('Overhead')

        expect(response).to redirect_to(
          organization_chart_of_accounts_account_path(chart_of_accounts, account)
        )
      end
    end

    context 'existing account' do
      let(:account) { create(:account, tenant: chart_of_accounts) }

      describe 'GET #show' do
        it 'returns http success' do
          get :show, params: {
            chart_of_accounts_id: chart_of_accounts.id,
            id: account.id
          }

          expect(response).to have_http_status(:success)
        end
      end

      describe 'GET #edit' do
        it 'returns http success' do
          get :edit, params: {
            chart_of_accounts_id: chart_of_accounts.id,
            id: account.id
          }

          expect(response).to have_http_status(:success)
        end
      end

      describe 'POST #update' do
        it 'redirects to the account' do
          gl_code = '111'

          post :update, params: {
            chart_of_accounts_id: chart_of_accounts.id,
            id: account.id,
            account: { gl_code: gl_code }
          }

          expect(account.reload.gl_code).to eq(gl_code)
          expect(response).to redirect_to(
            organization_chart_of_accounts_account_path(
              chart_of_accounts, account
            )
          )
        end
      end

      describe 'DELETE #destroy' do
        it 'redirects to the index' do
          klass = account.class

          expect do
            delete :destroy, params: {
              chart_of_accounts_id: chart_of_accounts.id,
              id: account.id
            }
          end.to change { klass.count }.by(-1)

          expect(response).to redirect_to(
            organization_chart_of_accounts_path(chart_of_accounts)
          )
        end
      end
    end
  end
end
