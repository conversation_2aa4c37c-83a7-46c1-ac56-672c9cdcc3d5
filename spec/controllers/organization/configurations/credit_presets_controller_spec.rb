require 'rails_helper'

RSpec.describe Organization::Configurations::CreditPresetsController,
               devise_login: :property_manager do
  let!(:configuration) { create(:configuration) }

  let(:account) { create(:expense_account) }

  let(:params) { { configuration_id: configuration.id } }

  describe 'GET #index' do
    it 'returns http success' do
      get :index, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the index' do
      expect do
        post :create, params: params.merge(
          credit_preset: {
            name: 'Preset',
            amount: '$30.00',
            account_id: account.id
          }
        )
      end.to change { configuration.credit_presets.count }.by(1)

      expect(response).to redirect_to \
        organization_configuration_credit_presets_path(configuration)
    end
  end

  context 'existing preset' do
    let!(:preset) { create(:credit_preset, configuration: configuration) }

    let(:params) { { configuration_id: configuration.id, id: preset.id } }

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: params

        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'redirects to the index' do
        patch :update, params: params.merge(
          credit_preset: {
            amount: '$10.00'
          }
        )

        expect(response).to redirect_to \
          organization_configuration_credit_presets_path(configuration)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the index' do
        expect do
          delete :destroy, params: params
        end.to change { configuration.credit_presets.count }.by(-1)

        expect(response).to redirect_to \
          organization_configuration_credit_presets_path(configuration)
      end
    end
  end
end
