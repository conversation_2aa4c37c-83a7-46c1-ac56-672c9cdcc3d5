require 'rails_helper'

RSpec.describe Organization::ChartsOfAccountsController,
               devise_login: :property_manager do
  describe 'GET #index' do
    context 'html' do
      it 'returns http success' do
        get :index, format: :html
        expect(response).to have_http_status(:success)
      end
    end

    context 'json' do
      it 'returns http success' do
        get :index, format: :json
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'json' do
      before do
        filename = 'spec/fixtures/importing/mp_coa.xlsx'
        type = MIME::Types.type_for(filename)[0]
        file = Rack::Test::UploadedFile.new(filename, type)

        post :create, format: :json, params: {
          chart_of_accounts: {
            name: 'HUD Chart',
            file: file
          }
        }
      end

      describe 'the response' do
        subject { response }

        it { is_expected.to have_http_status(:success) }
      end

      describe 'the chart' do
        subject { ChartOfAccounts.last }

        its(:name) { is_expected.to eq('HUD Chart') }
      end
    end
  end

  context 'existing chart of accoutns' do
    let(:chart) { create(:chart_of_accounts) }

    describe 'GET #new_upload' do
      it 'returns http success' do
        get :new_upload, params: { id: chart.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #upload' do
      it 'redirects to the configuration path' do
        path = 'spec/fixtures/importing/chart_of_accounts.xlsx'

        patch :upload, params: {
          id: chart.id, chart_of_accounts: {
            upload: Rack::Test::UploadedFile.new(path)
          }
        }

        expect(response).to redirect_to \
          configure_organization_chart_of_accounts_path(chart)
      end
    end

    describe 'GET #configure' do
      it 'returns http success' do
        get :configure, params: { id: chart.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: chart.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: chart.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PUT #update' do
      it 'redirects to the chart' do
        put :update, params: {
          id: chart.id,
          chart_of_accounts: {
            name: 'New Name'
          }
        }

        expect(response).to redirect_to(
          organization_chart_of_accounts_path(chart)
        )
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the index' do
        delete :destroy, params: { id: chart.id }
        expect(response).to redirect_to(organization_charts_of_accounts_path)
      end
    end
  end
end
