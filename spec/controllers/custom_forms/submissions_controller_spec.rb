require 'rails_helper'

RSpec.describe CustomForms::SubmissionsController do
  describe 'GET #new' do
    let!(:form) do
      form = CustomForms::Template::EventRegistration::V1.new('name').create!
      CustomForms::AutomationSettings::EventDateTime::V1.find_by(form: form).update!(event_at: Time.zone.now.tomorrow)
      CustomForms::PublishCustomForm.call(custom_form: form)

      form
    end

    it 'returns http success' do
      get :new, params: { token: form.token }
      expect(response).to have_http_status(:success)
    end

    context 'when the form is archived' do
      before { form.archive!(1.day.ago) }

      it 'raises an error' do
        expect do
          get :new, params: { token: form.token }
        end.to raise_error('Not Found')
      end
    end

    context 'when the form is not published' do
      before do
        form.update!(published_at: nil)
      end

      it 'raises an error' do
        expect do
          get :new, params: { token: form.token }
        end.to raise_error('Not Found')
      end
    end

    context 'when the form is missing required settings' do
      before do
        CustomForms::AutomationSettings::EventDateTime::V1.find_by(form_id: form.id).update_column(
          :event_at, nil
        )
      end

      it 'raises an error' do
        expect do
          get :new, params: { token: form.token }
        end.to raise_error('form is missing required settings')
      end
    end
  end
end
