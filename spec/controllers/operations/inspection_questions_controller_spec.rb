require 'rails_helper'

RSpec.describe Operations::InspectionQuestionsController,
               devise_login: :property_manager do
  let(:template) { create(:inspection_template) }

  let(:params) { { inspection_template_id: template.id, category: :unit } }

  describe 'GET #index' do
    it 'returns http success' do
      get :index, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'returns http success' do
      post :create, params: params.merge(
        inspection_question: {
          prompt: 'The Prompt',
          kind: :free_response
        }
      )

      expect(response).to have_http_status(:created)
    end
  end
end
