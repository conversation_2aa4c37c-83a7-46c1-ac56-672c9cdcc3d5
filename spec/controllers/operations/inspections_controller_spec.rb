require 'rails_helper'

RSpec.describe Operations::InspectionsController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #targets json' do
    xit 'returns http success' do
      get :targets, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'returns redirects to the inspection' do
      post :create, params: {
        inspection_report: {
          kind: :acquisition,
          name: 'Inspect Unit',
          template_id: create(:inspection_template).id,
          target_id: create(:unit).to_sgid
        }
      }

      inspection = Inspection::Report.last!

      expect(response).to redirect_to(operations_inspection_path(inspection))
    end
  end

  context 'with an existing inspection' do
    let!(:report) { create(:inspection_report, :completed) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: report.id }
        expect(response).to have_http_status(:success)
      end
    end
  end
end
