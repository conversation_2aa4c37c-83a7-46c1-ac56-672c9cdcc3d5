require 'rails_helper'

RSpec.describe Operations::PulseController,
               devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #activities' do
    it 'returns http success' do
      get :activities, format: :json
      expect(response).to have_http_status(:success)
    end

    it 'contains appropriate task json' do
      task = create(:task)
      create(:assignment, user: manager, assignable: task)
      get :activities, format: :json

      json = JSON.parse(response.body)[0]

      attribtues = {
        path: operations_project_task_path(task.project, task),
        type: 'task',
        title: task.name,
        description: task.description
      }.with_indifferent_access

      expect(json).to include(attribtues)
    end

    it 'contains appropriate work order json' do
      work_order = create(:maintenance_ticket)
      create(:assignment, user: manager, assignable: work_order)
      get :activities, format: :json

      json = JSON.parse(response.body)[0]

      attributes = {
        path: maintenance_ticket_path(work_order),
        type: 'work_order',
        title: work_order.subject,
        description: work_order.description
      }.with_indifferent_access

      expect(json).to include(attributes)
    end

    it 'contains appropriate tour jjson' do
      tour = create(:tour, tour_guide: manager)
      get :activities, format: :json

      json = JSON.parse(response.body)[0]

      attributes = {
        path: leasing_guest_card_path(tour.guest_card),
        type: 'tour',
        title: "Tour of #{tour.property.name}",
        description: "With #{tour.tenant.name}"
      }.with_indifferent_access

      expect(json).to include(attributes)
    end
  end
end
