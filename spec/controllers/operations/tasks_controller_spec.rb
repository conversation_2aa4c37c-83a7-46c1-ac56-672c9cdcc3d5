require 'rails_helper'
require_relative '../shared/approving_controller'

RSpec.describe Operations::TasksController,
               devise_login: :property_manager do
  let!(:project) { create(:project) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { project_id: project.id }
      expect(response).to have_http_status(:success)
    end
  end

  context 'existing task' do
    let!(:task) { create(:task, project: project) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { project_id: project.id, id: task.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { project_id: project.id, id: task.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #open' do
      it 'opens the task' do
        task.complete!
        get :open, params: { project_id: project.id, id: task.id }
        expect(response).to redirect_to([:operations, project, task])
        expect(task.reload.completed_at).to be_nil
      end
    end

    describe 'GET #complete' do
      it 'completes the task' do
        task.open!
        get :complete, params: { project_id: project.id, id: task.id }
        expect(response).to redirect_to([:operations, project, task])
        expect(task.reload.completed_at).to be_present
      end
    end

    describe 'GET #dependable.json' do
      it 'returns http success' do
        get :dependable, params: { project_id: project.id, id: task.id },
                         format: :json
        expect(response).to have_http_status(:success)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the project' do
        delete :destroy, params: { project_id: project.id, id: task.id }
        expect(response).to redirect_to([:operations, project])
      end
    end

    it_behaves_like 'an approving controller' do
      let(:params) { { id: task.id, project_id: task.project.id } }
    end
  end
end
