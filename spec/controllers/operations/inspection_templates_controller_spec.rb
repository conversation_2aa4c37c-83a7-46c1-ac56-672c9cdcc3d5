require 'rails_helper'

RSpec.describe Operations::InspectionTemplates<PERSON><PERSON>roller,
               devise_login: :property_manager do
  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the edit template path' do
      post :create, params: {
        inspection_template: {
          name: 'The Template'
        }
      }

      template = Inspection::Template.last!

      expect(response).to redirect_to(
        edit_operations_inspection_template_path(template)
      )
    end
  end

  context 'existing template' do
    let!(:template) { create(:inspection_template) }

    let(:params) { { id: template.id } }

    describe 'GET #index' do
      it 'returns http success' do
        get :index
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: params
        expect(response).to have_http_status(:success)
      end
    end
  end
end
