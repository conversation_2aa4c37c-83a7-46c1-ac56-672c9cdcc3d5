require 'rails_helper'

RSpec.describe Operations::ProjectsController,
               devise_login: :property_manager do
  let!(:board) { create(:project_board) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { project_board_id: board.id }
      expect(response).to have_http_status(:success)
    end
  end

  context 'exiting project' do
    let!(:project) { create(:project, board: board, members: [manager]) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: project.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: project.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #open' do
      it 'returns http success' do
        project.complete!

        get :open, params: { id: project.id }

        expect(response).to redirect_to(operations_project_path(project))
        expect(project.reload).to be_open
      end
    end

    describe 'GET #complete' do
      it 'returns http success' do
        project.open!

        get :complete, params: { id: project.id }

        expect(response).to redirect_to(operations_project_path(project))
        expect(project.reload).to be_completed
      end
    end

    describe 'GET #members' do
      it 'returns http success' do
        get :members, params: { id: project.id }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'redirects to project path' do
        post :update, params: {
          id: project.id,
          project: {
            name: 'New Name'
          }
        }

        expect(project.reload.name).to eq('New Name')

        expect(response).to redirect_to(operations_project_path(project))
      end

      describe 'DELETE #destroy' do
        it 'redirects to projects path' do
          expect do
            delete :destroy, params: { id: project.id }
          end.to change { Project.count }.by(-1)

          expect(response).to redirect_to(operations_project_board_path(board))
        end
      end

      it 'delete and add new project memberships' do
        user1 = create(:property_manager)
        user2 = create(:property_manager)
        user3 = create(:property_manager)
        project = create(:project)
        pm_owner = ProjectMembership.create!(project: project,
                                             user: manager, owner: true)
        pm1 = ProjectMembership.create!(project: project, user: user1)
        pm2 = ProjectMembership.create!(project: project, user: user2)

        patch :update, params: {
          id: project.id,
          project: {
            name: 'Name',
            member_ids: [user1.id, user3.id]
          }
        }

        expect(ProjectMembership.find_by(id: pm2.id)).to be_nil
        expect(project.project_memberships).to include(pm_owner)
        expect(project.project_memberships).to include(pm1)
        expect(project.members.map(&:id)).to match_array(
          [manager, user1, user3].map(&:id)
        )
      end
    end

    describe 'GET #new_template' do
      it 'returns http success' do
        get :new_template, params: { id: project.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'POST #template' do
      it 'redirects to the projects index' do
        post :template, params: {
          id: project.id,
          project_template: { name: 'The Template' }
        }
        expect(response).to redirect_to(operations_project_boards_path)
      end
    end
  end
end
