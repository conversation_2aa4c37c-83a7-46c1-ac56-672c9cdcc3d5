require 'rails_helper'

RSpec.describe Operations::ProjectBudgetsController,
               devise_login: :property_manager do
  let(:project) { create(:project, members: [manager]) }
  let(:account) { create(:expense_account) }
  let(:amount) { '$123.45' }

  describe 'POST #create' do
    let(:params) do
      {
        project_id: project.id,
        budget: {
          budget_amounts_attributes: [
            { account_id: account.id, amount: amount }
          ]
        }
      }
    end

    before { post :create, params: params, format: :json }

    it 'redirects to the project' do
      expect(response).to redirect_to(operations_project_path(project))
    end

    it 'creates budget amounts on the project' do
      budget_amount = project.budget_amounts.last
      expect(budget_amount.account).to eq(account)
      expect(budget_amount.amount.format).to eq(amount)
    end
  end
end
