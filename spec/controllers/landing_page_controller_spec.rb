require 'rails_helper'

RSpec.describe LandingPageController do
  describe 'GET #index' do
    it 'returns success' do
      request.env['HTTP_REFERER'] = 'test.host'
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'redirects logged in managers' do
      manager = create(:property_manager)
      sign_in manager

      get :index

      expect(response).to redirect_to(operations_pulse_path)
    end
  end

  describe 'static pages' do
    %i[security careers contact company].each do |path|
      describe "#{path} page" do
        it 'returns http success' do
          get path
          expect(response).to have_http_status(:success)
        end
      end
    end
  end
end
