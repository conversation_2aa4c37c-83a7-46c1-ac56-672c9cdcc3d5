require 'rails_helper'

RSpec.describe NotificationsController, devise_login: :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #show' do
    let(:notification) { create(:notification, user: manager) }

    before { get :show, params: { id: notification.id } }

    it 'marks the notification as seen' do
      expect(notification.reload).to be_seen
    end

    it 'redirects to the notification link' do
      expect(response).to redirect_to(notification.link)
    end
  end

  describe 'GET #mark_all_as_seen' do
    let!(:notification) { create(:notification, user: manager) }

    before { get :mark_all_as_seen }

    it 'marks notifications as seen' do
      expect(notification.reload).to be_seen
    end

    it 'redirects to notifications' do
      expect(response).to redirect_to(notifications_path)
    end
  end
end
