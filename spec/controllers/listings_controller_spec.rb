require 'rails_helper'

RSpec.describe ListingsController do
  let!(:listing) { create(:listing, :published) }

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #show' do
    it 'returns http success' do
      get :show, params: { id: listing.id }
      expect(response).to have_http_status(:success)
    end

    context 'archived property' do
      it '404s' do
        listing.floorplan.property.archive!(Time.zone.today - 1.day)
        expect do
          get :show, params: { id: listing.id }
        end.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end
end
