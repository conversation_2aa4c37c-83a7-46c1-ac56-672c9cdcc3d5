require 'rails_helper'

RSpec.describe Management::PropertiesController,
               devise_login: :property_manager do
  let(:service) { Property::Persist }

  describe 'GET #index' do
    it 'shows a list of properties' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  context 'existing property' do
    let(:property) { create(:property) }

    describe 'GET #show' do
      it 'shows a property by id' do
        get :show, params: { id: property.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: property.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      context 'successful' do
        it 'redirects to the property' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: true, property: property)
          end

          post :update, params: { id: property.id }

          expect(response).to redirect_to(property_path(property))
        end
      end

      context 'unsuccessful' do
        it 'renders the edit form' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: false, property: property, errors: [])
          end

          post :update, params: { id: property.id }

          expect(response).to render_template(:edit)
        end
      end
    end
  end

  context 'an unsetup property' do
    let(:property) { create(:property, setup: false) }

    describe 'GET #show' do
      it 'redirects to property setup' do
        get :show, params: { id: property.id }
        expect(response).to redirect_to(property_setup_path(property))
      end
    end
  end
end
