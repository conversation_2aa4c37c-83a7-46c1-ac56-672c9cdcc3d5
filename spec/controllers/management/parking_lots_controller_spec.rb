require 'rails_helper'

RSpec.describe Management::<PERSON>ing<PERSON>ots<PERSON>ontroller, devise_login:
                                                  :property_manager do
  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the parking lot' do
      post :create, params: {
        parking_lot: {
          name: 'East Lot',
          space_count: 2
        }
      }

      lot = ParkingLot.last

      expect(response).to redirect_to(parking_lot_path(lot))
    end
  end

  context 'existing parking lot' do
    let!(:parking_lot) { create(:parking_lot) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: parking_lot.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: parking_lot.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'redirects to the parking lot' do
        patch :update, params: {
          id: parking_lot.id,
          parking_lot: {
            name: 'New Lot Name'
          }
        }

        expect(parking_lot.reload.name).to eq('New Lot Name')

        expect(response).to redirect_to(parking_lot_path(parking_lot))
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the parking lots path' do
        expect do
          delete :destroy, params: { id: parking_lot.id }
        end.to change { ParkingLot.count }.by(-1)

        expect(response).to redirect_to(parking_lots_path)
      end
    end
  end
end
