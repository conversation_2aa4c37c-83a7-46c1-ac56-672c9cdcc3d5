require 'rails_helper'

RSpec.describe Management::FloorplansController,
               devise_login: :property_manager do
  let(:property) { create(:property) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { property_id: property.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'creates a new floorplan' do
      post :create, params: {
        property_id: property.id,
        floorplan: {
          name: 'Two Bedroom',
          square_feet: 500,
          bedrooms: 2,
          bathrooms: 2.5,
          price: '$550.25'
        }
      }
      expect(response).to have_http_status(:redirect)

      floorplan = property.floorplans.last
      expect(floorplan.name).to eq('Two Bedroom')
      expect(floorplan.square_feet).to eq(500)
      expect(floorplan.bedrooms).to eq(2)
      expect(floorplan.bathrooms).to eq(2.5)
      expect(floorplan.price.format).to eq('$550.25')
    end
  end

  context 'exisitng floorplan' do
    let!(:floorplan) { create(:floorplan, property: property) }

    let(:params) { { property_id: property.id, id: floorplan.id } }

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PUT #update' do
      it 'redirects to the property' do
        new_name = 'New Name'

        put :update, params: params.merge(
          floorplan: {
            name: new_name
          }
        )

        expect(response).to \
          redirect_to(property_path(property, anchor: 'floorplans'))
        expect(floorplan.reload.name).to eq(new_name)
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the property' do
        expect do
          delete :destroy, params: params
        end.to change { Floorplan.count }.by(-1)

        expect(response).to \
          redirect_to(property_path(property, anchor: 'floorplans'))
      end

      context 'with a unit' do
        it 'does not remove the floorplan' do
          create(:unit, property: property, floorplan: floorplan)

          expect do
            delete :destroy, params: params
          end.not_to change { Floorplan.count }

          expect(response).to \
            redirect_to(property_path(property, anchor: 'floorplans'))
        end
      end
    end
  end
end
