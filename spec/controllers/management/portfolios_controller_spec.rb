require 'rails_helper'

RSpec.describe Management::PortfoliosController,
               devise_login: :property_manager do
  describe 'GET #index' do
    context 'html' do
      it 'returns http success' do
        get :index
        expect(response).to have_http_status(:success)
      end
    end

    context 'json' do
      it 'returns http success' do
        get :index, format: :json
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'creates a portfolio' do
      name = 'Alever Properties'
      configuration = create(:configuration)

      post :create, params: {
        portfolio: {
          name: name,
          configuration_id: configuration.id
        }
      }

      portfolio = Portfolio.last!

      expect(response).to redirect_to(
        portfolio_setup_configuration_path(portfolio)
      )

      expect(portfolio.name).to eq(name)
    end
  end

  context 'existing portfolio' do
    let!(:portfolio) { create(:portfolio) }

    describe 'GET #show' do
      it 'returns http status success' do
        get :show, params: { id: portfolio.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: portfolio.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'updates a portfolio' do
        patch :update, params: {
          id: portfolio.id,
          portfolio: {
            name: 'New Name'
          }
        }

        expect(portfolio.reload.name).to eq('New Name')
      end
    end

    describe 'DELETE #destroy' do
      it 'deletes a portfolio' do
        expect do
          delete :destroy, params: { id: portfolio.id }
        end.to change { Portfolio.count }.by(-1)
      end
    end
  end
end
