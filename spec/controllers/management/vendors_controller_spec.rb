require 'rails_helper'

RSpec.describe Management::VendorsController,
               devise_login: :property_manager do
  let(:service) { Vendor::Persist }

  describe 'GET #new' do
    it 'returns http success' do
      create(:company) # TODO: remove
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'successful' do
      it 'redirects to the vendor' do
        vendor = create(:vendor)

        allow(service).to receive(:call) do
          OpenStruct.new(successful?: true, vendor: vendor)
        end

        post :create

        expect(response).to redirect_to(vendor_path(vendor))
      end
    end

    context 'unsuccessful' do
      it 'renders the new template' do
        allow(service).to receive(:call) do
          OpenStruct.new(successful?: false, errors: [], vendor: Vendor.new)
        end

        post :create

        expect(response).to render_template(:new)
      end
    end
  end

  context 'existing vendor' do
    let(:vendor) { create(:vendor) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: vendor.id }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: vendor.id }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'POST #update' do
      context 'successful' do
        it 'redirects to the vendor' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: true, vendor: vendor)
          end

          post :update, params: { id: vendor.id }

          expect(response).to redirect_to(vendor_path(vendor))
        end
      end

      context 'unsuccessful' do
        it 'renders the edit template' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: false, errors: [], vendor: vendor)
          end

          post :update, params: { id: vendor.id }

          expect(response).to render_template :edit
        end
      end
    end

    describe 'GET #invite' do
      before { get :invite, params: { id: vendor.id } }

      it 'redirects to the vendor' do
        expect(response).to redirect_to(vendor_path(vendor))
      end

      it 'creates a vendor invite' do
        invite = Vendor::Invite.last!
        expect(invite.vendor).to eq(vendor)
      end
    end
  end
end
