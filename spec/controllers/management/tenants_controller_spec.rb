require 'rails_helper'

RSpec.describe Management::Tenants<PERSON><PERSON>roller,
               devise_login: :property_manager do
  let!(:tenant) { create(:resident) }

  describe 'GET #index html' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #index xlsx' do
    it 'returns http success' do
      get :index, format: :xlsx
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #show' do
    it 'shows a tenant by id' do
      get :show, params: { id: tenant.id }

      expect(response).to have_http_status(:success)
      expect(assigns[:tenant]).to eq(tenant)
    end
  end

  describe 'GET #edit' do
    it 'returns http success' do
      get :edit, params: { id: tenant.id }

      expect(response).to have_http_status(:success)
    end
  end

  describe 'PATCH #update' do
    context 'successful' do
      it 'redirects to the tenant' do
        patch :update, params: {
          id: tenant.id, tenant: { first_name: 'New Name' }
        }

        expect(response).to redirect_to(tenant_path(tenant))
      end
    end

    context 'unsuccessful' do
      it 'renders the edit template' do
        patch :update, params: { id: tenant.id, tenant: { first_name: '' } }

        expect(response).to render_template(:edit)
      end
    end
  end

  describe 'GET #seven_day_notice' do
    it 'redirects to the tenant' do
      get :seven_day_notice, params: { id: tenant.id }
      expect(response).to redirect_to(tenant_path(tenant))
    end

    it 'sends an email'
    it 'creates a tenant event'
  end
end
