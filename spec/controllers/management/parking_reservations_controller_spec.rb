require 'rails_helper'

RSpec.describe Management::ParkingReservationsController,
               devise_login: :property_manager do
  let!(:parking_lot) { create(:parking_lot) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { parking_lot_id: parking_lot.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    let(:tenant) { create(:resident) }

    it 'redirects to the parking reservation' do
      post :create, params: {
        parking_lot_id: parking_lot.id,
        parking_reservation: {
          tenant_id: tenant.id,
          vehicle_attributes: {
            make: 'Make',
            model: 'Model',
            color: 'Color',
            year: '1998'
          }
        }
      }

      reservation = parking_lot.parking_reservations.last

      expect(response).to redirect_to(
        parking_lot_parking_reservation_path(parking_lot, reservation)
      )
    end

    it 'takes tenant name and such' do
      post :create, params: {
        parking_lot_id: parking_lot.id,
        parking_reservation: {
          tenant_attributes: {
            first_name: tenant.first_name,
            last_name: tenant.last_name,
            email: tenant.email,
            phone: tenant.phone
          },
          vehicle_attributes: {
            make: 'Make',
            model: 'Model',
            color: 'Color',
            year: '1998'
          }
        }
      }

      reservation = parking_lot.parking_reservations.last

      expect(response).to redirect_to(
        parking_lot_parking_reservation_path(parking_lot, reservation)
      )
      expect(reservation.tenant).to eq tenant
      expect(reservation).to be_single
    end

    it 'requires a tenant' do
      post :create, params: {
        parking_lot_id: parking_lot.id,
        parking_reservation: {
          vehicle_attributes: {
            make: 'Make',
            model: 'Model',
            color: 'Color',
            year: '1998'
          }
        }
      }

      expect(response).to have_http_status(:unprocessable_entity)
      expect(response).to render_template(:new)
      expect(flash[:error]).to have_content("Tenant can't be blank")
    end
  end

  context 'existing reservation' do
    let!(:reservation) do
      create(:parking_reservation, parking_lot: parking_lot)
    end

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: {
          parking_lot_id: parking_lot.id, id: reservation.id
        }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: {
          parking_lot_id: parking_lot.id, id: reservation.id
        }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'PATCH #update' do
      it 'returns http success' do
        patch :update, params: {
          parking_lot_id: parking_lot.id, id: reservation.id,
          parking_reservation: {
            vehicle_attributes: {
              id: reservation.vehicle.id,
              year: '2001',
              make: 'Make',
              model: 'Model',
              color: 'red',
              license_plate: 'Plate'
            }
          }
        }

        expect(reservation.vehicle.reload.year).to eq(2001)

        expect(response).to redirect_to(
          parking_lot_parking_reservation_path(parking_lot, reservation)
        )
      end
    end

    describe 'DELETE #destroy' do
      it 'redirects to the parking lot path' do
        expect do
          delete :destroy, params: {
            parking_lot_id: parking_lot.id, id: reservation.id
          }
        end.to change { ParkingReservation.count }.by(-1)

        expect(response).to redirect_to(parking_lot_path(parking_lot))
      end
    end
  end
end
