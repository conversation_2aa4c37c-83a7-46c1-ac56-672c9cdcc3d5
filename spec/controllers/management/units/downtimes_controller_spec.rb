require 'rails_helper'

RSpec.describe Management::Units::DowntimesController,
               devise_login: :property_manager do
  let(:unit) { create(:unit) }

  describe 'POST #create js' do
    it 'returns http success' do
      post :create, format: :js, xhr: true, params: {
        unit_id: unit.id,
        unit_downtime: {
          reason: 'not_rent_ready',
          start_date: Time.zone.now
        }
      }

      expect(response).to have_http_status(:success)
    end
  end

  context 'existing downtime' do
    let(:downtime) { create(:unit_downtime, unit: unit) }

    describe 'GET #terminate js' do
      it 'returns http success' do
        get :terminate, format: :js, xhr: true, params: {
          unit_id: unit.id, id: downtime.id
        }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'DELETE #destroy js' do
      it 'returns http success' do
        delete :destroy, format: :js, xhr: true, params: {
          unit_id: unit.id, id: downtime.id
        }

        expect(response).to have_http_status(:success)
      end
    end
  end
end
