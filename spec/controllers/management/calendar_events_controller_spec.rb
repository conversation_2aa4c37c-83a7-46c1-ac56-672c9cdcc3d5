require 'rails_helper'

RSpec.describe Management::CalendarEventsController,
               devise_login: :property_manager do
  describe 'POST #create json' do
    let(:params) do
      {
        calendar_event: {
          date: Time.zone.tomorrow, title: 'Birthday', description: 'Party'
        }
      }
    end

    before do
      post :create, format: :json, params: params
    end

    it 'returns http success' do
      expect(response).to have_http_status(:created)
    end

    it 'creates a calendar event' do
      event = CalendarEvent.last
      expect(event.start_date.to_date).to eq(Time.zone.tomorrow)
      expect(event.end_date.to_date).to eq(Time.zone.tomorrow)
      expect(event.title).to eq('Birthday')
      expect(event.description).to eq('Party')
    end

    context 'invalid params' do
      let(:params) { { calendar_event: { description: 'Party' } } }

      it 'returns unprocessable entity' do
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'GET #index json' do
    it 'returns http success' do
      get :index, format: :json
      expect(response).to have_http_status(:success)
    end
  end
end
