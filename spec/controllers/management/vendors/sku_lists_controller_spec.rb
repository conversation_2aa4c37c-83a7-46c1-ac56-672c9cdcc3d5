require 'rails_helper'

RSpec.describe Management::Vendors::SkuListsController,
               devise_login: :property_manager do
  let(:vendor) { create(:vendor, kind: :supplier) }

  let(:params) { { vendor_id: vendor.id } }

  describe 'GET #show xlsx' do
    it 'returns http success' do
      get :show, params: params, format: :xlsx
      expect(response).to have_http_status(:success)
    end
  end

  context 'existing sku list' do
    let!(:item) { create(:sku_list_item, vendor: vendor) }

    describe 'DELETE #destroy' do
      it 'redirects to the vendor' do
        delete :destroy, params: params
        expect(response).to \
          redirect_to(vendor_path(vendor, anchor: '/sku_list'))
      end
    end
  end
end
