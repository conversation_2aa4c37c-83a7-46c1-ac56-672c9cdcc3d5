require 'rails_helper'

RSpec.describe Management::LinkagesController,
               devise_login: :property_manager do
  let!(:linkage) { create(:linkage) }

  describe 'DELETE #destroy' do
    let(:params) { { id: linkage.to_sgid.to_s } }

    it 'removes the linkage' do
      expect do
        delete :destroy, params: params
      end.to change { Linkage.count }.by(-1)
    end

    it 'redirects back' do
      origin = '/some_path'
      request.env['HTTP_REFERER'] = origin
      delete :destroy, params: params
      expect(response).to redirect_to(origin)
    end
  end
end
