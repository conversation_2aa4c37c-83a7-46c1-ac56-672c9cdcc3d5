require 'rails_helper'

RSpec.describe Management::OwnersController, devise_login: :property_manager do
  let(:service) { Owner::Persist }

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'returns http success for json' do
      get :index, format: :json
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      create(:company) # TODO: remove

      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'success' do
      it 'redirects to the owner' do
        owner = create(:owner)

        allow(service).to receive(:call) do
          OpenStruct.new(successful?: true, owner: owner)
        end

        post :create

        expect(response).to redirect_to(owner_path(owner))
      end
    end

    context 'failure' do
      it 'renders new' do
        allow(service).to receive(:call) do
          OpenStruct.new(successful?: false, errors: [], owner: Owner.new)
        end

        post :create

        expect(response).to render_template(:new)
      end
    end
  end

  context 'existing owner' do
    let(:owner) { create(:owner) }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: { id: owner.id }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #edit' do
      it 'returns http success' do
        get :edit, params: { id: owner.id }

        expect(response).to have_http_status(:success)
      end
    end

    describe 'POST #update' do
      context 'success' do
        it 'redirects to the owner' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: true, owner: owner)
          end

          post :update, params: { id: owner.id }

          expect(response).to redirect_to(action: :show, id: owner.id)
        end
      end

      context 'failure' do
        it 'renders edit' do
          allow(service).to receive(:call) do
            OpenStruct.new(successful?: false, errors: [], owner: owner)
          end

          post :update, params: { id: owner.id }

          expect(response).to render_template(:edit)
        end
      end
    end
  end
end
