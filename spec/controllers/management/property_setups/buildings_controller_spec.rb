require 'rails_helper'
require_relative '../../shared/property_setup_importing_controller'

RSpec.describe Management::PropertySetups::BuildingsController,
               devise_login: :property_manager do
  it_behaves_like 'a property setup importing controller' do
    let!(:building) { create(:building, property: property) }

    let(:import_fixture) { 'spec/fixtures/importing/building_list.xlsx' }
  end
end
