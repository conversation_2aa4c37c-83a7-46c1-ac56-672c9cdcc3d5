require 'rails_helper'
require_relative '../../shared/property_setup_importing_controller'

RSpec.describe Management::PropertySetups::FloorplansController,
               devise_login: :property_manager do
  it_behaves_like 'a property setup importing controller' do
    let!(:floorplan) { create(:floorplan, property: property) }

    let(:import_fixture) { 'spec/fixtures/importing/floorplan_list.xlsx' }
  end
end
