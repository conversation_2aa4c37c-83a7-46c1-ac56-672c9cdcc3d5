require 'rails_helper'
require_relative '../../shared/property_setup_importing_controller'

RSpec.describe Management::PropertySetups::LeasesController,
               devise_login: :property_manager do
  it_behaves_like 'a property setup importing controller' do
    let(:unit) { create(:unit, property: property) }

    let!(:lease) { create(:lease, unit: unit) }

    let(:import_fixture) { 'spec/fixtures/importing/rent_roll.xlsx' }
  end
end
