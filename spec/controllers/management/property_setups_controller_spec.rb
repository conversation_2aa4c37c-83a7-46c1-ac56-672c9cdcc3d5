require 'rails_helper'

RSpec.describe Management::PropertySetupsController,
               devise_login: :property_manager do
  let(:property) { create(:property, setup: setup) }

  let(:params) { { property_id: property.id } }

  context 'a setup property' do
    let(:setup) { true }

    describe 'GET #show' do
      it 'redirects out of setup' do
        get :show, params: params
        expect(response).to redirect_to(property_path(property))
      end
    end
  end

  context 'a new property' do
    let(:setup) { false }

    describe 'GET #show' do
      it 'returns http success' do
        get :show, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #land' do
      it 'returns http success' do
        get :land, params: params
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #finish' do
      it 'redirects to the setup property' do
        expect do
          get :finish, params: params
        end.to change { property.reload.setup? }.to(true)

        expect(response).to redirect_to(property_path(property))
      end
    end
  end
end
