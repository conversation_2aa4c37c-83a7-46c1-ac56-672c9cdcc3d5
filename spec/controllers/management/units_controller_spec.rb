require 'rails_helper'

RSpec.describe Management::UnitsController, devise_login: :property_manager do
  let(:property) { create(:property) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { property_id: property.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    context 'successful' do
      it 'redirects to the unit' do
        unit = create(:unit)

        allow(Unit::Create).to receive(:call) do
          OpenStruct.new(successful?: true, unit: unit)
        end

        post :create, params: { property_id: property.id }
        expect(response).to redirect_to(unit_path(unit))
      end
    end

    context 'unsuccessful' do
      it 'renders the new template' do
        allow(Unit::Create).to receive(:call) do
          OpenStruct.new(successful?: false, unit: Unit.new, errors: [])
        end

        post :create, params: { property_id: property.id }
        expect(response).to render_template(:new)
      end
    end
  end

  describe 'GET #show' do
    it 'returns http success' do
      unit = create(:unit)
      get :show, params: { id: unit.id }
      expect(response).to have_http_status(:success)
    end
  end
end
