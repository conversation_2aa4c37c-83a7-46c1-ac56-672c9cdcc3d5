require 'rails_helper'

RSpec.describe Management::ParkingSpacesC<PERSON>roller,
               devise_login: :property_manager do
  let!(:parking_space) { create(:parking_space) }

  let(:parking_lot) { parking_space.parking_lot }

  let(:params) { { parking_lot_id: parking_lot.id } }

  describe 'GET #index html' do
    it 'returns http success' do
      get :index, params: params
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #index xlsx' do
    it 'returns http success' do
      get :index, format: :xlsx, params: params
      expect(response).to have_http_status(:success)
    end
  end
end
