require 'rails_helper'

RSpec.describe PayleaseWebhooksController do
  let(:tenant) { create(:resident) }
  let(:card) { create(:cash_pay_card, tenant: tenant) }
  let(:membership) { tenant.current_lease_membership }
  let(:property) { membership.property }

  let!(:merchant_account) do
    create(:merchant_account,
           location_id: 'testpropertycode',
           user_api_key: 'testkey',
           developer_id: 'testpmid')
  end

  let!(:rent) do
    create(:rent_invoice,
           description: 'Rent Invoice',
           seller: property,
           buyer: tenant,
           membership: membership,
           amount: Monetize.parse('$1234.56'))
  end

  describe 'processed' do
    it 'handles a processed payment' do
      time = Time.zone.now

      params = {
        pm_id: 'testpmid',
        key: 'testkey',
        PropertyCode: 'testpropertycode',
        ResidentID: card.external_id,
        TDate: time.strftime('%Y-%m-%d %H:%M:%S'),
        TAmount: '1234.56',
        PayLeaseTransactionID: '483akjfdfdsai8r'
      }

      Apartment::Tenant.switch do
        perform_enqueued_jobs do
          post :processed, params: params
        end
      end

      expect(response).to have_http_status :no_content

      pymt = tenant.paid_payments.last
      expect(pymt.amount).to eq Monetize.parse('$1234.56')
      expect(pymt.date).to eq time.to_date
      expect(pymt.pay_lease_transaction.pay_lease_id).to eq '483akjfdfdsai8r'
    end

    it 'handles a processed payment in json' do
      time = Time.zone.now

      params = {
        pm_id: 'testpmid',
        key: 'testkey',
        PropertyCode: 'testpropertycode',
        ResidentID: card.external_id,
        TDate: time.strftime('%Y-%m-%d %H:%M:%S'),
        TAmount: '1234.56',
        PayLeaseTransactionID: '483akjfdfdsai8r'
      }

      Apartment::Tenant.switch do
        perform_enqueued_jobs do
          post :processed, format: :json, body: params.to_json
        end
      end

      expect(response).to have_http_status :no_content

      pymt = tenant.paid_payments.last
      expect(pymt.amount).to eq Monetize.parse('$1234.56')
      expect(pymt.date).to eq time.to_date
      expect(pymt.pay_lease_transaction.pay_lease_id).to eq '483akjfdfdsai8r'
    end
  end

  describe 'cancelled' do
    it 'marks a payment as canceled'
    it 'handles auth'
  end
end
