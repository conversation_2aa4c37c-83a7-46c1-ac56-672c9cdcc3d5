require 'rails_helper'

RSpec.describe MailgunController do
  describe 'POST #webhook' do
    it 'updates the delivery status of a mail receipt' do
      receipt = create(:email_receipt, status: :pending, open_count: 0)

      key = ENV.fetch('MAILGUN_INGRESS_SIGNING_KEY')
      timestamp = Time.zone.now.to_i
      token = SecureRandom.hex
      data = [timestamp, token].join
      digest = OpenSSL::Digest.new('SHA256')
      signature = OpenSSL::HMAC.hexdigest(digest, key, data)

      payload = {
        signature: {
          timestamp: timestamp,
          token: token,
          signature: signature
        },
        'event-data' => {
          event: 'opened',
          'user-variables' => {
            customer: Customer.current_subdomain,
            token: receipt.token
          }
        }
      }

      post :webhook, params: payload

      receipt.reload
      expect(receipt).to be_opened
      expect(receipt.open_count).to eq(1)
    end
  end
end
