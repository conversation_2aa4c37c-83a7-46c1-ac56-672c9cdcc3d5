require 'rails_helper'

RSpec.describe ApplyController do
  let(:unit) { create(:unit) }

  describe 'GET #new' do
    it 'returns http success' do
      get :new, params: { floorplan_id: unit.floorplan_id }

      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'redirects to the new application' do
      post :create, params: { floorplan_id: unit.floorplan_id }

      application = LeaseApplication.last!
      applicant = application.applicants.first

      expect(response).to redirect_to(
        hosted_application_applicant_path(
          hosted_application_uuid: application.uuid, id: applicant.id
        )
      )
    end
  end

  context 'with an unsubmitted application' do
    let(:application) { create(:lease_application) }

    describe 'GET #success' do
      it 'redirects to the application' do
        get :success, params: { uuid: application.uuid }

        expect(response).to redirect_to(
          hosted_application_path(application.uuid)
        )
      end
    end
  end
end
