require 'rails_helper'

RSpec.describe TheClosingDocs::WebhooksController do
  describe 'POST #create' do
    it 'calls the refresh service' do
      screening_group = create(:the_closing_docs_screening_group, :submitted)

      perform_enqueued_jobs do
        expect(TheClosingDocs::ScreeningGroup::Refresh).to \
          receive(:call).with(screening_group)

        post :create, params: {
          code: 'BEGIN_SCREENING',
          screening_group_id: screening_group.api_id
        }
      end
    end
  end
end
