require 'rails_helper'
require_relative '../shared/electronic_signatures_context'

RSpec.describe ElectronicSignaturesController do
  include_context 'electronic signatures'

  let!(:electronic_signature) { create(:electronic_signature) }

  describe 'GET #show' do
    it 'returns http success' do
      get :show, params: { uuid: electronic_signature.uuid }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'PATCH #update' do
    it 'returns http success' do
      patch :update, format: :js, xhr: true, params: {
        uuid: electronic_signature.uuid,
        electronic_signature: {
          agree: '1',
          full_name: '<PERSON>',
          signature: 'base64'
        }
      }

      expect(response).to have_http_status(:success)
    end
  end
end
