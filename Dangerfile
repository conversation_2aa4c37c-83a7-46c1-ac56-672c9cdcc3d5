# Expect PR subjects with a ticket number like REV-123
unless /\A[A-Z]+-[0-9]+/.match?(github.pr_title)
  warn 'Generally PR subjects should start with a ticket number, e.g. REV-123'
end

# Add a warning for work in progress PRs that shouldn't be merged yet
if github.pr_draft? || github.pr_title.match?(/\bWIP|DRAFT\b/)
  warn 'This PR is in a draft state'
end

# Reject merge commits
if git.commits.any? { |c| c.message.start_with?('Merge branch') }
  fail 'Please rebase to remove any merge commits in this PR'
end

# https://github.com/jonallured/danger-commit_lint
commit_lint.check warn: :all, disable: %i[subject_length]

# Run rubocop on files changed in this PR. The force_exclusion option ensures
# that files excluded in .rubocop.yml are not checked, even if they are
# explicitly passed to rubocop as changed files.
rubocop.lint force_exclusion: true,
             include_cop_names: true,
             only_report_new_offenses: true

# Run brakeman on files changed in this PR
brakeman.lint

# Check for TODOs in modified files
todoist.warn_for_todos

# Display a summary of gem dependency changes
gem_changes.summarize_changes
