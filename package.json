{"name": "revela", "description": "description", "devDependencies": {"babel-eslint": "^9.0.0", "eslint": "^4.8.0", "eslint-config-airbnb": "^15.1.0", "eslint-nibble": "^4.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.4.0", "imports-loader": "^0.8.0", "react-hot-loader": "^1.3.0", "webpack-cli": "^4", "webpack-dev-server": "^1", "webpack-notifier": "^1", "wrangler": "^3.112.0"}, "dependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-do-expressions": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-proposal-pipeline-operator": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@excid3/uppy-activestorage-upload": "https://github.com/jevans-revela/uppy-activestorage-upload.git", "@observablehq/plot": "^0.6.0", "@rails/actiontext": "^7", "@rails/ujs": "~7.0", "@shopify/draggable": "^1.0.0-beta.12", "@uppy/aws-s3": "^4.2.3", "@uppy/core": "^4.4.2", "@uppy/dashboard": "^4.3.1", "@uppy/webcam": "^4.1.1", "@uppy/xhr-upload": "^4.3.2", "babel-loader": "^8.0.0", "classnames": "^2.2.5", "css-loader": "^0.25.0", "d3": "^4.2.6", "d3-format": "^1.0.2", "dragula": "^3.7.3", "dropzone": "^5", "element-closest": "^2.0.2", "file-loader": "^0.10.0", "filesize": "^3.5.10", "fomantic-ui": "2.8.7", "immutability-helper": "^2.3.1", "jquery-tablesort": "^0.0.11", "lodash": "^4.16.1", "magnific-popup": "^1.1.0", "moment": "^2.13.0", "nanoid": "^5.1.0", "natsort": "^1.0.6", "node-notifier": "^5.1.2", "pdfjs-dist": "2.7.570", "prop-types": "^15.6.0", "puppeteer": "^18.0.4", "qs": "^6.5.0", "react": "^16.0.0", "react-dom": "^16.0.0", "react-dropzone": "^4.1.0", "react-linkify": "^0.2.1", "react-recaptcha": "^2.2.6", "react-redux": "^5.0.6", "react-sizeme": "^2.3.5", "recompose": "^0.25.1", "redux": "^3.5.2", "redux-form": "^7.0.4", "redux-thunk": "^2.1.0", "signature_pad": "^2", "stimulus": "^2.0.0", "style-loader": "^0.13.1", "trix": "^1.2.0", "urijs": "^1.18.1", "url-loader": "^0.5.7", "uuid": "^2.0.2", "webpack": "^5", "whatwg-fetch": "^1.0.0", "worker-loader": "^3.0.8"}, "engines": {"node": "~20"}, "resolutions": {"graceful-fs": "^4.2.4"}, "scripts": {"postinstall": "scripts/build_semantic.sh && npm run build", "build": "webpack --config webpack/prod.config.js", "dev": "webpack -w --progress --config webpack/dev.config.js", "hot": "webpack-dev-server --hot --inline --config webpack/hot-dev.config.js", "heroku:postbuild": "npm run build"}}