{"particles": {"number": {"value": 45, "density": {"enable": true, "value_area": 600}}, "color": {"value": "#f05a2a"}, "shape": {"type": "circle", "stroke": {"width": 0, "color": "#000000"}, "polygon": {"nb_sides": 5}, "image": {"src": "img/github.svg", "width": 100, "height": 100}}, "opacity": {"value": 0.5, "random": false, "anim": {"enable": false, "speed": 1, "opacity_min": 0, "sync": false}}, "size": {"value": 2.5, "random": true, "anim": {"enable": false, "speed": 80, "size_min": 0.1, "sync": false}}, "line_linked": {"enable": true, "distance": 150, "color": "#f05a2a", "opacity": 0.4, "width": 1}, "move": {"enable": true, "speed": 1.5, "direction": "none", "random": false, "straight": false, "out_mode": "bounce", "bounce": false, "attract": {"enable": false, "rotateX": 600, "rotateY": 1200}}}, "retina_detect": true}